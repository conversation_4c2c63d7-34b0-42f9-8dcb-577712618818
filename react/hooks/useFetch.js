/*
 * @description: 自定义 Hooks
 */
import { useCallback, useState } from 'react';
import { message } from 'choerodon-ui';

const parseResponse = (data) => {
  if (data?.failed === true) {
    message.error(data?.message ?? 'Error occurred');
    return null;
  }
  return data;
};

/**
 * @description: 数据请求钩子, 用于处理 loading 问题
 * @param {Function} func 需要执行的请求 API
 * @return {Array<[Boolean, Function]>}
 */
export const useFetch = (func = () => {}) => {
  const [loading, setLoading] = useState(false);

  const fetchData = useCallback(
    async (...axiosConfig) => {
      setLoading(true);
      try {
        const res = await func(...axiosConfig);
        return parseResponse(res);
      } catch (err) {
        message.error(err?.message ?? 'Error occurred');
        return null;
      } finally { setLoading(false); }
    },
    [func]
  );
  return [loading, fetchData];
};
