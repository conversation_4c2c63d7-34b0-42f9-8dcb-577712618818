const config = {
  server: 'http://api.staging.saas.test.com',
  fileServer: 'http://minio.staging.saas.test.com',
  projectType: 'yqcloud',
  buildType: 'single',
  master: '@yqcloud/apps-master',
  port: 9092,
  theme: {
    'primary-color': '#2979ff',
    'icon-font-size-base': '16px',
  },
  devServerConfig: {
    allowedHosts: 'all',
    headers: [
      {
        key: 'Access-Control-Allow-Origin',
        value: '*',
      },
    ],
  },
  dashboard: {},
  modules: [
    '.',
  ],
  outward: '/iam/invite',
};

module.exports = config;
