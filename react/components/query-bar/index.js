import React from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';

import './index.less';

const QueryBar = (props) => {
  const { dataSet, queryDataSet, name, handleSearch, placeholder, buttons } = props;

  function defaultHandleSearch() {
    if (handleSearch) {
      handleSearch();
    } else {
      dataSet.query();
    }
  }

  return (
    <div className="query-bar">
      <Form dataSet={queryDataSet} style={{ width: '3rem' }} labelLayout="float">
        <TextField
          name={name}
          labelLayout="placeholder"
          prefix={<Icon type="icon-search" style={{ color: '#8c8c8c' }} />}
          valueChangeAction="blur"
          onEnterDown={defaultHandleSearch}
          placeholder={placeholder}
        />
      </Form>
      <div>
        { buttons }
      </div>
    </div>
  );
};

export default observer(QueryBar);
