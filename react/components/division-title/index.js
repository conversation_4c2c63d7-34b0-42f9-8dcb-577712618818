import React from 'react';
import { observer } from 'mobx-react-lite';
import './index.less';

const DivisionTitle = ((props) => {
  const { title, style = {} } = props;

  return (
    <div style={{ ...style }} className="division-content">
      <div className="division-line" />
      <div className="division-text">
        {title}
      </div>
    </div>
  );
});

export default observer(DivisionTitle);
