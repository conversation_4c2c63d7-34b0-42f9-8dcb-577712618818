const LocationDataSet = ({ tenantId }) => {
  const urlPrefix = '/iam/yqc';
  return {
    autoQuery: true,
    autoLocateFirst: false,
    dataKey: 'content',
    selection: 'single',
    pageing: false,
    idField: 'id',
    parentField: 'parentId',
    transport: {
      read: {
        url: `${urlPrefix}/${tenantId}/location`,
        method: 'get',
        params: {
          enabled_flag: true,
        },
      },
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'locationName', type: 'string' },
      { name: 'parentId', type: 'string' },
    ],
  };
};
export default LocationDataSet;
