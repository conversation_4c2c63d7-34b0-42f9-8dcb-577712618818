const EnabledFlagDataSet = ({ intl }) => {
  const valid = intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' });
  const inValid = intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' });

  return {
    fields: [
      { name: 'value', type: 'boolean' },
      { name: 'text', type: 'string' },
    ],
    data: [
      { value: false, text: inValid },
      { value: true, text: valid },
    ],
  };
};
export default EnabledFlagDataSet;
