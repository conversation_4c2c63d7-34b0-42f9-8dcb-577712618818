const TenantDataSet = ({ tenantId }) => {
  const urlPrefix = '/iam';
  
  return {
    autoQuery: true,
    autoQueryAfterSubmit: false,
    selection: false,
    paging: false,
    transport: {
      read: {
        url: `${urlPrefix}/v1/${tenantId}/tenant`,
        method: 'get',
      },
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'name', type: 'string' },
    ],
  };
};
export default TenantDataSet;
