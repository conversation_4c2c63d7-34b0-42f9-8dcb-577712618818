const CompanyDataSet = ({ tenantId }) => {
  const urlPrefix = '/iam/yqc';
  return {
    autoQuery: true,
    autoLocateFirst: false,
    dataKey: 'content',
    selection: 'single',
    pageing: false,
    idField: 'id',
    parentField: 'parentId',
    transport: {
      read: {
        url: `${urlPrefix}/${tenantId}/companies`,
        method: 'get',
        params: {
          enabled_flag: true,
        },
      },
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'companyName', type: 'string' },
      { name: 'parentId', type: 'string' },
    ],
  };
};
export default CompanyDataSet;
