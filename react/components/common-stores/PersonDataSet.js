const PersonDataSet = ({ tenantId }) => {
  const urlPrefix = '/iam';

  return {
    autoQuery: false,
    autoQueryAfterSubmit: false,
    selection: false,
    paging: true,
    pageSize: 50,
    dataKey: 'content',
    totalKey: 'totalPages',
    transport: {
      read: {
        url: `${urlPrefix}/v1/${tenantId}/persons?enabled_flag=true`,
        method: 'get',
      },
    },
    field: [
      { name: 'id', type: 'string' },
      { name: 'name', type: 'string' },
    ],
    queryFields: [
      { name: 'param', type: 'string' },
    ],
  };
};
export default PersonDataSet;
