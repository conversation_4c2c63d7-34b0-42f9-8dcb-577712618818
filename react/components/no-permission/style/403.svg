<?xml version="1.0" encoding="UTF-8"?>
<svg width="520px" height="400px" viewBox="0 0 520 400" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>images-403</title>
    <defs>
        <linearGradient x1="47.0652756%" y1="39.300423%" x2="50%" y2="70.3668017%" id="linearGradient-1">
            <stop stop-color="#D4D4D4" offset="0%"></stop>
            <stop stop-color="#F4F4F4" offset="100%"></stop>
        </linearGradient>
        <rect id="path-2" x="214.063279" y="197" width="228" height="129" rx="6"></rect>
        <filter x="-1.1%" y="-1.9%" width="102.2%" height="103.9%" filterUnits="objectBoundingBox" id="filter-3">
            <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="-1" dy="-2" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0682744565 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="53.2700277%" y1="48.1842183%" x2="50%" y2="50.4591429%" id="linearGradient-4">
            <stop stop-color="#F5F5F5" offset="0%"></stop>
            <stop stop-color="#E4E4E4" offset="100%"></stop>
        </linearGradient>
        <rect id="path-5" x="228.469882" y="211.214307" width="187.285839" height="115.252824" rx="6"></rect>
        <filter x="-1.3%" y="-2.2%" width="102.7%" height="104.3%" filterUnits="objectBoundingBox" id="filter-6">
            <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="-1" dy="-2" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0682744565 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="136.165855%" y1="49.3415748%" x2="-16.1558695%" y2="50%" id="linearGradient-7">
            <stop stop-color="#F7F7F7" offset="0%"></stop>
            <stop stop-color="#D2D2D2" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="178.566806%" y1="50%" x2="0%" y2="50%" id="linearGradient-8">
            <stop stop-color="#F3F3F3" offset="0%"></stop>
            <stop stop-color="#A0A0A0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="136.165855%" y1="49.5525931%" x2="-16.1558695%" y2="50%" id="linearGradient-9">
            <stop stop-color="#F7F7F7" offset="0%"></stop>
            <stop stop-color="#D2D2D2" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="136.165855%" y1="49.4459834%" x2="-16.1558695%" y2="50%" id="linearGradient-10">
            <stop stop-color="#F7F7F7" offset="0%"></stop>
            <stop stop-color="#D2D2D2" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="178.566806%" y1="50%" x2="0%" y2="50%" id="linearGradient-11">
            <stop stop-color="#F3F3F3" offset="0%"></stop>
            <stop stop-color="#A0A0A0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="63.3903952%" y1="0%" x2="63.3903952%" y2="150.27167%" id="linearGradient-12">
            <stop stop-color="#E5E5E5" offset="0%"></stop>
            <stop stop-color="#A5A5A5" offset="100%"></stop>
        </linearGradient>
        <path d="M355.728209,267.786577 C355.728209,291.073979 374.60638,309.952244 397.893876,309.952244 C421.181278,309.952244 440.059544,291.073979 440.059544,267.786577 C440.059544,244.499081 421.181278,225.62091 397.893876,225.62091 C374.60638,225.62091 355.728209,244.499081 355.728209,267.786577 Z" id="path-13"></path>
        <filter x="-3.0%" y="-3.0%" width="105.9%" height="105.9%" filterUnits="objectBoundingBox" id="filter-14">
            <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="-1" dy="-2" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0682744565 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="33.685902%" y1="-40.4555423%" x2="62.5%" y2="151.380215%" id="linearGradient-15">
            <stop stop-color="#F3F3F3" offset="0%"></stop>
            <stop stop-color="#A4A4A4" offset="100%"></stop>
        </linearGradient>
        <path d="M398.011003,266.966689 C398.907823,266.966689 399.634951,267.673848 399.634951,268.546265 L399.635559,273.740162 C400.556853,274.292857 401.173428,275.301392 401.173428,276.453964 C401.173428,278.200561 399.75752,279.616389 398.010952,279.616389 C396.264486,279.616389 394.848578,278.200561 394.848578,276.453964 C394.848578,275.301392 395.465153,274.292857 396.386417,273.740162 L396.387055,268.546265 C396.387055,267.722316 397.035636,267.045772 397.8632,266.973144 L398.011003,266.966689 Z" id="path-16"></path>
        <filter x="-39.5%" y="-19.8%" width="179.1%" height="139.5%" filterUnits="objectBoundingBox" id="filter-17">
            <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="-1" dy="-2" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0682744565 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="71.1472175%" y1="57.2389879%" x2="-17.4123908%" y2="31.3232376%" id="linearGradient-18">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#E8E8E8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100%" y1="100%" x2="0%" y2="100%" id="linearGradient-19">
            <stop stop-color="#F6F6F6" offset="0%"></stop>
            <stop stop-color="#CECECE" offset="100%"></stop>
        </linearGradient>
        <rect id="path-20" x="140.829714" y="290.450623" width="138.063279" height="68.4313644" rx="2"></rect>
        <filter x="-2.2%" y="-4.4%" width="104.3%" height="108.8%" filterUnits="objectBoundingBox" id="filter-21">
            <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="-1" dy="-2" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0682744565 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="108.259889%" y1="50%" x2="-19.7580915%" y2="50%" id="linearGradient-22">
            <stop stop-color="#ECECEC" offset="0%"></stop>
            <stop stop-color="#AEAEAE" offset="100%"></stop>
        </linearGradient>
        <rect id="path-23" x="150.434116" y="316.862729" width="14.406603" height="3.60165076" rx="1.80082538"></rect>
        <filter x="-17.4%" y="-69.4%" width="134.7%" height="238.8%" filterUnits="objectBoundingBox" id="filter-24">
            <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="-1" dy="-2" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0682744565 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="134.578%" y1="50%" x2="-19.7580915%" y2="50%" id="linearGradient-25">
            <stop stop-color="#EEEEEE" offset="0%"></stop>
            <stop stop-color="#B5B5B5" offset="100%"></stop>
        </linearGradient>
        <rect id="path-26" x="150.434116" y="308.458877" width="14.406603" height="3.60165076" rx="1.80082538"></rect>
        <filter x="-17.4%" y="-69.4%" width="134.7%" height="238.8%" filterUnits="objectBoundingBox" id="filter-27">
            <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="-1" dy="-2" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0682744565 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <rect id="path-28" x="150.434116" y="300.055025" width="14.406603" height="3.60165076" rx="1.80082538"></rect>
        <filter x="-17.4%" y="-69.4%" width="134.7%" height="238.8%" filterUnits="objectBoundingBox" id="filter-29">
            <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="-1" dy="-2" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0682744565 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="8.43750017%" x2="50%" y2="100%" id="linearGradient-30">
            <stop stop-color="#E7E7E7" offset="0%"></stop>
            <stop stop-color="#BBBBBB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="8.43750017%" x2="50%" y2="100%" id="linearGradient-31">
            <stop stop-color="#E7E7E7" offset="0%"></stop>
            <stop stop-color="#BBBBBB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="8.43750017%" x2="50%" y2="100%" id="linearGradient-32">
            <stop stop-color="#E7E7E7" offset="0%"></stop>
            <stop stop-color="#BBBBBB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="8.43750017%" x2="50%" y2="100%" id="linearGradient-33">
            <stop stop-color="#E7E7E7" offset="0%"></stop>
            <stop stop-color="#BBBBBB" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="新" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="403-pc" transform="translate(-460.000000, -139.000000)">
            <g id="images-403" transform="translate(460.000000, 139.000000)">
                <rect id="矩形" fill-opacity="0.01" fill="#FFFFFF" x="0" y="0" width="520" height="400"></rect>
                <path d="M471.142857,236.428571 L480.785714,236.428571 L480.785714,209.642857 L471.142857,209.642857 L471.142857,236.428571 Z M471.142857,285.714286 L480.785714,285.714286 L480.785714,258.928571 L471.142857,258.928571 L471.142857,285.714286 Z M447.571429,236.428571 L457.214286,236.428571 L457.214286,209.642857 L447.571429,209.642857 L447.571429,236.428571 Z M447.571429,285.714286 L457.214286,285.714286 L457.214286,258.928571 L447.571429,258.928571 L447.571429,285.714286 Z M412.214286,265.357143 L421.857143,265.357143 L421.857143,238.571429 L412.214286,238.571429 L412.214286,265.357143 Z M412.214286,315.714286 L421.857143,315.714286 L421.857143,288.928571 L412.214286,288.928571 L412.214286,315.714286 Z M387.571429,265.357143 L397.214286,265.357143 L397.214286,238.571429 L387.571429,238.571429 L387.571429,265.357143 Z M387.571429,315.714286 L397.214286,315.714286 L397.214286,288.928571 L387.571429,288.928571 L387.571429,315.714286 Z M329.714286,239.642857 L339.357143,239.642857 L339.357143,212.857143 L329.714286,212.857143 L329.714286,239.642857 Z M329.714286,291.071429 L339.357143,291.071429 L339.357143,264.285714 L329.714286,264.285714 L329.714286,291.071429 Z M306.142857,239.642857 L315.785714,239.642857 L315.785714,212.857143 L306.142857,212.857143 L306.142857,239.642857 Z M306.142857,291.071429 L315.785714,291.071429 L315.785714,264.285714 L306.142857,264.285714 L306.142857,291.071429 Z M265.428571,161.428571 L275.071429,161.428571 L275.071429,134.642857 L265.428571,134.642857 L265.428571,161.428571 Z M265.428571,210.714286 L275.071429,210.714286 L275.071429,183.928571 L265.428571,183.928571 L265.428571,210.714286 Z M265.428571,258.928571 L275.071429,258.928571 L275.071429,232.142857 L265.428571,232.142857 L265.428571,258.928571 Z M265.428571,308.214286 L275.071429,308.214286 L275.071429,281.428571 L265.428571,281.428571 L265.428571,308.214286 Z M241.857143,161.428571 L251.5,161.428571 L251.5,134.642857 L241.857143,134.642857 L241.857143,161.428571 Z M241.857143,210.714286 L251.5,210.714286 L251.5,183.928571 L241.857143,183.928571 L241.857143,210.714286 Z M241.857143,258.928571 L251.5,258.928571 L251.5,232.142857 L241.857143,232.142857 L241.857143,258.928571 Z M241.857143,308.214286 L251.5,308.214286 L251.5,281.428571 L241.857143,281.428571 L241.857143,308.214286 Z M165.785714,227.857143 L175.428571,227.857143 L175.428571,201.071429 L165.785714,201.071429 L165.785714,227.857143 Z M165.785714,277.142857 L175.428571,277.142857 L175.428571,250.357143 L165.785714,250.357143 L165.785714,277.142857 Z M142.214286,227.857143 L151.857143,227.857143 L151.857143,201.071429 L142.214286,201.071429 L142.214286,227.857143 Z M142.214286,277.142857 L151.857143,277.142857 L151.857143,250.357143 L142.214286,250.357143 L142.214286,277.142857 Z M61.8571429,204.285714 L71.5,204.285714 L71.5,177.5 L61.8571429,177.5 L61.8571429,204.285714 Z M61.8571429,253.571429 L71.5,253.571429 L71.5,226.785714 L61.8571429,226.785714 L61.8571429,253.571429 Z M38.2857143,204.285714 L47.9285714,204.285714 L47.9285714,177.5 L38.2857143,177.5 L38.2857143,204.285714 Z M38.2857143,253.571429 L47.9285714,253.571429 L47.9285714,226.785714 L38.2857143,226.785714 L38.2857143,253.571429 Z M456.798887,169.66037 L413.684274,197.324426 L413.684274,228.661226 L373.88617,228.661226 L373.88617,292.203646 L353.552813,292.203646 L353.552813,195.626275 L289.177011,195.626275 L289.177011,127.858224 L223.123213,92.5722372 L223.123213,226.133746 L196.018651,226.133746 L196.018651,251.546765 L179.929635,251.546765 L179.929635,189.702495 L167.690139,189.702495 L167.690139,128.470348 L158.530258,128.470348 L158.530258,50 L154.16747,50 L154.16747,128.470348 L145.007589,128.470348 L145.007589,189.702495 L124.871643,189.702495 L124.871643,265.092477 L78.2825926,265.092477 L78.2825926,142.253011 L19,168.870532 L19,350 L500.071429,350 L500.071429,197.324426 L456.798887,169.66037 Z" id="Fill-1" fill="url(#linearGradient-1)" opacity="0.151432292"></path>
                <g id="矩形-copy-13">
                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-2"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                </g>
                <path d="M442.063279,201.848609 L214.063279,201.848609 L214.063279,186.16177 C214.063279,182.758933 216.839926,180 220.264612,180 L435.861946,180 C439.28689,180 442.063279,182.758933 442.063279,186.16177 L442.063279,201.848609 Z" id="Fill-1" fill="url(#linearGradient-4)"></path>
                <path d="M230.230029,190.285535 C230.230029,192.421101 228.503604,194.152559 226.373988,194.152559 C224.244628,194.152559 222.51846,192.421101 222.51846,190.285535 C222.51846,188.149968 224.244628,186.41851 226.373988,186.41851 C228.503604,186.41851 230.230029,188.149968 230.230029,190.285535" id="Fill-5" fill="#FFFFFF"></path>
                <path d="M244.626432,190.285535 C244.626432,192.421101 242.900263,194.152559 240.770647,194.152559 C238.641287,194.152559 236.915118,192.421101 236.915118,190.285535 C236.915118,188.149968 238.641287,186.41851 240.770647,186.41851 C242.900263,186.41851 244.626432,188.149968 244.626432,190.285535" id="Fill-7" fill="#FFFFFF"></path>
                <path d="M259.023091,190.285535 C259.023091,192.421101 257.296922,194.152559 255.167306,194.152559 C253.037946,194.152559 251.311777,192.421101 251.311777,190.285535 C251.311777,188.149968 253.037946,186.41851 255.167306,186.41851 C257.296922,186.41851 259.023091,188.149968 259.023091,190.285535" id="Fill-9" fill="#FFFFFF"></path>
                <g id="矩形-copy-13" opacity="0.157296317">
                    <use fill="#C2C2C2" fill-rule="evenodd" xlink:href="#path-5"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                </g>
                <rect id="矩形-copy-19" fill="url(#linearGradient-7)" x="245.160043" y="228.470275" width="73.2335654" height="8.40385177" rx="4.20192588"></rect>
                <rect id="矩形-copy-26" fill="url(#linearGradient-8)" opacity="0.18" x="245.160043" y="282.495036" width="127.258327" height="9.60440202" rx="4.80220101"></rect>
                <rect id="矩形-copy-27" fill="url(#linearGradient-9)" x="245.160043" y="301.70384" width="88.8407187" height="8.40385177" rx="4.20192588"></rect>
                <rect id="矩形-copy-25" fill="url(#linearGradient-10)" x="245.160043" y="264.486783" width="91.2418192" height="9.60440202" rx="4.80220101"></rect>
                <rect id="矩形-copy-24" fill="url(#linearGradient-11)" opacity="0.18" x="245.160043" y="246.478529" width="146.467131" height="9.60440202" rx="4.80220101"></rect>
                <path d="M180.353916,256.862329 L185.617419,256.835216 L185.617419,232.824211 L248.78528,232.824211" id="路径-14" stroke="#D7D7D7" stroke-dasharray="3,3"></path>
                <path d="M164.840719,296.453374 L203.177958,296.453374 L203.177958,250.832465 L244.840719,250.832465" id="路径-14" stroke="#D7D7D7" stroke-dasharray="3,3"></path>
                <g id="Fill-1">
                    <use fill="url(#linearGradient-12)" fill-rule="evenodd" xlink:href="#path-13"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                </g>
                <path d="M388.907128,255.495221 C388.907128,250.533705 392.92752,246.511545 397.886854,246.511545 C402.846188,246.511545 406.866579,250.533705 406.866579,255.495221 L406.866579,259.996814 L411.356442,259.996814 L411.356442,255.497958 C411.356442,248.059475 405.329012,242.029464 397.893939,242.029464 L397.893869,242.029464 C390.458727,242.029464 384.431367,248.059475 384.431367,255.497958 L384.431367,259.996814 L388.907128,259.996814 L388.907128,255.495221 Z" id="Fill-1" fill="#FFFFFF"></path>
                <path d="M417.294803,287.229642 L378.610146,287.229642 C377.685008,287.229642 376.928204,286.473822 376.928204,285.550043 L376.928204,260.915923 C376.928204,259.992143 377.685008,259.236324 378.610146,259.236324 L417.294803,259.236324 C418.219801,259.236324 418.976745,259.992143 418.976745,260.915923 L418.976745,285.550043 C418.976745,286.473822 418.219801,287.229642 417.294803,287.229642" id="Fill-3" fill="#FFFFFF"></path>
                <g id="形状结合">
                    <use fill="url(#linearGradient-15)" fill-rule="evenodd" xlink:href="#path-16"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-17)" xlink:href="#path-16"></use>
                </g>
                <path d="M204.458877,261.637415 C199.154503,261.637415 194.854475,265.937443 194.854475,271.241817 C194.854475,276.54617 199.154503,280.846219 204.458877,280.846219 C209.763251,280.846219 214.063279,276.54617 214.063279,271.241817 C214.063279,265.937443 209.763251,261.637415 204.458877,261.637415" id="Fill-1" fill="#C9C9C9"></path>
                <path d="M207.611998,274.152395 L207.369444,274.394927 C207.168523,274.595869 206.842745,274.595869 206.641803,274.394927 L201.305765,269.058889 C201.104823,268.857968 201.104823,268.53219 201.305765,268.331248 L201.548297,268.088694 C201.749239,267.887773 202.075017,267.887773 202.275938,268.088694 L207.611998,273.424754 C207.81294,273.625696 207.81294,273.951453 207.611998,274.152395" id="Fill-3" fill="#FFFFFF"></path>
                <path d="M201.305756,274.152395 L201.54831,274.394927 C201.749231,274.595869 202.075009,274.595869 202.275951,274.394927 L207.611989,269.058889 C207.812931,268.857968 207.812931,268.53219 207.611989,268.331248 L207.369457,268.088694 C207.168515,267.887773 206.842737,267.887773 206.641816,268.088694 L201.305756,273.424754 C201.104814,273.625696 201.104814,273.951453 201.305756,274.152395" id="Fill-5" fill="#FFFFFF"></path>
                <g id="矩形-copy">
                    <use fill="url(#linearGradient-18)" fill-rule="evenodd" xlink:href="#path-20"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-21)" xlink:href="#path-20"></use>
                    <use stroke="url(#linearGradient-19)" stroke-width="1" xlink:href="#path-20"></use>
                </g>
                <g id="矩形" opacity="0.6">
                    <use fill="url(#linearGradient-22)" fill-rule="evenodd" xlink:href="#path-23"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-24)" xlink:href="#path-23"></use>
                </g>
                <g id="矩形" opacity="0.18">
                    <use fill="url(#linearGradient-25)" fill-rule="evenodd" xlink:href="#path-26"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-27)" xlink:href="#path-26"></use>
                </g>
                <g id="矩形" opacity="0.6">
                    <use fill="url(#linearGradient-22)" fill-rule="evenodd" xlink:href="#path-28"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-29)" xlink:href="#path-28"></use>
                </g>
                <path d="M226.068782,348.062688 C226.068782,348.730612 225.409586,349.277586 224.603903,349.277586 L215.528157,349.277586 C214.722474,349.277586 214.063279,348.730612 214.063279,348.062688 L214.063279,318.077627 C214.063279,317.409212 214.722474,316.862729 215.528157,316.862729 L224.603903,316.862729 C225.409586,316.862729 226.068782,317.409212 226.068782,318.077627 L226.068782,348.062688 Z" id="Fill-5" fill="url(#linearGradient-30)" opacity="0.831170945"></path>
                <path d="M209.261078,347.944914 C209.261078,348.677588 208.60185,349.277586 207.796128,349.277586 L198.720526,349.277586 C197.914803,349.277586 197.255575,348.677588 197.255575,347.944914 L197.255575,324.197615 C197.255575,323.464941 197.914803,322.86548 198.720526,322.86548 L207.796128,322.86548 C208.60185,322.86548 209.261078,323.464941 209.261078,324.197615 L209.261078,347.944914 Z" id="Fill-7-Copy" fill="url(#linearGradient-31)" opacity="0.531366257"></path>
                <path d="M241.675935,347.944914 C241.675935,348.677588 241.08263,349.277586 240.35748,349.277586 L232.189438,349.277586 C231.464287,349.277586 230.870983,348.677588 230.870983,347.944914 L230.870983,324.197615 C230.870983,323.464941 231.464287,322.86548 232.189438,322.86548 L240.35748,322.86548 C241.08263,322.86548 241.675935,323.464941 241.675935,324.197615 L241.675935,347.944914 Z" id="Fill-7-Copy-2" fill="url(#linearGradient-32)" opacity="0.618698847"></path>
                <path d="M192.453374,348.050155 C192.453374,348.72497 191.794147,349.277586 190.988424,349.277586 L181.912822,349.277586 C181.107099,349.277586 180.447872,348.72497 180.447872,348.050155 L180.447872,336.097919 C180.447872,335.423104 181.107099,334.870983 181.912822,334.870983 L190.988424,334.870983 C191.794147,334.870983 192.453374,335.423104 192.453374,336.097919 L192.453374,348.050155 Z" id="Fill-7" fill="url(#linearGradient-33)" opacity="0.624116443"></path>
                <path d="M258.483638,348.050155 C258.483638,348.72497 257.824411,349.277586 257.018688,349.277586 L247.943086,349.277586 C247.137363,349.277586 246.478136,348.72497 246.478136,348.050155 L246.478136,336.097919 C246.478136,335.423104 247.137363,334.870983 247.943086,334.870983 L257.018688,334.870983 C257.824411,334.870983 258.483638,335.423104 258.483638,336.097919 L258.483638,348.050155 Z" id="Fill-7" fill="url(#linearGradient-33)" opacity="0.624116443"></path>
                <line x1="179.247322" y1="301.336721" x2="261.544619" y2="301.336721" id="Stroke-42" stroke="#CACACA" stroke-width="0.76436019" stroke-linecap="round" stroke-linejoin="round"></line>
                <line x1="179.247322" y1="306.820093" x2="248.392046" y2="306.820093" id="Stroke-44" stroke="#CACACA" stroke-width="0.76436019" stroke-linecap="round" stroke-linejoin="round"></line>
                <rect id="矩形-copy-13" fill="#CCCCCC" fill-rule="nonzero" opacity="0.16" x="76" y="239.352203" width="104.447872" height="38.4176081" rx="2"></rect>
                <rect id="矩形-copy-19" fill="#DFDFDF" fill-rule="nonzero" x="85.604402" y="248.956605" width="43.2198091" height="6.00275126" rx="3.00137563"></rect>
                <rect id="矩形-copy-24" fill="#DADADA" fill-rule="nonzero" opacity="0.4" x="85.604402" y="262.162658" width="85.2390679" height="6.00275126" rx="3.00137563"></rect>
            </g>
        </g>
    </g>
</svg>