import React from 'react';
import { observer } from 'mobx-react-lite';
import { Tooltip } from 'choerodon-ui/pro';
import { YqAvatar, Icon } from '@zknow/components';
import { axios } from '@yqcloud/apps-master';
import { Spin } from 'choerodon-ui';
import omit from 'lodash/omit';
import Store from './store/store';
import { useFetch } from '../../hooks';

/**
 * @description 通过配置解析人员卡片信息
 * @param {Array} config 人员卡片配置
 * @param {Object} data 人员信息
 * @param {String} lang 语言环境 en_US | zh_CN
 * @returns {Array} 人员卡片信息
 */
const parsePersonInfo = (config = [], data = {}, lang = 'zh_CN') => {
  return config?.map(f => {
    const { label = {}, name, nameField = name } = f;
    return {
      label: label[lang],
      value: data[nameField] || '-',
    };
  });
};

const FieldItem = ({ label, value }) => {
  return <div className="field-wrap">
    <Tooltip theme="dark" title={label}>
      <div className="field-label">{label}</div>
    </Tooltip>
    <div className="field-value">{value}</div>
  </div>;
};

const CardContent = () => {
  const { intl, id, prefixCls, cacheMapRef, tenantId, language, personCardConfig, AppState } = React.useContext(Store);

  const [personInfo, setPersonInfo] = React.useState({});
  const [loading, fetchData] = useFetch(axios.get);
  const cacheMap = cacheMapRef?.current;
  const iCall = AppState?.getICall?.ref?.call;

  React.useEffect(() => {
    if (id) {
      if (cacheMap?.get(id)) {
        setPersonInfo(cacheMap?.get(id));
      } else {
        fetchData(`/lc/v1/${tenantId}/card_configs/apply?id=${id}`).then(res => {
          if (res) {
            const _personInfo = {
              imageUrl: res?.image_url,
              realName: res?.real_name,
              phoneNumber: res?.phone,
              cardInfo: parsePersonInfo(personCardConfig, res, language) || [],
              badges: res?.badges,
            };
            setPersonInfo(_personInfo);
            cacheMap.set(id, _personInfo); // set cache
          }
        });
      }
    }
  }, [id, cacheMap]);

  const renderBadges = () => {
    const total = personInfo.badges.totalElements;
    const count = total > 99 ? '99+' : (total || 0);
    return (
      <div className="field-wrap">
        <div className="field-label-badge">{intl.formatMessage({ id: 'iam.accountCard.desc.account.card.badges', defaultMessage: '勋章' })}</div>
        <div className="field-value-badge">
          <div className="field-value-badge-cell">
            {(personInfo.badges.content || []).map((badge, i) => (
              <Tooltip title={badge.badgeName} key={badge?.newestEarnedTime}>
                <span className="field-value-badge-item"><img
                  src={`${window._env_.ICON_SERVER}/static/itsm/medal/${badge.badgeIcon}`}
                  alt=""
                /></span>
              </Tooltip>
            ))}
            <div className="field-value-badge-cell-after">{intl.formatMessage({ id: 'iam.accountCard.desc.sample.badge.count', defaultMessage: '共{count}个' }, { count })}</div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Spin spinning={loading}>
      <div className={`${prefixCls}-wrap`}>
        <div className="head-wrap">
          <YqAvatar src={personInfo?.imageUrl || ''} size={32}>{personInfo.realName || ''}</YqAvatar>
          <div className="user-name">{personInfo?.realName}</div>
          {personInfo.phoneNumber && iCall
            ? (
              <div onClick={() => iCall(String(personInfo.phoneNumber))} className="phone-item">
                <Icon type="PhoneTelephone" fill="currentColor" theme="filled" />
              </div>
            )
            : null}
        </div>
        <div className="info-wrap">
          {(personInfo?.cardInfo || []).map(item => <FieldItem {...item} iCall={iCall} />)}
          {personInfo?.badges && renderBadges()}
        </div>
      </div>
    </Spin>
  );
};

// MainView
const AvatarTooltip = (props) => {
  const { placement = 'bottomLeft', popupClassName = '' } = props;
  const { prefixCls, cacheMapRef, id } = React.useContext(Store);

  React.useEffect(() => {
    // clear cache
    return () => { cacheMapRef?.current?.clear(); };
  }, []);

  if (!id) {
    return (
      <>
        {props.children}
      </>
    );
  }
  return (
    <Tooltip
      {...omit(props, ['children'])}
      placement={placement}
      popupClassName={`${prefixCls}-card ${popupClassName}`}
      title={() => <CardContent />}
    >
      {/* Tooltip children element must should wrapped as React Element! */}
      {React.isValidElement(props.children) ? props.children : <span>{props.children}</span>}
    </Tooltip>
  );
};
export default observer(AvatarTooltip);
