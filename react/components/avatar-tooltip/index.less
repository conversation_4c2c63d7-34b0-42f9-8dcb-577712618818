@import "~choerodon-ui/lib/style/themes/default";
@import "~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables";

.avatar-tooltip {
  &-card {
    padding-top: 0;
    padding-bottom: 0;
    width: 3rem;
    background: #ffffff;
    box-shadow: 0rem 0.02rem 0.08rem 0rem rgba(26, 20, 61, 0.1) !important;
    border-radius: 0.04rem;
    border: 0.01rem solid #e5e6eb;
    box-sizing: content-box;
    filter: none; //大坑: 默认有阴影滤镜
    > div {
      display: inline;

      > .c7n-pro-tooltip-popup-arrow {
        display: none; // 移除箭头
      }

      > .c7n-pro-tooltip-popup-inner {
        background: #ffffff;
        box-shadow: none;
        height: 100%;
        padding: 0.12rem 0.16rem;

        //FIX: 移除loading占位样式
        .c7n-spin-container::before {
          display: none;
        }

        .avatar-tooltip-wrap {
          > .head-wrap {
            display: flex;
            align-items: center;
            border-bottom: 1px solid #e5e6eb;
            padding-bottom: 0.12rem;
            margin-bottom: 0.08rem;

            span.c7n-avatar-string {
              // FIX: 头像负缩放变反
              transform: scale(1) translateX(-50%) !important;
            }

            > .user-name {
              margin-left: 0.04rem;
              margin-right: 8px;
              height: 0.22rem;
              font-size: 0.14rem;
              font-weight: 500;
              color: #12274d;
              line-height: 0.22rem;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }

            > .phone-item {
              display: inline-block;
              width: 16px;
              height: 16px;
              border-radius: 2px;
              background-color: @yq-primary-color-10;
              color: @primary-color;
              text-align: center;
              line-height: 18px;
              cursor: pointer;

              &:hover {
                background-color: @primary-color;
                color: #fff;
              }

              > .yqcloud-icon-park-wrapper {
                font-size: 12px;
              }
            }
          }

          > .info-wrap {
            > .field-wrap {
              display: table-row;

              > .field-label {
                max-width: 0.6rem;
                display: table-cell;
                font-size: 0.13rem;
                font-weight: 400;
                color: rgba(18, 39, 77, 0.65);
                margin-right: 0.16rem;
                text-align: right;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              > .field-value {
                padding: 0.05rem 0 0.05rem 0.16rem;
                word-break: break-all;
                font-size: 0.13rem;
                font-weight: 400;
                color: #12274d;
                // line-height: 0.22em;
              }

              .field-label-badge {
                max-width: 0.6rem;
                display: table-cell;
                font-size: 13px;
                font-weight: 400;
                color: rgba(18, 39, 77, 0.65);
                margin-right: 0.16rem;
                text-align: right;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                line-height: 32px;
              }

              .field-value-badge {
                display: table-cell;
                position: relative;
                height: 32px;

                &-cell {
                  position: absolute;
                  top: 0;
                  left: 0;
                  display: flex;
                  align-items: center;
                  height: 32px;
                  padding-left: 12px;

                  &-after {
                    flex: 1;
                    font-size: 12px;
                    font-weight: 400;
                    color: rgba(18, 39, 77, 0.65);
                    white-space: nowrap;
                    margin-left: 4px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }
                }

                &-item {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  flex: 0 0 28px;
                  width: 28px;

                  img {
                    width: 28px;
                    height: 28px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
