import React from 'react';
import { inject } from 'mobx-react';
import { formatterCollections } from '@zknow/utils';
import { injectIntl } from 'react-intl';

const Store = React.createContext();

const StoreProvider = inject('AppState', 'HeaderStore')(formatterCollections({ code: 'iam.accountCard' })(injectIntl((props) => {
  const {
    AppState: { currentMenuType: { organizationId: tenantId }, currentLanguage: language },
    HeaderStore,
  } = props;
  const { personCardConfig = [] } = HeaderStore?.getTenantPersonConfig || [];
  const prefixCls = 'avatar-tooltip';
  const cacheMapRef = React.useRef(new Map());

  const value = {
    ...props,
    cacheMapRef,
    prefixCls,
    tenantId,
    language,
    personCardConfig,
  };
  return (<Store.Provider value={value}>{props.children}</Store.Provider>);
})));

export { Store as default, StoreProvider };
