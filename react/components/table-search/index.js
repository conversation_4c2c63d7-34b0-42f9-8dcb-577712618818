import React, { useState, useCallback } from 'react';
import { TextField } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import './index.less';

const SearchLine = ({
  onSearch,
  placeholder,
}) => {
  const [value, setValue] = useState();
  const handleSearch = useCallback((e) => onSearch(e.target.value), []);
  const handleChange = useCallback((v) => setValue(v), []);

  return (
    <div className="table-search-line">
      <TextField
        className="table-search-input"
        prefix={<Icon className="search-icon" type="icon-search" />}
        value={value}
        onEnterDown={handleSearch}
        onChange={handleChange}
        enterButton
        placeholder={placeholder}
      />
    </div>
  );
};

export default SearchLine;
