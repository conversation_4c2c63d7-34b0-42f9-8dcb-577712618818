import React from 'react';
import { injectIntl } from 'react-intl';

import './index.less';

const EnabledFlag = injectIntl((props) => {
  const { enabledFlag, intl, enabledText, disabledText } = props;

  const renderText = () => {
    if (enabledFlag === undefined || enabledFlag === null) {
      return '';
    } else if (enabledFlag) {
      return enabledText || intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' });
    } else {
      return disabledText || intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' });
    }
  };

  return (
    <span className="enabled-flag" name={`${enabledFlag}`}>
      {renderText()}
    </span>
  );
});

export default EnabledFlag;
