import { cloneElement } from 'react';

// const editors = new Map();
if (!window.LC_EDITORS) {
  window.LC_EDITORS = new Map();
}

// 需要默认过滤
const filters = ['VariablePage', 'ManyToMany', 'Chart', 'Lov'];

const EditorRegister = {
  registry(type, value) {
    if (value.icon) {
      value.icon = cloneElement(value.icon, {});
    }
    if (!window.LC_EDITORS.get(type)) {
      window.LC_EDITORS.set(type, value);
    }
  },
  get(type) {
    return window.LC_EDITORS.get(type);
  },
  keys() {
    return [...window.LC_EDITORS.keys()].filter(
      (key) => !filters.includes(key)
    );
  },
  clear() {
    window.LC_EDITORS.clear();
  },
};

export default EditorRegister;
