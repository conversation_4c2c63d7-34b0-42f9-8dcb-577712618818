import React, { useContext, useEffect } from 'react';
import stores from './stores';

export default function (props) {
  const { menuMap } = props;
  const { currentPage, setCurrentPage } = useContext(stores);

  const signCurrentUrl = () => {
    if (currentPage) { // 如果currentPage有值 则把pageCode=currentPage放在链接的最后（如果之前就有pageCode则替换，没有pageCode则加上）
      const param = `pageCode=${currentPage}`;
      let currentUrl = window.location.href;
      if (currentUrl.includes('pageCode')) {
        // 原先有params就要去掉
        currentUrl = currentUrl.slice(0, currentUrl.indexOf('pageCode') - 1);
      }
      const updateUrl = `${currentUrl}&${param}`;
      window.history.replaceState({ path: updateUrl }, '', updateUrl);
    }
  };

  useEffect(() => {
    if (window.location.href.includes('pageCode')) { // 如果当前url包含pageCode==='XXX'，则设置当前页为XXX
      const urlString = window.location.href;
      const regex = /[?&]pageCode=([^&#]+)/;
      const match = urlString.match(regex);
      const pageCode = match[1] || menuMap[0]?.code;
      if (pageCode === 'undefined') { // 某些错误情况下pageCode会错误地成为undefined，也得不能让他报错
        setCurrentPage(menuMap[0]?.code);
      } else { // 这里是正常情况
        setCurrentPage(pageCode); // 取pageCode的值
      }
    } else {
      setCurrentPage(menuMap[0]?.code);
    }
  }, []);

  useEffect(() => {
    signCurrentUrl(); // 每次切换页面都要注册url
  }, [currentPage]);

  const chooseDisplayPage = (code) => {
    let res;
    menuMap.forEach((item, index) => {
      if (code === item.code) {
        res = index;
      }
    });
    return res;
  };

  if (currentPage && menuMap) {
    return React.createElement(menuMap[chooseDisplayPage(currentPage)].component, { ...props });
  } else {
    return React.createElement(menuMap[0].component, { ...props });
  }
}
