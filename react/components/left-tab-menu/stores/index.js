import React, { useEffect, useMemo, useState } from 'react';
import { injectIntl } from 'react-intl';
import { inject } from 'mobx-react';

const Store = React.createContext({});
export default Store;

export const StoreProvider = injectIntl(
  inject(
    'AppState',
    'HeaderStore',
  )((props) => {
    const {
      intl,
      children,
      AppState: {
        currentMenuType: { tenantId },
        currentLanguage,
      },
      match: {
        params: { itemId },
      },
      HeaderStore: {
        getTenantConfig: { menuTheme = 'blue', userCriteriaFlag },
      },
      edit: initEdit, // 继承自父组件的edit状态，如果这个edit===undefined则默认不可编辑状态
    } = props;

    const [currentPage, setCurrentPage] = useState();
    const [edit, setEdit] = useState(initEdit || false); // initEdit是继承自父组件的edit状态，如果initEdit===undefined则默认不可编辑状态

    const isLightTheme = ['gray', 'white'].includes(menuTheme);
    const value = {
      ...props,
      intl,
      itemId,
      tenantId,
      currentLanguage,
      isLightTheme,
      menuTheme,
      currentPage,
      setCurrentPage,
      edit,
      setEdit,
    };

    return <Store.Provider value={value}>{children}</Store.Provider>;
  }),
);
