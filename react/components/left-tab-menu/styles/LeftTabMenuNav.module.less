@import "~choerodon-ui/lib/style/themes/default";
@import "~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables";

.wrapper {
  padding: 8px;
  width: 100%;
  height: 100%;
  background: #266ce2;
  color: #fff;
  overflow-x: hidden;
  overflow-y: auto;
  overflow-y: overlay;

  .back {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    margin: 0 8px 20px 8px;
    padding: 7px;
    cursor: pointer;

    &:hover {
      background: rgba(255, 255, 255, 0.25);
    }
  }

  .navItem {
    width: 48px;
    height: 48px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 7px 0;
    margin-top: 12px;
    cursor: pointer;

    &:hover,
    &.navItemActive {
      color: #fff;
      background: rgba(255, 255, 255, .25);
    }

    .text {
      transform: scale(0.715);
      word-break: break-all;
      white-space: nowrap;
    }
  }
}

.light-theme {
  .back {
    background: rgba(18, 39, 77, .06);
    &:hover {
      background: rgba(18, 39, 77, .06);
    }
  }
  .navItem {
    &:hover,
    &.navItemActive {
      color: @primary-color;
      background: @yq-primary-color-10;
    }
  }
}

.blue {
  composes: wrapper;
  background: #266ce2;
}
.dark-blue {
  composes: wrapper;
  background: #12274d;
}
.gray {
  composes: wrapper;
  composes: light-theme;
  background: #eff1f4;
  color: @yq-text-4;
}
.white {
  composes: wrapper;
  composes: light-theme;
  background: #fff;
  color: @yq-text-4;
  border-right: 1px solid #e5e8ed;
}
