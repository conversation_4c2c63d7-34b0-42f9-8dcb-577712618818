import React from 'react';
import { observer } from 'mobx-react-lite';
import { withRouter } from 'react-router-dom';
import { Icon } from '@zknow/components';
import { Tooltip, Modal } from 'choerodon-ui/pro';
import { useContext } from 'react';
import classnames from 'classnames';
import styles from './styles/LeftTabMenuNav.module.less';
import stores from './stores';

const LeftNavigation = (props) => {
  const { menuMap } = props;
  const { intl, history, menuTheme, currentPage, setCurrentPage } = useContext(stores);
  const historyPush = (url, clearFromItem) => {
    let search = history.location.search;
    if (clearFromItem) {
      search = search.replace(/[?&]from=[\w]*/g, '');
      search = search.replace(/[?&]pageCode=[\w]*/g, '');
    }
    history.push(`${url}${search}`);
  };
  const handleReturnCheck = () => {
    // 返回时要去掉url上的pageCode
    const clearFromItem = true;
    historyPush('/iam/open_app_config', clearFromItem);
  };

  // React.useEffect(() => {
  //   const pathname = history.location.pathname;
  //   const pathList = pathname.split('/');
  //   const pathCode = pathList[pathList.length - 1];
  //   const menuCode = navItemsCode.includes(pathCode) ? pathCode : 'basic_info';
  // }, [history.location.pathname]);

  const handleChangePage = (code) => {
    setCurrentPage(code);
  };
  return (
    <div className={styles[menuTheme]}>
      <Tooltip placement="right" title={intl.formatMessage({ id: 'zknow.common.button.back', defaultMessage: '返回' })}>
        <div className={styles.back} onClick={handleReturnCheck}>
          <Icon type="Return" size={18} />
        </div>
      </Tooltip>
      {menuMap.map(({ code, icon, textCode, defaultMessage }) => {
        return (
          <div
            className={classnames(styles.navItem, currentPage === code ? styles.navItemActive : '')}
            key={code}
            onClick={() => handleChangePage(code)}
          >
            <Icon type={icon} size={18} />
            <span className={styles.text}>{intl.formatMessage({ id: textCode, defaultMessage })}</span>
          </div>
        );
      })}
    </div>
  );
};

export default withRouter(observer(LeftNavigation));
