import React, { useContext } from 'react';
import Store, { StoreProvider } from './stores';
import styles from './styles/LeftTabMenuDetail.module.less';
import LeftNavigation from './LeftNavigation';
import RightView from './RightView';

export default function LeftTabMenu(props) {
  return (
    <StoreProvider {...props}>
      <LeftAndRight {...props} />
    </StoreProvider>
  );
}

const LeftAndRight = (props) => {
  const { menuMap, setEdit, edit } = props; // 用户在父级定义的useState，如果用户定义了优先用用户定义的
  const context = useContext(Store);
  const { edit: _edit, setEdit: _setEdit, currentPage } = context; // store中定义的useState，如果用户未定义，则这个才生效
  return (
    <div className={styles.wrapper}>
      <div className={styles.left}>
        <LeftNavigation {...props} menuMap={menuMap} />
      </div>
      <div className={styles.right}>
        <RightView {...props} menuMap={menuMap} edit={edit || _edit} setEdit={setEdit || _setEdit} currentPage={currentPage} />
      </div>
    </div>
  );
};
