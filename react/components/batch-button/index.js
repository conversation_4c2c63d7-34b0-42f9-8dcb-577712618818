import React from 'react';
import { observer } from 'mobx-react-lite';
import { Dropdown, Menu } from 'choerodon-ui/pro';
import { Icon, Button } from '@zknow/components';

import './index.less';

const { Item } = Menu;

const BatchButton = (props) => {
  const { text, dataSet, items, disabled } = props;

  const actionDisabled = typeof disabled === 'undefined' ? dataSet?.selected.length <= 0 : disabled;

  const actionContent = () => (
    <Menu>
      {
        items.map(item => (
          <Item disabled={item.disabled}>
            <div className="batch-action-container" onClick={item.handleClick}>
              <Icon type={item.icon} style={{ marginRight: '0.08rem' }} />
              { item.text }
            </div>
          </Item>
        ))
      }
    </Menu>
  );

  return (
    <Dropdown trigger="click" overlay={actionContent()}>
      <Button funcType="raised" disabled={actionDisabled}>
        { text }
        <Icon type="icon-drop-down" style={{ marginLeft: '0.08rem' }} />
      </Button>
    </Dropdown>
  );
};

export default observer(BatchButton);
