import React from 'react';
import { StoreProvider } from './stores';
import MainView from './MainView';

const FlowRender = (props) => (
  <StoreProvider {...props}>
    <MainView />
  </StoreProvider>
);

const toDomainElements = (dataSet) => {
  const nodes = dataSet.map(r => ({
    id: r.get('id'),
    data: {
      label: r.get('name'),
      description: r.get('description'),
      path: r.get('path'),
      level: r.get('path').split('/').length,
      hasChildren: dataSet.some(x => x.get('parentId') === r.get('id')),
    },
    position: {
      x: 0,
      y: 0,
    },
    dragHandle: '.itsm-component-flow-render-domain-node',
    type: 'domain',
  }));

  const edges = [];

  dataSet.forEach(r => {
    if (r.get('parentId')) {
      edges.push({
        id: `${r.get('parentId')}-${r.get('id')}`,
        source: r.get('parentId'),
        target: r.get('id'),
        arrowHeadType: 'arrowclosed',
      });
    }
  });
  return [...nodes, ...edges];
};

export default FlowRender;
export { toDomainElements };
