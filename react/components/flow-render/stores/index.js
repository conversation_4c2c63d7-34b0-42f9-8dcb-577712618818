import React, { createContext } from 'react';
import ElementStore from './ElementStore';

const Store = createContext();

export default Store;

export const StoreProvider = props => {
  const { children, elements } = props;
  const elementStore = ElementStore();

  const value = {
    ...props,
    elements,
    elementStore,
  };
  return <Store.Provider value={value}>{children}</Store.Provider>;
};
