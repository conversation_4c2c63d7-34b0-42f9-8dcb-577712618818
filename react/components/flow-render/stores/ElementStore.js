import { useLocalStore } from 'mobx-react-lite';

export default function ElementsStore() {
  return useLocalStore(() => ({
    elements: [],

    setElements(data) {
      this.elements = data;
    },

    get getElements() {
      return this.elements;
    },

    // 用来记录删除的元素，便于撤回操作
    deleteList: [],

    setDeleteList(data) {
      this.deleteList = data;
    },

    get getDeleteList() {
      return this.deleteList;
    },
  }));
}
