import React, { useContext, useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import ReactFlow, { MiniMap, Controls } from 'react-flow-renderer';
import { DomainNode } from './nodes';
import getLayoutElements from './layout';
import Store from './stores';

const NodeTypes = {
  domain: DomainNode,
};

const DefaultClassName = 'itsm-component-flow-render-common-node';

const MainView = () => {
  const { elements: initElements = [], elementStore } = useContext(Store);
  const [rfInstance, setRfInstance] = useState();

  useEffect(() => {
    initElements.forEach(element => {
      element.className = DefaultClassName;
    });
    elementStore.setElements(getLayoutElements(initElements.slice(), 'LR'));
  }, []);

  useEffect(() => {
    rfInstance && rfInstance.fitView();
  }, [rfInstance]);

  return (
    <>
      <ReactFlow
        onLoad={setRfInstance}
        nodeTypes={NodeTypes}
        elements={elementStore.getElements.slice()}
      >
        <MiniMap nodeStrokeWidth={3} />
        <Controls />
      </ReactFlow>
    </>
  );
};

export default observer(MainView);
