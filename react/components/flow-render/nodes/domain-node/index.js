import React, { useContext, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Handle, isEdge, isNode } from 'react-flow-renderer';
import { Icon } from '@zknow/components';
import Store from '../../stores';

import './index.less';

const prefixCls = 'itsm-component-flow-render-domain-node';

const DomainNode = (props) => {
  const {
    id,
    data: { label, description, path, level, hasChildren },
    sourcePosition = 'left',
    targetPosition = 'right',
  } = props;
  const { elementStore } = useContext(Store);
  const [collapse, setCollapse] = useState(false);

  const handleClose = () => {
    const elements = elementStore.getElements.slice();
    const hidden = [];
    // find node
    elements.forEach(el => {
      if (isNode(el) && el.data.level > level) {
        if (el.data.path.includes(path) && el.data.path !== path) {
          el.isHidden = !collapse;
          hidden.push(el.id);
        }
      }
    });
    // find edge
    elements.forEach(el => {
      if (isEdge(el) && hidden.includes(el.target)) {
        el.isHidden = !collapse;
      }
    });
    elementStore.setElements(elements);
    setCollapse(!collapse);
  };

  return (
    <div className={`${prefixCls}-container`}>
      <div className={prefixCls}>
        <Handle
          type="target"
          position={targetPosition}
          id="left"
          style={{ visibility: 'hidden' }}
          isConnectable={false}
        />
        <div className={`${prefixCls}-label`}>
          {label}
        </div>
        {
          description && (
            <div className={`${prefixCls}-desc`}>
              {description}
            </div>
          )
        }
        <Handle
          type="source"
          position={sourcePosition}
          id="right"
          style={{ visibility: 'hidden' }}
          isConnectable={false}
        />
      </div>
      {
        hasChildren && (
          <div className={`${prefixCls}-button`}>
            <Icon type={collapse ? 'right-c' : 'left-c'} onClick={handleClose} style={{ color: '#595959' }} />
          </div>
        )
      }
    </div>
  );
};

export default observer(DomainNode);
