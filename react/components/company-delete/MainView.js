import React from 'react';
import classnames from 'classnames';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { Modal, Menu } from 'choerodon-ui/pro';
import { Button, Icon } from '@zknow/components';

import './index.less';

export default injectIntl(observer((props) => {
  const { config, intl, tableLineRecord, feature = 'table-action-out' } = props;
  const { icon, name, id, color = 'default' } = config;
  const flag = tableLineRecord.get('isLeaf');
  async function handleDelete() {
    Modal.confirm({
      title: intl.formatMessage({ id: 'iam.components.desc.company.delete.confirm', defaultMessage: '请确认是否删除该公司？' }),
    }).then(async btn => {
      if (btn === 'ok') {
        await tableLineRecord.dataSet.delete(tableLineRecord, false);
      } else {
        return null;
      }
    });
  }

  if (feature === 'table-action') {
    return (
      <Menu.Item
        key={id}
        onClick={handleDelete}
      >
        <div className="yq-company-delete-btn">
          <Icon type={icon} />
          <span>{name}</span>
        </div>
      </Menu.Item>
    );
  }

  return (
    <Button
      className={classnames({
        'yq-company-delete-btn-tableBtnCenter': feature === 'table-action-outer',
      })}
      funcType="flat"
      color={color}
      icon={icon}
      key={id}
      onClick={handleDelete}
      disabled={!flag}
    >
      {name}
    </Button>
  );
}));
