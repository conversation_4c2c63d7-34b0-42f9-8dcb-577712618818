import axios from 'axios';
import { prompt } from '@zknow/utils';

/**
 * 数据请求后的错误拦截
 * 不建议使用此错误处理方法
 * @param data
 * @param hasReturn
 */
function handlePromptError(data, hasReturn = true) {
  if (hasReturn && !data) return false;

  if (data && data.failed) {
    prompt(data.message);
    return false;
  }

  return true;
}

/**
 * 参数 长度低于2则前面加 0，否则不加
 * @param {string | number} str
 * @returns {string}
 */
function padZero(str) {
  return str.toString().padStart(2, '0');
}

/**
 * 格式化时间，转化为 YYYY-MM-DD hh:mm:ss
 * @param {Date} timestamp
 * @returns {string}
 */
function formatDate(timestamp) {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minutes = date.getMinutes();
  const seconds = date.getSeconds();

  return `${[year, month, day].map(padZero).join('-')} ${[hour, minutes, seconds].map(padZero).join(':')}`;
}

/**
 * 计算剩余时间
 * @param now 当前时间 时间戳
 * @param end 结束时间 时间戳
 * @returns {string}
 */
function getTimeLeft(now, end) {
  if (now >= end) {
    return '剩余 0 天';
  }
  const resTime = end - now;
  const days = Math.floor(resTime / (24 * 3600 * 1000));
  return `剩余 ${days} 天`;
}

/**
 * 将毫秒数转为时分秒格式
 * @param time 毫秒数
 */
function timeConvert(time) {
  if (!time || typeof time !== 'number') {
    return;
  }
  // 毫秒转为秒
  const now = time / 1000;
  const sec = Math.floor((now % 60) % 60);
  const min = Math.floor(now / 60) % 60;
  const hour = Math.floor(now / 3600);

  let result = `${sec}s`;
  if (hour > 0) {
    result = `${hour}h ${min}m ${sec}s`;
  } else if (hour <= 0 && min > 0) {
    result = `${min}m ${sec}s`;
  }

  return result;
}

function removeEndsChar(str, char) {
  if (typeof str !== 'string') return '';

  return str.endsWith(char) ? str.slice(0, -1) : str;
}

function codeValidator(value, msg) {
  if (!/^[A-Z0-9_]+$/.test(value)) {
    return msg;
  }
}

/* 拷贝一份对对象 */
function deepClone(obj) {
  const copy = JSON.stringify(obj);
  const objClone = JSON.parse(copy);
  return objClone;
}

/**
 * @description: 递归生成树，获取树形结构，
 * @param {*} availableTree
 * @return {*}isNil
 */
const availableTree = (idField, parentField, allData, parentId = '0') => allData
  .filter(item => item[parentField] === parentId)
  .map(item => ({ ...item, children: availableTree(idField, parentField, allData, item[idField]) }));

/* 获取url上面参数的值, 针对于hash路由 */
function getQueryString(name) {
  const url = window.location.hash;
  const theRequest = {};
  if (url.indexOf('?') !== -1) {
    const str = url.substr(url.indexOf('?') + 1);
    const strs = str.split('&');
    for (let i = 0; i < strs.length; i += 1) {
      theRequest[strs[i].split('=')[0]] = decodeURIComponent(strs[i].split('=')[1]);
      if (theRequest[name]) {
        return theRequest[name];
      }
    }
  }
}

function getItfSearch(params) {
  // eslint-disable-next-line no-restricted-syntax
  for (const key in params) {
    if (key.startsWith('search_')) {
      params[key.split('search_')[1]] = params[key];
      delete params[key];
    }
  }
}

function download(fileName, content) {
  const aTag = document.createElement('a');
  const blob = new Blob([content]);
  aTag.download = fileName;
  aTag.href = URL.createObjectURL(blob);
  aTag.click();
  URL.revokeObjectURL(blob);
}

// 生成随机字符串
function randomString(len) {
  len = len || 8;
  const $chars = 'abcdefhijkmnprstwxyz'; /* 默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1 */
  const maxPos = $chars.length;
  let pwd = '';
  for (let i = 0; i < len; i++) {
    pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
  }
  return pwd;
}

let config;
// check password
async function checkPassword(password, tId, intl) {
  const intlPrefix = 'iam.person-info';
  if (!config) {
    config = await axios.get(`iam/yqc/${tId}/password-policies/query`);
  }
  const {
    /**
     * 至少包含几种字符类型
     * @type {number}
     */
    characterTypeCount,
    /**
     * 必须包含数字
     * @type {boolean}
     */
    digitsCount,
    /**
     * 密码最小长度
     * @type {number}
     */
    minLength,
    /**
     * 必须包含特殊字符
     * @type {boolean}
     */
    specialCharCount,
    /**
     * 必须包含大写
     * @type {boolean}
     */
    uppercaseCount,
    /**
     * 必须包含小写
     * @type {boolean}
     */
    lowercaseCount,
  } = config;
  const specialReg = new RegExp("[`~!@#$+^&*()=|{}':;',\\[\\].<>《》/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？ ]");

  if (characterTypeCount && characterTypeCount > 0) {
    let charCount = 0;
    if (/[A-Z]/.test(password)) charCount += 1;
    if (/[a-z]/.test(password)) charCount += 1;
    if (/[0-9]/.test(password)) charCount += 1;
    if (specialReg.test(password)) charCount += 1;
    if (charCount < characterTypeCount) return `${intl.formatMessage({ id: 'iam.common.desc.person.info.contain', defaultMessage: '至少包含' })}${characterTypeCount}${intl.formatMessage({ id: 'iam.common.desc.person.info.character.kind', defaultMessage: '种字符类型' })}`;
  }
  if (digitsCount && !/[0-9]/.test(password)) {
    return `${intl.formatMessage({ id: 'iam.common.desc.person.info.must.contain.number', defaultMessage: '必须包含数字' })}`;
  }
  if (lowercaseCount && !/[a-z]/.test(password)) {
    return `${intl.formatMessage({ id: 'iam.common.desc.person.info.must.contain.lowercase', defaultMessage: '必须包含小写字母' })}`;
  }
  if (uppercaseCount && !/[A-Z]/.test(password)) {
    return `${intl.formatMessage({ id: 'iam.common.desc.person.info.must.contain.uppercase', defaultMessage: '必须包含大写字母' })}`;
  }
  if (specialCharCount && !specialReg.test(password)) {
    return `${intl.formatMessage({ id: 'iam.common.desc.person.info.must.contain.specail.charcter', defaultMessage: '必须包含特殊符号' })}`;
  }
  if (password.length < minLength) {
    return `${intl.formatMessage({ id: 'iam.common.desc.person.info.length.greater.than', defaultMessage: '密码长度必须大于' })}${minLength}`;
  }
}

// 获取平铺树形数组某个节点的所有子节点 id
const getFlatTreeChildrenIds = (startId, allData) => {
  const children = allData.filter(o => o.parentId === startId).map(o => o.id);
  if (children.length === 0) {
    return [];
  }
  const results = [...children];
  children.forEach(id => {
    results.push(...getFlatTreeChildrenIds(id, allData));
  });
  return results;
};

function flat(arr) {
  return Array.isArray(arr) ? arr.reduce((prev, { key, value }) => ({ ...prev, [key]: value }), {}) : arr;
}

// 驼峰转下划线
function camelToSnake(str) {
  return str.replace(/[A-Z]/g, (match) => {
    return `_${ match.toLowerCase()}`;
  });
}

export {
  formatDate,
  getTimeLeft,
  timeConvert,
  handlePromptError,
  removeEndsChar,
  codeValidator,
  deepClone,
  availableTree,
  getQueryString,
  getItfSearch,
  download,
  randomString,
  checkPassword,
  getFlatTreeChildrenIds,
  flat,
  camelToSnake,
};
