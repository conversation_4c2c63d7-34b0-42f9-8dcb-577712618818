import axios from 'axios';

const toggleMenu = ({ type, tenantId, menu }) => {
  const menuId = menu.get('id');
  const enabledFlag = menu.get('enabledFlag');
  const urlPrefix = type === 'site' ? '/iam/yqc/v1/menuSolutionRelation' : `/iam/yqc/v1/${tenantId}/menuSolutionRelation`;
  const url = enabledFlag ? `${urlPrefix}/${menuId}/disable` : `${urlPrefix}/${menuId}/enable`;
  return axios.put(url);
};

const batchEnableMenu = ({ type, tenantId, data }) => {
  const url = type === 'site'
    ? '/iam/yqc/v1/menuSolutionRelation/batchEnable'
    : `/iam/yqc/v1/${tenantId}/menuSolutionRelation/batchEnable`;

  return axios.put(url, data);
};

const batchDisableMenu = ({ type, tenantId, data }) => {
  const url = type === 'site'
    ? '/iam/yqc/v1/menuSolutionRelation/batchDisable'
    : `/iam/yqc/v1/${tenantId}/menuSolutionRelation/batchDisable`;

  return axios.put(url, data);
};

const batchDelete = ({ type, tenantId, data }) => {
  const url = type === 'site'
    ? '/iam/yqc/v1/menuSolutionRelation/batchDelete'
    : `/iam/yqc/v1/${tenantId}/menuSolutionRelation/batchDelete`;

  return axios.put(url, data);
};

// 批量更新菜单
const batchUpdateMenu = ({ type, tenantId, data }) => {
  const url = type === 'site'
    ? '/iam/yqc/v1/menuSolutionRelation/batch'
    : `/iam/yqc/v1/${tenantId}/menuSolutionRelation/batch`;
  return axios.put(url, data);
};

export {
  toggleMenu,
  batchEnableMenu,
  batchDisableMenu,
  batchDelete,
  batchUpdateMenu,
};
