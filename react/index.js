import React from 'react';
import { Route, Switch } from 'react-router-dom';
import { inject } from 'mobx-react';
import { nomatch } from '@yqcloud/apps-master';
import { asyncRouter, formatterCollections } from '@zknow/utils';
import { ModalProvider } from 'choerodon-ui/pro';

import './index.less';

const AccountSite = asyncRouter(() => import('./routes/account-site'));
const Account = asyncRouter(() => import('./routes/account'));
const AccountLc = asyncRouter(() => import('./routes/account-lc'));
const SiteRole = asyncRouter(() => import('./routes/site-role'));
const Role = asyncRouter(() => import('./routes/role'));
const SecurityPolicy = asyncRouter(() => import('./routes/security-policy'));
const LDAP = asyncRouter(() => import('./routes/ldap'));
const PersonInfo = asyncRouter(() => import('./routes/person-info'));
const PasswordChange = asyncRouter(() => import('./routes/person-info/password/index'));
const VerifyEmail = asyncRouter(() => import('./routes/person-info/verify-email/index'));
const LoginHistory = asyncRouter(() => import('./routes/person-info/login-history'));
const OpenLoginConfig = asyncRouter(() => import('./routes/open-login-config'));
const AppMappingManagement = asyncRouter(() => import('./routes/app-mapping-management'));
const OpenAppBind = asyncRouter(() => import('./routes/person-info/open-app-bind/index'));
const SingleSignOn = asyncRouter(() => import('./routes/single-sign-on'));
const MenuConfig = asyncRouter(() => import('./routes/menu'));
const MenuSolution = asyncRouter(() => import('./routes/menu-solution'));
const Root = asyncRouter(() => import('./routes/root'));
const Department = asyncRouter(() => import('./routes/department'));
const Group = asyncRouter(() => import('./routes/group'));
const GroupLc = asyncRouter(() => import('./routes/group-lc'));
const Domain = asyncRouter(() => import('./routes/domain'));
const DomainHierarchy = asyncRouter(() => import('./routes/domain-hierarchy'));
const Invitation = asyncRouter(() => import('./routes/invitation'));
const Location = asyncRouter(() => import('./routes/location/LocationRouter.js'));
const Company = asyncRouter(() => import('./routes/company-lc'));
const BusinessUnit = asyncRouter(() => import('./routes/business-unit'));
const ClientAuthorization = asyncRouter(() => import('./routes/client-authorization'));
const Interface = asyncRouter(() => import('./routes/interface'));
const InterfaceRegister = asyncRouter(() => import('./routes/interface-register'));
const InterfaceMonitor = asyncRouter(() => import('./routes/interface-monitor'));
const AccountCard = asyncRouter(() => import('./routes/account-card'));
const Solution = asyncRouter(() => import('./routes/solution'));
const InterFaceLogin = asyncRouter(() => import('./routes/interface-login'));
const Manufacture = asyncRouter(() => import('./routes/manufacture'));

function IAMIndex({ match, AppState: { currentLanguage: language } }) {
  return (
    <div className="c7ncd-root">
      <ModalProvider>
        <Switch>
          <Route path={`${match.url}/site/account`} component={AccountSite} />
          <Route path={`${match.url}/account`} component={AccountLc} />
          <Route path={`${match.url}/site/role`} component={SiteRole} />
          <Route path={`${match.url}/role`} component={Role} />
          <Route path={`${match.url}/security_policy`} component={SecurityPolicy} />
          <Route path={`${match.url}/ldap`} component={LDAP} />
          <Route path={`${match.url}/person_info`} component={PersonInfo} />
          <Route path={`${match.url}/verify_email`} component={VerifyEmail} />
          <Route path={`${match.url}/open_app_bind`} component={OpenAppBind} />
          <Route path={`${match.url}/password_change`} component={PasswordChange} />
          <Route path={`${match.url}/login_history`} component={LoginHistory} />
          <Route path={`${match.url}/open_app_config`} component={OpenLoginConfig} />
          <Route path={`${match.url}/open_app_field_mapping`} component={AppMappingManagement} />
          <Route path={`${match.url}/site/open_app_config`} component={OpenLoginConfig} />
          <Route path={`${match.url}/single_sign_on`} component={SingleSignOn} />
          <Route path={`${match.url}/site/menu`} component={MenuConfig} />
          <Route path={`${match.url}/menu`} component={MenuConfig} />
          <Route path={`${match.url}/site/solution`} component={Solution} />
          <Route path={`${match.url}/solution`} component={Solution} />
          <Route path={`${match.url}/site/menu_solution`} component={MenuSolution} />
          <Route path={`${match.url}/menu_solution`} component={MenuSolution} />
          <Route path={`${match.url}/department`} component={AccountLc} />
          <Route path={`${match.url}/group`} component={GroupLc} />
          <Route path={`${match.url}/root`} component={Root} />
          <Route path={`${match.url}/domain`} component={Domain} />
          <Route path={`${match.url}/domain_hierarchy`} component={DomainHierarchy} />
          <Route path={`${match.url}/invite`} component={Invitation} />
          <Route path={`${match.url}/company`} component={Company} />
          <Route path={`${match.url}/location`} component={Location} />
          <Route path={`${match.url}/business_unit`} component={BusinessUnit} />
          <Route path={`${match.url}/client`} component={ClientAuthorization} />
          <Route path={`${match.url}/interface`} component={Interface} />
          <Route path={`${match.url}/interface_register`} component={InterfaceRegister} />
          <Route path={`${match.url}/interface_monitor`} component={InterfaceMonitor} />
          <Route path={`${match.url}/site/interface`} component={Interface} />
          <Route path={`${match.url}/site/interface_register`} component={InterfaceRegister} />
          <Route path={`${match.url}/site/interface_monitor`} component={InterfaceMonitor} />
          <Route path={`${match.url}/account_card`} component={AccountCard} />
          <Route path={`${match.url}/interface_login`} component={InterFaceLogin} />
          <Route path={`${match.url}/manufacture`} component={Manufacture} />
          <Route path="*" component={nomatch} />
        </Switch>
      </ModalProvider>
    </div>
  );
}

export default inject('AppState')(formatterCollections({
  code: ['iam.common', 'iam.components', 'iam.renderer'],
})(IAMIndex));
