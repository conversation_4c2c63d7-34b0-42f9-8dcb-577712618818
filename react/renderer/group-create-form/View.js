import React, { useMemo, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { Modal, message } from 'choerodon-ui/pro';
import { Button } from '@zknow/components';
import { axios } from '@yqcloud/apps-master';
import { injectIntl } from 'react-intl';
import { ExternalComponent } from '@zknow/components';

export default injectIntl(observer((props) => {
  const { tenantId, tableDataSet, config, intl, viewDataSet } = props;
  const { icon, name, id, color = 'default' } = config;
  const modalKey = Modal.key();
  const pageRef = useRef(null);

  const FormView = useMemo(() => <ExternalComponent
    viewCode="T_IAM_USER_GROUP_ADD"
    system={{ scope: 'lcr', module: 'PageLoader' }}
    mode="MODIFY"
    pageRef={pageRef} // 获取页面 Ref
  />, []);

  // 下划线转换驼峰
  function toHump(str) {
    return str.replace(/_(\w)/g, (all, letter) => {
      return letter.toUpperCase();
    });
  }

  /**
   * @description 数据保存, 调用原接口
   */
  const handleSave = async () => {
    if (pageRef.current) {
      const { formDataSet } = pageRef.current; // 取表单中的 formDataSet
      const formData = formDataSet.current?.toJSONData();
      // 剔除包含:的字段 下划线转驼峰
      const saveData = Object.entries(formData)
        .filter(([key]) => !key.includes(':'))
        .reduce((prev, [key, value]) => ({ ...prev, [toHump(key)]: value }), {});
      try {
        const res = await axios.post(`iam/yqc/${tenantId}/userGroups`,
          JSON.stringify(saveData));
        if (res?.failed) {
          message.error(res?.message || intl.formatMessage({ id: 'iam.renderer.desc.save.failed', defaultMessage: '保存失败' }));
          return false; // 不关闭弹窗
        } else {
          message.success(intl.formatMessage({ id: 'zknow.common.success.save', defaultMessage: '保存成功' }));
          tableDataSet.query();
          return true;
        }
      } catch (e) {
        message.error(intl.formatMessage({ id: 'iam.renderer.desc.save.failed', defaultMessage: '保存失败' }));
        throw new Error(e);
      }
    }
  };

  const handleOpenModal = () => {
    Modal.open({
      key: modalKey,
      title: intl.formatMessage({ id: 'iam.renderer.desc.group.create', defaultMessage: '新建人员组' }),
      style: { width: 800 },
      destroyOnClose: true,
      children: FormView,
      onOk: handleSave,
    });
  };

  return (<Button
    key={id}
    icon={icon}
    color={color}
    funcType="raised"
    onClick={handleOpenModal}
  >{name}</Button>);
}));
