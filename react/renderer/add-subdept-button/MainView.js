import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Modal } from 'choerodon-ui/pro';
import { Button } from '@zknow/components';
import FormView from './FormView';
import Store from './store';

import './index.less';

export default observer(() => {
  const {
    tenantId,
    tableDataSet,
    commonDataSet,
    config,
    intl,
    feature,
    intlPrefix,
    AppState: { currentMenuType: { domainId, domainName } },
  } = useContext(Store);
  const { icon, name, id: btnId, color = 'default' } = config;
  const modalKey = Modal.key();

  /**
   * @description: 创建弹窗
   */
  const handleOpenModal = () => {
    const currentRecord = tableDataSet.current;
    if (currentRecord) {
      commonDataSet.create({ parentId: currentRecord?.get('id'), parentName: currentRecord?.get('name') });
    } else {
      let newDepartment = {};
      if (domainId) {
        newDepartment = {
          domainManageFlag: true,
          domainId,
          domainName,
        };
      }
      commonDataSet.create(newDepartment);
    }

    const formProps = {
      intl,
      record: commonDataSet.current,
      intlPrefix,
      tenantId,
      commonDataSet,
    };
    /**
     * @description 新建子部门保存
     */
    const handleOk = async () => {
      const _currentRecord = commonDataSet.current;
      if (_currentRecord) {
        await commonDataSet.submit();
        await tableDataSet.query();
      }
    };

    Modal.open({
      key: modalKey,
      style: { width: 800 },
      title: intl.formatMessage({
        id: 'iam.renderer.group.createSub',
        defaultMessage: '新建子组',
      }),
      children: <FormView {...formProps} />,
      onOk: handleOk,
      onCancel: () => {
        commonDataSet.reset();
      },
      okText: intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' }),
    });
  };

  return (
    <Button
      className="group-add-subGroup-btn"
      key={btnId}
      icon={icon}
      color={color}
      funcType="flat"
      onClick={handleOpenModal}
    >{name}</Button>);
});
