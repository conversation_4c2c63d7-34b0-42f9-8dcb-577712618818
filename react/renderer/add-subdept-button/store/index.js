import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import CommonDataSet from './CommonDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
    } = props;
    const intlPrefix = 'group';
    const prefixCls = 'group';

    const commonDataSet = useMemo(() => new DataSet(CommonDataSet({ intlPrefix, intl, tenantId })), [intlPrefix, intl, tenantId]);

    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      tenantId,
      commonDataSet,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
));
