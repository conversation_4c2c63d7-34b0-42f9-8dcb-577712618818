import React from 'react';
import { Form, CheckBox, TextField, Lov, Select, Spin, TextArea } from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';

export default observer(({ commonDataSet: formDataSet, intl, record }) => {
  return (
    <Spin dataSet={formDataSet}>
      <Form
        record={record}
        labelLayout="horizontal"
        labelWidth="auto"
        columns={2}
      >
        <TextField name="name" autoFocus />
        <Lov name="ownerId" />
        <Lov name="parentName" disabled />
        <TextField name="email" />
        {/* <CheckBox name="domainEnabledFlag" /> */}
        {/* <Lov name="domainId" disabled={!record?.get('domainEnabledFlag')} /> */}
        <TextArea name="description" newLine colSpan={2} rows={1} autoSize={{ minRows: 1, maxRows: 4 }} resize="height" />
        {record?.get('parentId') && <Select name="type" />}
        {record?.get('parentId')?.id && <CheckBox name="recursiveFlag" />}
      </Form>
    </Spin>
  );
});
