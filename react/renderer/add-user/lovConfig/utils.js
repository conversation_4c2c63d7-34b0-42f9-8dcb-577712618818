/**
 * 表格视图，根据columns配置补全表格ds
 * @param fields
 * @param code
 * @param columns
 * @returns {Map<unknown, unknown>}
 */
export function getFieldMap(fields, code = 'id', columns) {
  const fieldMap = [];
  fields.map(field => {
    // 改为了所有添加进数据源的字段都是可编辑的字段，添加转换
    field.editable = true;
    fieldMap.push([field[code] || field.code, field]);
    return field;
  });
  // 根据表格列补全字段
  if (columns?.length) {
    columns.map(column => {
      const field = {
        ...column,
        name: column.label,
        editable: true,
        code: column.objectFieldPath,
        id: column.objectFieldPath, // objectFieldId会重复，改为objectFieldPath
        widgetConfig: {},
      };
      // 防止字段重复
      if (new Map(fieldMap).get(column[code] || column.code)) {
        return column;
      }
      try {
        field.widgetConfig = JSON.parse(column?.widgetConfig) || {};
      } catch (e) {
        // console.log(column?.widgetConfig);
      }
      fieldMap.push([field[code] || field.code, field]);
      return column;
    });
  }
  return new Map(fieldMap);
}

export function getQueryFieldMap({ fuzzySearch = [], searchType, advancedSearch = [] }) {
  const fields = searchType === 'advanced' ? advancedSearch : fuzzySearch;
  return getFieldMap(fields);
}

export function getQueryFields({ fuzzySearch = [], searchType, advancedSearch = [] }, transform) {
  if (searchType === 'advanced') {
    return advancedSearch
      .sort(({ hidden }, { hidden: hidden2 }) => Number(hidden) - Number(hidden2))
      .map(({ id }) => transform(id));
  } else if (searchType === 'fuzzy') {
    return fuzzySearch.map(({ id }) => transform(id));
  }
  return [];
}

export function transformField(fieldMap, field) {
  const { code, name } = field;
  const presetProps = {
    name: code,
    type: 'auto',
    label: name,
  };
  return presetProps;
}
