import { message, DataSet } from 'choerodon-ui/pro';
import moment from 'moment';
import { getFieldMap, getQueryFieldMap, getQueryFields } from './utils';

const OPTION_FIELDS = ['MultipleSelect', 'Select', 'SelectBox', 'Radio'];
const BOOLEAN_FIELDS = ['Switch', 'CheckBox'];
const DATE_FIELDS = ['DateTime', 'Date', 'Time'];
const DEFAULT_DATE_FORMAT = {
  DateTime: 'YYYY-MM-DD HH:mm:ss',
  Date: 'YYYY-MM-DD',
  Time: 'HH:mm:ss',
};

// TODO: 多个重复文件，需要整合
export const transformResponse = (data, lovTitle, transform, intl, tenantId) => {
  let originData = {};

  try {
    originData = JSON.parse(data);
    if (originData.failed) {
      message.error(originData.message);
      return {};
    }
  } catch (e) {
    return data;
  }

  const {
    pageSize = 20,
    jsonData,
  } = originData;

  const {
    layout,
    dataSource: {
      parentFieldCode, idField, nameFieldCode, searchType,
      advancedSearch, fuzzySearch, fields = [], lovWidth,
    },
  } = JSON.parse(jsonData);
  const textField = nameFieldCode || 'name';
  const valueField = 'id';
  const title = lovTitle || originData.name;
  const fieldMap = getFieldMap(fields, 'code');
  const _fuzzySearch = fuzzySearch.map(fuzzy => ({ ...fuzzy, id: `${fuzzy.id}${fuzzy.code}` }));
  const queryFieldMap = getQueryFieldMap({ searchType, advancedSearch, fuzzySearch: _fuzzySearch });
  let queryFields = [];
  if (queryFieldMap.size > 0) {
    queryFields = getQueryFields({ fuzzySearch: _fuzzySearch, searchType, advancedSearch }, (id) => transform(queryFieldMap, queryFieldMap.get(id)));
  }
  const lovItems = new Map();
  let queryFieldsStyle = {};
  const fieldOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' }), value: 'true' },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' }), value: 'false' },
    ],
  });
  queryFields.forEach((queryItem, index) => {
    const field = fieldMap.get(queryItem.name);
    const lovItem = {
      display: queryItem.label || queryItem.name,
      conditionField: 'Y',
      conditionFieldType: null,
      conditionFieldName: queryItem.id,
      conditionFieldSelectUrl: null,
      conditionFieldSelectVf: null,
      conditionFieldSelectTf: null,
      conditionFieldSelectCode: null,
      conditionFieldLovCode: null,
      conditionFieldSequence: index + 1,
      conditionFieldRequired: queryItem.required,
      gridField: 'N',
      fieldProps: {
        ...queryItem,
        options: BOOLEAN_FIELDS.includes(field?.widgetType) ? fieldOptions : undefined,
        type: BOOLEAN_FIELDS.includes(field?.widgetType) ? 'string' : 'auto',
      },
    };
    lovItems.set(queryItem.name, lovItem);
    queryFieldsStyle = {
      ...queryFieldsStyle,
      [queryItem.name]: {
        width: queryItem.width,
      },
    };
  });
  layout.forEach((tableItem, index) => {
    const { width, minWidth, name: fieldCode, displayFlag } = tableItem.props;
    const field = fieldMap.get(fieldCode);
    const item = lovItems.get(fieldCode);
    const fieldProps = {
      type: 'auto',
    };
    const { widgetConfig, widgetType } = field || {};
    const { dataSource, lookupCode, options } = widgetConfig || {};
    if (OPTION_FIELDS.includes(widgetType)) {
      if (dataSource === 'lookup') {
        fieldProps.lookupCode = lookupCode;
        fieldProps.valueField = 'value';
        fieldProps.textField = 'meaning';
        fieldProps.lookupUrl = (lpCode) => `/hpfm/v1/${tenantId === 0 ? '' : `${tenantId}/`}lookup/queryByCode/all?lookupTypeCode=${lpCode}`;
      } else if (dataSource === 'optionSet') {
        fieldProps.options = new DataSet({ paging: false, data: options || [] });
      }
    } else if (BOOLEAN_FIELDS.includes(widgetType)) {
      fieldProps.options = fieldOptions;
      fieldProps.type = 'string';
    }
    const lovItem = {
      gridFieldName: fieldCode || field?.code,
      gridFieldWidth: width,
      gridFieldMinWidth: minWidth,
      gridFieldAlign: tableItem.gridFieldAlign,
      gridField: displayFlag === undefined || displayFlag ? 'Y' : 'N',
      display: tableItem.props.label || tableItem.name,
      gridFieldSequence: index + 1,
      width,
      fieldProps: {
        ...fieldProps,
        placeholder: tableItem.placeholder || tableItem.props.label || tableItem.name,
        transformResponse: (value) => {
          if (field?.widgetType === 'RichText') {
            let res = value;
            try {
              if (typeof value === 'string') {
                res = JSON.parse(value);
              }
            } catch (e) {
              res = value;
            }
            if (typeof res === 'string') {
              res = value.replace(new RegExp('<[^>]*>', 'g'), ' ');
              res = res.replace(new RegExp('\\s+', 'g'), ' ');
              res = res.replace(new RegExp('&nbsp;', 'g'), '');
            } else if (res?.length) {
              res = res
                ?.filter(v => !v?.insert?.image)
                ?.map(v => v?.insert?.replace('\n', '') || '')
                ?.join('');
            }
            return res;
          } else if (value && DATE_FIELDS.includes(field?.widgetType)) {
            const fieldFormat = field.widgetConfig?.format?.replace('yyyy', 'YYYY')?.replace('mm', 'MM')?.replace('dd', 'DD')?.replace(':MM', ':mm');
            const realFormat = fieldFormat || DEFAULT_DATE_FORMAT[field?.widgetType];
            return moment(value)?.format(realFormat);
          }
          return value;
        },
      },
    };
    if (item) {
      Object.assign(item, lovItem);
    } else {
      lovItems.set(fieldCode, lovItem);
    }
  });

  function getColumns() {
    const tableColumns = [];
    const columns = [...lovItems.values()];
    columns.map((column, index) => {
      tableColumns.push({
        name: column.gridFieldName,
        lock: index === 0,
        width: column.width,
        hidden: column.gridField === 'N',
      });
      return column;
    });
    return tableColumns;
  }
  return {
    originData,
    title,
    width: lovWidth || 800,
    customUrl: null,
    lovPageSize: pageSize,
    lovItems: [...lovItems.values()],
    treeFlag: parentFieldCode ? 'Y' : 'N',
    parentIdField: parentFieldCode,
    idField: idField || 'id',
    textField,
    valueField,
    editableFlag: originData.editFlag === 'N' ? 'N' : 'Y',
    queryColumns: queryFields && queryFields.length ? 1 : 0,
    delayLoadFlag: true,
    tableProps: {
      columns: getColumns(),
      queryBarProps: {
        inlineSearch: false,
        queryFieldsStyle,
      },
    },
    dataSetProps: {
      primaryKey: 'primaryKey',
      paging: 'server',
    },
  };
};

export function transformRequest() {
  return [];
}
