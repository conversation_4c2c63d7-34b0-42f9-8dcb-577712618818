import { getQueryParams } from '@zknow/utils';
import axios from 'axios';
import { message } from 'choerodon-ui';
import { transformResponse } from '../lovConfig';
import { transformField } from '../lovConfig/utils';

export default ({ tenantId, intl, intlPrefix, groupId, userLovCode, tableDataSet }) => {
  return {
    autoQuery: false,
    selection: false,
    // autoCreate: false,
    paging: false,
    autoCreate: true,
    transport: {
    },
    fields: [
      {
        name: 'multipleChoiceValue',
        // label: multipleConfig.multipleLabel,
        type: 'object',
        idField: 'id',
        parentField: 'parentId',
        multiple: true,
        computedProps: {
          lovDefineAxiosConfig: () => {
            return (lovCode) => ({
              url: `/lc/v1/${tenantId}/object_options/id/${lovCode}`,
              method: 'GET',
              transformResponse: data => transformResponse(data, data?.name, (map, f) => transformField(map, f), intl, tenantId),
            });
          },
          lovQueryAxiosConfig: () => {
            return (lovCode, lovConfig = {}, { data, params }) => {
              lovConfig.method = 'POST';
              return {
                url: `/lc/v1/engine/${tenantId}/options/${lovCode}/queryWithCondition`,
                method: 'POST',
                data: {
                  params: {
                    ...getQueryParams(data, ['current_params', '__page_params']),
                    __page_params: data?.__page_params,
                  },
                },
                params,
                transformResponse: (originData) => {
                  try {
                    const jsonData = JSON.parse(originData);
                    return {
                      ...jsonData,
                      content: jsonData?.content?.map(item => {
                        return {
                          ...item,
                          primaryKey: item.id,
                        };
                      }) || [],
                    };
                  } catch (error) {
                    return [];
                  }
                },

              };
            };
          },
          lovCode: () => {
            if (userLovCode) {
              return userLovCode;
            }
            return undefined;
          },
        },
      },
    ],
    events: {
      update: async ({ dataSet, name, value, oldValue }) => {
        if (name === 'multipleChoiceValue') {
          const userIds = value.map(v => v?.id);
          const res = await axios.post(`/iam/yqc/${tenantId}/userGroups/assignUserForGroup?groupId=${groupId}`, userIds);
          if (!res?.failed) {
            message.success(intl.formatMessage({ id: 'iam.renderer.model.group.invite.success', defaultMessage: '邀请成功' }));
            tableDataSet.query();
          } else {
            message.error(res.message);
          }
          dataSet.reset();
        }
      },
    },
  };
};
