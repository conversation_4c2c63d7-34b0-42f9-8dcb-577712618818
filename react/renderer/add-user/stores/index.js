import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { withRouter } from 'react-router-dom';
import { DataSet } from 'choerodon-ui/pro';
import { flat } from '@/utils';
import UserDataSet from './UserDataSet';
import MultipleDataSet from './MultipleDataSet';
import useStore from './useStore';

const Store = createContext();

export default Store;

export const StoreProvider = withRouter(injectIntl(inject('AppState')((props) => {
  const {
    children,
    intl,
    instanceId,
    AppState: { currentMenuType: { organizationId: tenantId }, userInfo: { personId: userId } },
    config,
    tableDataSet,
  } = props;

  const groupId = instanceId;

  const intlPrefix = 'group';
  const prefixCls = 'iam-group-detail';
  const mainStore = useStore();
  const {
    lovCode,
  } = flat(config?.widgetConfig?.customConfig?.slice() || []);
  const userDataSet = useMemo(() => new DataSet(UserDataSet({ intl, intlPrefix, tenantId, groupId })), [groupId]);
  const userListDataSet = useMemo(() => new DataSet(UserDataSet({ intl, intlPrefix, tenantId, groupId, unAssign: true, mainStore })), []);
  const multipleDataSet = useMemo(() => new DataSet(MultipleDataSet({ intl, intlPrefix, tenantId, groupId, userLovCode: lovCode, tableDataSet })), []);

  const value = {
    ...props,
    tenantId,
    groupId,
    userId,
    intlPrefix,
    prefixCls,
    userDataSet,
    userListDataSet,
    mainStore,
    multipleDataSet,
    tableDataSet,
  };
  return (
    <Store.Provider value={value}>
      {children}
    </Store.Provider>
  );
})));
