import React, { useContext, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import axios from 'axios';
import { Icon } from '@zknow/components';
import { Modal, message, Table, Lov } from 'choerodon-ui/pro';
import { injectIntl } from 'react-intl';
import Store from './stores';
import './index.less';

const { Column } = Table;
const modalKey = Modal.key();

export default injectIntl(observer((props) => {
  //
  const { groupId, intl, intlPrefix, prefixCls, userListDataSet, mainStore, userDataSet, multipleDataSet, tableDataSet, tenantId, config, formDataSet, history, history: { location: { search } } } = useContext(Store);
  const { icon, name, id, color = 'default' } = config;
  const modalStyle = useMemo(() => ({ width: 800, maxHeight: 578 }), []);

  function handleClick() {
    userListDataSet.unSelectAll();
    mainStore.setSelectValue([]);
    userListDataSet.query();
    Modal.open({
      key: modalKey,
      style: modalStyle,
      title: intl.formatMessage({ id: 'iam.renderer.desc.group.add.user', defaultMessage: '添加人员' }),
      drawer: false,
      className: `${prefixCls}-modal`,
      children: (
        <Table
          pristine
          placeholder={intl.formatMessage({ id: 'iam.renderer.desc.group.add.user', defaultMessage: '添加人员' })}
          dataSet={userListDataSet}
          queryBarProps={{
            fuzzyQuery: false,
            simpleMode: true,
            inlineSearch: false,
            queryFieldsStyle: {
              realName: { width: 140 },
              email: { width: 200 },
              phone: { width: 140 },
            },
          }}
        >
          <Column name="realName" />
          <Column name="email" />
          <Column name="phone" />
        </Table>
      ),
      destroyOnClose: true,
      onOk: async () => {
        const userIds = mainStore.getSelectValue;
        if (userIds?.length) {
          try {
            const res = await axios.post(`/iam/yqc/${tenantId}/userGroups/assignUserForGroup?groupId=${groupId}`, userIds);
            if (!res?.failed) {
              message.success(intl.formatMessage({ id: 'iam.renderer.model.group.invite.success', defaultMessage: '邀请成功' }));
              tableDataSet.query();
              return true;
            }
            message.success(res.message);
            return false;
          } catch (e) {
            return false;
          }
        }
        return true;
      },
    });
  }

  const handleLovOk = async () => {
    const userIds = multipleDataSet.current?.getField('multipleChoiceValue').options.selected?.map(v => v.get('id'));
    if (userIds.length > 0) {
      try {
        const res = await axios.post(`/iam/yqc/${tenantId}/userGroups/assignUserForGroup?groupId=${groupId}`, userIds);
        if (!res?.failed) {
          message.success(intl.formatMessage({ id: 'iam.renderer.model.group.invite.success', defaultMessage: '邀请成功' }));
          multipleDataSet.current?.getField('multipleChoiceValue').options?.clearCachedSelected();
          tableDataSet.query();
          return true;
        }
        message.error(res.message);
        return false;
      } catch (e) {
        return false;
      }
    } else {
      multipleDataSet.current?.getField('multipleChoiceValue').options?.clearCachedSelected();
    }
  };

  return (
    // <Button
    //   key={id}
    //   color={color}
    //   funcType="raised"
    //   icon={icon}
    //   onClick={handleClick}
    // >{name}</Button>
    <Lov
      key={id}
      dataSet={multipleDataSet}
      name="multipleChoiceValue"
      mode="button"
      clearButton={false}
      funcType="raised"
      color={color}
      // modalProps={{
      //   onOk: () => {
      //     handleLovOk();
      //   },
      //   afterClose: () => multipleDataSet.current?.getField('multipleChoiceValue').options?.clearCachedSelected(),
      // }}
    >
      <span className={`${prefixCls}-lov-icon`}>
        <Icon type={icon} />
      </span>
      {name}
    </Lov>
  );
}));
