import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { withRouter } from 'react-router-dom';
import { DataSet } from 'choerodon-ui/pro';
import UserDataSet from './UserDataSet';
import useStore from './useStore';

const Store = createContext();

export default Store;

export const StoreProvider = withRouter(injectIntl(inject('AppState')((props) => {
  const {
    children,
    intl,
    instanceId,
    AppState: { currentMenuType: { organizationId: tenantId }, userInfo: { personId: userId } },
  } = props;

  const groupId = instanceId;

  const intlPrefix = 'group';
  const prefixCls = 'iam-group-detail';
  const mainStore = useStore();

  const value = {
    ...props,
    tenantId,
    groupId,
    userId,
    intlPrefix,
    prefixCls,
    mainStore,
  };
  return (
    <Store.Provider value={value}>
      {children}
    </Store.Provider>
  );
})));
