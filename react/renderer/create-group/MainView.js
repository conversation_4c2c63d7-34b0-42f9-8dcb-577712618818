import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { axios } from '@yqcloud/apps-master';
import { Button } from '@zknow/components';
import { message } from 'choerodon-ui/pro';
import { injectIntl } from 'react-intl';
import Store from './stores';
import './index.less';

export default injectIntl(observer((props) => {
  const { modal, intl, tenantId, config, formDataSet, parentDataSet } = useContext(Store);
  const { icon, name, id, color = 'default' } = config;

  async function handleClick() {
    const data = formDataSet?.get(0)?.toData();
    const params = {
      description: data.description,
      domainEnabledFlag: data.domain_enabled_flag,
      domainId: data.domain_id,
      email: data.email,
      name: data.name,
      ownerId: data.owner_id,
      parentId: data.parent_id,
      recursiveFlag: data.recursive_flag,
      type: data.type,
      __id: data._id,
      _status: 'create',
    };
    try {
      const res = await axios.post(`/iam/yqc/${tenantId}/userGroups`, params);
      if (res && !res?.failed) {
        message.success(intl.formatMessage({ id: 'zknow.common.success.save', defaultMessage: '保存成功' }));
        if (parentDataSet) {
          parentDataSet.query();
        }
        modal.close();
        return true;
      } else {
        message.error(res?.message);
      }
    } catch (e) {
      throw Error(e);
    }
  }

  return (
    <Button
      key={id}
      color={color}
      funcType="raised"
      icon={icon}
      onClick={handleClick}
    >{name}</Button>
  );
}));
