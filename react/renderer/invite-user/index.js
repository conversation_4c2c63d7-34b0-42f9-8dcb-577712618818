import React, { useMemo } from 'react';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { StoreProvider } from './stores';
import MainView from './MainView';

export default inject('AppState')(observer((props) => {
  const { AppState: { currentLanguage: language } } = props;
  return (
    <StoreProvider {...props}>
      <MainView />
    </StoreProvider>
  );
}));

/* externalize: InviteUserRenderer */
