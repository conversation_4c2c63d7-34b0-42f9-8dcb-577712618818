import React, { useState, useEffect, useContext, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { axios } from '@yqcloud/apps-master';
import { Button } from '@zknow/components';
import { Modal, message, Table, Form, TextField, Icon } from 'choerodon-ui/pro';
import { injectIntl } from 'react-intl';
import Store from './stores';
import './index.less';

const modalKey = Modal.key();

export default injectIntl(observer((props) => {
  const { intl, tenantId, config, groupId, tableDataSet, userInviteDataSet } = useContext(Store);
  const { icon, name, id, color = 'default' } = config;
  const customConfig = config?.widgetConfig?.customConfig || [];
  const smallStyle = useMemo(() => ({ width: 520 }), []);

  function handleClick() {
    userInviteDataSet.create({});
    Modal.open({
      key: modalKey,
      style: smallStyle,
      title: intl.formatMessage({ id: 'iam.renderer.desc.group.invite.user', defaultMessage: '邀请人员' }),
      drawer: false,
      children: (
        <div>
          <div>
            <Icon type="help_outline" style={{ color: '#2979ff', fontSize: '0.18rem', marginTop: '-0.05rem', marginRight: '0.1rem' }} />
            <span style={{ color: '#595959' }}>{intl.formatMessage({ id: 'iam.renderer.desc.group.invite.warning', defaultMessage: '保存成功后将通过邮箱发送邀请' })}</span></div>
          <Form
            // header="保存成功后将通过邮箱发送邀请"
            labelWidth="auto"
            dataSet={userInviteDataSet}
          >
            <TextField name="realName" />
            <TextField name="email" />
            <TextField name="phone" />
          </Form>
        </div>
      ),
      destroyOnClose: true,
      onOk: async () => {
        const userData = userInviteDataSet.current.toData();
        if (userData && Object.keys(userData).length > 1) {
          userData.inviteGroupId = groupId;
          try {
            const res = await axios.post(`iam/yqc/v1/${tenantId}/users?`, userData);
            if (!res?.failed) {
              tableDataSet.query();
              return true;
            }
            message.error(res.message);
            return false;
          } catch (e) {
            return false;
          }
        }
        return true;
      },
    });
  }

  return (
    <Button
      key={id}
      color={color}
      funcType="raised"
      icon={icon}
      onClick={handleClick}
    >{name}</Button>
  );
}));
