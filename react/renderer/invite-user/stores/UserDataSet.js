export default ({ intl, tenantId, groupId, unAssign = false, mainStore, isInvite = false }) => {
  const urlPrefix = `/iam/yqc/${tenantId}/userGroups`;
  const name = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const email = intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' });
  const mobile = intl.formatMessage({ id: 'iam.renderer.model.mobile', defaultMessage: '手机' });

  return {
    autoQuery: !unAssign,
    primaryKey: 'id',
    selection: unAssign ? 'multiple' : false,
    paging: true,
    pageSize: unAssign ? 5 : 10,
    autoLocateFirst: false,
    transport: {
      read: {
        url: `${urlPrefix}/queryUserForGroup?groupId=${groupId}&unAssign=${unAssign}`,
        method: 'get',
      },
      destroy: ({ data: [data] }) => ({
        url: `${urlPrefix}/removeUserForGroup?groupId=${groupId}&userId=${data.id}`,
        method: 'delete',
      }),
    },
    fields: [
      { name: 'realName', label: name, type: 'string', required: isInvite },
      { name: 'email', label: email, type: 'string', required: isInvite },
      { name: 'phone', label: mobile, type: 'string' },
    ],
    queryFields: [
      { name: 'realName', label: name, type: 'string' },
      { name: 'email', label: email, type: 'string' },
      { name: 'phone', label: mobile, type: 'string' },
    ],
    events: {
      load: ({ dataSet }) => {
        if (unAssign) {
          dataSet.map(record => {
            const id = record.get('id');
            if (mainStore.getSelectValue?.includes(id)) {
              record.isSelected = true;
            }
            return record;
          });
        }
      },
      select: ({ dataSet, record, previous }) => {
        if (unAssign) {
          const id = record.get('id');
          mainStore.setSelectValue([...(mainStore.getSelectValue?.filter(itemId => itemId !== id) || []), id]);
          return true;
        }
      },
      unSelect: ({ dataSet, record }) => {
        if (unAssign) {
          const id = record.get('id');
          mainStore.setSelectValue([...(mainStore.getSelectValue?.filter(itemId => itemId !== id) || [])]);
          return true;
        }
      },
      selectAll: ({ dataSet }) => {
        if (unAssign) {
          const ids = dataSet.map(record => record.get('id'));
          mainStore.setSelectValue([...(mainStore.getSelectValue?.filter(itemId => !ids.includes(itemId)) || []), ...ids]);
          return true;
        }
      },
      unSelectAll: ({ dataSet }) => {
        if (unAssign) {
          const ids = dataSet.map(record => record.get('id'));
          mainStore.setSelectValue([...(mainStore.getSelectValue?.filter(itemId => !ids.includes(itemId)) || [])]);
          return true;
        }
      },
    },
  };
};
