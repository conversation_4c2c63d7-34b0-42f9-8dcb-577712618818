import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import { DataSet } from 'choerodon-ui/pro';
import UserDataSet from './UserDataSet';
import useStore from './useStore';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: 'iam.renderer' })(injectIntl((props) => {
  const {
    children,
    intl,
    instanceId,
    AppState: { currentMenuType: { organizationId: tenantId }, userInfo: { personId: userId } },
  } = props;

  const groupId = instanceId;

  const intlPrefix = 'group';
  const prefixCls = 'iam-group-detail';
  const mainStore = useStore();
  const userInviteDataSet = useMemo(() => new DataSet(UserDataSet({ intl, intlPrefix, tenantId, groupId, isInvite: true })), [groupId]);

  const value = {
    ...props,
    tenantId,
    userId,
    intlPrefix,
    prefixCls,
    userInviteDataSet,
    groupId,
  };
  return (
    <Store.Provider value={value}>
      {children}
    </Store.Provider>
  );
})));
