import React from 'react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { Modal, message } from 'choerodon-ui/pro';
import { axios } from '@yqcloud/apps-master';
import { Button } from '@zknow/components';
import './index.less';

export default injectIntl(observer((props) => {
  const {
    config,
    intl,
    tableLineRecord,
    feature,
    formDataSet,
    AppState: { currentMenuType: { organizationId: tenantId } },
  } = props;
  const { icon, name, id, color = 'default' } = config;
  async function handleDelete() {
    Modal.confirm({
      title: intl.formatMessage({ id: 'iam.renderer.desc.group.user.delete.confirm', defaultMessage: '请确认是否移除该人员？' }),
    }).then(async btn => {
      if (btn === 'ok') {
        const groupId = formDataSet?.current?.get('id');
        const userId = tableLineRecord?.get('user_id');
        const res = await axios.delete(`/iam/yqc/${tenantId}/userGroups/removeUserForGroup?groupId=${groupId}&userId=${userId}`);
        if (res?.failed) {
          message?.error(res?.message);
        } else {
          tableLineRecord?.dataSet.query();
          message.success(intl.formatMessage({ id: 'zknow.common.success.remove', defaultMessage: '移除成功' }));
        }
      } else {
        return null;
      }
    });
  }

  return (
    <Button
      funcType="flat"
      color={color}
      className="remover-group-user-btn"
      icon={icon}
      key={id}
      onClick={handleDelete}
    >
      {name}
    </Button>
  );
}));
