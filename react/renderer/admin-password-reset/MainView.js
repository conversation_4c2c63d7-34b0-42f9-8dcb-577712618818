/* eslint-disable no-nested-ternary */
import React, { useMemo, useEffect, useState, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import axios from 'axios';
import { message, DataSet, Modal, Form, Password, Tooltip } from 'choerodon-ui/pro';
import { Button } from '@zknow/components';
import { formatterCollections } from '@zknow/utils';
import { Alert } from 'choerodon-ui';
import './index.less';

const MainView = observer(props => {
  const { config, intl, tableLineRecord, tenantId, fixedActionFlag, viewRecord } = props;
  const { id, name, icon = 'settings_backup_restore', color = 'default' } = config;

  const [pwdPolicies, setPwdPolicies] = useState(null); // 租户密码规则
  const [pwdPermission, setPermission] = useState(null); // 是否有权限重置密码
  // error message
  const errMap = useMemo(() => ({
    'hoth.warn.password.ldapUserCantUpdatePassword': intl.formatMessage({ id: 'iam.renderer.desc.account.password.ldap.not.allowed', defaultMessage: 'LDAP用户不能重置密码' }),
    'hoth.warn.password.same': intl.formatMessage({ id: 'iam.renderer.desc.account.password.cannot.same', defaultMessage: '新密码不能与旧密码相同' }),
  }), []);

  useEffect(() => {
    (async () => {
      try {
        // 密码策略
        const res = await axios.get(`/iam/yqc/${tenantId}/password-policies`);
        if (res?.failed) {
          message.error(res?.message ?? intl.formatMessage({ id: 'iam.renderer.desc.message.failed', defaultMessage: '操作失败' }));
        } else {
          setPwdPolicies(res);
        }
      } catch (e) {
        throw Error(e);
      }
    })();
  }, []);

  useEffect(() => {
    (async () => {
      try {
        // 是否有权限重置密码
        const ret = await axios.post(`/iam/yqc/v1/${tenantId}/menus/check-permissions`, ['zknow-iam.custom-user.resetPassword']);
        if (ret?.failed) {
          message.error(ret?.message ?? intl.formatMessage({ id: 'iam.renderer.desc.message.failed', defaultMessage: '操作失败' }));
        } else {
          setPermission(ret.find(i => i.code === 'zknow-iam.custom-user.resetPassword')?.approve);
        }
      } catch (e) {
        throw Error(e);
      }
    })();
  }, []);

  /**
   * @description 校验租户密码规则
   * @param {String} password
   */
  const verifyPassword = useCallback((password = '') => {
    const { maxLength, minLength, characterTypeCount, uppercaseCount, lowercaseCount, specialCharCount, digitsCount } = pwdPolicies || {};
    if (minLength && password.length < minLength) {
      return `${intl.formatMessage({ id: 'iam.renderer.desc.account.password.not.less.than', defaultMessage: '密码长度不能小于' })} ${minLength}`;
    } else if (maxLength && password.length > maxLength) {
      return `${intl.formatMessage({ id: 'iam.renderer.desc.account.password.not.more.than', defaultMessage: '密码长度不能大于' })} ${maxLength}`;
    } else if (uppercaseCount && !new RegExp('[A-Z]').test(password)) {
      return intl.formatMessage({ id: 'iam.renderer.desc.account.password.must.contain.uppercase', defaultMessage: '密码必须包含大写字母' });
    } else if (lowercaseCount && !new RegExp('[a-z]').test(password)) {
      return intl.formatMessage({ id: 'iam.renderer.desc.account.password.must.contain.lowercase', defaultMessage: '密码必须包含小写字母' });
    } else if (digitsCount && !new RegExp('[0-9]').test(password)) {
      return intl.formatMessage({ id: 'iam.renderer.desc.account.password.must.contain.number', defaultMessage: '密码必须包含数字' });
    } else if (specialCharCount && !new RegExp('[!@#$%^&*+=-_]').test(password)) {
      return intl.formatMessage({ id: 'iam.renderer.desc.account.password.must.contain.special', defaultMessage: '密码必须包含特殊字符' });
    }
    return true;
  }, [pwdPolicies]);

  const passwordDataSet = useMemo(() => new DataSet({
    autoCreate: true,
    fields: [{
      name: 'password',
      type: 'string',
      label: intl.formatMessage({ id: 'iam.renderer.desc.account.new.password', defaultMessage: '新密码' }),
      trim: 'both',
      required: true,
      validator: (value, fieldName, record) => {
        const verifyRes = verifyPassword(value);
        if (verifyRes !== true) {
          return verifyRes;
        } else if (record.get('rePassword') && record.get('rePassword') !== value) {
          return intl.formatMessage({ id: 'iam.renderer.desc.account.password.inconsistent', defaultMessage: '两次输入的密码不一致' });
        }
        return true;
      },
    }, {
      name: 'rePassword',
      type: 'string',
      label: intl.formatMessage({ id: 'iam.renderer.desc.account.new.password.repeat', defaultMessage: '确认新密码' }),
      trim: 'both',
      required: true,
      validator: (value, fieldName, record) => {
        const verifyRes = verifyPassword(value);
        if (verifyRes !== true) {
          return verifyRes;
        } else if (record.get('password') && record.get('password') !== value) {
          return intl.formatMessage({ id: 'iam.renderer.desc.account.password.inconsistent', defaultMessage: '两次输入的密码不一致' });
        }
        return true;
      },
    }],
  }), [verifyPassword]);

  /**
   * @description 密码重置弹窗
   */
  const handleOpenModal = () => {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.renderer.desc.account.password.reset', defaultMessage: '重置密码' }),
      children: (
        <>
          {renderAlert()}
          <Form dataSet={passwordDataSet}>
            <Password name="password" />
            <Password name="rePassword" />
          </Form>
        </>
      ),
      destroyOnClose: true,
      onOk: async () => {
        if (await passwordDataSet?.current?.validate(true)) {
          const { password } = passwordDataSet?.current?.toJSONData();
          try {
            const res = await axios.put(`/iam/yqc/v1/${tenantId}/users/reset/password?userId=${tableLineRecord?.get('id')}&password=${encodeURIComponent(btoa(password))}`);
            // if (!res?.failed) {
            //   message.error(errMap[res?.code] ?? res?.message ?? intl.formatMessage({ id: 'iam.renderer.desc.message.failed', defaultMessage: '操作失败' }));
            // } else {
            //   message.success(intl.formatMessage({ id: 'zknow.common.success.action', defaultMessage: '操作成功' }));
            //   return true;
            // }
            if (!res?.failed) {
              message.success(intl.formatMessage({ id: 'zknow.common.success.action', defaultMessage: '操作成功' }));
              return true;
            }
          } catch (e) {
            throw Error(e);
          }
        }
        return false;
      },
      afterClose: () => passwordDataSet?.reset(),
    });
  };

  function renderAlert() {
    const upperComma = pwdPolicies?.lowercaseCount || pwdPolicies?.digitsCount || pwdPolicies?.specialCharCount;
    const lowerComma = pwdPolicies?.digitsCount || pwdPolicies?.specialCharCount;
    const digitsComma = pwdPolicies?.specialCharCount;
    return (
      <Alert
        type="info"
        showIcon
        style={{
          marginBottom: '16px',
        }}
        message={
          intl.formatMessage(
            { id: 'iam.renderer.desc.account.password.tips', defaultMessage: '密码至少包含{characterTypeCount}种字符类型，必须包含：{uppercaseCount}{lowercaseCount}{digitsCount}{specialCharCount}。{hasSpecialChar}' },
            {
              characterTypeCount: pwdPolicies?.characterTypeCount,
              uppercaseCount: pwdPolicies?.uppercaseCount ? upperComma ? `${intl.formatMessage({ id: 'iam.renderer.desc.account.uppercase.letter', defaultMessage: '大写字母' })}、` : `${intl.formatMessage({ id: 'iam.renderer.desc.account.uppercase.letter', defaultMessage: '大写字母' })}` : '',
              lowercaseCount: pwdPolicies?.lowercaseCount ? lowerComma ? `${intl.formatMessage({ id: 'iam.renderer.desc.account.lowercase.letter', defaultMessage: '小写字母' })}、` : `${intl.formatMessage({ id: 'iam.renderer.desc.account.lowercase.letter', defaultMessage: '小写字母' })}` : '',
              digitsCount: pwdPolicies?.digitsCount ? digitsComma ? `${intl.formatMessage({ id: 'iam.renderer.desc.account.number', defaultMessage: '数字' })}、` : `${intl.formatMessage({ id: 'iam.renderer.desc.account.number', defaultMessage: '数字' })}` : '',
              specialCharCount: pwdPolicies?.specialCharCount ? `${intl.formatMessage({ id: 'iam.renderer.desc.account.symbol', defaultMessage: '特殊符号' })}` : '',
              hasSpecialChar: pwdPolicies?.specialCharCount ? `${intl.formatMessage({ id: 'iam.renderer.desc.account.password.complexity.value.help', defaultMessage: '特殊字符包括：[ ]~~@#$%&*\\-_=+l/()<>,.;:!' })}` : '',
            },
          )
        }
      />
    );
  }

  // TODO: 租户管理员权限,权限控制应该放在低代码组件上
  return pwdPermission
    && tableLineRecord?.get('is_enabled')
    && (
      <Tooltip title={fixedActionFlag && viewRecord?.index === 0 ? intl.formatMessage({ id: 'iam.renderer.desc.account.password.reset', defaultMessage: '重置密码' }) : null}>
        <Button
          className="csm-account-password-reset"
          key={id}
          color={color}
          funcType="flat"
          onClick={handleOpenModal}
          icon={icon}
        >
          {fixedActionFlag && viewRecord?.index === 0 ? null : intl.formatMessage({ id: 'iam.renderer.desc.account.password.reset', defaultMessage: '重置密码' })}
        </Button>
      </Tooltip>
    );
});

export default inject('AppState')(formatterCollections({ code: ['zknow.common', 'iam.render'] })(injectIntl(MainView)));
