import React, { useState, useEffect, useContext, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { axios } from '@yqcloud/apps-master';
import { Button } from '@zknow/components';
import { Modal, message, Table } from 'choerodon-ui/pro';
import { injectIntl } from 'react-intl';
import Store from './stores';
import './index.less';

const { Column } = Table;
const modalKey = Modal.key();

export default injectIntl(observer((props) => {
  //
  const { modal, groupId, intl, intlPrefix, prefixCls, tenantId, config, formDataSet, history, history: { location: { search } } } = useContext(Store);
  const { icon, name, id, color = 'default' } = config;
  const customConfig = config?.widgetConfig?.customConfig || [];
  const modalStyle = useMemo(() => ({ width: 800, maxHeight: 578 }), []);

  function handleClick() {
    const data = formDataSet?.get(0)?.toData();
    const params = {
      code: data.code,
      companyId: data.company_id,
      domainId: data.domain_id,
      domainManageFlag: data.domain_manage_flag,
      managerId: data.manager_id,
      name: data.name,
      parentId: data.parent_id,
      parentName: data['parent_id:name'],
      __id: data._id,
      _status: 'create',
    };
    axios.post(`/iam/yqc/${tenantId}/departments`, params)
      .then(res => {
        if (res && !res.failed) {
          message.success(intl.formatMessage({ id: 'zknow.common.success.save', defaultMessage: '保存成功' }));
          modal.close();
          return true;
        } else {
          message.error(res?.message);
        }
      });
  }

  return (
    <Button
      key={id}
      color={color}
      funcType="raised"
      icon={icon}
      onClick={handleClick}
    >{name}</Button>
  );
}));
