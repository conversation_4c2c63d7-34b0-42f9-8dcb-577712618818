import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { withRouter } from 'react-router-dom';
import { DataSet } from 'choerodon-ui/pro';
import { formatterCollections } from '@zknow/utils';
import RoleDataSet from './RoleDataSet';
import useStore from './useStore';

const Store = createContext();

export default Store;

export const StoreProvider = withRouter(inject('AppState')(formatterCollections({ code: ['iam.common', 'iam.component'] })(injectIntl(
  (props) => {
    const {
      children,
      intl,
      instanceId,
      AppState: { currentMenuType: { organizationId: tenantId }, userInfo: { tenantAdmin, personId: userId } },
    } = props;

    const groupId = instanceId;

    const intlPrefix = 'group';
    const prefixCls = 'iam-group-detail';
    const mainStore = useStore();
    const roleDataSet = useMemo(() => new DataSet(RoleDataSet({ intl, intlPrefix, tenantId, groupId })), [groupId]);
    const roleListDataSet = useMemo(() => new DataSet(RoleDataSet({ intl, intlPrefix, tenantId, groupId, unAssign: true, mainStore })), []);

    const value = {
      ...props,
      tenantId,
      userId,
      intlPrefix,
      prefixCls,
      roleListDataSet,
      roleDataSet,
      mainStore,
      groupId,
      tenantAdmin,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }
))));
