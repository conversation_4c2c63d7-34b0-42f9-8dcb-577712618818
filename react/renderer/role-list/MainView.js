import React, { useState, useEffect, useContext, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { axios } from '@yqcloud/apps-master';
import { Modal, message, Table, Form, TextField, Icon } from 'choerodon-ui/pro';
import { injectIntl } from 'react-intl';
import { TableHoverAction, Button } from '@zknow/components';
import ModalView from './ModalView';
import Store from './stores';
import './index.less';

const { Column } = Table;
const modalKey = Modal.key();

export default injectIntl(observer((props) => {
  //
  const {
    intl,
    intlPrefix,
    prefixCls,
    tenantId,
    config,
    groupId,
    roleListDataSet,
    roleDataSet,
    mainStore,
    history: { location: { search } },
  } = useContext(Store);
  const { icon, name, id, color = 'default' } = config;
  const modalStyle = useMemo(() => ({ width: 800, maxHeight: 578 }), []);
  const [isOwner, setOwner] = useState(false);
  const [isVirtual, setVirtual] = useState(false);

  useEffect(async () => {
    const res = await axios.get(`/iam/yqc/${tenantId}/userGroups/group/validateManage?groupId=${groupId}`);
    setOwner(res);
  }, [groupId]);

  const openModal = () => {
    roleListDataSet.unSelectAll();
    mainStore.setSelectValue([]);
    roleListDataSet.query();
    Modal.open({
      key: modalKey,
      style: modalStyle,
      title: intl.formatMessage({ id: 'iam.component.groupList.addRole', defaultMessage: '添加角色' }),
      drawer: false,
      className: `${prefixCls}-modal`,
      children: (
        <div className="modal-table">
          <Table
            pristine
            placeholder={intl.formatMessage({ id: 'iam.component.groupList.addRole', defaultMessage: '添加角色' })}
            dataSet={roleListDataSet}
            queryBarProps={{
              fuzzyQuery: false,
              simpleMode: true,
              inlineSearch: false,
              queryFieldsStyle: {
                name: {
                  width: 140,
                },
                description: {
                  width: 200,
                },
              },
            }}
            autoHeight
          >
            <Column name="name" />
            <Column name="description" />
          </Table>
        </div>
      ),
      destroyOnClose: true,
      onOk: async () => {
        const roleIds = mainStore.getSelectValue;
        if (roleIds?.length) {
          try {
            const res = await axios.post(`/iam/yqc/v1/${tenantId}/memberRoles/assignRolesForGroup?groupId=${groupId}`, roleIds);
            if (!res?.failed) {
              roleDataSet.query();
              return true;
            }
            return false;
          } catch (e) {
            return false;
          }
        }
        return true;
      },
    });
  };

  async function handleDelete(record) {
    await roleDataSet.delete(record);
  }

  // 头部按钮组
  const buttons = useMemo(() => {
    if (isOwner) {
      return (
        [
          <Button funcType="raised" icon="plus" color="primary" onClick={openModal}>
            {intl.formatMessage({ id: 'zknow.common.button.append', defaultMessage: '添加' })}
          </Button>,
        ]
      );
    } else {
      return [];
    }
  }, [isOwner]);

  // 操作栏
  function renderAction({ record }) {
    if (isOwner) {
      return (
        <TableHoverAction
          record={record}
          actions={[
            {
              name: intl.formatMessage({ id: 'iam.common.action.remove', defaultMessage: '移除' }),
              icon: 'delete',
              onClick: () => handleDelete(record),
            },
          ]}
        />
      );
    } else {
      return null;
    }
  }

  return (
    <Table
      pristine
      buttons={buttons}
      dataSet={roleDataSet}
      title={intl.formatMessage({ id: 'zknow.common.model.role', defaultMessage: '角色' })}
      canFold
      queryBarProps={{
        title: intl.formatMessage({ id: 'zknow.common.model.role', defaultMessage: '角色' }),
      }}
    >
      <Column name="name" />
      <Column name="description" />
      <Column width={10} renderer={renderAction} tooltip="none" />
    </Table>
  );
}));
