import React from 'react';
import { observer } from 'mobx-react-lite';
import { message } from 'choerodon-ui/pro';
import { Button } from '@zknow/components';
import { axios } from '@yqcloud/apps-master';
import { injectIntl } from 'react-intl';

import './index.less';

export default injectIntl(observer((props) => {
  const { tenantId, tableDataSet, config, intl } = props;
  const { icon, name, id, color = 'default' } = config;

  async function removeUser() {
    const userId = tableDataSet?.current?.get('id');
    if (userId) {
      try {
        const res = await axios.delete(`/iam/yqc/v1/${tenantId}/users/remove/department/${userId}`);
        if (!res?.failed) {
          message.success(intl.formatMessage({ id: 'zknow.common.success.remove', defaultMessage: '移除成功' }));
          tableDataSet.query();
          return true;
        }
        message.error(res.message);
        return false;
      } catch (e) {
        return false;
      }
    }
  }

  return (
    <Button
      key={id}
      icon={icon}
      color={color}
      funcType="flat"
      onClick={removeUser}
      className="iam-remove-user-department-btn"
    >
      {name}
    </Button>
  );
}));
