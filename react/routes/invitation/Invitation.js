import React, { useContext } from 'react';
import { with<PERSON>outer } from 'react-router-dom';
import { Button, Spin, message, Dropdown, Menu } from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';
import { Icon } from '@zknow/components';
import expired from '@/assets/images/expired.svg';
import defaultSystemLogoImg from './assets/yqcloud-logo.svg';
import './index.less';
import MainView from './MainView';
import Store from './stores';

const Invitation = observer((props) => {
  const { title, expire, personId, token, tenantId, setPolicy, intl, intlPrefix, showHeader, supportLanguages } = useContext(Store);

  function redirectLogin() {
    localStorage.clear();
    window.location.replace('');
  }

  const languageMenu = (
    <div className="languageMenu-main">
      <Menu>
        {(supportLanguages || [{ name: '简体中文', code: 'zh_CN' }, { name: 'English', code: 'en_US' }]).map((item) => (
          <Menu.Item onClick={() => props.changeLanguage(item.code)}>{item.name}</Menu.Item>
        ))}
      </Menu>
    </div>
  );

  if (expire === true) {
    return (
      <div>
        <Dropdown className="languageMenu" overlay={languageMenu}>
          <div className="language">
            <span className="language-current">
              {(supportLanguages || [{ name: '简体中文', code: 'zh_CN' }, { name: 'English', code: 'en_US' }]).find(r => r.code === props.language)?.name}
            </span>
            <Icon type="down" size="16" fill="#595959" />
          </div>
        </Dropdown>
        <div className="invite">
          <div className="invite-expired-content">
            <div className="invite-expired-content-logo">
              <img alt="" src={expired} className="invite-expired-content-logo-icon" />
            </div>
            <div className="notice">{intl.formatMessage({ id: 'iam.invitation.desc.invalid', defaultMessage: '邀请链接已失效' })}</div>
            <Button
              className="back-home"
              onClick={redirectLogin}
              funcType="raised "
              color="primary"
            >{intl.formatMessage({ id: 'iam.invitation.desc.back.home', defaultMessage: '返回首页' })}</Button>
          </div>
        </div>
      </div>
    );
  } else {
    return (
      <div>
        <Dropdown className="languageMenu" overlay={languageMenu}>
          <div className="language">
            <span className="language-current">
              {(supportLanguages || [{ name: '简体中文', code: 'zh_CN' }, { name: 'English', code: 'en_US' }]).find(r => r.code === props.language)?.name}
            </span>
            <Icon type="down" size="16" fill="#595959" />
          </div>
        </Dropdown>
        <div className="invite">
          <div className="invite-content">
            {showHeader && (
              <>
                <div className="invite-content-logo">
                  <img alt="" src={defaultSystemLogoImg} className="invite-content-logo-icon" />
                </div>
                <div className="invite-content-text">{title}</div>
              </>
            )}
            <MainView language={props.language} />
          </div>
          <div className="invite-footer">
            © Copyright ZKnow Technologies Co., Ltd.All Rights Reserved 上海甄知科技有限公司
          </div>
        </div>;
      </div>
    );
  }
});
export default withRouter(Invitation);
