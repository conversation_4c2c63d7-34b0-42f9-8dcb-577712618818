import React, { useState, useEffect } from 'react';
import { message } from 'choerodon-ui/pro';
import axios from 'axios';
import { IntlProvider } from 'react-intl';
import { getCookie, setCookie } from '@zknow/utils';
import { StoreProvider } from './stores';
import Invitation from './Invitation';

export default (props) => {
  const [language, setLanguage] = useState(getCookie('yqcloud_language') ? getCookie('yqcloud_language') : 'zh_CN');
  const [data, setData] = useState();
  function changeLanguage(l) {
    setLanguage(l);
    setCookie('yqcloud_language', l);
  }

  useEffect(() => {
    (async () => {
      try {
        const res = await axios.get(`${window._env_.API_HOST}/hpfm/v1/0/prompt/${language}?promptKey=iam.invitation,zknow.common`);
        if (res?.failed) {
          message.success(res?.message);
        } else {
          setData(res);
        }
      } catch (e) {
        // eslint-disable-next-line no-console
        console.error(e);
        throw (e);
      }
    })();
  }, [language]);

  return (
    <IntlProvider messages={data} locale={language.replace('_', '-')}>
      <StoreProvider {...props} language={language}>
        <Invitation changeLanguage={changeLanguage} language={language} />
      </StoreProvider>
    </IntlProvider>
  );
};
