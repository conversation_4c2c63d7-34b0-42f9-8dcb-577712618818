export default ({ intl, intlPrefix }) => {
  function passwordValidator(value, name, form) {
    if (value !== form.getField('password').getValue()) {
      return `${intl.formatMessage({ id: 'iam.invitation.model.password.wrong.warning', defaultMessage: '您两次输入的密码不一致，请重新输入' })}`;
    }
    return true;
  }
  return {
    autoQuery: false,
    paging: false,
    selection: false,
    parentField: 'parentId',
    idField: 'id',
    fields: [
      { name: 'email', type: 'string' },
      { name: 'password', type: 'string' },
      { name: 'confirmPassword', type: 'string', validator: passwordValidator },
    ],
  };
};
