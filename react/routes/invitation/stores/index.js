import React, { createContext, useState, useEffect, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import { axios } from '@zknow/utils';
import { formatterCollections } from '@zknow/utils';
import AccountDataSet from './AccountDataSet';

const Stores = createContext();
/* 获取url上面参数的值, 针对于hash路由 */
function getQueryString(name) {
  const url = window.location.hash;
  const theRequest = {};
  if (url.indexOf('?') !== -1) {
    const str = url.substr(url.indexOf('?') + 1);
    const strs = str.split('&');
    for (let i = 0; i < strs.length; i += 1) {
      theRequest[strs[i].split('=')[0]] = decodeURIComponent(strs[i].split('=')[1]);
      if (theRequest[name]) {
        return theRequest[name];
      }
    }
  }
}

export default Stores;

export const StoreProvider = inject('AppState')(injectIntl(
  (props) => {
    const {
      children,
      intl,
      language,
    } = props;

    const intlPrefix = 'iam.invitation';

    const [title, setTitle] = useState('');
    const [logo, setLogo] = useState(null);
    const [tenantId, setTenantId] = useState(null);
    const [tenantName, setTenantName] = useState(null);
    const [personId, setPersonId] = useState(null);
    const token = getQueryString('token');
    const [expire, setExpire] = useState(null);
    const [email, setEmail] = useState(null);
    const [policy, setPolicy] = useState(null);
    const [showHeader, setShowHeader] = useState(true);
    const [supportLanguages, setSupportLanguages] = useState([]);

    const accountDataSet = useMemo(() => new DataSet(AccountDataSet({ intl, intlPrefix })), [email, intl]);

    useEffect(() => {
      if (token) {
        axios.get(`${window._env_.API_HOST}/iam/yqc/v1/users/invite/check?token=${token}`)
          .then((res) => {
            if (!res.failed) {
              setPolicy(res);
              setLogo(res.log);
              setTenantId(res.tenantId);
              setPersonId(res.personId);
              setTenantName(res.tenantName);
              setEmail(res.email);
              setExpire(res.name);
              setSupportLanguages(res.supportLanguages);
            } else {
              setExpire(true);
            }
          })
          .catch((error) => {
            setExpire(true);
            // eslint-disable-next-line no-console
            console.log(error);
          });
      }
    }, [language]);

    const value = {
      ...props,
      title,
      tenantName,
      language,
      token,
      logo,
      setTitle,
      accountDataSet,
      personId,
      email,
      tenantId,
      expire,
      policy,
      setPolicy,
      intl,
      intlPrefix,
      showHeader,
      setShowHeader,
      supportLanguages,
    };

    return (
      <Stores.Provider value={value}>
        {children}
      </Stores.Provider>
    );
  }
));
