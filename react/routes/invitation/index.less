@import "~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables";

@media (min-width: 768px) {
  .invite {
    // background-color: #f5f5f5;
    background-size: cover;

    .invite-content {
      // box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.20);
      // border-radius: 2px;
      // background-color: #fff;
    }
  }
}

@media (max-width: 767px) {
  .loginSpan {
    opacity: 0;
  }

  body {
    background-image: none;
  }

  .forget-passwd,
  .registry-org {
    display: none;
  }

  .login #content .control-group {
    margin-top: 0;
  }

  .login #content {
    padding-left: 0;
    padding-right: 0;
  }

  .c7n-pro-field:before {
    border-style: none;
    border-bottom-style: solid;
    border-radius: 0;
  }

  #loginButton {
    margin: 0 30px;
  }
}

.split {
  margin: 0 10px;
}

.controls-mobile {
  text-align: center;
}

.hiddenLoginErrorMsg {
  display: none;
}

.language {
  position: fixed;
  top: 16px;
  right: 24px;
  width: 92px;
  height: 24px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  span {
    display: inline-block;
  }
  &-current {
    display: inline-block;
    width: 80px;
    height: 24px;
    font-size: 16px;
    color: #595959;
  }
}

.languageMenu-main {
  position: fixed;
  top: 45px;
  right: 35px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 0.08rem 0.16rem 0 rgba(0, 0, 0, 0.2)
}

.invite {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  .logo {
    width: 0.8rem;
    height: 0.8rem;
  }
  &-expired-content {
    width: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    &-logo-icon {
      width: 3.8rem;
      height: 3.8rem;
    }
  }
  .notice {
    text-align: center;
    font-size: 0.2rem;
    margin-bottom: 0.1rem;
  }
  &-content {
    width: 354px;
    min-height: 434px;

    &-text {
      font-size: 24px;
      line-height: 33px;
      font-weight: 500;
      text-align: center;
      display: block;
      margin-top: 22px;
      margin-bottom: 30px;
      color: #262626;
    }

    &-logo {
      display: flex;
      justify-content: center;
      margin-top: 50px;

      &-icon {
        color: #3f51b5;
        width: 0.57rem;
        height: 0.57rem;
      }

      &-text {
        display: block;
        font-family: Monospaced Number, Chinese Quote, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif;
        font-weight: 500;
        line-height: 36px;
        height: 38px;
        margin-left: 8px;
        font-size: 24px;
        color: rgba(0, 0, 0, 0.65);
      }
    }
  }

  &-footer {
    position: absolute;
    z-index: 999;
    bottom: 10px;
    color: #555;
    font-size: 12px;
    left: calc(50vw - 228px);
  }
}

.c7n-pro-field-label,
.c7n-pro-field-wrapper {
  padding: 0.1rem 0 !important;
}

.icon-visibility_off,
.icon-visibility {
  margin-top: 2px;
}

.c7n-pro-btn {
  &.c7n-pro-btn-primary {
    height: 38px;
  }
}

.invite-email {
  .back {
    height: 20px;
    font-size: 14px;
    color: #12274d;
    margin-bottom: 12px;
    line-height: 20px;
    opacity: 0.65;
    cursor: pointer;
    display: flex;
    align-items: center;
  }

  .verify-title {
    min-height: 40px;
    font-size: 28px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #12274d;
    line-height: 40px;
    margin-bottom: 12px;
  }

  .verify-tips {
    min-height: 20px;
    font-size: 14px;
    font-weight: 400;
    color: #12274d;
    line-height: 20px;
    opacity: 0.65;
  }

  .verify-email {
    margin: 20px 0;
    display: block;
  }

  .verify-captcha {
    width: 100%;
  }

  .verify-captcha-skip {
    margin-top: 12px;
    color: #2979ff;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      cursor: pointer;
    }

    .yqcloud-icon-park-wrapper {
      font-size: 14px;
      align-items: center;
    }

    &.time {
      color: #12274d;
    }
  }

  .success {
    display: flex;
    justify-content: center;
    padding: 33px 0 21px 0;
    background-color: @yq-fill-2;
    margin-top: 24px;

    .yqcloud-icon-park-wrapper {
      align-items: center;
      margin-right: 8px;
    }

    .success-title {
      font-size: 16px;
      font-weight: 500;
    }
  }

  .success-skip {
    color: #12274d;
  }
}
