import { observer } from 'mobx-react-lite';
import React, { useContext, useState, useEffect, useRef, useMemo } from 'react';
import { Form, TextField, Password, message, Tooltip } from 'choerodon-ui/pro';
import { Button, Icon } from '@zknow/components';
import { setCookie, getEnv } from '@zknow/utils';
import { useCaptcha } from '@zknow/aj-captcha';
import axios from 'axios';
import { getAccessToken, removeAccessToken } from '@zknow/utils';
import useInterval from '@/components/use-interval';
import defaultImg from '@/assets/images/favicon.png';
import Captcha from './captcha';
import Store from './stores';
import './MainView.less';

function MainView(props) {
  const { language } = props;
  const { setTitle, logo, tenantName, token, accountDataSet, email, personId, tenantId, policy, intl, intlPrefix, setShowHeader } = useContext(Store);
  const [step, setStep] = useState(0);
  const [time1, setTime1] = useState(3);
  const [time2, setTime2] = useState(3);
  const [warningStr, setWarning] = useState(null);
  const [captcha, setCaptcha] = useState('');
  const [delay, setDelay] = useState(0);
  const [captchaKey, setCaptchaKey] = useState('');
  const timer1 = useRef();
  const timer2 = useRef();
  const backUrl = `${window.location.origin}/#/iam/invite?token=${token}`;
  const clientId = window.location.host?.split('.')[0];
  const [run] = useCaptcha({ path: `${window._env_.API_HOST}/oauth/public`, type: 'slide', extraParams: { lang: language, validateHelp: intl.formatMessage({ id: 'iam.invitation.captcha.complete.please', defaultMessage: '请完成安全验证' }), validateTips: intl.formatMessage({ id: 'iam.invitation.captcha.slide.right', defaultMessage: '向右滑动完成验证' }) } });

  useInterval(() => {
    if (delay > 0) {
      setDelay(delay - 1);
    }
  }, 1000);

  useEffect(() => {
    if (email) {
      accountDataSet.create({ email });
    }
  }, [email, intl]);

  useEffect(() => {
    if (policy) {
      const { uppercaseCount, lowercaseCount, digitsCount, specialCharCount, characterTypeCount, minLength } = policy;
      const warningArr = [];
      if (uppercaseCount) {
        warningArr.push(intl.formatMessage({ id: 'iam.invitation.desc.uppercase', defaultMessage: '大写字母' }));
      }
      if (lowercaseCount) {
        warningArr.push(intl.formatMessage({ id: 'iam.invitation.desc.lowercase', defaultMessage: '小写字母' }));
      }
      if (digitsCount) {
        warningArr.push(intl.formatMessage({ id: 'iam.invitation.desc.digits.count', defaultMessage: '数字' }));
      }
      if (specialCharCount) {
        warningArr.push(intl.formatMessage({ id: 'iam.invitation.desc.special.char.count', defaultMessage: '符号' }));
      }
      setWarning(warningArr.join(','));
    }
  }, [policy]);

  useEffect(() => {
    if (time1 === 1) {
      clearInterval(timer1.current);
      localStorage.clear();
      setShowHeader(true);
      setStep(5);
    }
  }, [time1]);

  useEffect(() => {
    if (time2 === 1) {
      clearInterval(timer2.current);
      localStorage.clear();
      window.location.replace('');
    }
  }, [time2]);

  // 邀请租户
  function renderStep0() {
    setTitle(intl.formatMessage({ id: 'iam.invitation.desc.add.tennant', defaultMessage: '邀请加入租户' }));
    function handleNewClick() {
      if (policy?.userCheckedFlag) {
        setShowHeader(false);
        setStep(2);
      } else {
        setStep(5);
      }
    }
    function handleOldClick() {
      setStep(1);
    }
    return (
      <React.Fragment>
        <div className="invite-tenant">
          <div className="invite-tenant-logo">
            {
              logo && token ? <img className="logo" src={`${window._env_.API_HOST}/hfle/v1/0/files/download-by-key?fileKey=${logo}&access_token=${token}`} alt="logo" />
                : <img className="logo" alt="" src={defaultImg} />
            }
          </div>
          <div className="invite-tenant-name">{tenantName}</div>
        </div>
        <div className="invite-tenant-button">
          <Button
            className="invite-tenant-button-new"
            onClick={handleNewClick}
            funcType="raised "
            color="primary"
          >{intl.formatMessage({ id: 'iam.invitation.desc.join', defaultMessage: '我要加入' })}</Button>
          <Button
            className="invite-tenant-button-old"
            onClick={handleOldClick}
            funcType="flat"
            color="dark"
          >{intl.formatMessage({ id: 'iam.invitation.desc.use.account.join', defaultMessage: '使用已有帐号加入' })}</Button>
        </div>
      </React.Fragment>
    );
  }

  function timer1Open() {
    timer1.current = setInterval(() => {
      setTime1((pre) => {
        return pre - 1;
      });
    }, 1000);
  }

  function timer2Open() {
    timer2.current = setInterval(() => {
      setTime2((pre) => {
        return pre - 1;
      });
    }, 1000);
  }

  function backPrev() {
    setShowHeader(true);
    setStep(0);
  }

  // 获取验证码
  async function getCaptcha() {
    const data = await run();
    const { captchaVerification } = data;
    const verKey = captchaVerification?.split('---')?.[0];
    if (verKey) {
      const ajCaptchaKey = localStorage.getItem('checkCaptchaKey');
      const res = await axios.post(`${window._env_.API_HOST}/oauth/public/yqc/sendWebCaptcha`, {
        phoneOrEmail: email,
        type: 'email',
        captchaKey: ajCaptchaKey,
      });
      if (!res?.failed) {
        setCaptchaKey(res?.captchaKey);
        message.success(res?.message);
        setStep(3);
      } else {
        message.error(res?.message);
      }
    } else {
      return false;
    }
  }

  function captchaBack() {
    setCaptcha('');
    setStep(2);
  }

  async function resendCaptcha() {
    const data = await run();
    const { captchaVerification } = data;
    const verKey = captchaVerification?.split('---')?.[0];
    if (verKey) {
      const ajCaptchaKey = localStorage.getItem('checkCaptchaKey');
      const res = await await axios.post(`${window._env_.API_HOST}/oauth/public/yqc/sendWebCaptcha`, {
        phoneOrEmail: email,
        type: 'email',
        captchaKey: ajCaptchaKey,
      });
      if (!res?.failed) {
        setCaptchaKey(res.captchaKey);
        setDelay(res.interval);
        message.success(res?.message);
      } else {
        message.error(res?.message);
      }
    } else {
      return false;
    }
  }

  async function handleVerify() {
    const res = await axios.post(`${window._env_.API_HOST}/oauth/public/yqc/valid-user-email`, {
      email,
      verificationCode: captcha,
      clientId,
      captchaKey,
    });
    if (!res?.failed) {
      setStep(4);
      timer1Open();
    } else {
      message.error(res.message);
    }
  }

  function renderStep3() {
    return (
      <div className="invite-email">
        <div onClick={captchaBack} className="back">
          <Icon type="ArrowLeft" size={16} />
          <div style={{ marginLeft: 6 }}>{intl.formatMessage({ id: 'zknow.common.button.back', defaultMessage: '返回' })}</div>
        </div>
        <div className="verify-title">{intl.formatMessage({ id: 'iam.invitation.desc.email.verify', defaultMessage: '邮箱验证' })}</div>
        <div className="verify-tips">{intl.formatMessage({ id: 'iam.invitation.email.captcha.tips', defaultMessage: '验证码已发送至 {email}，有效期5分钟' }, { email })}</div>
        <Captcha value={captcha} onChange={setCaptcha} theme="box" autoFocus length={6} />

        <Button
          funcType="raised"
          color="primary"
          className="verify-captcha"
          disabled={captcha?.length !== 6}
          onClick={handleVerify}
        >
          {intl.formatMessage({ id: 'zknow.common.button.confirm', defaultMessage: '确认' })}
        </Button>
        {delay !== 0 && (
          <div className="verify-captcha-skip time">{intl.formatMessage({ id: 'iam.invitation.email.captcha.resend.second', defaultMessage: '{second}秒后可重新获取验证码' }, { second: delay })}</div>
        )}
        {delay === 0 && (
          <div
            className="verify-captcha-skip"
            onClick={resendCaptcha}
          >
            {intl.formatMessage({ id: 'iam.invitation.desc.email.captcha.resend', defaultMessage: '重新获取验证码' })}
          </div>
        )}
      </div>
    );
  }

  function skipVerify() {
    setShowHeader(true);
    setStep(5);
  }

  function renderStep2() {
    return (
      <div className="invite-email">
        <div onClick={backPrev} className="back">
          <Icon type="ArrowLeft" size={16} />
          <div style={{ marginLeft: 6 }}>{intl.formatMessage({ id: 'zknow.common.button.back', defaultMessage: '返回' })}</div>
        </div>
        <div className="verify-title">{intl.formatMessage({ id: 'iam.invitation.desc.email.verify', defaultMessage: '邮箱验证' })}</div>
        <div className="verify-tips">{intl.formatMessage({ id: 'iam.invitation.desc.email.verify.tips', defaultMessage: '为提升账号安全性，请进行邮箱验证' })}</div>
        <TextField className="verify-email" value={email} disabled />

        <Button
          funcType="raised"
          color="primary"
          className="verify-captcha"
          onClick={getCaptcha}
        >
          {intl.formatMessage({ id: 'iam.invitation.desc.email.captcha', defaultMessage: '获取验证码' })}
        </Button>
        <div
          className="verify-captcha-skip"
          onClick={skipVerify}
        >
          {intl.formatMessage({ id: 'iam.invitation.desc.email.verify.skip', defaultMessage: '跳过验证' })}
          <Tooltip title={intl.formatMessage({ id: 'iam.invitation.desc.email.verify.skip.tips', defaultMessage: '为了您的账号安全，请进行邮箱验证。可先跳过验证，账号初次登录时再进行验证。' })}>
            <Icon type="help" />
          </Tooltip>
        </div>
      </div>
    );
  }

  function renderStep4() {
    setTitle(intl.formatMessage({ id: 'iam.invitation.desc.set.password.success', defaultMessage: '设置密码成功' }));
    return (
      <div className="invite-email">
        <div className="verify-title">{intl.formatMessage({ id: 'iam.invitation.desc.email.verify', defaultMessage: '邮箱验证' })}</div>
        <div className="verify-tips">{intl.formatMessage({ id: 'iam.invitation.email.captcha.tips', defaultMessage: '验证码已发送至 {email}，有效期5分钟' }, { email })}</div>
        <div className="success">
          <Icon type="CheckOne" theme="filled" size={20} fill="#55C12A" />
          <span className="success-title">
            {intl.formatMessage({ id: 'iam.invitation.desc.email.verify.success', defaultMessage: '验证成功' })}
          </span>
        </div>
        <div className="verify-captcha-skip success-skip"><span style={{ color: '#2979ff' }}>{time1}</span>{intl.formatMessage({ id: 'iam.invitation.desc.email.setting.password', defaultMessage: '秒后跳转到设置密码' })}</div>
      </div>
    );
  }

  async function logout() {
    // 如果用户已经登录系统，需要退出已有账号，并清空相关信息
    const currentToken = getAccessToken();
    if (currentToken) {
      const tokenList = currentToken.split(' ') || [];
      const accessToken = tokenList?.length > 1 ? tokenList[1] : tokenList[0];
      if (accessToken) {
        try {
          await axios.get(`${window._env_.API_HOST}/oauth/logout?access_token=${accessToken}`);
        } catch (e) {
          // 调用退出接口返回302
        }
      }
      if (removeAccessToken) {
        removeAccessToken();
      }
      localStorage.clear();
      sessionStorage.clear();
    }
    return true;
  }

  function renderStep6() {
    return (
      <div className="invite-email">
        <div className="verify-title">{intl.formatMessage({ id: 'iam.invitation.desc.set.password', defaultMessage: '设置密码' })}</div>
        <div className="verify-tips">{intl.formatMessage({ id: 'iam.invitation.email.can.use', defaultMessage: '您可以使用{email}登录' }, { email })}</div>
        <div className="success">
          <Icon type="CheckOne" theme="filled" size={20} fill="#55C12A" />
          <span className="success-title">
            {intl.formatMessage({ id: 'iam.invitation.desc.set.password.success', defaultMessage: '设置密码成功' })}
          </span>
        </div>
        <div className="verify-captcha-skip success-skip"><span style={{ color: '#2979ff' }}>{time2}</span>{intl.formatMessage({ id: 'iam.invitation.desc.after.seconds.login', defaultMessage: '秒后跳转至登录' })}</div>
      </div>
    );
  }

  // 设置密码
  function renderStep5() {
    setTitle(intl.formatMessage({ id: 'iam.invitation.desc.set.password', defaultMessage: '设置密码' }));
    async function handleCreateAccount() {
      if (personId && token) {
        const { uppercaseCount, lowercaseCount, digitsCount, specialCharCount, characterTypeCount, minLength } = policy;

        /**
         * 密码规则校验
         * characterTypeCount : 至少包含几种
         * minLength : 最少长度
         * lowercaseCount 小写 uppercaseCount: 大写 digitsCount：数字 specialCharCount:符号
         */

        const modifyPassword = accountDataSet.current?.get('password');
        if (modifyPassword.length < minLength) {
          message.error(`${intl.formatMessage({ id: 'iam.invitation.desc.password.atlast', defaultMessage: '密码长度至少' })}${minLength}位`);
          return false;
        }
        if (uppercaseCount && !((/[A-Z]+/g).test(modifyPassword))) {
          message.error(`${intl.formatMessage({ id: 'iam.invitation.desc.password.contain', defaultMessage: '密码至少包含' })}${warningStr}`);
          return false;
        }
        if (lowercaseCount && !((/[a-z]+/g).test(modifyPassword))) {
          message.error(`${intl.formatMessage({ id: 'iam.invitation.desc.password.contain', defaultMessage: '密码至少包含' })}${warningStr}`);
          return false;
        }
        if (digitsCount && !((/[0-9]+/g).test(modifyPassword))) {
          message.error(`${intl.formatMessage({ id: 'iam.invitation.desc.password.contain', defaultMessage: '密码至少包含' })}${warningStr}`);
          return false;
        }
        if (specialCharCount && (/^[\u4e00-\u9fa5a-zA-Z0-9]+$/).test(modifyPassword)) {
          message.error(`${intl.formatMessage({ id: 'iam.invitation.desc.password.contain', defaultMessage: '密码至少包含' })}${warningStr}`);
          return false;
        }
        const account = {
          personId,
          user: {
            email: accountDataSet.current?.get('email'),
            password: modifyPassword,
          },
        };
        axios.post(`${window._env_.API_HOST}/iam/yqc/v1/${tenantId}/users/invite/agree/create?token=${token}`, JSON.stringify(account))
          .then((resp) => {
            if (!resp?.failed) {
              message.success(intl.formatMessage({ id: 'iam.invitation.desc.bind.success', defaultMessage: '绑定成功' }));
              setShowHeader(false);
              setStep(6);
              timer2Open();
            } else {
              message.error(resp?.message);
            }
          })
          .catch((error) => {
            // eslint-disable-next-line no-console
            console.log(error);
          });
      }
    }
    return (
      <Form
        id="account"
        onSubmit={(value, item, form) => {
          handleCreateAccount();
        }}
        className="invite-password"
        labelLayout="horizontal"
        dataSet={accountDataSet}
      >
        <TextField
          labelWidth={0}
          validator={(value) => {
            if (!value) {
              return `${intl.formatMessage({ id: 'iam.invitation.desc.input.email', defaultMessage: '请输入邮箱' })}`;
            }
          }}
          name="email"
          disabled
          placeholder={`${intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' })}`}
        />
        <Password
          labelWidth={0}
          name="password"
          required
          restrict={/[^\x00-\xff ]/gi}
          placeholder={`${intl.formatMessage({ id: 'iam.invitation.desc.set.password', defaultMessage: '设置密码' })}`}
        />
        <Password
          labelWidth={0}
          name="confirmPassword"
          required
          restrict={/[^\x00-\xff ]/gi}
          placeholder={`${intl.formatMessage({ id: 'iam.invitation.desc.confirm.password', defaultMessage: '确认密码' })}`}
        />
        <div className="invite-password-bottom">
          <Button className="invite-password-button" style={{ marginTop: '.3rem' }} funcType="raised " color="primary" type="submit">{intl.formatMessage({ id: 'zknow.common.button.confirm', defaultMessage: '确认' })}</Button>
          <a onClick={backPrev}>{intl.formatMessage({ id: 'zknow.common.button.back', defaultMessage: '返回' })}</a>
        </div>
      </Form>
    );
  }

  async function renderStep1() {
    setCookie('isInvite', true, { path: '/', domain: window._env_?.COOKIE_SERVER });
    setCookie('backUrl', backUrl, { path: '/', domain: window._env_?.COOKIE_SERVER });
    setCookie('language', props.language, { path: '/', domain: window._env_?.COOKIE_SERVER });
    // 退出当前账号，防止误绑定到当前登录账号
    await logout();
    // 绑定已有账号，需要为平台层
    const newOrigin = window.location.origin?.replace(/(http[s]?:\/\/).+?\./, `$1${getEnv('CLIENT_ID') || 'apps'}.`);
    window.location.href = `${newOrigin}/#/?inviteId=${personId}&inviteToken=${token}&inviteTenantId=${tenantId}`;
    return null;
  }

  switch (step) {
    case 0:
      return renderStep0();
    case 1:
      renderStep1();
      return null;
    case 2:
      return renderStep2();
    case 3:
      return renderStep3();
    case 4:
      return renderStep4();
    case 5:
      return renderStep5();
    case 6:
      return renderStep6();
    default:
      return null;
  }
}

export default observer(MainView);
