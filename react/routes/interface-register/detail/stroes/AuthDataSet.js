import { DataSet } from 'choerodon-ui/pro';

export default ({ intl, interfaceServerId, tenantId }) => {
  const url = `/hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}interface-servers`;

  const authOptions = new DataSet({
    data: [
      { meaning: 'None', value: 'NONE' },
      { meaning: 'Basic', value: 'BASIC' },
      { meaning: 'Oauth2', value: 'OAUTH2' },
      { meaning: 'Bearer token', value: 'BEARER_TOKEN' },
    ],
  });

  const grantTypeOptions = new DataSet({
    data: [
      { meaning: 'password', value: 'password' },
      { meaning: 'client_credentials', value: 'BASIclient_credentialsC' },
    ],
  });

  const methodOptions = new DataSet({
    data: [
      { meaning: 'GET', value: 'GET' },
      { meaning: 'POST', value: 'POST' },
      { meaning: 'PUT', value: 'PUT' },
      { meaning: 'DELETE', value: 'DELETE' },
      { meaning: 'OPTIONS', value: 'OPTIONS' },
      { meaning: 'HEAD', value: 'HEAD' },
    ],
  });

  return {
    transport: {
      read: {
        url: `${url}/${interfaceServerId}?interfaceServerId=${interfaceServerId}`,
        method: 'get',
        transformResponse: (res) => {
          if (!res) return res;
          const data = JSON.parse(res).httpAuthorization;
          let DEFAULT = {
            connectionRequestTimeout: 10000,
            connectTimeout: 30000,
            socketTimeout: 30000,
          };
          if (!data?.authType || data?.authType === 'NONE') {
            return {
              authType: 'NONE',
              ...DEFAULT,
            };
          }
          const authJson = JSON.parse(data?.authJson);
          if (authJson.connectTimeout) {
            DEFAULT = {};
          }
          return {
            ...authJson,
            ...DEFAULT,
            authType: data?.authType,
          };
        },
      },
    },
    fields: [
      { name: 'authType', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.authType', defaultMessage: '接口认证模式' }), defaultValue: 'NONE', options: authOptions },
      { name: 'grant_type', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.grant_type', defaultMessage: '授权模式' }), options: grantTypeOptions },
      { name: 'username', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.username', defaultMessage: '用户名' }) },
      { name: 'password', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.password', defaultMessage: '密码' }) },
      { name: 'client_id', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.client_id', defaultMessage: '客户端ID' }) },
      { name: 'client_secret', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.client_secret', defaultMessage: '客户端密钥' }) },
      { name: 'accessTokenUrl', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.accessTokenUrl', defaultMessage: 'Token地址' }) },
      { name: 'httpMethod', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.httpMethod', defaultMessage: '请求方法' }), options: methodOptions },
      { name: 'connectionRequestTimeout', type: 'number', label: intl.formatMessage({ id: 'iam.interface.model.connectionRequestTimeout', defaultMessage: '连接请求超时时间' }) },
      { name: 'connectTimeout', type: 'number', label: intl.formatMessage({ id: 'iam.interface.model.connectTimeout', defaultMessage: '连接超时时间' }) },
      { name: 'socketTimeout', type: 'number', label: intl.formatMessage({ id: 'iam.interface.model.socketTimeout', defaultMessage: '请求交互超时时间' }) },
      { name: 'token', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.token', defaultMessage: 'Token' }) },
    ],
  };
};
