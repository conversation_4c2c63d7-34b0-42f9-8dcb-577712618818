import { DataSet } from 'choerodon-ui/pro';

export default ({ intl, interfaceServerId, tenantId }) => {
  const url = `/hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}interface-servers`;

  const interfaceOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.interface.model.interfaceType.restful' }), value: 'REST' },
      // { meaning: intl.formatMessage({ id: 'SOAP' }), value: 'SOAP' },
    ],
  });

  const httpOptions = new DataSet({
    data: [
      { meaning: 'http', value: 'http://' },
      { meaning: 'https', value: 'https://' },
    ],
  });

  const categoryOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.interface.model.category.outer', defaultMessage: '外部接口' }), value: 'EXTERNAL' },
      { meaning: intl.formatMessage({ id: 'iam.interface.model.category.inner', defaultMessage: '内部接口' }), value: 'INTERNAL' },
    ],
  });

  const statusOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.button.enable', defaultMessage: '启用' }), value: 1 },
      { meaning: intl.formatMessage({ id: 'zknow.common.button.deactivated', defaultMessage: '停用' }), value: 0 },
    ],
  });

  const publicOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.desc.public', defaultMessage: '公开' }), value: true },
      { meaning: intl.formatMessage({ id: 'zknow.common.desc.private', defaultMessage: '私有' }), value: false },
    ],
  });

  const contentType = new DataSet({
    data: [
      { value: 'application/json' },
      { value: 'application/xml' },
      { value: 'application/x-www-form-urlencoded' },
      { value: 'application/x-www-form-urlerl' },
      { value: 'multipart/form-data' },
      { value: 'text/xml' },
      { value: 'text/html' },
      { value: 'text/plain' },
      { value: 'application/soap+xml' },
    ],
  });

  function getUpdateData(data) {
    const domainUrl = data.protocol ? data.protocol + data.domainUrl : data.domainUrl;
    if (!data?.httpAuthorization?.authType) {
      data.httpAuthorization = {
        authType: 'NONE',
        authJson: '{}',
      };
    }
    return {
      ...data,
      domainUrl,
    };
  }

  return {
    autoQuery: !!interfaceServerId,
    selection: false,
    paging: false,
    primaryKey: 'interfaceServerId',
    transport: {
      read: {
        url: `${url}/${interfaceServerId}?interfaceServerId=${interfaceServerId}`,
        method: 'get',
        transformResponse: (res) => {
          if (!res) return res;
          const data = JSON.parse(res);
          let domainUrl = data?.domainUrl;
          let protocol;
          if (domainUrl?.startsWith('http://')) {
            protocol = 'http://';
            domainUrl = domainUrl.split('http://')[1];
          } else if (domainUrl?.startsWith('https://')) {
            protocol = 'https://';
            domainUrl = domainUrl.split('https://')[1];
          }
          if (data?.serviceCategory === 'INTERNAL') {
            protocol = '';
          }
          return {
            protocol,
            ...data,
            domainUrl,
          };
        },
      },
      create: ({ data: [data] }) => ({
        url: `${url}`,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => {
        return ({
          url: `${url}`,
          method: 'put',
          data: getUpdateData(data),
        });
      },
      destroy: ({ data: [data] }) => ({
        url: `${url}/version?versionId=${data.id}`,
        method: 'delete',
      }),
    },
    fields: [
      { name: 'serverName', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.serverName', defaultMessage: '服务名称' }), required: true },
      { name: 'serverCode', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }), required: true, format: 'uppercase' },
      { name: 'serviceCategory', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.category', defaultMessage: '接口分类' }), options: categoryOptions, required: true },
      { name: 'serviceType', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.interfaceType', defaultMessage: '接口类型' }), required: true, options: interfaceOptions },
      { name: 'protocol', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.protocol', defaultMessage: '服务协议' }), options: httpOptions },
      { name: 'domainUrl', type: 'string', required: true, label: intl.formatMessage({ id: 'iam.interface.model.interfaceUrl', defaultMessage: '接口地址' }), help: intl.formatMessage({ id: 'iam.interface.model.interfaceUrl.help', defaultMessage: '内部接口地址为燕千云内部服务，外部接口为实际接口地址，支持http/https。' }) },
      { name: 'certificationConfig', type: 'boolean', label: intl.formatMessage({ id: 'iam.interface.model.certificationConfig', defaultMessage: '认证配置' }) },
      { name: 'publicFlag', type: 'boolean', label: intl.formatMessage({ id: 'iam.interface.model.publicFlag', defaultMessage: '是否公开' }), options: publicOptions },
      { name: 'enabledFlag', type: 'number', label: intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' }), options: statusOptions },
      { name: 'requestContentType', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.requestContentType', defaultMessage: '请求ContentType' }), options: contentType },
      { name: 'responseContentType', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.responseContentType', defaultMessage: '响应ContentType' }), options: contentType },
      { name: 'httpAuthorization', type: 'object' },
    ],
  };
};
