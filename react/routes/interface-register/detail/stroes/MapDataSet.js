import { DataSet } from 'choerodon-ui/pro';

export default ({ intl, tenantId, type, serverCode, interfaceCode }) => {
  const url = `/hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}transforms/interface-linked/load?level=${type}&namespace=YQCLOUD&serverCode=${serverCode}&interfaceCode=${interfaceCode}&transformLevel=${type}&sourceRef=HZERO-INTERFACE&queryType=FWZC&tenantId=${tenantId}`;
  const transformType = intl.formatMessage({ id: 'zknow.common.model.type', defaultMessage: '类型' });

  const mapTypeOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.interfaceRegister.model.json.to.json', defaultMessage: 'JSON 转 JSON' }), value: 'REST_TO_REST' },
      { meaning: intl.formatMessage({ id: 'iam.interfaceRegister.model.json.to.xml', defaultMessage: 'JSON 转 XML' }), value: 'REST_TO_SOAP' },
      { meaning: intl.formatMessage({ id: 'iam.interfaceRegister.model.xml.to.json', defaultMessage: 'XML 转 JSON' }), value: 'SOAP_TO_REST' },
      { meaning: intl.formatMessage({ id: 'iam.interfaceRegister.model.xml.to.xml', defaultMessage: 'XML 转 XML' }), value: 'SOAP_TO_SOAP' },
    ],
  });

  const defaultCode = '%dw 2.0 \t\noutput application/json \t\n--- \t\n';

  return {
    autoQuery: false,
    paging: false,
    transport: {
      read: {
        url,
        method: 'get',
      },
      create: ({ data: [data] }) => ({
        url: `hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}transforms`,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => ({
        url: `hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}transforms`,
        method: 'post',
        data,
      }),
    },
    fields: [
      { name: 'transformType', type: 'string', label: transformType, options: mapTypeOptions, required: true },
      { name: 'transformScript', type: 'string', defaultValue: defaultCode },
    ],
  };
};
