import { DataSet } from 'choerodon-ui/pro';

export default ({ intl, id, tenantId }) => {
  const url = `hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}${id}/monitor`;
  const recordCall = intl.formatMessage({ id: 'iam.interfaceRegister.model.recording.call.details', defaultMessage: '记录调用详情' });

  const authOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.interfaceRegister.model.no.record', defaultMessage: '不记录' }), value: '0' },
      { meaning: intl.formatMessage({ id: 'iam.interfaceRegister.model.record.basic.information.and.request.response.message', defaultMessage: '记录基础信息及请求响应报文' }), value: '1' },
      { meaning: intl.formatMessage({ id: 'iam.interfaceRegister.model.only.basic.information.is.recorded', defaultMessage: '仅记录基础信息' }), value: '2' },
    ],
  });

  const alarmOptions = new DataSet({
    data: [
      { meaning: '接口业务状态失败', value: 'HITF.BUSINESS_STATE_FAIL' },
      { meaning: '接口健康检查', value: 'HITF.HEALCH_CHECK' },
      { meaning: '接口响应失败', value: 'HITF.RESPONSE_STATUS_FAIL' },
    ],
  });

  function getUpdateData(data) {
    if (!data.healthCheckFlag) {
      const list = ['interfaceUsecaseId', 'checkPeriod', 'statisticsPeriod', 'checkThreshold', 'preAlertCode'];
      list.forEach(item => delete data[item]);
    }
    return {
      ...data,
      healthCheckFlag: data.healthCheckFlag ? 1 : 0,
    };
  }
  
  return {
    autoQuery: false,
    paging: false,
    transport: {
      read: {
        url,
        method: 'get',
        transformResponse: (data) => {
          if (!data) return data;
          const res = JSON.parse(data);
          return {
            ...res,
            healthCheckFlag: res.healthCheckFlag === 1,
          };
        },
      },
      create: ({ data: [data] }) => ({
        url,
        method: 'post',
        data: getUpdateData(data),
      }),
      update: ({ data: [data] }) => ({
        url,
        method: 'put',
        data: getUpdateData(data),
      }),
    },
    fields: [
      { name: 'invokeDetailsFlag', type: 'string', label: recordCall, options: authOptions },
      { name: 'healthCheckFlag', type: 'boolean', label: '是否开启健康检查', defaultValue: false },
      { name: 'alertCode', type: 'string', label: '告警代码', options: alarmOptions },
      { name: 'interfaceUsecaseId', typr: 'string', label: '所用测试用例', dynamicProps: { required: ({ record }) => record.get('healthCheckFlag') } },
      { name: 'checkPeriod', typr: 'number', label: '检查周期(秒)', dynamicProps: { required: ({ record }) => record.get('healthCheckFlag') } },
      { name: 'statisticsPeriod', typr: 'number', label: '统计周期(秒)', dynamicProps: { required: ({ record }) => record.get('healthCheckFlag') } },
      { name: 'checkThreshold', typr: 'number', label: '异常阈值', dynamicProps: { required: ({ record }) => record.get('healthCheckFlag') } },
      { name: 'preAlertCode', typr: 'string', label: '预警代码', options: alarmOptions, dynamicProps: { required: ({ record }) => record.get('healthCheckFlag') } },
    ],
  };
};
