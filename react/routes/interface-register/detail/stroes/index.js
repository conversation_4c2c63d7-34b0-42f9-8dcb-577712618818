import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import DetailDataSet from './DetailDataSet';
import InterfaceDataSet from './InterfaceDataSet';
import AuthDataSet from './AuthDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: ['iam.interface', 'iam.interfaceRegister'] })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      match,
      AppState: { currentMenuType: { organizationId: tenantId }, getUserInfo },
    } = props;
    const interfaceServerId = match?.params?.id;
    const prefixCls = 'iam-interface';
    const userData = getUserInfo;
    const statusMap = {
      ENABLED: { name: intl.formatMessage({ id: 'iam.interface.model.status.published', defaultMessage: '已发布' }), color: '#7BC95A' },
      DISABLED: { name: intl.formatMessage({ id: 'iam.interface.model.status.unpublished', defaultMessage: '未发布' }), color: '#FF9500' },
      NEW: { name: intl.formatMessage({ id: 'iam.interface.model.status.draft', defaultMessage: '草稿' }), color: '#6454F4' },
    };

    const detailDataSet = useMemo(() => new DataSet(DetailDataSet({ intl, interfaceServerId, tenantId })));
    const interfaceDataSet = useMemo(() => new DataSet(InterfaceDataSet({ intl, interfaceServerId, tenantId })));
    const authDataSet = useMemo(() => new DataSet(AuthDataSet({ intl, interfaceServerId, tenantId })));

    const value = {
      ...props,
      prefixCls,
      detailDataSet,
      tenantId,
      interfaceDataSet,
      statusMap,
      authDataSet,
      interfaceServerId,
      userData,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
