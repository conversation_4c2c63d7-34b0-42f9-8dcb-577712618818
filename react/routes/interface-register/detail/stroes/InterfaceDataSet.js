import { DataSet } from 'choerodon-ui/pro';
import { getQueryParams } from '@zknow/utils';
import { getItfSearch } from '@/utils';

export default ({ intl, interfaceServerId, tenantId }) => {
  const getUrl = `/hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}interface-servers/${interfaceServerId}?interfaceServerId=${interfaceServerId}`;
  const createUrl = `/hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}interfaces/${interfaceServerId}`;

  const methodOptions = new DataSet({
    data: [
      { meaning: 'GET', value: 'GET' },
      { meaning: 'POST', value: 'POST' },
      { meaning: 'PUT', value: 'PUT' },
      { meaning: 'DELETE', value: 'DELETE' },
      { meaning: 'OPTIONS', value: 'OPTIONS' },
      { meaning: 'HEAD', value: 'HEAD' },
    ],
  });

  const publishOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.interface.model.interfaceType.restful', defaultMessage: 'RESTful' }), value: 'REST' },
      // { meaning: intl.formatMessage({ id: 'SOAP' }), value: 'SOAP' },
    ],
  });

  const asyncOptions = new DataSet({
    selection: 'single',
    paging: false,
    data: [
      { text: intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' }), value: 1 },
      { text: intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' }), value: 0 },
    ],
  });

  const contentTypeOptions = new DataSet({
    data: [
      { meaning: 'application/json', value: 'application/json' },
      { meaning: 'application/xml', value: 'application/xml' },
      { meaning: 'application/x-www-form-urlencoded', value: 'application/x-www-form-urlencoded' },
      { meaning: 'application/x-www-form-urlerl', value: 'application/x-www-form-urlerl' },
      { meaning: 'multipart/form-data', value: 'multipart/form-data' },
      { meaning: 'text/xml', value: 'text/xml' },
      { meaning: 'text/html', value: 'text/html' },
      { meaning: 'text/plain', value: 'text/plain' },
      { meaning: 'application/soap+xml', value: 'application/soap+xml' },
    ],
  });

  const httpMap = ['SocketTimeout', 'ConnectTimeout', 'ConnectionRequestTimeout'];

  function createData(data) {
    const httpConfigList = httpMap.map(item => {
      const v = {
        propertyCode: item,
        propertyValue: data[item],
      };
      delete data[item];
      return v;
    });
    return {
      ...data,
      httpConfigList,
    };
  }

  return {
    autoQuery: true,
    paging: 'server',
    pageSize: 20,
    selection: false,
    autoLocateFirst: false,
    transport: {
      read: ({ data }) => ({
        url: getUrl,
        method: 'get',
        data: getQueryParams(data),
        transformResponse: (r) => {
          if (!r) return r;
          const res = JSON.parse(r);
          return res?.pageInterfaces;
        },
      }),
      create: ({ data: [data] }) => ({
        url: createUrl,
        method: 'post',
        data: createData(data),
      }),
      destroy: {
        url: `/hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}interfaces`,
        method: 'delete',
      },
    },
    fields: [
      { name: 'publishType', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.publishType', defaultMessage: '发布类型' }), options: publishOptions, required: true },
      { name: 'interfaceName', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' }), required: true },
      { name: 'interfaceCode', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }), required: true, format: 'uppercase' },
      { name: 'interfaceUrl', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.interfaceUrl', defaultMessage: '接口地址' }), required: true },
      { name: 'requestMethod', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.requestMethod', defaultMessage: '请求方法' }), options: methodOptions, required: true },
      { name: 'requestHeader', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.requestHeader', defaultMessage: 'ContentType' }), options: contentTypeOptions },
      { name: 'timeZone', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.timeZone', defaultMessage: '时区' }), lookupCode: 'TIME_ZONE' },
      { name: 'dateTimeFormat', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.dateFormat', defaultMessage: '日期格式' }), lookupCode: 'DATE_FORMAT' },
      {
        name: 'asyncFlag',
        type: 'number',
        label: intl.formatMessage({ id: 'iam.interface.model.asyncFlag', defaultMessage: '异步调用' }),
        defaultValue: 1,
        textField: 'text',
        valueField: 'value',
        options: asyncOptions,
      },
      { name: 'retryInterval', type: 'number', label: intl.formatMessage({ id: 'iam.interface.model.retryInterval', defaultMessage: '失败重试间隔' }) },
      { name: 'retryTimes', type: 'number', label: intl.formatMessage({ id: 'iam.interface.model.retryTimes', defaultMessage: '失败重试次数' }) },
      { name: 'publishState', type: 'number' },
      { name: 'publishUrl', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.publishUrl', defaultMessage: '发布地址' }) },
      { name: 'version', type: 'number', label: intl.formatMessage({ id: 'iam.interface.model.version', defaultMessage: '当前版本' }) },
      { name: 'formatVersion', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.version', defaultMessage: '当前版本' }) },
      { name: 'status', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' }) },
      { name: 'SocketTimeout', type: 'number', label: intl.formatMessage({ id: 'iam.interface.model.SocketTimeout', defaultMessage: '数据读取超时时间(socketTimeout)' }), defaultValue: -1, required: true },
      { name: 'ConnectTimeout', type: 'number', label: intl.formatMessage({ id: 'iam.interface.model.ConnectTimeout', defaultMessage: '连接超时时间(connectionTimeout)' }), defaultValue: 30000, required: true },
      { name: 'ConnectionRequestTimeout', type: 'number', label: intl.formatMessage({ id: 'iam.interface.model.ConnectionRequestTimeout', defaultMessage: '连接池超时时间(connectionRequestTimeout)' }), defaultValue: 15000, required: true },
    ],
    queryFields: [
      { name: 'interfaceName', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' }) },
      { name: 'interfaceCode', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }), format: 'uppercase' },
    ],
    events: {
      query: ({ params, data }) => {
        getItfSearch(params);
      },
    },
  };
};
