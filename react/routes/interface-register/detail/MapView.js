import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, Select, CodeArea } from 'choerodon-ui/pro';
import '../index.less';

export default observer((props) => {
  const { dataSet, tenantId, intl, modal, itfTenantId } = props;
  const editFlag = String(itfTenantId) !== '0' && tenantId === '0';
  const handleOk = async () => {
    try {
      if (await dataSet.validate()) {
        await dataSet.submit();
        dataSet.query();
        return false;
      } else {
        return false;
      }
    } catch (err) {
      return false;
    }
  };

  modal.handleOk(() => handleOk());
  useEffect(() => {
    if (editFlag) {
      modal.update({
        footer: (okBtn, cancelBtn) => {
          return null;
        },
      });
    }
  }, [editFlag]);

  return (
    <div className="interface-field-mapping">
      <Form
        header={intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' })}
        labelWidth="auto"
        labelLayout="horizontal"
        columns={1}
        dataSet={dataSet}
        disabled={editFlag}
      >
        <Select name="transformType" />
      </Form>
      <div className="multiple-form-top">
        <Form
          header={intl.formatMessage({ id: 'iam.interface.register.mapping.field', defaultMessage: '字段映射维护' })}
          labelWidth="auto"
          labelLayout="horizontal"
          columns={1}
          dataSet={dataSet}
        >
          <CodeArea
            name="transformScript"
            style={{ height: '700px', fontFamily: 'Consolas,Menlo,Courier,monospace' }}
            disabled={editFlag}
          />
        </Form>
      </div>
    </div>
  );
});
