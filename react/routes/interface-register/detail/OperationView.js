import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Button } from '@zknow/components';
import { Form, Select, message } from 'choerodon-ui/pro';
import { axios } from '@zknow/utils';

export default observer((props) => {
  const { dataSet, tenantId, intl, modal, createFlag } = props;
  const [isEdit, setEdit] = useState(false);

  const handleOk = async () => {
    try {
      if (await dataSet.validate()) {
        if (createFlag) {
          await dataSet.submit();
        } else {
          const data = dataSet.current.toData();
          const res = await axios.put(`hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}${data.interfaceId}/monitor/${data.interfaceMonitorId}`, {
            ...data,
            healthCheckFlag: data.healthCheckFlag ? 1 : 0,
          });
          if (res?.failed) {
            message.error(res?.message);
            return false;
          }
        }
        dataSet.query();
        setEdit(false);
        return false;
      } else {
        return false;
      }
    } catch (err) {
      return false;
    }
  };

  modal.handleOk(() => handleOk());
  const handleCancel = () => {
    dataSet.reset();
    setEdit(false);
  };
  useEffect(() => {
    modal.update({
      footer: (okBtn, cancelBtn) => {
        const statusFlag = dataSet?.current?.get('status') === 'ENABLED';
        const myCancelBtn = <Button funcType="raised" onClick={handleCancel}>{intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}</Button>;
        return (
          <div>
            {isEdit && okBtn}
            {!isEdit && !statusFlag && <Button
              funcType="raised"
              color="primary"
              icon="Write"
              onClick={() => setEdit(true)}
            >
              {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
            </Button>}
            {isEdit && myCancelBtn}
          </div>
        );
      },
    });
  }, [isEdit]);

  return (
    <>
      <Form
        labelWidth="auto"
        labelLayout="horizontal"
        disabled={!isEdit}
        columns={1}
        dataSet={dataSet}
      >
        <Select name="invokeDetailsFlag" />
      </Form>
    </>
  );
});
