import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, Select, NumberField, Table } from 'choerodon-ui/pro';

const { Column } = Table;

export default observer((props) => {
  const { modal, dataSet, detailDataSet } = props;
  const record = dataSet.current;

  const handleOk = async () => {
    const res = dataSet?.current.toData();
    let data;
    detailDataSet.current.set('httpAuthorization.authType', res.authType);
    switch (res.authType) {
      case 'NONE':
        data = {};
        break;
      case 'BASIC':
        data = {
          username: res.username,
          password: res.password,
        };
        break;
      case 'OAUTH2':
        data = res;
        delete data.authType;
        delete data.__dirty;
        delete data.token;
        break;
      case 'BEARER_TOKEN':
        data = {
          token: res.token,
        };
        break;
      default:
        break;
    }
    detailDataSet.current.set('httpAuthorization.authJson', JSON.stringify(data));
  };

  modal.handleOk(() => handleOk());
  modal.handleCancel(() => dataSet.reset());

  return (
    <>
      <Form
        labelLayout="horizontal"
        dataSet={dataSet}
        columns={2}
      >
        <Select name="authType" />
        {record?.get('authType') === 'OAUTH2' && <Select name="grant_type" />}
        {record?.get('authType') !== 'BEARER_TOKEN' && record?.get('authType') !== 'NONE' && <TextField name="username" />}
        {record?.get('authType') !== 'BEARER_TOKEN' && record?.get('authType') !== 'NONE' && <TextField name="password" />}
        {record?.get('authType') === 'OAUTH2' && <TextField name="client_id" />}
        {record?.get('authType') === 'OAUTH2' && <TextField name="client_secret" />}
        {record?.get('authType') === 'OAUTH2' && <TextField name="accessTokenUrl" />}
        {/* {record?.get('authType') === 'OAUTH2' && <TextField name="mimeType" />} */}
        {/* {record?.get('authType') === 'OAUTH2' && <TextField name="charset" />} */}
        {record?.get('authType') === 'OAUTH2' && <Select name="httpMethod" />}
        {/* {record?.get('authType') === 'OAUTH2' && <TextField name="accessTokenField" />}
        {record?.get('authType') === 'OAUTH2' && <TextField name="refreshTokenField" />}
        {record?.get('authType') === 'OAUTH2' && <TextField name="replayAccessTokenWrapKey" />} */}
        {record?.get('authType') === 'OAUTH2' && <NumberField step={1} name="connectionRequestTimeout" addonAfter="ms" />}
        {record?.get('authType') === 'OAUTH2' && <NumberField step={1} name="connectTimeout" addonAfter="ms" />}
        {record?.get('authType') === 'OAUTH2' && <NumberField step={1} name="socketTimeout" addonAfter="ms" />}
        {record?.get('authType') === 'BEARER_TOKEN' && <TextField name="token" />}
      </Form>
    </>
  );
});
