import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import axios from 'axios';
import { Button } from '@zknow/components';
import { Form, TextField, Select, SelectBox, message, NumberField } from 'choerodon-ui/pro';

export default observer((props) => {
  const { modal, dataSet, type, interfaceDataSet, intl, tenantId } = props;
  const [isEdit, setEdit] = useState(false);

  const handleOk = async () => {
    try {
      if (await dataSet.validate()) {
        await dataSet.submit();
        dataSet.query();
        if (type === 'modify') {
          interfaceDataSet.query();
          setEdit(false);
          return false;
        } else {
          return true;
        }
      } else {
        return false;
      }
    } catch (err) {
      return false;
    }
  };

  modal.handleOk(() => handleOk());
  modal.handleCancel(() => dataSet.reset());
  const handleCancel = () => {
    dataSet.reset();
    setEdit(false);
  };

  const handleItfPublish = async () => {
    const record = dataSet.current;
    const flag = record.get('status') === 'ENABLED';
    try {
      const res = await axios.post(`hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}interfaces/${flag ? 'offline' : 'release'}`, record.toData());
      if (!res?.failed) {
        dataSet.query();
        interfaceDataSet.query();
        message.success(flag ? intl.formatMessage({ id: 'iam.interface.register.detail.publish.stop', defaultMessage: '停止发布' }) : intl.formatMessage({ id: 'iam.interface.register.detail.publish.success', defaultMessage: '发布成功' }));
      } else {
        message.error(res.message);
      }
      return true;
    } catch (e) {
      return false;
    }
  };

  useEffect(() => {
    if (type === 'modify') {
      modal.update({
        footer: (okBtn, cancelBtn) => {
          const statusFlag = dataSet?.current?.get('status') === 'ENABLED';
          const myCancelBtn = <Button funcType="raised" onClick={handleCancel}>{intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}</Button>;
          return (
            <div>
              <Button
                funcType="raised"
                color={statusFlag ? 'secondary' : 'primary'}
                icon={statusFlag ? 'reduce-one' : 'check-one'}
                onClick={handleItfPublish}
              >
                {statusFlag ? intl.formatMessage({ id: 'iam.interface.register.detail.publish.stop', defaultMessage: '停止发布' }) : intl.formatMessage({ id: 'zknow.common.button.publish', defaultMessage: '发布' })}
              </Button>
              {isEdit && okBtn}
              {!isEdit && !statusFlag && <Button
                funcType="raised"
                color="secondary"
                icon="Write"
                onClick={() => setEdit(true)}
              >
                {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
              </Button>}
              {isEdit && myCancelBtn}
            </div>
          );
        },
      });
    }
  }, [isEdit, dataSet, dataSet?.current?.get('status')]);

  return (
    <>
      <Form
        labelWidth="auto"
        labelLayout="horizontal"
        dataSet={dataSet}
        disabled={!isEdit && type === 'modify'}
        columns={type === 'modify' ? 1 : 2}
      >
        <Select name="publishType" />
        <TextField autoFocus name="interfaceName" />
        <TextField name="interfaceCode" disabled={type === 'modify'} restrict="_|A-Z|a-z|0-9" />
        <TextField name="interfaceUrl" />
        <Select name="requestMethod" />
        <Select name="requestHeader" />
        <Select name="timeZone" />
        <Select name="dateTimeFormat" />
        <SelectBox name="asyncFlag" />
      </Form>
      <div className="multiple-form-top">
        <Form
          labelWidth="100"
          header={intl.formatMessage({ id: 'iam.interface.register.connectConfig', defaultMessage: '连接配置' })}
          labelLayout="horizontal"
          dataSet={dataSet}
          disabled={!isEdit && type === 'modify'}
          columns={type === 'modify' ? 1 : 2}
        >
          <NumberField name="ConnectionRequestTimeout" step={1} addonAfter="ms" />
          <NumberField name="ConnectTimeout" step={1} addonAfter="ms" />
          <NumberField name="SocketTimeout" step={1} addonAfter="ms" />
          <NumberField name="retryInterval" step={1} addonAfter="ms" />
          <NumberField name="retryTimes" step={1} />
        </Form>
      </div>
    </>
  );
});
