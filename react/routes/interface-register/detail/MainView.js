import React, { useState, useCallback, useContext, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Button, ClickText, StatusTag, TableHoverAction } from '@zknow/components';
import { Content, TabPage, Header } from '@yqcloud/apps-master';
import { DataSet, SelectBox, Form, Lov, message, Modal, TextField, Radio, Table, Select } from 'choerodon-ui/pro';
import { axios } from '@zknow/utils';
import ItfDetailDataSet from './stroes/ItfDetailDataSet';
import OperationDataSet from './stroes/OperationDataSet';
import MapDataSet from './stroes/MapDataSet';
import FormView from './FormView';
import AuthConfig from './AuthConfig';
import OperationView from './OperationView';
import MapView from './MapView';
import Store from './stroes';

import './index.less';

const { Column } = Table;
const modalKey = Modal.key();

export default observer(() => {
  const {
    intl,
    prefixCls,
    interfaceDataSet,
    detailDataSet,
    authDataSet,
    history,
    statusMap,
    interfaceServerId,
    tenantId,
    userData,
  } = useContext(Store);

  const [isEdit, setEdit] = useState(false);

  const handleSave = async () => {
    try {
      if (await detailDataSet.validate()) {
        const res = await detailDataSet.submit();
        if (!res?.failed) {
          detailDataSet.query();
          setEdit(false);
        } else {
          message.error(res?.message);
        }
      } else {
        return false;
      }
    } catch (err) {
      return false;
    }
  };

  function getUpdateData(data) {
    const domainUrl = data.protocol ? data.protocol + data.domainUrl : data.domainUrl;
    return {
      ...data,
      domainUrl,
      httpAuthorization: data.httpAuthorization.authType ? data.httpAuthorization : { authType: 'NONE' },
    };
  }
  // 发布
  const handlePublish = async () => {
    const flag = detailDataSet.current.get('status') === 'PUBLISHED';
    try {
      const res = await axios.put(`hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}interface-servers/${flag ? 'offline' : 'release'}`, getUpdateData(detailDataSet.current.toData()));
      if (!res?.failed) {
        detailDataSet.query();
        message.success(flag ? intl.formatMessage({ id: 'iam.interface.register.detail.publish.stop', defaultMessage: '停止发布' }) : intl.formatMessage({ id: 'iam.interface.register.detail.publish.success', defaultMessage: '发布成功' }));
      } else {
        message.error(res.message);
      }
      return true;
    } catch (e) {
      return false;
    }
  };
  // 启停用
  const handleStop = async () => {
    const flag = detailDataSet.current.get('enabledFlag');
    detailDataSet.current.set('enabledFlag', flag ? 0 : 1);
    await detailDataSet.submit();
    detailDataSet.query();
  };
  // 认证配置
  const handleAuthConfig = async () => {
    await authDataSet.query();
    Modal.open({
      title: intl.formatMessage({ id: 'iam.interface.register.detail.authConfig', defaultMessage: '认证配置' }),
      children: (
        <AuthConfig
          dataSet={authDataSet}
          detailDataSet={detailDataSet}
          intl={intl}
        />
      ),
      key: Modal.key(),
      style: { width: 800 },
      drawer: false,
      destroyOnClose: true,
    });
  };
  // header 按钮
  const renderButtons = useCallback(() => {
    const statusFlag = detailDataSet?.current?.get('status') === 'PUBLISHED';
    const enabledFlag = detailDataSet?.current?.get('enabledFlag') === 1;
    const editBtn = [];

    if (enabledFlag) {
      editBtn.push(
        <Button
          funcType="raised"
          color={statusFlag ? 'secondary' : 'primary'}
          icon={statusFlag ? 'reduce-one' : 'check-one'}
          onClick={handlePublish}
          key="publish"
        >
          {statusFlag ? intl.formatMessage({ id: 'iam.interface.register.detail.publish.stop', defaultMessage: '停止发布' }) : intl.formatMessage({ id: 'zknow.common.button.publish', defaultMessage: '发布' })}
        </Button>
      );
    }

    const confirmBtn = [
      <Button
        funcType="raised"
        color="primary"
        icon="config"
        onClick={handleAuthConfig}
        key="config"
      >
        {intl.formatMessage({ id: 'iam.interface.register.detail.authConfig', defaultMessage: '认证配置' })}
      </Button>,
      <Button
        funcType="raised"
        color="primary"
        key="save"
        onClick={handleSave}
      >
        {intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}
      </Button>,
      <Button
        funcType="raised"
        color="default"
        key="cancel"
        onClick={() => {
          setEdit(false);
          detailDataSet.reset();
        }}
      >
        {intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}
      </Button>,
    ];

    if (!statusFlag) {
      editBtn.push(<Button
        funcType="raised"
        color={enabledFlag ? 'secondary' : 'primary'}
        icon={enabledFlag ? 'reduce-one' : 'check-one'}
        onClick={handleStop}
        key="active"
      >
        {enabledFlag ? intl.formatMessage({ id: 'zknow.common.button.enable', defaultMessage: '启用' }) : intl.formatMessage({ id: 'zknow.common.button.deactivated', defaultMessage: '停用' })}
      </Button>);
      editBtn.push(<Button
        funcType="raised"
        color="secondary"
        icon="Write"
        onClick={() => setEdit(true)}
        key="edit"
      >
        {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
      </Button>);
    }
    return !isEdit ? editBtn : confirmBtn;
  }, [isEdit]);
  //  接口详情
  const openModal = async (type, record) => {
    let dataSet = interfaceDataSet;
    if (type === 'modify') {
      const id = record.get('interfaceId');
      dataSet = new DataSet(ItfDetailDataSet({ intl, id, interfaceServerId, tenantId }));
      await dataSet.query();
    }
    Modal.open({
      title: type === 'modify'
        ? <div>{intl.formatMessage({ id: 'iam.interface.detail.title', defaultMessage: '接口详情' })}</div>
        : intl.formatMessage({ id: 'iam.interface.register.create', defaultMessage: '新建接口' }),
      children: (
        <FormView
          dataSet={dataSet}
          intl={intl}
          type={type}
          tenantId={tenantId}
          interfaceDataSet={interfaceDataSet}
        />
      ),
      key: modalKey,
      style: { width: 800 },
      drawer: type === 'modify',
      destroyOnClose: true,
    });
  };
  //  运维配置
  const openOperationModal = async (record) => {
    const id = record.get('interfaceId');
    const operationDataSet = new DataSet(OperationDataSet({ intl, id, tenantId }));
    const res = await operationDataSet.query();
    const createFlag = res?.status === 204;
    if (createFlag || res?.data === '') {
      // 无数据时，创建一条记录
      operationDataSet.create();
    }
    Modal.open({
      title: <div>{intl.formatMessage({ id: 'iam.interface.register.operation.config', defaultMessage: '运维配置' })}</div>,
      children: (
        <OperationView
          dataSet={operationDataSet}
          intl={intl}
          tenantId={tenantId}
          createFlag={createFlag}
        />
      ),
      key: modalKey,
      style: { width: 800 },
      drawer: true,
      destroyOnClose: true,
    });
  };

  // 字段映射
  const openMapView = async (record, type) => {
    const serverCode = record.get('serverCode');
    const interfaceCode = record.get('interfaceCode');
    const mapDataSet = new DataSet(MapDataSet({ intl, tenantId, type, serverCode, interfaceCode }));
    const res = await mapDataSet.query();
    const createFlag = res?.status === 204;
    if (createFlag || res?.data === '') {
      // 无数据时，创建一条记录
      mapDataSet.create({
        tenantId,
        namespace: tenantId === '0' ? 'HZERO' : userData.tenantNum,
        serverCode,
        interfaceCode,
        transformLevel: type,
        statusCode: 'NEW',
      });
    }
    const itfTenantId = detailDataSet?.current?.get('tenantId');
    Modal.open({
      title: <div>{type === 'REQUEST' ? intl.formatMessage({ id: 'iam.interface.register.mapping.request', defaultMessage: '请求字段映射' }) : intl.formatMessage({ id: 'iam.interface.register.mapping.response', defaultMessage: '响应字段映射' })}</div>,
      children: (
        <MapView
          dataSet={mapDataSet}
          intl={intl}
          itfTenantId={itfTenantId}
          tenantId={tenantId}
          createFlag={createFlag}
        />
      ),
      okText: intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' }),
      key: modalKey,
      style: { width: 800 },
      drawer: true,
      destroyOnClose: true,
    });
  };

  const handleCreate = () => {
    interfaceDataSet.create({
      status: 'NEW',
      namespace: tenantId === '0' ? 'HZERO' : userData.tenantNum,
    });
    openModal('create', interfaceDataSet?.current);
  };

  const buttons = [
    <Button funcType="raised" icon="plus" color="primary" onClick={() => handleCreate()}>
      {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
    </Button>,
  ];

  const renderName = ({ record }) => {
    return (
      <ClickText
        record={record}
        valueField="interfaceName"
        onClick={() => openModal('modify', record)}
      />
    );
  };
  //  发布状态
  const renderPublishState = ({ value }) => {
    const { name, color } = statusMap[value] || { name: '' };
    return <StatusTag name={name} color={color}>{name}</StatusTag>;
  };
  // 接口发布
  const handleItfPublish = async (record) => {
    const flag = record.get('status') === 'ENABLED';
    try {
      const url = `/hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}interfaces`;
      const id = record.get('interfaceId');
      const data = await axios.get(`${url}/${id}?interfaceId=${id}`);
      const res = await axios.post(`hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}interfaces/${flag ? 'offline' : 'release'}`, data);
      if (res?.failed || data?.failed) {
        message.error(res.message || data.message);
      } else {
        interfaceDataSet.query();
        message.success(flag ? intl.formatMessage({ id: 'iam.interface.register.detail.publish.stop', defaultMessage: '停止发布' }) : intl.formatMessage({ id: 'iam.interface.register.detail.publish.success', defaultMessage: '发布成功' }));
      }
      return true;
    } catch (e) {
      return false;
    }
  };

  const renderAction = (dataSet, record) => {
    const flag = record.get('status') === 'ENABLED';
    const actions = [
      {
        key: 'publish',
        name: flag ? intl.formatMessage({ id: 'iam.interface.register.detail.publish.stop', defaultMessage: '停止发布' }) : intl.formatMessage({ id: 'zknow.common.button.publish', defaultMessage: '发布' }),
        icon: flag ? 'reduce-one' : 'check-one',
        onClick: () => handleItfPublish(record),
      },
      {
        key: 'request',
        name: intl.formatMessage({ id: 'iam.interface.register.mapping.request', defaultMessage: '请求字段映射' }),
        icon: 'text',
        onClick: () => openMapView(record, 'REQUEST'),
      },
      {
        key: 'response',
        name: intl.formatMessage({ id: 'iam.interface.register.mapping.response', defaultMessage: '响应字段映射' }),
        icon: 'TextRecognition',
        onClick: () => openMapView(record, 'RESPONSE'),
      },
      {
        key: 'operation',
        name: intl.formatMessage({ id: 'iam.interface.register.operation.config', defaultMessage: '运维配置' }),
        icon: 'config',
        onClick: () => openOperationModal(record),
      },
    ];
    return <TableHoverAction
      record={record}
      actions={actions}
    />;
  };

  return (
    <TabPage>
      <Header backPath={`/iam/${tenantId === '0' ? 'site/' : ''}interface_register${history.location?.search}`}>
        <h1>{intl.formatMessage({ id: 'iam.interface.register.detail.title', defaultMessage: '接口注册详情' })}</h1>
        <div>
          {renderButtons()}
        </div>
      </Header>
      <Content className={prefixCls} style={{ paddingBottom: 0 }}>
        <Form labelWidth="auto" disabled={!isEdit} columns={2} labelLayout="horizontal" dataSet={detailDataSet}>
          <TextField name="serverName" autoFocus />
          <TextField name="serverCode" disabled />
          <Select name="serviceType" />
          <Select name="serviceCategory" disabled />
          {detailDataSet?.current?.get('serviceCategory') !== 'INTERNAL' && <Select name="protocol" />}
          <TextField
            name="domainUrl"
            addonBefore={detailDataSet?.current?.get('protocol')}
          />
          <SelectBox name="publicFlag" disabled />
          <SelectBox name="enabledFlag" />
          <TextField name="requestContentType" />
          <TextField name="responseContentType" />
        </Form>
        <Table
          dataSet={interfaceDataSet}
          labelLayout="float"
          pristine
          autoHeight
          buttons={buttons}
          queryBarProps={{
            fuzzyQuery: false,
          }}
        >
          <Column name="interfaceName" renderer={renderName} />
          <Column name="interfaceCode" />
          <Column name="interfaceUrl" tooltip="overflow" />
          <Column name="publishUrl" tooltip="overflow" />
          <Column name="formatVersion" />
          <Column name="status" renderer={renderPublishState} />
          <Column
            width={50}
            renderer={({ dataSet, record }) => renderAction(dataSet, record)}
          />
        </Table>
      </Content>
    </TabPage>
  );
});
