import React from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, Select, SelectBox } from 'choerodon-ui/pro';

export default observer((props) => {
  const { modal, dataSet, intl } = props;
  const record = dataSet?.current;

  const handleOk = async () => {
    try {
      if (await dataSet.validate()) {
        await dataSet.submit();
        dataSet.query();
        return true;
      } else {
        return false;
      }
    } catch (err) {
      return false;
    }
  };

  modal.handleOk(() => handleOk());
  modal.handleCancel(() => dataSet.reset());

  return (
    <Form
      dataSet={dataSet}
      labelWidth="auto"
    >
      <TextField name="serverName" autoFocus />
      <TextField name="serverCode" restrict="_|A-Z|a-z|0-9" />
      <Select name="serviceCategory" />
      {record?.get('serviceCategory') !== 'INTERNAL' && <Select name="protocol" />}
      <TextField name="domainUrl" addonBefore={record?.get('protocol')} />
      <Select name="serviceType" />
      <SelectBox name="publicFlag" />
    </Form>
  );
});
