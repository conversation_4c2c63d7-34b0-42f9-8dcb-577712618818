import React, { useContext, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import { Content, TabPage, axios } from '@yqcloud/apps-master';
import { Modal, Table, message } from 'choerodon-ui/pro';
import { TableHoverAction, TableStatus, ClickText, Button, StatusTag } from '@zknow/components';
import FormView from './FormView';
import Store from './stores';

import '../index.less';

const modalKey = Modal.key();
const { Column } = Table;
function MainView() {
  const context = useContext(Store);
  const {
    intl,
    prefixCls,
    interfaceDataSet,
    statusMap,
    history,
    tenantId,
    tenantCode,
  } = context;

  const handleRemove = async (record) => {
    try {
      const res = await axios.delete(`/hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}interface-servers`, record);
      if (!res?.failed) {
        interfaceDataSet.query();
      } else {
        message.error(res?.message);
      }
    } catch (e) {
      return false;
    }
  };

  const handleStop = async (record, flag) => {
    const url = `/hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}interface-servers`;
    const interfaceServerId = record.get('interfaceServerId');
    try {
      const data = await axios.get(`${url}/${interfaceServerId}?interfaceServerId=${interfaceServerId}`);
      data.enabledFlag = flag ? 0 : 1;
      const res = await axios.put(url, data);
      if (data?.failed || res.failed) {
        message.error(data?.message || res?.message);
      } else {
        interfaceDataSet.query();
        return true;
      }
    } catch (e) {
      return false;
    }
  };

  const renderAction = (dataSet, record) => {
    const stop = record.get('status') === 'PUBLISHED'
      ? []
      : [{
        key: 'stop',
        name: record.get('enabledFlag') === 1 ? intl.formatMessage({ id: 'zknow.common.button.deactivated', defaultMessage: '停用' }) : intl.formatMessage({ id: 'zknow.common.button.enable', defaultMessage: '启用' }),
        icon: record?.get('enabledFlag') === 1 ? 'reduce-one' : 'check-one',
        onClick: () => handleStop(record, record?.get('enabledFlag') === 1),
      }];
    const actions = [
      {
        key: 'delete',
        name: intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' }),
        icon: 'delete',
        onClick: () => handleRemove(record),
      },
      ...stop,
    ];
    return <TableHoverAction record={record} actions={actions} />;
  };

  const handleConfirm = async () => {
    try {
      const res = await interfaceDataSet.validate();
      if (res && await interfaceDataSet.submit()) {
        interfaceDataSet.query();
        return true;
      } else {
        return false;
      }
    } catch (err) {
      return false;
    }
  };

  const openModal = useCallback((type, record) => {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.interface.register.add', defaultMessage: '新建接口详情' }),
      children: <FormView dataSet={interfaceDataSet} intl={intl} />,
      // onOk: handleConfirm,
      key: modalKey,
      drawer: type === 'modify',
      destroyOnClose: true,
    });
  }, [interfaceDataSet?.current?.get('serviceCategory')]);

  const handleCreate = () => {
    interfaceDataSet.create({
      tenantId,
      invokeVerifySignFlag: false,
      enabledFlag: 1,
      namespace: tenantId === '0' ? 'HZERO' : tenantCode,
      authType: 'NONE',
    });
    openModal('create', interfaceDataSet?.current);
  };

  const buttons = [
    <Button funcType="raised" color="primary" icon="Plus" onClick={() => handleCreate()}>
      {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
    </Button>,
  ];

  const renderPublishState = ({ value }) => {
    const { name, color } = statusMap[value] || { name: '' };
    return <StatusTag name={name} color={color}>{name}</StatusTag>;
  };

  const renderStatus = ({ record }) => {
    const flag = record.get('enabledFlag');
    return (
      <TableStatus
        status={flag === 1}
        enabledText={intl.formatMessage({ id: 'zknow.common.button.enable', defaultMessage: '启用' })}
        disabledText={intl.formatMessage({ id: 'zknow.common.button.deactivated', defaultMessage: '停用' })}
      />
    );
  };

  const renderName = ({ record }) => {
    return (
      <ClickText
        record={record}
        history={history}
        path={`/iam/${tenantId === '0' ? 'site/' : ''}interface_register/detail/${record.get('interfaceServerId')}`}
        valueField="serverName"
      />
    );
  };

  return (
    <TabPage>
      <Content style={{ padding: 0 }}>
        <Table
          labelLayout="float"
          dataSet={interfaceDataSet}
          className={`${prefixCls}-table`}
          autoHeight
          pristine
          buttons={buttons}
          queryBarProps={{
            fuzzyQuery: false,
          }}
        >
          <Column name="serverName" renderer={renderName} />
          <Column name="serverCode" />
          <Column name="tenantName" />
          <Column name="serviceCategory" />
          <Column name="serviceType" />
          <Column name="status" renderer={renderPublishState} />
          <Column name="enabledFlag" align="left" renderer={renderStatus} />
          <Column
            width={50}
            renderer={({ dataSet, record }) => renderAction(dataSet, record)}
          />
        </Table>
      </Content>
    </TabPage>
  );
}

export default observer(MainView);
