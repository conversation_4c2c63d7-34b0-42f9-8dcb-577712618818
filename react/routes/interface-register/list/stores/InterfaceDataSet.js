import { DataSet } from 'choerodon-ui/pro';
import { getQueryParams } from '@zknow/utils';
import { getItfSearch } from '@/utils';

export default ({ intl, tenantId }) => {
  const url = `/hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}interface-servers`;

  const interfaceOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.interface.model.interfaceType.restful' }), value: 'REST' },
      // { meaning: intl.formatMessage({ id: 'SOAP' }), value: 'SOAP' },
    ],
  });

  const categoryOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.interface.model.category.outer', defaultMessage: '外部接口' }), value: 'EXTERNAL' },
      { meaning: intl.formatMessage({ id: 'iam.interface.model.category.inner', defaultMessage: '内部接口' }), value: 'INTERNAL' },
    ],
  });

  const publicOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.desc.public', defaultMessage: '公开' }), value: true },
      { meaning: intl.formatMessage({ id: 'zknow.common.desc.private', defaultMessage: '私有' }), value: false },
    ],
  });

  const httpOptions = new DataSet({
    data: [
      { meaning: 'http', value: 'http://' },
      { meaning: 'https', value: 'https://' },
    ],
  });

  function getCreateData(data) {
    const domainUrl = data.protocol ? data.protocol + data.domainUrl : data.domainUrl;
    return {
      ...data,
      domainUrl,
      invokeVerifySignFlag: false,
      enabledFlag: 1,
      authType: 'NONE',
    };
  }

  return {
    autoQuery: true,
    selection: false,
    autoLocateFirst: false,
    transport: {
      read: ({ data }) => ({
        url: `${url}?authFlag=2`,
        method: 'get',
        data: getQueryParams(data),
      }),
      create: ({ data: [data] }) => ({
        url: `${url}`,
        method: 'post',
        data: getCreateData(data),
      }),
      update: ({ data: [data] }) => {
        return ({
          url: `${url}`,
          method: 'put',
          data,
        });
      },
    },
    fields: [
      { name: 'serverName', type: 'string', required: true, label: intl.formatMessage({ id: 'iam.interface.model.serverName', defaultMessage: '服务名称' }) },
      { name: 'serverCode', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }), required: true, format: 'uppercase' },
      { name: 'serviceCategory', type: 'string', required: true, label: intl.formatMessage({ id: 'iam.interface.model.category', defaultMessage: '接口分类' }), options: categoryOptions },
      { name: 'serviceType', type: 'string', required: true, label: intl.formatMessage({ id: 'iam.interface.model.interfaceType', defaultMessage: '接口类型' }), options: interfaceOptions },
      { name: 'status', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.status', defaultMessage: '发布状态' }) },
      { name: 'enabledFlag', type: 'number', label: intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' }) },
      { name: 'protocol', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.protocol', defaultMessage: '服务协议' }), options: httpOptions, dynamicProps: { required: ({ record }) => record.get('serviceCategory') === 'EXTERNAL' } },
      { name: 'domainUrl', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.interfaceUrl', defaultMessage: '接口地址' }), required: true, help: intl.formatMessage({ id: 'iam.interface.model.interfaceUrl.help', defaultMessage: '内部接口地址为燕千云内部服务，外部接口为实际接口地址，支持http/https。' }) },
      { name: 'publicFlag', type: 'boolean', label: intl.formatMessage({ id: 'iam.interface.model.publicFlag', defaultMessage: '是否公开' }), defaultValue: false, required: true, options: publicOptions },
      { name: 'tenantName', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.tenant', defaultMessage: '租户' }) },
    ],
    queryFields: [
      { name: 'serverName', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.serverName', defaultMessage: '服务名称' }) },
      { name: 'serverCode', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }) },
      { name: 'serviceType', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.interfaceType', defaultMessage: '接口类型' }), options: interfaceOptions },
    ],
    events: {
      query: ({ params, data }) => {
        getItfSearch(params);
      },
      update: ({ dataSet, record, name, value, oldValue }) => {
        if (name === 'serviceCategory' && value === 'INTERNAL') {
          record.set('protocol', '');
        }
      },
    },
  };
};
