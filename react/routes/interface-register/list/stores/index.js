import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { color as colorUtils, formatterCollections } from '@zknow/utils';
import InterfaceDataSet from './InterfaceDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: ['iam.interface', 'iam.interfaceRegister'] })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      AppState: { getUserInfo },
    } = props;

    const prefixCls = 'iam-interface';
    const tenantCode = getUserInfo.tenantNum;
    const statusMap = {
      PUBLISHED: { name: intl.formatMessage({ id: 'iam.interfaceRegister.desc.published', defaultMessage: '已发布' }), color: colorUtils?.getColorCorrespondingValue('green') },
      OFFLINE: { name: intl.formatMessage({ id: 'iam.interfaceRegister.desc.unpublished', defaultMessage: '未发布' }), color: colorUtils?.getColorCorrespondingValue('orange') },
      NEW: { name: intl.formatMessage({ id: 'iam.interfaceRegister.desc.draft', defaultMessage: '草稿' }), color: colorUtils?.getColorCorrespondingValue('purple') },
    };

    const interfaceDataSet = useMemo(() => new DataSet(InterfaceDataSet({ intl, tenantId })));

    const value = {
      ...props,
      prefixCls,
      interfaceDataSet,
      tenantId,
      statusMap,
      tenantCode,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
