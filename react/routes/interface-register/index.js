import React from 'react';
import { Route, Switch } from 'react-router-dom';
import { nomatch } from '@yqcloud/apps-master';
import List from './list';
import Detail from './detail';

export default (props) => {
  const { match } = props;

  return (
    <Switch>
      <Route path={`${match.url}/detail/:id`} component={Detail} />
      <Route path={`${match.url}`} component={List} />
      <Route path="*" component={nomatch} />
    </Switch>
  );
};
