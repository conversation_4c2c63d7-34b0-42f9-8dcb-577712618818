import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { axios } from '@yqcloud/apps-master';
import { ExternalComponent, Icon, YqTable } from '@zknow/components';
import { Button, Table, Tooltip, message } from 'choerodon-ui/pro';
// import { Table } from 'choerodon-ui';
const { Column } = Table;
const defaultCodeStyle = {
  'max-width': '1.8rem',
  overflow: 'hidden',
  'text-overflow': 'ellipsis',
  'white-space': 'nowrap',
};

export default observer((props) => {
  const {
    modal, dataSet, type, curDataSet, parentRoleId, front,
    context: { intl, prefixCls, intlPrefix, tenantId, commonDataSet },
  } = props;

  modal.handleOk(async () => {
    try {
      if (front === 'sub-roles') {
        const postData = dataSet.selected.map((v) => {
          return {
            parentRoleId,
            roleId: v.get('id'),
            _status: 'create',
          };
        });
        const resp = await axios.post('iam/yqc/role_rels', JSON.stringify(postData));
        if (resp) {
          curDataSet.query();
        } else {
          return false;
        }
      } else if (front === 'interface') {
        const postData = dataSet.selected.map((v) => v.toData());
        const resp = await axios.post(`hitf/v1/client-roles/${parentRoleId}`, JSON.stringify(postData));
        if (resp) {
          curDataSet.query();
        } else {
          return false;
        }
      } else {
        const postData = [];
        dataSet.selected.forEach((v) => {
          postData.push(v.get('id'));
        });
        const resp = await axios.post(`iam/yqc/role_permissions/data_rule/${parentRoleId}`, JSON.stringify(postData));
        if (resp) {
          curDataSet.query();
        } else {
          return false;
        }
      }
    } catch (err) {
      message.error(err?.message);
      return false;
    }
  });

  // eslint-disable-next-line no-shadow
  const rendererCode = ({ record }) => {
    return (
      <Tooltip placement="top" title={record.get('code')}>
        <div className="menu-code" style={{ ...defaultCodeStyle }}>{record.get('code')}</div>
      </Tooltip>
    );
  };

  if (front === 'sub-roles') {
    return (
      <div className="modal-table">
        <Table
          labelLayout="float"
          style={{ maxHeight: 500 }}
          dataSet={dataSet}
          autoHeight
          placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
          className={`${intlPrefix}-table`}
          onRow={({ record }) => ({
            onClick: (e) => {
              if (record.isSelected) {
                dataSet.unSelect(record);
              } else {
                dataSet.select(record);
              }
            },
          })}
          queryBarProps={{
            title: intl.formatMessage({ id: 'iam.siteRole.desc.role.child.roles.add', defaultMessage: '添加子角色' }),
            fuzzyQuery: false,
            inlineSearch: false,
            simpleMode: true,
            queryFieldsStyle: {
              name: {
                width: 140,
              },
              code: {
                width: 140,
              },
              description: {
                width: 200,
              },
            },
          }}
        >
          <Column name="name" />
          <Column name="code" />
          <Column name="description" />
        </Table>
      </div>
    );
  } else if (front === 'interface') {
    return (
      <div className="modal-table">
        <Table
          labelLayout="float"
          dataSet={dataSet}
          autoHeight
          className={`${intlPrefix}-table`}
          onRow={({ record }) => ({
            onClick: (e) => {
              if (record.isSelected) {
                dataSet.unSelect(record);
              } else {
                dataSet.select(record);
              }
            },
          })}
          queryBarProps={{
            title: '',
            fuzzyQuery: false,
            inlineSearch: false,
            simpleMode: true,
            queryFieldsStyle: {
              interfaceName: {
                width: 140,
              },
              interfaceCode: {
                width: 140,
              },
              serverName: {
                width: 140,
              },
            },
          }}
        >
          <Column name="interfaceName" />
          <Column name="interfaceCode" />
          <Column name="serverName" />
          <Column name="serverCode" />
          <Column name="tenantName" />
        </Table>
      </div>
    );
  }
  return (
    <div className="modal-table">
      <Table
        labelLayout="float"
        style={{ maxHeight: 500 }}
        dataSet={dataSet}
        autoHeight
        placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
        className={`${intlPrefix}-table`}
        onRow={({ record }) => ({
          onClick: (e) => {
            if (record.isSelected) {
              dataSet.unSelect(record);
            } else {
              dataSet.select(record);
            }
          },
        })}
        queryBarProps={{
          fuzzyQuery: false,
          inlineSearch: false,
          simpleMode: true,
          queryFieldsStyle: {
            businessObject: {
              width: 140,
            },
            code: {
              width: 140,
            },
            description: {
              width: 200,
            },
          },
        }}
      >
        <Column name="description" />
        <Column name="code" width={150} />
        <Column name="businessObject" width={100} />
        <Column name="operation" width={80} />
        <Column name="type" width={80} />
      </Table>
    </div>
  );
});
