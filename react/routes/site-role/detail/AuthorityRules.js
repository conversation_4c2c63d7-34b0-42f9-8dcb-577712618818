import React, { useContext, useRef, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Table, Lov, message, Modal } from 'choerodon-ui/pro';
import { axios } from '@yqcloud/apps-master';
import { Button, TableHoverAction, YqTable } from '@zknow/components';
import FormView from './FormView';
import Store from '../stores';

const { Column } = Table;
const modalKey = Modal.key();
const AccountList = (props) => {
  const { editable, roleId, handleRefresh, currentRole } = props;
  const context = useContext(Store);
  const { authorityRolesDataSet, intl, intlPrefix, transDataSetRule } = context;
  const mdModalStyle = useMemo(() => ({ width: 800 }), []);

  function handleCreate() {
    openModal('create');
    transDataSetRule.setQueryParameter('front', 'rules');
    transDataSetRule.setQueryParameter('id', roleId);
    transDataSetRule.query().then(() => {
    });
  }

  const createButton = () => (
    <Button icon="plus" funcType="raised" color="primary" onClick={handleCreate} disabled={!editable}>
      {intl.formatMessage({ id: 'zknow.common.button.append', defaultMessage: '添加' })}
    </Button>
  );

  function openModal(type) {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.siteRole.desc.role.rules.add', defaultMessage: '添加数据权限' }),
      children: (
        <FormView
          type={type}
          parentRoleId={roleId}
          context={context}
          dataSet={transDataSetRule}
          curDataSet={authorityRolesDataSet}
        />
      ),
      key: modalKey,
      drawer: false,
      style: mdModalStyle,
      destroyOnClose: true,
    });
  }

  const buttons = useMemo(() => {
    if (currentRole?.get('builtIn')) {
      return [];
    } else {
      return createButton();
    }
  }, [editable, currentRole]);

  function handleRemove(record) {
    const urlPrefix = `/iam/yqc/role_permissions/data_rule/${roleId}?permission_id=${record.get('id')}`;
    axios.delete(`${urlPrefix}`).then(res => {
      if (res && res.failed) {
        message.error(res.message);
      } else {
        handleRefresh();
      }
    }).catch(e => {
      message.error(e.message);
    });
  }

  function renderAction({ record }) {
    const actions = [];
    if (!currentRole?.get('builtIn')) {
      actions.push({
        name: intl.formatMessage({ id: 'zknow.common.button.remove', defaultMessage: '移除' }),
        icon: 'delete',
        onClick: () => handleRemove(record),
      });
    }
    return <TableHoverAction record={record} actions={actions} />;
  }

  return (
    <div>
      <Table
        labelLayout="float"
        dataSet={authorityRolesDataSet}
        placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
        buttons={[buttons]}
        queryBarProps={{
          title: intl.formatMessage({ id: 'iam.siteRole.desc.role.authority.rules', defaultMessage: '数据权限' }),
        }}
      >
        <Column name="description" width={280} />
        <Column name="code" width={200} />
        <Column name="businessObject" />
        <Column name="operation" />
        <Column name="type" />
        <Column width={1} renderer={renderAction} />
      </Table>
    </div>
  );
};

export default observer(AccountList);
