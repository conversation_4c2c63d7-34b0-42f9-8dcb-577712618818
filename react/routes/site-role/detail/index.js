import React, { useState, useContext, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, TextArea, Modal, Select, message, CheckBox } from 'choerodon-ui/pro';
import { TabPage, Header, Content, axios } from '@yqcloud/apps-master';
import { Button } from '@zknow/components';
import { Tabs } from 'choerodon-ui';
import ChildRoles from './ChildRoles';
import AuthorityRules from './AuthorityRules';
import MenuTreeList from './MenuTreeList';
import Interface from './Interface';
import Store from '../stores';

import './index.less';

const { TabPane } = Tabs;

const DetailForm = (props) => {
  const { match: { params: { id } } } = props;
  const [editable, setEditable] = useState(false);
  const [init, setInit] = useState(false);
  const { roleDetailDataSet, loadRolesInfo, intl, intlPrefix, prefixCls, history, menuDataSet } = useContext(Store);

  const search = history.location?.search;

  const currentRole = roleDetailDataSet.current;

  function refresh() {
    roleDetailDataSet.setQueryParameter('id', id);
    roleDetailDataSet.query().then(() => {
      roleDetailDataSet.current = roleDetailDataSet.get(0);
    });
  }

  useEffect(() => {
    if (id) {
      refresh();
    }
  }, [id]);

  function toggleEdit() {
    setEditable(!editable);
  }

  async function handleSave() {
    const permissions = [];
    menuDataSet.forEach(v => {
      if (v.get('permissions')) {
        v.get('permissions').forEach(i => permissions.push(i));
      }
      permissions.push(v.toData());
    });
    const permissionsIds = permissions.filter(v => v.granted).map(v => {
      if (v.permissionId) {
        return v.permissionId;
      } else if (v.menuPermissionId) {
        return v.menuPermissionId;
      } else {
        return v.id;
      }
    });
    const originChildren = roleDetailDataSet.children;
    // 不提交级联数据
    roleDetailDataSet.children = {};
    const [submitRes, putRes] = await Promise.all([
      roleDetailDataSet.submit(),
      axios.put(`iam/yqc/v1/roles/${id}/permission-sets/assign`, permissionsIds),
    ]);
    roleDetailDataSet.children = originChildren;
    try {
      if (!putRes.failed) {
        if (!submitRes) {
          message.success(intl.formatMessage({ id: 'zknow.common.success.save', defaultMessage: '保存成功' }));
          menuDataSet.query();
        }
        if (submitRes !== undefined) {
          refresh();
        }
        toggleEdit();
      } else {
        message.error(putRes.message || intl.formatMessage({ id: 'iam.renderer.desc.save.failed', defaultMessage: '保存失败' }));
      }
    } catch (e) {
      message.error(e.message);
    }
  }

  function handleCancel() {
    roleDetailDataSet.reset();
    toggleEdit();
  }

  function handleChangeEnabledFlag(flag) {
    axios.put(flag ? '/iam/yqc/v1/roles/enable' : '/iam/yqc/v1/roles/disable', [currentRole.toData()]).then(res => {
      if (res && res.failed) {
        message.error(res.message);
      } else {
        message.success(intl.formatMessage({ id: 'zknow.common.success.save', defaultMessage: '保存成功' }));
        loadRolesInfo();
      }
    }).catch(e => {
      message.error(e.message);
    });
  }

  const actionButtons = () => {
    if (currentRole?.get('enabled')) {
      return editable ? (
        <div className={`${prefixCls}-form-buttons`}>
          <Button funcType="raised" onClick={handleSave} color="primary">{intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}</Button>
          <Button funcType="raised" onClick={handleCancel}>{intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}</Button>
        </div>
      ) : (
        <div className={`${prefixCls}-form-buttons`}>
          <Button funcType="raised" onClick={toggleEdit} color="default" icon="write">{intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}</Button>
        </div>
      );
    } else {
      return (
        <div className={`${prefixCls}-form-buttons`}>
          <Button funcType="raised" onClick={() => handleChangeEnabledFlag(true)} icon="icon-read">{intl.formatMessage({ id: 'zknow.common.action.make.valid', defaultMessage: '生效' })}</Button>
        </div>
      );
    }
  };

  return (
    <TabPage>
      <Header backPath={`/iam/site/role${search}`} dataSet={roleDetailDataSet} onRefresh={refresh}>
        <h1>{intl.formatMessage({ id: 'iam.siteRole.desc.role.detail', defaultMessage: '角色详情' })}</h1>
        <div>{ !currentRole?.get('builtIn') && actionButtons() }
        </div>
      </Header>
      <Content style={{ padding: '0.18rem 0.16rem' }}>
        <Form disabled={!editable} dataSet={roleDetailDataSet} labelLayout="horizontal" labelAlign="right" columns={2} header={intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' })}>
          <TextField name="name" />
          <TextField name="code" />
          <Select name="level" />
          <CheckBox name="allocableFlag" />
          <TextArea name="description" newLine colSpan={2} rows={1} autoSize={{ minRows: 1, maxRows: 4 }} resize="height" />
        </Form>
        <Tabs defaultActiveKey="1" type="card" className={`${prefixCls}-detail-tabs`} style={{ marginTop: '0.24rem' }} onChange={() => { setInit(false); Modal.destroyAll(); }}>
          <TabPane tab={intl.formatMessage({ id: 'iam.siteRole.desc.role.assign.menu', defaultMessage: '菜单权限' })} key="1" forceRender={init}>
            <MenuTreeList editable={editable} currentRole={currentRole} />
          </TabPane>
          <TabPane tab={intl.formatMessage({ id: 'iam.siteRole.desc.role.child.role', defaultMessage: '子角色' })} key="2">
            <ChildRoles editable={false} roleId={id} handleRefresh={refresh} currentRole={currentRole} />
          </TabPane>
          <TabPane tab={intl.formatMessage({ id: 'iam.siteRole.desc.role.authority.rules', defaultMessage: '数据权限' })} key="3">
            <AuthorityRules editable roleId={id} handleRefresh={refresh} currentRole={currentRole} />
          </TabPane>
          <TabPane tab={intl.formatMessage({ id: 'iam.siteRole.desc.role.interface.authorization', defaultMessage: '接口权限' })} key="4">
            <Interface editable roleId={id} handleRefresh={refresh} currentRole={currentRole} />
          </TabPane>
        </Tabs>
      </Content>
    </TabPage>
  );
};

export default observer(DetailForm);
