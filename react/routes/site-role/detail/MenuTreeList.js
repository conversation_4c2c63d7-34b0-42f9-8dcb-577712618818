import React, { useContext, useEffect } from 'react'; import { observer } from 'mobx-react-lite';
import { Table, Modal, DataSet } from 'choerodon-ui/pro';
import { ClickText } from '@zknow/components';
import PermissionList from './PermissionList';
import PermissionDataSet from '../stores/PermissionDataSet';
import Store from '../stores';
import './index.less';

const { Column } = Table;
const modalKey = Modal.key();

const MenuTreeList = (props) => {
  const { editable } = props;
  const { menuDataSet, intl, intlPrefix, roleDetailDataSet } = useContext(Store);
  const permissions = {};

  const optionMapData = {
    top: intl.formatMessage({ id: 'iam.common.desc.top.menu', defaultMessage: '顶层菜单' }),
    filter: intl.formatMessage({ id: 'iam.common.desc.filter', defaultMessage: '筛选器' }),
    filter_item: intl.formatMessage({ id: 'iam.common.desc.filter.item', defaultMessage: '筛选项' }),
    menu: intl.formatMessage({ id: 'zknow.common.model.menu', defaultMessage: '菜单' }),
    link: intl.formatMessage({ id: 'iam.common.desc.link', defaultMessage: '链接' }),
    solution: intl.formatMessage({ id: 'iam.siteRole.desc.role.menu.solution', defaultMessage: '解决方案' }),
    dir: intl.formatMessage({ id: 'iam.common.model.menu.dir', defaultMessage: '目录' }),
  };

  const currentRole = roleDetailDataSet.current;
  useEffect(() => {
    if (menuDataSet.status === 'ready') {
      menuDataSet?.filter(record => record.get('type') !== 'root').forEach(record => {
        // 过滤权限类型为page ,page类型的默认
        record.get('permissions') && record.set('allPermission', record.get('permissions')?.filter(p => p.type !== 'page').length);
      });
    }
  }, [menuDataSet.status]);

  useEffect(() => {
    if (menuDataSet.status === 'ready') {
      menuDataSet.forEach(record => {
        // 所有的permissions
        const assigendIds = record.get('permissions')?.filter(o => o.granted && o.type !== 'page');
        record.set('assignedPermission', assigendIds?.length || 0);
      });
    }
  }, [menuDataSet.status]);

  function handleClickName(record) {
    if (!permissions[record.get('id')]) {
      const permissionDataSet = new DataSet(PermissionDataSet({ intl, intlPrefix, menuDataSet }));
      // 过滤 权限 type为 page 类型的
      const canSelectedPermission = record.get('permissions')?.filter((v) => v.type !== 'page');
      permissionDataSet.loadData(canSelectedPermission);
      permissionDataSet.forEach(r => {
        if (r.get('granted')) {
          permissionDataSet.select(r);
        }
      });
      permissions[record.get('id')] = permissionDataSet;
    }
    Modal.open({
      title: null,
      drawer: true,
      style: { width: 800 },
      children: (
        <PermissionList
          menu={record}
          dataSet={permissions[record.get('id')]}
          editable={editable}
          currentRole={currentRole}
        />
      ),
      key: modalKey,
      footer: () => [],
      destroyOnClose: true,
    });
  }

  const renderName = ({ record, value }) => {
    const clickable = record.get('type') !== 'root';
    return clickable ? (
      <ClickText record={record} onClick={() => handleClickName(record)} valueField="name" />
    ) : value;
  };

  function renderPermission({ record }) {
    if (record.get('type') === 'root' || record.get('permissionId') || !record.get('allPermission')) {
      return '-';
    } else {
      return `(${record.get('assignedPermission')}/${record.get('allPermission')})`;
    }
  }

  function renderType({ record }) {
    return <span>{optionMapData[record.get('type')] || record.get('type')}</span>;
  }

  return (
    <Table
      dataSet={menuDataSet}
      className="menu-tree-table"
      queryBar="none"
      mode="tree"
    >
      <Column name="name" renderer={renderName} />
      <Column name="granted" editor={editable} />
      <Column name="type" renderer={renderType} />
      <Column tooltip="overflow" name="permission" renderer={renderPermission} />
      <Column tooltip="overflow" name="path" />
    </Table>
  );
};

export default observer(MenuTreeList);
