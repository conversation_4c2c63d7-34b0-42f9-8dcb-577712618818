import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Table } from 'choerodon-ui/pro';

const { Column } = Table;

const PermissionList = (props) => {
  const { menu, dataSet, editable } = props;

  useEffect(() => {
    dataSet.forEach(record => {
      const casualVar = record.isSelected;
      record.selectable = editable;
      record.isSelected = casualVar;
    });
  }, [editable]);

  useEffect(() => {
    menu.set('assignedPermission', dataSet.selected.length);
  }, [dataSet.selected.length]);

  return (
    <Table
      dataSet={dataSet}
      queryBar="none"
      pristine
      alwaysShowRowBox
      disabled
      className="permission-table-content"
    >
      <Column name="description" />
      <Column tooltip="overflow" name="code" />
      <Column name="method" width={100} />
      <Column tooltip="overflow" name="path" />
    </Table>
  );
};

export default observer(PermissionList);
