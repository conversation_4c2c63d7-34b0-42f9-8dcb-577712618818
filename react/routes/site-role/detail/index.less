@import '~choerodon-ui/lib/style/themes/default';

.flex-center {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.iam-role {
  &-header {
    .flex-center();
  }

  &-back-button {
    display: flex;
    align-items: center;
  }

  &-detail-tabs {
    margin-top: 24rem;

    .c7n-tabs-content {
      padding-bottom: 16px;
    }

    .c7n-tabs-nav {
      margin-left: 0 !important;
    }
  }
}

.menu-tree-table {
  .toolbar-icon {
    display: none;
  }
}

.permission-table-content {
  th:first-child {
    padding-left: 0 !important;
  }
}

.modal-table {
  margin: -24px;

  .c7n-pro-table-body {
    height: calc(100vh - 500px);
  }
}
