import React, { useContext, useRef, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Table, Lov, message } from 'choerodon-ui/pro';
import { axios } from '@yqcloud/apps-master';
import { Button, TableHoverAction, YqTable } from '@zknow/components';
import EnabledFlag from '@/components/enabled-flag';
import Store from '../stores';

const { Column } = Table;

const AccountList = (props) => {
  const { editable, roleId, handleRefresh, currentRole } = props;
  const { accountDataSet, lovDataSet, intl, intlPrefix } = useContext(Store);

  const lovRef = useRef();

  function handleCreate() {
    lovRef.current?.onClick();
  }

  const createButton = () => (
    <Button funcType="raised" color="primary" onClick={handleCreate} disabled={!editable}>
      {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
    </Button>
  );

  const buttons = useMemo(() => [
    createButton(),
  ], [editable]);

  function handleRemove(record) {
    const urlPrefix = '/iam/yqc/v1/member-roles';
    const memberRoleDTO = {
      roleId,
      memberId: record.get('id'),
      memberType: 'user',
    };
    axios.put(`${urlPrefix}/batch-delete`, [memberRoleDTO]).then(res => {
      if (res && res.failed) {
        message.error(res.message);
      } else {
        handleRefresh();
      }
    }).catch(e => {
      message.error(e.message);
    });
  }

  function renderAction({ record }) {
    const actions = [];
    if (!currentRole?.get('builtIn')) {
      actions.push({
        name: intl.formatMessage({ id: 'zknow.common.button.remove', defaultMessage: '移除' }),
        icon: 'delete',
        onClick: () => handleRemove(record),
      });
    }
    return <TableHoverAction record={record} actions={actions} />;
  }

  return (
    <div>
      <Lov multiple type="button" style={{ display: 'none' }} ref={lovRef} dataSet={lovDataSet} name="account" />
      <YqTable
        labelLayout="float"
        dataSet={accountDataSet}
        pristine
        buttons={buttons}
        placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
      >
        <Column name="description" />
        <Column name="code" />
        <Column name="businessObjectName" />
        <Column name="opertion" />
        <Column name="type" />
        <Column name="condition" />
        <Column name="enabled" renderer={({ value }) => <EnabledFlag enabledFlag={value} />} />
        <Column width={1} renderer={renderAction} />
      </YqTable>
    </div>
  );
};

export default observer(AccountList);
