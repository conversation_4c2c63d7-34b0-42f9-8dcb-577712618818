const RoleLevelDataSet = ({ intl, intlPrefix }) => ({
  fields: [
    { name: 'text', type: 'string' },
    { name: 'value', type: 'string' },
  ],
  data: [
    { text: intl.formatMessage({ id: 'iam.siteRole.model.role.level.site', defaultMessage: '平台层' }), value: 'site' },
    { text: intl.formatMessage({ id: 'iam.siteRole.model.role.level.organization', defaultMessage: '租户层' }), value: 'organization' },
  ],
});

const MenuTypeDataSet = ({ intl, intlPrefix }) => ({
  fields: [
    { name: 'text', type: 'string' },
    { name: 'value', type: 'string' },
  ],
  data: [
    { text: intl.formatMessage({ id: 'iam.siteRole.model.role.menu.root', defaultMessage: '目录' }), value: 'root' },
    { text: intl.formatMessage({ id: 'zknow.common.model.menu', defaultMessage: '菜单' }), value: 'menu' },
  ],
});

export { RoleLevelDataSet, MenuTypeDataSet };
