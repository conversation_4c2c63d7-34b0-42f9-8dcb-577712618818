import qs from 'qs';
import { getQueryParams } from '@zknow/utils';
import { DataSet } from 'choerodon-ui/pro';

const ChildRolesDataSet = ({ intl, intlPrefix, operationMap, typeMap, source }) => {
  const rolesPrefix = '/iam/yqc/v1/roles/sub_role';
  const rulesPrefix = '/iam/yqc/role_permissions/data_rule/add';
  const nameLabel = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const codeLabel = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const descriptionLabel = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const objectLabel = intl.formatMessage({ id: 'zknow.common.model.businessObject', defaultMessage: '业务对象' });
  const operationLabel = intl.formatMessage({ id: 'zknow.common.button.action', defaultMessage: '操作' });
  const typeLabel = intl.formatMessage({ id: 'zknow.common.model.type', defaultMessage: '类型' });

  const operationMapDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.condition.isUpdate', defaultMessage: '更新' }), value: 'update' },
      { meaning: intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' }), value: 'insert' },
      { meaning: intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' }), value: 'delete' },
      { meaning: intl.formatMessage({ id: 'zknow.common.button.search', defaultMessage: '查询' }), value: 'select' },
      { meaning: intl.formatMessage({ id: 'iam.siteRole.model.role.other', defaultMessage: '其他' }), value: 'other' },
    ],
  });
  const typeMapDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.siteRole.model.role.record', defaultMessage: '记录' }), value: 'record' },
      { meaning: intl.formatMessage({ id: 'zknow.common.model.field', defaultMessage: '字段' }), value: 'field' },
    ],
  });

  const rolesFields = [
    { name: 'name', type: 'string', label: nameLabel },
    { name: 'code', type: 'string', label: codeLabel },
    { name: 'description', type: 'string', label: descriptionLabel },
  ];
  const rulesFields = [
    { name: 'description', type: 'string', label: descriptionLabel },
    { name: 'code', type: 'string', label: codeLabel },
    { name: 'businessObject', type: 'string', label: objectLabel },
    { name: 'operation', type: 'string', label: operationLabel, options: operationMapDs },
    { name: 'type', type: 'string', label: typeLabel, options: typeMapDs },
  ];

  const queryFieldsArr = source === 'roles' ? rolesFields : rulesFields;
  return {
    autoQuery: false,
    autoLocateFirst: false,
    dataKey: 'content',
    primaryKey: 'userid',
    totalKey: 'totalElements',
    autoQueryAfterSubmit: true,
    paging: true,
    pageSize: 10,
    selection: 'multiple',
    // cacheSelection: true,
    transport: {
      read: ({ data: { id, front }, data }) => ({
        url: front === 'roles' ? `${rolesPrefix}/${id}` : `${rulesPrefix}/${id}`,
        method: 'get',
        paramsSerializer: (params) => {
          delete params.id;
          delete params.front;
          return qs.stringify(params);
        },
        data: getQueryParams(data),
      }),
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'name', type: 'string', label: nameLabel },
      { name: 'code', type: 'string', label: codeLabel },
      { name: 'description', type: 'string', label: descriptionLabel },
      { name: 'businessObjectName', type: 'string', label: objectLabel },
      { name: 'businessObject', type: 'string', label: objectLabel },
      {
        name: 'operation',
        type: 'string',
        label: operationLabel,
        transformResponse: (value) => (value ? operationMap[value] : undefined),
      },
      {
        name: 'type',
        type: 'string',
        label: typeLabel,
        transformResponse: (value) => (value ? typeMap[value] : undefined),
      },
    ],
    queryFields: queryFieldsArr,
  };
};

export default ChildRolesDataSet;
