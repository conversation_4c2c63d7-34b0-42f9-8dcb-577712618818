import qs from 'qs';
import { getQueryParams } from '@zknow/utils';

const AccountDataSet = ({ intl, intlPrefix }) => {
  const urlPrefix = '/iam/yqc/v1/member-roles';

  const realName = intl.formatMessage({ id: 'iam.siteRole.model.role.account.real.name', defaultMessage: '帐号' });
  const phone = intl.formatMessage({ id: 'zknow.common.model.phone', defaultMessage: '手机号' });
  const email = intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' });
  const status = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });

  return {
    autoQuery: false,
    autoLocateFirst: false,
    dataKey: 'content',
    totalKey: 'totalElement',
    autoQueryAfterSubmit: true,
    paging: 'server',
    pageSize: 20,
    selection: false,
    transport: {
      read: ({ data: { id }, data }) => ({
        url: `${urlPrefix}/role-users/${id}`,
        method: 'get',
        data: getQueryParams(data),
        paramsSerializer: (params) => {
          delete params.id;
          return qs.stringify(params);
        },
      }),
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'realName', type: 'string', label: realName, required: true },
      { name: 'phone', type: 'string', label: phone, required: true },
      { name: 'email', type: 'string', label: email },
      { name: 'enabled', type: 'boolean', label: status },
    ],
    queryFields: [
      { name: 'condition', type: 'string' },
    ],
  };
};

export default AccountDataSet;
