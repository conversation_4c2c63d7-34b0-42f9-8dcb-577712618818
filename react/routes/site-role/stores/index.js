import React, { createContext, useMemo } from 'react';
import { axios } from '@yqcloud/apps-master';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import { RoleLevelDataSet, MenuTypeDataSet } from './FieldDataSet';
import AccountDataSet from './AccountDataSet';
import MenuDataSet from './MenuDataSet';
import PermissionDataSet from './PermissionDataSet';
import RoleDataSet from './RoleDataSet';
import ChildRolesDataSet from './ChildRolesDataSet';
import AuthorityRolesDataSet from './AuthorityRulesDataSet';
import TransDataSet from './TransDataSet';
import LovDataSet from './LovDataSet';
import InterfaceDataSet from './InterfaceDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({
  code: 'iam.siteRole',
})(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
    } = props;

    const intlPrefix = 'iam.role';
    const prefixCls = 'iam-role';

    const operationMap = {
      update: intl.formatMessage({ id: 'zknow.common.condition.isUpdate', defaultMessage: '更新' }),
      insert: intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' }),
      delete: intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' }),
      select: intl.formatMessage({ id: 'zknow.common.button.search', defaultMessage: '查询' }),
      other: intl.formatMessage({ id: 'iam.siteRole.model.role.other', defaultMessage: '其他' }),
    };

    const typeMap = {
      api: intl.formatMessage({ id: 'iam.siteRole.model.role.api', defaultMessage: '接口类型' }),
      page: intl.formatMessage({ id: 'iam.siteRole.model.role.page', defaultMessage: '页面' }),
      record: intl.formatMessage({ id: 'iam.siteRole.model.role.record', defaultMessage: '记录' }),
      field: intl.formatMessage({ id: 'zknow.common.model.field', defaultMessage: '字段' }),
    };

    const roleLevelDataSet = useMemo(() => new DataSet(RoleLevelDataSet({ intl, intlPrefix })), []);

    const menuTypeDataSet = useMemo(() => new DataSet(MenuTypeDataSet({ intl, intlPrefix })), []);

    const accountDataSet = useMemo(() => new DataSet(AccountDataSet({ intl, intlPrefix })), []);

    const menuDataSet = useMemo(() => new DataSet(MenuDataSet({ intl, intlPrefix, menuTypeDataSet })), []);

    const roleDataSet = useMemo(() => new DataSet(RoleDataSet({
      intl,
      intlPrefix,
      roleLevelDataSet,
      accountDataSet,
      menuDataSet,
    })), [tenantId, accountDataSet, menuDataSet]);

    const childRolesDataSet = useMemo(() => new DataSet(ChildRolesDataSet({ intl, intlPrefix })), []);
    const authorityRolesDataSet = useMemo(() => new DataSet(AuthorityRolesDataSet({ intl, intlPrefix, operationMap, typeMap })), []);
    const roleDetailDataSet = useMemo(() => new DataSet(RoleDataSet({
      intl,
      intlPrefix,
      roleLevelDataSet,
      childRolesDataSet,
      authorityRolesDataSet,
      menuDataSet,
    })), [tenantId, childRolesDataSet, authorityRolesDataSet, menuDataSet]);
    const transDataSetRole = useMemo(() => new DataSet(TransDataSet({
      intl,
      intlPrefix,
      typeMap,
      operationMap,
      source: 'roles',
    })), [tenantId]);

    const transDataSetRule = useMemo(() => new DataSet(TransDataSet({
      intl,
      intlPrefix,
      typeMap,
      operationMap,
      source: 'rule',
    })), [tenantId]);

    const lovDataSet = useMemo(() => new DataSet(LovDataSet({ roleDetailDataSet, accountDataSet })), []);

    const interfaceDataSet = useMemo(() => new DataSet(InterfaceDataSet({ intl, intlPrefix, tenantId })), []);

    const authDataSet = useMemo(() => new DataSet(InterfaceDataSet({ intl, intlPrefix, tenantId, type: 'auth' })), []);

    async function loadRolesInfo() {
      // const data = await axios.get('/iam/yqc/v1/member-roles/self-roles');
      // data && props.AppState.setRolesInfo(data);
    }

    const value = {
      ...props,
      tenantId,
      intlPrefix,
      prefixCls,
      roleLevelDataSet,
      menuTypeDataSet,
      accountDataSet,
      menuDataSet,
      roleDataSet,
      roleDetailDataSet,
      lovDataSet,
      loadRolesInfo,
      childRolesDataSet,
      transDataSetRole,
      transDataSetRule,
      authDataSet,
      interfaceDataSet,
      authorityRolesDataSet,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
