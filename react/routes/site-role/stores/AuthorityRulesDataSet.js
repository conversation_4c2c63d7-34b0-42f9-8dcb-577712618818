import qs from 'qs';
import { DataSet } from 'choerodon-ui/pro';
import { getQueryParams } from '@zknow/utils';

const ChildRolesDataSet = ({ intl, intlPrefix, operationMap, typeMap }) => {
  const urlPrefix = '/iam/yqc/role_permissions/data_rule';
  const codeLabel = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const objectLabel = intl.formatMessage({ id: 'zknow.common.model.businessObject', defaultMessage: '业务对象' });
  const operationLabel = intl.formatMessage({ id: 'zknow.common.button.action', defaultMessage: '操作' });
  const typeLabel = intl.formatMessage({ id: 'zknow.common.model.type', defaultMessage: '类型' });
  const descriptionLabel = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const conditionLabel = intl.formatMessage({ id: 'iam.siteRole.model.role.condition.label', defaultMessage: '条件' });

  const operationMapDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.condition.isUpdate', defaultMessage: '更新' }), value: 'update' },
      { meaning: intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' }), value: 'insert' },
      { meaning: intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' }), value: 'delete' },
      { meaning: intl.formatMessage({ id: 'zknow.common.button.search', defaultMessage: '查询' }), value: 'select' },
      { meaning: intl.formatMessage({ id: 'iam.siteRole.model.role.other', defaultMessage: '其他' }), value: 'other' },
    ],
  });
  const typeMapDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.siteRole.model.role.api', defaultMessage: '接口类型' }), value: 'api' },
      { meaning: intl.formatMessage({ id: 'iam.siteRole.model.role.page', defaultMessage: '页面' }), value: 'page' },
      { meaning: intl.formatMessage({ id: 'iam.siteRole.model.role.record', defaultMessage: '记录' }), value: 'record' },
      { meaning: intl.formatMessage({ id: 'zknow.common.model.field', defaultMessage: '字段' }), value: 'field' },
    ],
  });

  return {
    autoQuery: false,
    autoLocateFirst: false,
    dataKey: 'content',
    totalKey: 'totalElement',
    autoQueryAfterSubmit: true,
    paging: 'server',
    pageSize: 20,
    selection: false,
    transport: {
      read: ({ data: { id }, data }) => ({
        url: `${urlPrefix}/${id}`,
        method: 'get',
        data: getQueryParams(data),
        paramsSerializer: (params) => {
          delete params.id;
          return qs.stringify(params);
        },
      }),
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'description', type: 'string', label: descriptionLabel },
      { name: 'code', type: 'string', label: codeLabel },
      { name: 'businessObjectName', type: 'string', label: objectLabel },
      { name: 'businessObject', type: 'string', label: objectLabel },
      {
        name: 'operation',
        type: 'string',
        label: operationLabel,
        transformResponse: (value) => (value ? operationMap[value] : undefined),
      },
      {
        name: 'type',
        type: 'string',
        label: typeLabel,
        transformResponse: (value) => (value ? typeMap[value] : undefined),
      },
    ],
    queryFields: [
      { name: 'description', type: 'string', label: descriptionLabel },
      { name: 'code', type: 'string', label: codeLabel },
      { name: 'businessObject', type: 'string', label: objectLabel },
      { name: 'operation', type: 'string', label: operationLabel, options: operationMapDs },
      { name: 'type', type: 'string', label: typeLabel, options: typeMapDs },
    ],
  };
};

export default ChildRolesDataSet;
