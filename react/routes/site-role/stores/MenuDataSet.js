import { getQueryParams } from '@zknow/utils';

const MenuDataSet = ({ intl, intlPrefix, menuTypeDataSet, assignedPermissionDataSet }) => {
  const urlPrefix = '/iam/yqc/v1/roles';

  function flatTree(treeData, childrenKey) {
    if (treeData.find(node => node[childrenKey])) {
      treeData.forEach(node => {
        if (node[childrenKey]?.length > 0) {
          treeData.push(...node[childrenKey]);
          delete node[childrenKey];
        }
      });
      return flatTree(treeData, childrenKey);
    } else {
      return treeData;
    }
  }

  const menuName = intl.formatMessage({ id: 'iam.siteRole.model.role.menu.name', defaultMessage: '菜单名称' });
  const type = intl.formatMessage({ id: 'zknow.common.model.type', defaultMessage: '类型' });
  const permission = intl.formatMessage({ id: 'iam.siteRole.model.role.menu.permission', defaultMessage: '权限' });
  const entrance = intl.formatMessage({ id: 'iam.siteRole.model.role.menu.entrance', defaultMessage: '页面入口' });

  return {
    autoQuery: false,
    autoLocateFirst: false,
    dataKey: 'content',
    totalKey: 'totalElement',
    autoQueryAfterSubmit: true,
    paging: false,
    selection: false,
    idField: 'id',
    parentField: 'parentId',
    transport: {
      read: ({ data: { id }, dataSet, data }) => {
        const name = dataSet.queryDataSet?.current?.get('name');
        return {
          url: `/iam/yqc/v1/roles/${id}/solution`,
          method: 'get',
          data: getQueryParams(data),
          transformResponse: (data) => {
            const res = JSON.parse(data);
            const treeData = [];
          res?.forEach((s) => {
            s.relations.forEach((m) => {
              treeData.push({
                ...m,
                parentId: m.parentId !== '0' ? m.parentId : s.id,
              });
            });
            treeData.push({
              ...s,
              type: 'solution',
              relations: [],
              parentId: '0',
            });
          });
          // 找不到父级id并且父级id不为0 则过滤 /脏数据/
          const ids = [];
          let idsL = -1;
          while (idsL !== ids.length) {
            idsL = ids.length;
            treeData.forEach(v => {
              if (!ids.includes(v.id)) {
                const flag = treeData.find(item => item.id === v.parentId);
                if (v.parentId !== '0' && !flag) {
                  ids.push(v.id);
                } else if (flag && ids.includes(v.parentId)) {
                  ids.push(v.id);
                }
              }
            });
          }
          const allData = treeData.filter(v => !ids.includes(v.id));
          // 查询名称
          let result = [];
          if (name) {
            result = allData.map(v => (v.name.indexOf(name) !== -1 ? v : null)).filter(v => v !== null);
            allData.forEach(v => {
              const d = result.find(j => j.parentId === v.id);
              if (d && !result.find(j => j.id === v.id)) {
                result.push(v);
              }
            });
          }
          return result.length ? result : allData;
          },
        };
      },
      create: ({ data: [data] }) => ({
        url: urlPrefix,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => ({
        url: urlPrefix,
        method: 'put',
        data,
      }),
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'name', type: 'string', label: menuName },
      { name: 'type', type: 'string', label: type, textField: 'text', valueField: 'value', options: menuTypeDataSet },
      { name: 'allPermission', type: 'number' },
      { name: 'assignedPermission', type: 'number' },
      { name: 'permission', type: 'string', label: permission },
      { name: 'path', type: 'string', label: entrance },
      { name: 'permissions', type: 'object' },
      { name: 'granted', type: 'boolean' },
    ],
    queryFields: [
      { name: 'name', type: 'string', label: menuName },
    ],
  };
};

export default MenuDataSet;
