const PermissionDataSet = ({ intl, intlPrefix, menuDataSet }) => {
  const description = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const method = intl.formatMessage({ id: 'iam.siteRole.model.role.permission.method', defaultMessage: '请求方式' });
  const path = intl.formatMessage({ id: 'iam.siteRole.model.role.permission.path', defaultMessage: '路径' });
  const code = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });

  return {
    autoQuery: false,
    autoLocateFirst: false,
    selection: 'multiple',
    paging: false,
    fields: [
      { name: 'id', type: 'string' },
      { name: 'description', type: 'string', label: description },
      { name: 'code', type: 'string', label: code },
      { name: 'method', type: 'string', label: method },
      { name: 'path', type: 'string', label: path },
      { name: 'granted', type: 'boolean' },
    ],
    events: {
      selectAll: ({ dataSet }) => {
        const permissions = dataSet.toData();
        if (permissions.length > 0) {
          menuDataSet.find(v => v.get('menuId') === permissions[0].menuId).set('permissions', permissions.map(v => {
            return {
              ...v,
              granted: true,
            };
          }));
        }
      },
      unSelectAll: ({ dataSet }) => {
        const permissions = dataSet.toData();
        const permissionIds = permissions.map(v => v.id);
        if (permissions.length > 0) {
          menuDataSet.forEach(r => {
            const curPermissions = r.get('permissions');
            r.set('permissions', curPermissions && curPermissions.map(v => {
              if (permissionIds.includes(v.id)) {
                return {
                  ...v,
                  granted: false,
                };
              } 
              return v;
            }));
          });
        }
      },
      select: ({ record }) => {
        const menuId = record.get('menuId');
        const id = record.get('id');
        const menuRecord = menuDataSet.find(v => v.get('menuId') === menuId);
        menuRecord.set('permissions', menuRecord.get('permissions').map(v => {
          if (id === v.id) {
            return {
              ...v,
              granted: true,
            };
          }
          return v;
        }));
      },
      unSelect: ({ record }) => {
        const id = record.get('id');
        menuDataSet.forEach(r => {
          const curPermissions = r.get('permissions');
          r.set('permissions', curPermissions && curPermissions.map(v => {
            if (id === v.id) {
              return {
                ...v,
                granted: false,
              };
            } 
            return v;
          }));
        });
      },
    },
  };
};

export default PermissionDataSet;
