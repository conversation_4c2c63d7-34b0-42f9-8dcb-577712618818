import { axios } from '@yqcloud/apps-master';
import { message } from 'choerodon-ui/pro';

const LovDataSet = ({ roleDetailDataSet, accountDataSet }) => {
  function handleAddAccount(data) {
    const roleId = roleDetailDataSet.current?.get('id');
    const postData = [];
    data.forEach(o => {
      const memberRoleDTO = {
        roleId,
        memberId: o.id,
        memberType: 'user',
      };
      postData.push(memberRoleDTO);
    });
    axios.post('/iam/yqc/v1/member-roles/batch-assign', postData).then(res => {
      if (res.failed) {
        message.error(res.message);
      } else {
        accountDataSet.query();
      }
    }).catch(e => {
      message.error(e.message);
    });
  }

  return {
    autoCreated: true,
    autoQuery: false,
    fields: [
      {
        name: 'account',
        lovCode: 'SITE_USER',
        type: 'object',
      },
    ],
    events: {
      update: ({ record, name, value }) => {
        if (value?.length > 0) {
          handleAddAccount(value);
          record.set(name, []);
        }
      },
    },
  };
};
export default LovDataSet;
