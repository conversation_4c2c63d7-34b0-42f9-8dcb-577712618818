import React from 'react';
import { observer } from 'mobx-react-lite';
import { Form, Select, TextField, TextArea, CheckBox } from 'choerodon-ui/pro';

const CreateForm = (props) => {
  const { dataSet, loadRolesInfo, modal } = props;

  async function handleOk() {
    if (await dataSet.submit()) {
      dataSet.query();
      return loadRolesInfo();
    } else {
      return false;
    }
  }

  function handleCancel() {
    dataSet.reset();
  }

  modal.handleOk(handleOk);
  modal.handleCancel(handleCancel);

  return (
    <Form dataSet={dataSet} labelLayout="horizontal" labelAlign="right" labelWidth="auto" columns={2}>
      <TextField name="name" autoFocus />
      <TextField name="code" restrict="_|A-Z|a-z|0-9" />
      <Select name="level" />
      <CheckBox name="allocableFlag" />
      <TextArea name="description" newLine colSpan={2} rows={1} autoSize={{ minRows: 1, maxRows: 4 }} resize="height" /> 
    </Form>
  );
};

export default observer(CreateForm);
