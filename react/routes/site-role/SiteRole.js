import React from 'react';
import { Route, Switch, withRouter } from 'react-router-dom';
import { nomatch } from '@yqcloud/apps-master';
import ListView from './ListView.js';
import Detail from './detail';

const SiteRole = ({ match }) => (
  <Switch>
    <Route path={`${match.url}/detail/:id`} component={Detail} />
    <Route path={`${match.url}`} component={ListView} />
    <Route path="*" component={nomatch} />
  </Switch>
);
export default withRouter(SiteRole);
