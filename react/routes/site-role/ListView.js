import React, { useEffect, useContext, useMemo, useState } from 'react';
import uniq from 'lodash/uniq';
import { v4 as uuidv4 } from 'uuid';
import { observer } from 'mobx-react-lite';
import { TabPage, Content, axios } from '@yqcloud/apps-master';
import { Icon, ClickText, TableHoverAction, TableStatus, Button as YQButton, YqTable, useFilter } from '@zknow/components';
import { Table, Button, Modal, Dropdown, Menu, message } from 'choerodon-ui/pro';
import CreateForm from './form/CreateForm';
import Store from './stores';
import './index.less';

const { Column } = Table;
const { Item } = Menu;
const modalKey = Modal.key();

const ListView = () => {
  const { intl, intlPrefix, roleDataSet, loadRolesInfo, history, match, prefixCls } = useContext(Store);
  const [search, setSearch] = useState(null);

  useFilter(roleDataSet);

  function renderCreate() {
    return (
      <YQButton icon="plus" funcType="raised" color="primary" onClick={openCreateModal}>
        {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
      </YQButton>
    );
  }

  function openCreateModal() {
    roleDataSet.create();
    Modal.open({
      key: modalKey,
      title: intl.formatMessage({ id: 'iam.siteRole.desc.role.create', defaultMessage: '新建角色' }),
      children: <CreateForm dataSet={roleDataSet} loadRolesInfo={loadRolesInfo} />,
      style: { width: '8rem' },
    });
  }

  function openModal({ batch }) {
    Modal.open({
      key: modalKey,
      title: intl.formatMessage({ id: 'iam.siteRole.desc.role.disable.confirm', defaultMessage: '确认失效' }),
      children: <span>{intl.formatMessage({ id: 'iam.siteRole.desc.role.disable.tip', defaultMessage: '角色可能会影响用户可见的菜单，请确认是否要失效角色？' })}</span>,
      style: { width: '5.2rem' },
      destroyOnClose: true,
      onOk: () => {
        const requestData = batch ? roleDataSet.selected.map(r => r.toData()) : [roleDataSet.current?.toData()];
        axios.put('/iam/yqc/v1/roles/disable', requestData).then(res => {
          if (res && res.failed) {
            message.error(res.message);
          } else {
            message.success(intl.formatMessage({ id: 'zknow.common.success.save', defaultMessage: '保存成功' }));
            loadRolesInfo();
            roleDataSet.query();
          }
        }).catch(e => {
          message.error(e.message);
        });
      },
    });
  }

  function handleChangeEnabledFlag({ batch, enabledFlag, disabled }) {
    if (disabled) {
      return false;
    }

    if (enabledFlag) {
      const requestData = batch ? roleDataSet.selected.map(r => r.toData()) : [roleDataSet.current?.toData()];
      axios.put('/iam/yqc/v1/roles/enable', requestData).then(res => {
        if (res && res.failed) {
          message.error(res.message);
        } else {
          message.success(intl.formatMessage({ id: 'zknow.common.success.save', defaultMessage: '保存成功' }));
          loadRolesInfo();
          roleDataSet.query();
        }
      }).catch(e => {
        message.error(e.message);
      });
    } else {
      openModal({ batch });
    }
  }

  const actionContent = () => (
    <Menu>
      <Item disabled={uniq(roleDataSet.selected.map(r => r.get('enabled'))).includes(false)}>
        <div onClick={() => handleChangeEnabledFlag({ batch: true, enabledFlag: false, disabled: uniq(roleDataSet.selected.map(r => r.get('enabled'))).includes(false) })}>
          <Icon type="icon-Expires" style={{ marginRight: '0.04rem' }} />
          {intl.formatMessage({ id: 'iam.common.desc.batch.disabled', defaultMessage: '批量失效' })}
        </div>
      </Item>
      <Item disabled={uniq(roleDataSet.selected.map(r => r.get('enabled'))).includes(true)}>
        <div onClick={() => handleChangeEnabledFlag({ batch: true, enabledFlag: true, disabled: uniq(roleDataSet.selected.map(r => r.get('enabled'))).includes(true) })}>
          <Icon type="icon-read" style={{ marginRight: '0.04rem' }} />
          {intl.formatMessage({ id: 'iam.common.desc.batch.enabled', defaultMessage: '批量生效' })}
        </div>
      </Item>
    </Menu>
  );

  function renderBatchAction() {
    const disabled = roleDataSet.selected.length <= 0;
    return (
      <Dropdown trigger="click" overlay={actionContent()}>
        <Button funcType="raised" disabled={disabled}>
          {intl.formatMessage({ id: 'zknow.common.button.batch.action', defaultMessage: '批量操作' })}
          <Icon type="icon-drop-down" style={{ marginLeft: '0.08rem' }} />
        </Button>
      </Dropdown>
    );
  }

  const buttons = useMemo(() => [
    renderCreate(),
    // renderBatchAction(),
  ], [roleDataSet.selected.length]);

  function renderName({ record, name }) {
    return (
      <ClickText
        record={record}
        history={history}
        valueField={name}
        path={`${match.url}/detail/${record?.get('id')}`}
      />
    );
  }

  function renderAction({ record }) {
    const enabled = record?.get('enabled');
    const actions = enabled ? [
      {
        key: 'disable',
        name: intl.formatMessage({ id: 'zknow.common.action.make.invalid', defaultMessage: '失效' }),
        icon: 'icon-Expires',
        onClick: () => handleChangeEnabledFlag({ batch: false, enabledFlag: false }),
      },
    ] : [
      {
        key: 'enable',
        name: intl.formatMessage({ id: 'zknow.common.action.make.valid', defaultMessage: '生效' }),
        icon: 'icon-read',
        onClick: () => handleChangeEnabledFlag({ batch: false, enabledFlag: true }),
      },
    ];
    return <TableHoverAction record={record} actions={actions} />;
  }

  const renderIsBuiltIn = ({ record }) => {
    return record.get('builtIn') ? intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' }) : intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' });
  };

  const renderStatus = ({ record }) => {
    const flag = record.getPristineValue('enabled');
    return (
      <TableStatus
        status={flag}
        enabledText={intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' })}
        disabledText={intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' })}
      />
    );
  };

  return (
    <TabPage>
      <Content className={`${prefixCls}-content`} style={{ padding: 0 }}>
        <Table
          labelLayout="float"
          dataSet={roleDataSet}
          pristine
          placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
          autoHeight
          autoLocateFirst={false}
          buttons={buttons}
          queryBarProps={{
            title: intl.formatMessage({ id: 'zknow.common.model.role', defaultMessage: '角色' }),
          }}
        >
          <Column name="name" renderer={renderName} tooltip="overflow" />
          <Column name="code" minWidth={300} tooltip="overflow" />
          <Column name="description" width={150} />
          <Column name="builtIn" renderer={renderIsBuiltIn} />
          <Column name="level" width={100} />
          <Column name="enabled" width={100} renderer={renderStatus} />
          <Column renderer={({ record }) => renderAction({ record })} width={10} tooltip="none" />
        </Table>
      </Content>
    </TabPage>
  );
};

export default observer(ListView);
