import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, Select, Password, Lov } from 'choerodon-ui/pro';
import { MobileField } from '@zknow/components';

export default observer((props) => {
  const { modal, userInfoDataSet, accountDataSet, intl, intlPrefix, prefixCls } = props;

  async function handleOk() {
    try {
      if (await userInfoDataSet.submit()) {
        await accountDataSet.query();
        await userInfoDataSet.reset();
      } else {
        return false;
      }
    } catch (err) {
      return false;
    }
  }

  modal.handleOk(() => handleOk());
  modal.handleCancel(() => {
    userInfoDataSet.reset();
  });

  return (
    <div>
      <Form labelLayout="horizontal" labelAlign="right" labelWidth="auto" dataSet={userInfoDataSet}>
        <TextField autoFocus name="realName" />
        <TextField name="email" />
        <Lov multiple name="roles" />
        <Password name="password" />
        <TextField name="phone" />
      </Form>
    </div>
  );
});
