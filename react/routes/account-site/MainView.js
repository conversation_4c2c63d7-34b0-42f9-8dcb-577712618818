import React, { useContext, Fragment, useCallback, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Table, Modal, message } from 'choerodon-ui/pro';
import { Icon, TableHoverAction, ClickText, Button, YqTable, TableStatus, StatusTag, ModalTitle } from '@zknow/components';
import { axios, TabPage, Content } from '@yqcloud/apps-master';
import { color as colorUtils } from '@zknow/utils';
import AccountDetail from './account-detail';
import ButtonGroup from './account-detail/ButtonGroup';
import CreateModal from './CreateModal';
import Store from './stores';
// import './index.less';

const { Column } = Table;

const modalKey = Modal.key();

const MainView = () => {
  const {
    prefixCls, intl, intlPrefix, accountDataSet, userInfoDataSet,
    enterpriseDataSet, AppState: { currentMenuType: { domainId, domainName } },
  } = useContext(Store);

  const handleChangeEdit = useCallback((flag) => {
    userInfoDataSet.current.setState('isEdit', flag);
  }, [userInfoDataSet.current]);

  const handleOpenModal = useCallback((record) => {
    Modal.open({
      style: { width: 600 },
      title: <ModalTitle title={intl.formatMessage({ id: 'iam.common.model.menu.title.account', defaultMessage: '账号' })} dataSet={accountDataSet} />,
      children: (
        <AccountDetail
          userInfoDataSet={userInfoDataSet}
          enterpriseDataSet={enterpriseDataSet}
          accountDataSet={accountDataSet}
          intl={intl}
          intlPrefix={intlPrefix}
          prefixCls={prefixCls}
        />
      ),
      closable: true,
      drawer: true,
      onClose: () => {
        userInfoDataSet.current = null;
      },
      destroyOnClose: true,
      footer: (
        <div className="flex-align-center">
          <ButtonGroup
            onChangeEdit={handleChangeEdit}
            dataSet={userInfoDataSet}
            accountDataSet={accountDataSet}
            prefixCls={prefixCls}
            intl={intl}
            intlPrefix={intlPrefix}
          />
        </div>
      ),
      key: modalKey,
    });
  }, []);

  const handleClick = async (record) => {
    userInfoDataSet.setQueryParameter('userId', record.get('id'));
    const resp = await userInfoDataSet.query();
    if (resp) {
      enterpriseDataSet.setQueryParameter('accountId', record.get('id'));
      enterpriseDataSet.query();
      handleOpenModal(record);
    }
  };

  const renderName = ({ record, value }) => {
    return (
      <ClickText
        record={record}
        valueField="realName"
        onClick={handleClick}
      />
    );
  };

  const renderStatus = ({ record }) => {
    const flag = record.getPristineValue('enabled');
    return (
      <TableStatus
        status={flag}
        enabledText={intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' })}
        disabledText={intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' })}
      />
    );
  };

  const renderLocked = ({ record }) => {
    const flag = !record.get('locked');
    if (flag) {
      return (
        <StatusTag
          name="flag"
          color={colorUtils?.getColorCorrespondingValue('blue')}
        >
          {intl.formatMessage({ id: 'iam.common.model.normal', defaultMessage: '正常' })}
        </StatusTag>
      );
    }
    return (
      <StatusTag
        name="flag"
        color={colorUtils?.getColorCorrespondingValue('orange')}
      >
        {intl.formatMessage({ id: 'iam.common.model.locked', defaultMessage: '锁定' })}
      </StatusTag>
    );
  };

  const handleConfirmOk = (record) => {
    const successMsg = intl.formatMessage({ id: 'zknow.common.success.invalid', defaultMessage: '失效成功' });
    axios.post(`iam/hzero/v1/users/${record.get('id')}/frozen`).then((res) => {
      if (res) {
        message.success(successMsg);
      }
      accountDataSet.query();
    }).catch((err) => {
      message.error(err?.message);
    });
  };

  const openConfirmModal = (record, isBatch) => {
    const content = intl.formatMessage({ id: 'iam.accountSite.desc.account.site.disabled.confirm.message', defaultMessage: '失效后，帐号将无法登录平台！' });
    Modal.confirm({
      title: intl.formatMessage({ id: 'iam.accountSite.desc.general.disable.confirm', defaultMessage: '确认失效？' }),
      children: (
        <div>
          {content}
        </div>
      ),
      onOk: () => handleConfirmOk(record, isBatch),
      key: modalKey,
      destroyOnClose: true,
    });
  };

  async function handleEnable(record) {
    try {
      const successMsg = intl.formatMessage({ id: 'zknow.common.success.valid', defaultMessage: '生效成功' });
      const userId = record.get('id');
      const res = await axios.post(`iam/hzero/v1/users/${userId}/unfrozen`);
      if (!res?.failed) {
        message.success(successMsg);
      } else {
        message.error(res.message);
      }
    } catch (err) {
      message.error(err.message);
    }
    accountDataSet.query();
  }

  async function handleUnlock(record) {
    record.set('locked', false);
    await accountDataSet.submit();
    await accountDataSet.query();
  }

  const renderTableAction = ({ dataSet, record }) => {
    const flag = record.get('enabled');
    const locked = record.get('locked');
    const actions = [];

    if (locked) {
      actions.push(
        {
          name: intl.formatMessage({ id: 'iam.common.desc.unlock', defaultMessage: '解锁' }),
          icon: 'unlock',
          onClick: () => handleUnlock(record),
        }
      );
    }
    if (flag) {
      actions.push({
        name: intl.formatMessage({ id: 'zknow.common.action.make.invalid', defaultMessage: '失效' }),
        icon: 'icon-Expires',
        onClick: () => openConfirmModal(record),
      });
    } else {
      actions.push({
        name: intl.formatMessage({ id: 'zknow.common.action.make.valid', defaultMessage: '生效' }),
        icon: 'icon-read',
        onClick: () => handleEnable(record),
      });
    }

    return (
      <TableHoverAction
        record={record}
        actions={actions}
      />
    );
  };

  const handleClickCreateBtn = () => {
    let newUser = {};
    if (domainId) {
      newUser = {
        domainEnabledFlag: true,
        domainId,
        domainName,
      };
    }
    userInfoDataSet.create(newUser);
    Modal.open({
      title: intl.formatMessage({ id: 'iam.accountSite.desc.account.site.create', defaultMessage: '新建账号' }),
      children: (
        <CreateModal
          userInfoDataSet={userInfoDataSet}
          accountDataSet={accountDataSet}
          intl={intl}
          intlPrefix={intlPrefix}
          prefixCls={prefixCls}
        />
      ),
      style: { width: 520 },
      closable: true,
      key: modalKey,
      destroyOnClose: true,
    });
  };

  const tableButtons = (
    <Button icon="plus" funcType="raised" color="primary" className={`${prefixCls}-primary-button`} onClick={handleClickCreateBtn}>
      {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
    </Button>
  );

  return (
    <Fragment>
      <TabPage>
        <Content className={`${prefixCls}-content`} style={{ padding: 0 }}>
          <Table
            labelLayout="float"
            pristine
            dataSet={accountDataSet}
            placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
            autoHeight
            autoLocateFirst={false}
            buttons={[tableButtons]}
            queryBarProps={{
              title: intl.formatMessage({ id: 'iam.common.model.menu.title.account', defaultMessage: '账号' }),
            }}
          >
            <Column name="realName" tooltip="overflow" width={150} renderer={renderName} />
            <Column name="email" tooltip="overflow" />
            <Column name="phone" />
            <Column name="locked" renderer={renderLocked} />
            <Column align="left" name="enabled" renderer={renderStatus} />
            <Column width={0} renderer={renderTableAction} tooltip="none" />
          </Table>
        </Content>
      </TabPage>
    </Fragment>
  );
};

export default observer(MainView);
