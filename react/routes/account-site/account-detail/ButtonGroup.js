import React, { useState, useCallback, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Modal } from 'choerodon-ui/pro';
import { Button } from '@zknow/components';

const modalKey = Modal.key();

const ButtonGroup = ({
  onChangeEdit,
  intl,
  intlPrefix,
  dataSet,
  accountDataSet,
}) => {
  const [isEdit, setIsEdit] = useState(false);

  const handleOk = async (ds) => {
    // 防止默认密码校验不通过
    // ds.current.set('password', '000000');

    try {
      if (await ds.submit()) {
        await ds.query();
        await accountDataSet.query();
        setIsEdit(false);
        onChangeEdit(false);
      } else {
        setIsEdit(false);
        onChangeEdit(false);
        dataSet.reset();
        return false;
      }
    } catch (err) {
      return false;
    }
  };

  const handleClickSaveBtn = useCallback(() => {
    handleOk(dataSet);
  }, [dataSet.current]);

  const handleClickCancelBtn = useCallback(() => {
    setIsEdit(false);
    onChangeEdit(false);
    dataSet.reset();
  }, []);

  const handleClickEditBtn = useCallback(() => {
    setIsEdit(true);
    onChangeEdit(true);
  }, [isEdit]);

  if (isEdit) {
    return (
      <div>
        <Button key="confirm" funcType="raised" color="primary" onClick={handleClickSaveBtn}>
          {intl.formatMessage({ id: 'zknow.common.button.ok', defaultMessage: '确定' })}
        </Button>
        <Button key="cancel" funcType="raised" onClick={handleClickCancelBtn}>
          {intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}
        </Button>
      </div>
    );
  }

  return (
    <div>
      <Button key="edit" color="primary" funcType="raised" icon="icon-edit" onClick={handleClickEditBtn}>
        {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
      </Button>
    </div>
  );
};

export default observer(ButtonGroup);
