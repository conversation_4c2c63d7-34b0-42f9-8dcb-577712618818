@import "~choerodon-ui/lib/style/themes/default";

// .account-site-detail-avatarline {
//   display: flex;
//   align-items: center;
//   justify-content: space-between;
//   margin-bottom: 0.32rem;

//   .flex-align-center {
//     display: flex;
//     align-items: center;
//   }

//   .account-name {
//     font-size: 0.16rem;
//     font-family: PingFangSC-Medium, PingFang SC;
//     font-weight: 700;
//     color: #020e26;
//   }

//   .account-source {
//     border-radius: 0.02rem;
//     padding: 0 0.08rem;
//     border: 0.01rem solid #ffb4b0;
//     font-size: 0.12rem;
//     font-family: PingFangSC-Regular, PingFang SC;
//     font-weight: 400;
//     color: #f8353f;
//     margin-left: 0.08rem;
//   }

//   .account-status {
//     background: #8c8c8c;
//     border-radius: 0.02rem;
//     padding: 0 0.08rem;
//     color: #fff;
//     font-size: 0.12rem;
//     font-family: PingFangSC-Regular, PingFang SC;
//     font-weight: 400;
//     margin-left: 0.08rem;
//   }

//   .account-email {
//     font-size: 0.12rem;
//     font-family: PingFangSC-Regular, PingFang SC;
//     font-weight: 400;
//     color: #8c8c8c;
//     margin-left: 0.04rem;
//   }

//   .button-flex-center {
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     font-size: 0.14rem;
//     line-height: 0.14rem;
//   }

//   .create-icon {
//     margin-right: 0.08rem;
//   }

//   .expires-icon {
//     margin-right: 0.08rem;
//   }
// }

// .account-site-detail-avatar-content {
//   margin-right: 0.12rem;
// }
