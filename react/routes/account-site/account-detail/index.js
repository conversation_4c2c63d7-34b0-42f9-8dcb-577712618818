import React from 'react';
import { observer } from 'mobx-react-lite';
import { Table, TextField, Form, Lov, Output } from 'choerodon-ui/pro';
import './index.less';

const { Column } = Table;

const AccountDetail = ((props) => {
  const { userInfoDataSet, intl, intlPrefix, prefixCls, enterpriseDataSet, accountDataSet } = props;
  const isEdit = userInfoDataSet?.current?.getState('isEdit');
  return (
    <React.Fragment>
      <Form
        header={intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' })}
        dataSet={userInfoDataSet}
        labelLayout="horizontal"
        disabled={!isEdit}
      >
        <TextField autoFocus name="realName" />
        <TextField name="email" />
        <Lov multiple name="roles" />
        <TextField name="phone" />
      </Form>
      <Table
        labelLayout="float"
        pristine
        dataSet={enterpriseDataSet}
        queryBarProps={{
          title: intl.formatMessage({ id: 'iam.accountSite.desc.account.site.affiliated.enterprises', defaultMessage: '关联人员' }),
          queryFieldsStyle: {
            realName: { width: 100 },
            email: { width: 200 },
            tenantName: { width: 140 },
          },
        }}
      >
        <Column lock="left" name="realName" />
        <Column name="email" width={230} />
        <Column name="tenantName" />
      </Table>
    </React.Fragment>
  );
});

export default observer(AccountDetail);
