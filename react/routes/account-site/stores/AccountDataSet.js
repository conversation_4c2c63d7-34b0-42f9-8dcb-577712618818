import { DataSet } from 'choerodon-ui/pro';
import { getQueryParams } from '@zknow/utils';

export default ({ intlPrefix, intl, orgId }) => {
  const url = 'iam/yqc/v1/users';
  const hzeroUrl = 'iam/hzero/v1/users';
  const realName = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const phone = intl.formatMessage({ id: 'zknow.common.model.phone', defaultMessage: '手机号' });
  const email = intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' });
  const status = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const locked = intl.formatMessage({ id: 'iam.accountSite.model.account.site.secure.status', defaultMessage: '安全状态' });
  const tenantName = intl.formatMessage({ id: 'iam.accountSite.model.account.site.tenant.name', defaultMessage: '所属企业' });

  const commonBooleanDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' }), value: true },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' }), value: false },
    ],
  });

  const enabledFlagDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' }), value: 'true' },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' }), value: 'false' },
    ],
  });

  const lockedDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.common.model.normal', defaultMessage: '正常' }), value: 'false' },
      { meaning: intl.formatMessage({ id: 'iam.common.model.locked', defaultMessage: '锁定' }), value: 'true' },
    ],
  });
  return {
    autoQuery: true,
    selection: false,
    paging: true,
    primaryKey: 'id',
    autoLocateFirst: false,
    pageSize: 20,
    transport: {
      read: ({ data }) => ({
        url: `${url}/paging`,
        method: 'get',
        data: getQueryParams(data),
      }),
      create: ({ data }) => ({
        url,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => ({
        url: `${hzeroUrl}/${data.id}/unlocked`,
        method: 'post',
        data,
      }),
      destroy: ({ data }) => ({
        url: `${url}/${data.id}`,
        method: 'delete',
      }),
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'realName', type: 'string', label: realName },
      { name: 'phone', type: 'string', label: phone },
      { name: 'email', type: 'string', label: email },
      { name: 'enabled', type: 'boolean', label: status },
      { name: 'locked', type: 'boolean', label: locked },
      { name: 'tenantName', type: 'string', label: tenantName },
      { name: 'userSource', lookupCode: 'ACCOUNT_SOURCE' },
    ],
    queryFields: [
      { name: 'realName', type: 'string', label: realName },
      { name: 'phone', type: 'string', label: phone },
      { name: 'email', type: 'string', label: email },
      { name: 'enabled', type: 'string', label: status, options: enabledFlagDs },
      { name: 'locked', type: 'string', label: locked, options: lockedDs },
    ],
  };
};
