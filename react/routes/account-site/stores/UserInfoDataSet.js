export default ({ intlPrefix, intl, orgId, enterpriseDataSet }) => {
  const url = 'iam/yqc/v1/users';
  const status = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });

  const phonePattern = /^[1][1-9][0-9]{9}$/;
  // eslint-disable-next-line no-useless-escape
  const emailPattern = /^([a-zA-Z0-9_.-])+@([a-zA-Z0-9-])+((\.[a-zA-Z0-9-]{2,}){1,4})$/;

  const realName = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const phone = intl.formatMessage({ id: 'zknow.common.model.phone', defaultMessage: '手机号' });
  const email = intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' });
  const language = intl.formatMessage({ id: 'zknow.common.model.language', defaultMessage: '语言' });
  const timeZone = intl.formatMessage({ id: 'zknow.common.model.timeZone', defaultMessage: '时区' });
  const password = intl.formatMessage({ id: 'iam.accountSite.model.account.site.password', defaultMessage: '默认密码' });
  const role = intl.formatMessage({ id: 'zknow.common.model.role', defaultMessage: '角色' });

  const phoneValidator = (value, name, record) => {
    const phoneValue = record.get('phone');
    if (phoneValue) {
      if (phonePattern.test(phoneValue)) {
        return true;
      }
      return intl.formatMessage({ id: 'iam.accountSite.model.account.site.phone.error', defaultMessage: '请填写正确的手机号' });
    }
    return true;
  };

  const pwdValidator = (value, name, record) => {
    const passwordValue = record.get('password');
    if (passwordValue) {
      const reg = /(?!^[0-9]+$)(?!^[A-z]+$)(?!^[^A-z0-9]+$)^.{6,}$/;
      if (passwordValue && !reg.test(passwordValue)) {
        return intl.formatMessage({ id: 'iam.accountSite.model.account.site.password.error', defaultMessage: '密码最短为6位，至少包含字母、数字和符号中的2种' });
      }
    }
    return true;
  };

  const emailValidator = (value, name, record) => {
    const emailValue = record.get('email');
    if (emailPattern.test(emailValue)) {
      return true;
    }
    return intl.formatMessage({ id: 'iam.accountSite.model.account.site.email.error', defaultMessage: '请填写正确的邮箱' });
  };

  return {
    autoQuery: false,
    selection: false,
    paging: false,
    primaryKey: 'id',
    transport: {
      read: ({ data }) => ({
        url: `${url}/${data.userId}/info`,
        method: 'get',
        paramsSerializer: () => '',
      }),
      create: ({ data: [data] }) => {
        return {
          url,
          method: 'post',
          data,
        };
      },
      update: ({ data: [data] }) => {
        data.password = undefined;
        return {
          url,
          method: 'put',
          data,
        };
      },
      destroy: ({ data }) => ({
        url: `${url}/${data.id}`,
        method: 'delete',
      }),
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'realName', label: realName, type: 'string', required: true },
      {
        name: 'phone',
        label: phone,
        type: 'string',
        validator: phoneValidator,
      },
      {
        name: 'email',
        label: email,
        type: 'email',
        required: true,
      },
      { name: 'enabled', type: 'boolean', label: status },
      {
        name: 'internationalTelCode',
        type: 'string',
        defaultValue: '+86',
        lookupCode: 'INTL_TEL_CODE',
      },
      {
        name: 'language',
        label: language,
        type: 'string',
        lookupUrl: 'hpfm/v1/languages/list',
        textField: 'description',
        valueField: 'code',
      },
      { name: 'languageName', type: 'string' },
      {
        name: 'timeZone',
        label: timeZone,
        type: 'string',
        lookupUrl: 'hpfm/v1/lookup/queryCode?lookupTypeCode=TIME_ZONE',
      },
      { name: 'timeZoneMeaning', type: 'string' },
      {
        name: 'roles',
        type: 'object',
        label: role,
        lovCode: 'SITE_ROLE',
        textField: 'name',
        lovPara: {
          isEnabled: true,
          allocableFlag: true,
        },
      },
      {
        name: 'password',
        label: password,
        type: 'string',
        required: true,
        validator: pwdValidator,
        dynamicProps: { required: ({ record }) => !record.get('id') },
      },
      { name: 'imageUrl', type: 'string' },
      { name: 'userSource', lookupCode: 'ACCOUNT_SOURCE' },
    ],
    events: {
      // ❗由于密码在保存后会被加密变得很长，下面两个事件保证密码在前端显示长度不变。
      submit({ dataSet }) {
        const pwd = dataSet?.current?.get('password');
        dataSet.setState('_password', pwd);
      },
      submitSuccess({ dataSet, data }) {
        const pwd = dataSet.getState('_password');
        data.content[0].password = pwd;
      },
    },
  };
};
