import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import AccountDataSet from './AccountDataSet';
import UserInfoDataSet from './UserInfoDataSet';
import EnterpriseDataSet from './EnterpriseDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: 'iam.accountSite' })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId } },
    } = props;
    const intlPrefix = 'account.site';
    const prefixCls = 'account-site';
    const accountDataSet = useMemo(() => new DataSet(AccountDataSet({ intlPrefix, intl, orgId: organizationId })), []);
    const enterpriseDataSet = useMemo(() => new DataSet(EnterpriseDataSet({ intlPrefix, intl, orgId: organizationId })), []);
    const userInfoDataSet = useMemo(() => new DataSet(UserInfoDataSet({ intlPrefix, intl, orgId: organizationId, enterpriseDataSet })), []);
    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      orgId: organizationId,
      accountDataSet,
      enterpriseDataSet,
      userInfoDataSet,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
