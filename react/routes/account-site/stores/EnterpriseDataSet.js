import qs from 'qs';
import { getQueryParams } from '@zknow/utils';

export default ({ intlPrefix, intl, orgId }) => {
  const url = '/iam/yqc/v1/users/paging';
  const name = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const phone = intl.formatMessage({ id: 'zknow.common.model.phone', defaultMessage: '手机号' });
  const email = intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' });
  const tenantName = intl.formatMessage({ id: 'zknow.common.model.tenant.name', defaultMessage: '租户名称' });

  return {
    autoQuery: false,
    selection: false,
    paging: true,
    autoLocateFirst: false,
    pageSize: 20,
    transport: {
      read: ({ data: { accountId }, data }) => ({
        url: `${url}?account=${accountId}`,
        method: 'get',
        paramsSerializer: (params) => {
          delete params.accountId;
          return qs.stringify(params);
        },
        data: getQueryParams(data),
      }),
    },
    fields: [
      { name: 'cid', type: 'string' },
      { name: 'realName', type: 'string', label: name },
      { name: 'email', type: 'string', label: email },
      { name: 'phone', type: 'string', label: phone },
      { name: 'tenantName', type: 'string', label: tenantName },
    ],
    // queryFields: [
    //   { name: 'realName', type: 'string', label: name },
    //   { name: 'email', type: 'string', label: email },
    //   { name: 'phone', type: 'string', label: phone },
    //   { name: 'tenantName', type: 'string', label: tenantName },
    // ],
  };
};
