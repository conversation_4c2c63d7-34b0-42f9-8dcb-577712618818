@import "~choerodon-ui/lib/style/themes/default";

.account-site-main {
  height: 100%;
}

.account-site-content {
  display: flex;
  height: 100%;
  background-color: #fff;
}

.account-site-table-content {
  height: calc(100% - 0.48rem);
  overflow-y: auto;
}

.account-site-table-name-content {
  display: flex;
  align-items: center;
  cursor: pointer;

  .account-source {
    border-radius: 0.02rem;
    padding: 0 0.08rem;
    border: 0.01rem solid #ffb4b0;
    font-size: 0.12rem;
    line-height: 0.22rem;
    font-weight: 400;
    color: #f8353f;
    margin-left: 0.08rem;
  }
  .unactive-tag {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #595959;
    font-size: 0.12rem;
    margin-left: 0.08rem;
    width: 52px;
    height: 22px;
    background: #f5f5f5;
    border-radius: 2px;
    border: 1px solid rgba(0, 0, 0, 0.15);
  }
}

.account-site-unactive-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #595959;
  font-size: 0.12rem;
  margin-left: 0.08rem;
  width: 52px;
  height: 22px;
  background: #f5f5f5;
  border-radius: 2px;
  border: 1px solid rgba(0, 0, 0, 0.15);
}

.account-site-table-status {
  display: flex;
  align-items: center;

  .icon-dot {
    color: #75c940;
  }

  .icon-dot-gray {
    color: rgba(0, 0, 0, 0.45);
  }
}

.account-site-headerline {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.12rem;

  .create-icon {
    margin-right: 0.08rem;
  }

  .dropdown-icon {
    margin-left: 0.08rem;
  }
}

.button-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.14rem;
  line-height: 0.14rem;
}

.close-fields-left {
  padding-right: 0;
  .c7n-pro-field-wrapper {
    padding-right: 0;
  }
  .c7n-pro-select {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
  }
  .c7n-pro-select-float-label {
    &::before {
      border-bottom-right-radius: 0;
      border-top-right-radius: 0;
    }
  }
}

.close-fields-right {
  padding-left: 0;
  .c7n-pro-field-wrapper {
    padding-left: 0;
  }
  .c7n-pro-input {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
  }
  .c7n-pro-input-float-label {
    &::before {
      border-bottom-left-radius: 0;
      border-top-left-radius: 0;
    }
  }
}

.horizontal-field-label {
  line-height: 0.32rem;
  text-align: right;
}

.two-columns-grid {
  .horizontal-form-grid {
    display: inline-grid;
    width: calc(50% - 0.24rem);
    margin-right: 0.24rem;
    grid-template-columns: 1rem auto;
    grid-row-gap: 0.16rem;
    grid-column-gap: 0.16rem;
  }

  .label-left {
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 0.32rem;
    height: 0.32rem;
  }

  .right-input {
    line-height: 0.32rem;
    height: 0.32rem;
  }
}

.flex-align-center {
  display: flex;
  align-items: center;
}
