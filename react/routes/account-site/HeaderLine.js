import React, { useContext, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import { Modal, Dropdown, Menu, message } from 'choerodon-ui/pro';
import { Icon, Button } from '@zknow/components';
import { axios } from '@yqcloud/apps-master';
import SearchLine from '../../components/table-search';
import CreateModal from './CreateModal';
import Store from './stores';
import './index.less';

const modalKey = Modal.key();

const HeaderLine = () => {
  const { prefixCls, intl, intlPrefix, userInfoDataSet, accountDataSet } = useContext(Store);

  const handleOk = async (dataSet) => {
    try {
      if (await dataSet.submit()) {
        await dataSet.query();
      } else {
        return false;
      }
    } catch (err) {
      return false;
    }
  };

  const handleClickCreateBtn = () => {
    userInfoDataSet.create();
    Modal.open({
      title: intl.formatMessage({ id: 'iam.accountSite.desc.account.site.create', defaultMessage: '新建账号' }),
      children: (
        <CreateModal
          userInfoDataSet={userInfoDataSet}
          accountDataSet={accountDataSet}
          intl={intl}
          intlPrefix={intlPrefix}
        />
      ),
      style: { width: 800 },
      closable: true,
      key: modalKey,
      destroyOnClose: true,
    });
  };

  const handleBatchImport = () => {
    const selectRecords = accountDataSet.selected;
    // selectRecords.forEach((data) => {
    //   data.set('enabledFlag', false);
    // });
    // handleOk(accountDataSet);
  };

  const handleBatchExport = () => {
    const selectRecords = accountDataSet.selected;
    // selectRecords.forEach((data) => {
    //   data.set('enabledFlag', false);
    // });
    // handleOk(accountDataSet);
  };

  const handleBatchDisable = () => {
    const successMsg = intl.formatMessage({ id: 'zknow.common.success.invalid', defaultMessage: '失效成功' });
    const selectRecords = accountDataSet.selected;
    const data = [];
    selectRecords.forEach((r) => {
      data.push({
        id: r.get('id'),
      });
    });
    axios.put('iam/yqc/v1/users/list', data).then((res) => {
      if (res) {
        message.success(successMsg);
      }
      accountDataSet.query();
    }).catch((err) => {
      message.error(err?.message);
    });
  };

  const handleSearch = useCallback((value) => {
    accountDataSet.setQueryParameter('condition', value);
    accountDataSet.query();
  }, []);

  const openConfirmModal = () => {
    const content = intl.formatMessage({ id: 'iam.accountSite.desc.account.site.disabled.confirm.message', defaultMessage: '失效后，帐号将无法登录平台！' });
    Modal.confirm({
      title: intl.formatMessage({ id: 'iam.accountSite.desc.general.disable.confirm', defaultMessage: '确认失效？' }),
      children: (
        <div>
          {content}
        </div>
      ),
      onOk: () => handleBatchDisable(),
      key: modalKey,
      destroyOnClose: true,
    });
  };

  const menu = (
    <Menu>
      <Menu.Item onClick={handleBatchImport}>
        <span>{intl.formatMessage({ id: 'iam.accountSite.desc.account.site.batch.import', defaultMessage: '批量导入' })}</span>
      </Menu.Item>
      <Menu.Item onClick={handleBatchExport}>
        <span>{intl.formatMessage({ id: 'iam.accountSite.desc.account.site.batch.export', defaultMessage: '批量导出' })}</span>
      </Menu.Item>
      <Menu.Item onClick={openConfirmModal}>
        <span>{intl.formatMessage({ id: 'iam.accountSite.desc.account.site.batch.disable', defaultMessage: '批量失效' })}</span>
      </Menu.Item>
    </Menu>
  );

  return (
    <div className={`${prefixCls}-headerline`}>
      <SearchLine
        onSearch={handleSearch}
        placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
      />
      <div>
        <Button funcType="raised" color="primary" className={`${prefixCls}-primary-button`} onClick={handleClickCreateBtn}>
          {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
        </Button>
        <Dropdown trigger={['click']} overlay={menu}>
          <Button funcType="raised" color="default" disabled={accountDataSet.selected.length === 0}>
            <div className="button-flex-center">
              <span>{intl.formatMessage({ id: 'zknow.common.button.batch.action', defaultMessage: '批量操作' })}</span>
              <Icon style={{ fontSize: '0.14rem' }} className="dropdown-icon" type="icon-drop-down" />
            </div>
          </Button>
        </Dropdown>
      </div>
    </div>
  );
};

export default observer(HeaderLine);
