import React, { useContext, useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { TabPage, Content } from '@yqcloud/apps-master';
import { Spin } from 'choerodon-ui/pro';
import FlowRender, { toDomainElements } from '@/components/flow-render';
import EmptyIcon from '@/assets/images/empty-content-part.svg';
import Store from './stores';

import './index.less';

function MainView() {
  const {
    hierarchyDataSet,
    prefixCls,
    intlPrefix,
    intl,
  } = useContext(Store);
  const [elements, setElements] = useState(false);

  useEffect(() => {
    if (hierarchyDataSet.status === 'ready') {
      setElements(toDomainElements(hierarchyDataSet));
    }
  }, [hierarchyDataSet.status, hierarchyDataSet.length]);

  return (
    <TabPage>
      <Content className={prefixCls}>
        {elements?.length ? (
          <FlowRender elements={elements} />
        ) : (
          <Spin
            spinning={hierarchyDataSet.status !== 'ready'}
            wrapperClassName={`${prefixCls}-spin-container`}
          >
            <div className="empty-wrapper">
              <img src={EmptyIcon} alt="" />
              <span>{intl.formatMessage({ id: 'iam.domainHierarchy.desc.hierarchy.empty', defaultMessage: '暂无域层级，请先在域定义中设置主域' })}</span>
            </div>
          </Spin>
        )}
      </Content>
    </TabPage>
  );
}

export default observer(MainView);
