import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import { formatterCollections } from '@zknow/utils';
import HierarchyDataSet from './HierarchyDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: 'iam.domainHierarchy' })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
    } = props;
    const intlPrefix = 'iam.hierarchy';
    const prefixCls = 'iam-hierarchy';
    const hierarchyDataSet = useMemo(() => new DataSet(HierarchyDataSet({ intl, intlPrefix, tenantId })), [tenantId]);

    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      intl,
      hierarchyDataSet,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }
)));
