export default ({ tenantId }) => {
  const url = `/iam/yqc/${tenantId}/domains/queryPrimary`;

  return {
    autoQuery: true,
    selection: false,
    paging: false,
    parentField: 'parentId',
    idField: 'id',
    transport: {
      read: {
        url,
        method: 'get',
        transformResponse: (response) => {
          try {
            const data = JSON.parse(response);
            if (data && data.failed) {
              return data;
            } else {
              data.filter((item) => (+item.parentId === 0)).forEach((root) => {
                root.parentId = null;
              });
              return data;
            }
          } catch (e) {
            return response;
          }
        },
      },
    },
  };
};
