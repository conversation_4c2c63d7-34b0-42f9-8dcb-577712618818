import React, { useState, useCallback, useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Button } from '@zknow/components';
import { Content, TabPage, Header } from '@yqcloud/apps-master';
import { Tabs } from 'choerodon-ui';
import Store from './stores';
import Basic from './basic';
import Application from './application';

const { TabPane } = Tabs;

function MainView() {
  const {
    intl,
    prefixCls,
    history,
    detailDataSet,
    solutionId,
  } = useContext(Store);
  const [isEdit, setEdit] = useState(false);

  return (
    <TabPage>
      <Header backPath={`/iam/site/solution${history.location?.search}`} dataSet={detailDataSet}>
        <h1>{intl.formatMessage({ id: 'iam.solution.desc.menu.solution.detail', defaultMessage: '解决方案详情' })}</h1>
      </Header>
      <Content className={prefixCls}>
        <div className={`${prefixCls}-wrapper`}>
          <Basic isEdit={isEdit} />
          <Tabs type="card" className={`${prefixCls}-tabs`}>
            <TabPane tab={intl.formatMessage({ id: 'zknow.common.button.apply', defaultMessage: '应用' })} key="application">
              <Application
                solutionId={solutionId}
              />
            </TabPane>
          </Tabs>
        </div>
      </Content>
    </TabPage>
  );
}

export default observer(MainView);
