import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import { formatterCollections } from '@zknow/utils';
import DetailDataSet from './DetailDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({
  code: 'iam.solution',
})(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId, type } },
      match,
    } = props;
    const solutionId = match?.params?.id;
    const prefixCls = 'iam-solution-detail';

    const menuTypeMeaning = useMemo(() => ({
      top: intl.formatMessage({ id: 'iam.common.desc.top.menu', defaultMessage: '顶层菜单' }),
      filter: intl.formatMessage({ id: 'iam.common.desc.filter', defaultMessage: '筛选器' }),
      filter_item: intl.formatMessage({ id: 'iam.common.desc.filter.item', defaultMessage: '筛选项' }),
      menu: intl.formatMessage({ id: 'zknow.common.model.menu', defaultMessage: '菜单' }),
      link: intl.formatMessage({ id: 'iam.common.desc.link', defaultMessage: '链接' }),
      dir: intl.formatMessage({ id: 'iam.common.model.menu.dir', defaultMessage: '目录' }),
    }), []);

    const detailDataSet = useMemo(
      () => new DataSet(DetailDataSet({ intl, tenantId, type, solutionId })),
      [tenantId, solutionId],
    );

    const value = {
      ...props,
      prefixCls,
      intl,
      solutionId,
      detailDataSet,
      tenantId,
      type,
      menuTypeMeaning,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }
)));
