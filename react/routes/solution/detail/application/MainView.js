import React, { useContext, useMemo, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import { axios } from '@yqcloud/apps-master';
import { Table, Modal } from 'choerodon-ui/pro';
import { TabPage } from '@yqcloud/apps-master';
import { TableHoverAction, YqAvatarPreset, useFilter, ClickText, TableStatus } from '@zknow/components';
import Store from './stores';

import './index.less';

const { Column } = Table;

const MainView = () => {
  const context = useContext(Store);
  const {
    intl,
    intlPrefix,
    prefixCls,
    history,
    appDataSet,
    tenantId,
    type,
  } = context;
  const avatarStyle = useMemo(() => ({ width: 28, height: 28, marginRight: 12 }), []);

  useFilter(appDataSet);
  function renderName({ record, name }) {
    return (
      <span className={`${prefixCls}-name`}>
        <YqAvatarPreset
          style={avatarStyle}
          key={record.get('id')}
          src={record.get('logo')}
          alternate={record.get(name)}
          size={28}
        />
        {record?.get(name)}
      </span>
    );
  }

  return (
    <TabPage>
      <Table
        dataSet={appDataSet}
        pristine
        autoHeight
        queryBarProps={{
          title: intl.formatMessage({ id: 'zknow.common.button.apply', defaultMessage: '应用' }),
          fuzzyQuery: false,
        }}
      >
        <Column name="name" renderer={renderName} />
        <Column name="code" />
      </Table>
    </TabPage>
  );
};

export default observer(MainView);
