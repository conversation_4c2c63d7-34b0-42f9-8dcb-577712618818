@import '~choerodon-ui/lib/style/themes/default';
@prefix-cls: app-application;
.@{prefix-cls} {
  padding: 0;
  &-content {
    padding: 0;
    height: calc(100% - 0.58rem);

    .c7n-pro-output-multiple-block {
      color: @primary-color;
      background: @minor-color;
    }
  }

  &-name {
    .br-link-text {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    display: flex;
    align-items: center;
  }
}
.tableActionAlignRight {
  .c7n-pro-table-cell-inner {
    overflow: inherit;
    position: relative !important;
  }
}

.iam-solution-detail-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  .iam-solution-detail-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    .app-application-content {
      height: 100%;
    }
    .c7n-pro-table-wrapper {
      height: 100%;
    }
  }
  .c7n-tabs-content.c7n-tabs-content-no-animated {
    flex: 1;
    overflow: auto;
    .c7n-tabs-tabpane.c7n-tabs-tabpane-active {
      height: 100%;
      .c7n-pro-table-wrapper {
        display: flex;
        flex-direction: column;
        .c7n-spin-nested-loading {
          flex: 1;
          overflow: auto;
          .c7n-spin-container {
            height: 100%;
            .c7n-pro-table.c7n-pro-table-parity-row {
              height: 100%;
              .c7n-pro-table-head {
                overflow: unset;
              }
              .c7n-pro-table-content {
                height: 100%;
                display: flex;
                flex-direction: column;
              }
            }
          }
        }
      }
    }
  }
}
