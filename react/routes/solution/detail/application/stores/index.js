import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import AppDataSet from './AppDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({
  code: 'iam.solution',
})(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { type } },
      solutionId,
    } = props;

    const prefixCls = 'app-application';

    const enableOptionDs = useMemo(() => new DataSet({
      data: [
        { meaning: intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' }), value: true },
        { meaning: intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' }), value: false },
      ],
    }), []);

    const appDataSet = useMemo(() => new DataSet(AppDataSet({
      intl,
      enableOptionDs,
      solutionId,
    })), []);

    const value = {
      ...props,
      appDataSet,
      prefixCls,
      type,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
