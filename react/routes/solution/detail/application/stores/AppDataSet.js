import { getQueryParams } from '@zknow/utils';

export default (props) => {
  const { intl, enableOptionDs, solutionId } = props;
  const url = `/app/v1/apps?solutionId=${solutionId}`;
  const name = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const code = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const creationDate = intl.formatMessage({ id: 'zknow.common.model.creationDate' });
  const status = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const provider = intl.formatMessage({ id: 'provider' });

  return {
    autoQuery: false,
    paging: true,
    pageSize: 20,
    selection: false,
    primaryKey: 'id',
    autoLocateFirst: false,
    transport: {
      read: ({ data }) => {
        return {
          url,
          method: 'get',
          data: getQueryParams(data),
          transformResponse: (resp) => {
            let jsonData = {};
            try {
              jsonData = JSON.parse(resp);
              jsonData = {
                ...jsonData,
                content: jsonData?.content?.map(app => {
                  return {
                    ...app,
                    labels: app.labels?.map(label => label.label)?.join(','),
                  };
                }),
              };
            } catch (e) {
              jsonData = {};
            }
            return jsonData;
          },
        }; 
      },
      update: ({ data: [data] }) => ({
        url,
        method: 'put',
        data,
      }),
    },
    fields: [
      {
        name: 'name',
        type: 'string',
        label: name,
        isIntl: true,
      },
      {
        name: 'code',
        type: 'string',
        label: code,
      },
      {
        name: 'tenantName',
        type: 'string',
        label: provider,
      },
      {
        name: 'creationDate',
        type: 'datetime',
        label: creationDate,
      },
      {
        name: 'enabledFlag',
        type: 'boolean',
        label: status,
        options: enableOptionDs,
      },
      {
        name: 'logo',
        type: 'string',
      },
    ],
    queryFields: [
      { name: 'name', type: 'string', label: name },
      { name: 'code', type: 'string', label: code },
    ],
  };
};
