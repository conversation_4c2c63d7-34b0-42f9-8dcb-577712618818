import { DataSet } from 'choerodon-ui/pro';
import { getQueryParams } from '@zknow/utils';

export default ({ intl, tenantId, type }) => {
  const url = `/iam/yqc/v1/${type === 'site' ? '' : `${tenantId}/`}menuSolution`;
  const name = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const logo = intl.formatMessage({ id: 'zknow.common.model.icon', defaultMessage: '图标' });
  const description = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const rankNum = intl.formatMessage({ id: 'zknow.common.model.order', defaultMessage: '排序' });
  const level = intl.formatMessage({ id: 'iam.solution.model.level', defaultMessage: '层级' });
  const codeLabel = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const status = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const isPreset = intl.formatMessage({ id: 'iam.solution.model.is.preset', defaultMessage: '是否预置' });

  const enabledFlagDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' }), value: true },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' }), value: false },
    ],
  });

  const presetDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' }), value: true },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' }), value: false },
    ],
  });

  const levelDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.solution.model.user', defaultMessage: '个人中心' }), value: 'user' },
      { meaning: intl.formatMessage({ id: 'zknow.common.model.platform', defaultMessage: '平台' }), value: 'site' },
      { meaning: intl.formatMessage({ id: 'zknow.common.model.tenant', defaultMessage: '租户' }), value: 'organization' },
    ],
  });

  const querList = [
    { name: 'name', type: 'string', label: name },
    { name: 'code', type: 'string', label: codeLabel, format: 'uppercase' },
    { name: 'description', type: 'string', label: description },
    // { name: 'rankNum', type: 'string', label: rankNum },
    // { name: 'presetFlag', type: 'boolean', label: isPreset, options: presetDs },
    { name: 'enabledFlag', type: 'boolean', label: status, options: enabledFlagDs },
  ];
  // const queryFieldList = type !== 'site' ? querList : [...querList, {
  //   name: 'level',
  //   type: 'string',
  //   label: level,
  //   options: levelDs,
  // }];

  return {
    autoQuery: false,
    autoLocateFirst: false,
    selection: false,
    autoQueryAfterSubmit: false,
    pageSize: 20,
    primaryKey: 'id',
    fields: [
      {
        name: 'name',
        type: 'string',
        label: name,
        required: true,
        dynamicProps: {
          isIntl: ({ record }) => {
            return tenantId === record?.get('tenantId');
          },
        },
      },
      { name: 'code', type: 'string', label: codeLabel },
      { name: 'enabledFlag', type: 'boolean', label: status, options: enabledFlagDs, defaultValue: true },
      { name: 'description', type: 'string', label: description },
      {
        name: 'logo',
        type: 'string',
        label: logo,
        defaultValue: JSON.stringify({ type: 'icon', backgroundColor: '#2979FF', displayIcon: 'ad-product' }),
      },
      { name: 'rankNum', type: 'number', label: rankNum, defaultValue: 0 },
      { name: 'level', type: 'string', label: level, options: levelDs, defaultValue: 'organization' },
      { name: 'tenantId', type: 'string', label: isPreset },
    ],
    transport: {
      read: ({ data, params }) => ({
        url,
        method: 'get',
        data: getQueryParams({ ...data, level: 'organization' }),
      }),
      create: ({ data: [data] }) => ({
        url: `${url}${data.baseId ? `?baseId=${data.baseId}` : ''}`,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => ({
        url,
        method: 'put',
        data,
      }),
      destroy: ({ data: [data] }) => ({
        url: `${url}/${data.id}`,
        method: 'delete',
      }),
    },
    queryFields: querList,
  };
};
