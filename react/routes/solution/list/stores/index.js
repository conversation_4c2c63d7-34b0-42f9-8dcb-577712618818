import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import { formatterCollections } from '@zknow/utils';
import ListDataSet from './ListDataSet';

const Store = createContext();

export default Store;
export const StoreProvider = inject('AppState')(formatterCollections({
  code: 'iam.solution',
})(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId, type } },
    } = props;
    const prefixCls = 'iam-menu-solution';
    const listDataset = useMemo(() => new DataSet(ListDataSet({ intl, tenantId, type })), [tenantId]);

    const value = {
      ...props,
      prefixCls,
      intl,
      listDataset,
      type,
      tenantId,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }
)));
