import React from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, TextArea, NumberField, Select } from 'choerodon-ui/pro';
import { ExternalComponent } from '@zknow/components';

export default observer((props) => {
  const { modal, dataSet, type } = props;

  modal.handleOk(async () => {
    try {
      const res = await dataSet.submit();
      if (res && !res.failed) {
        await dataSet.query();
        return true;
      } else {
        return false;
      }
    } catch (err) {
      return false;
    }
  });

  return (
    <div>
      <Form labelLayout="horizontal" record={dataSet.current} labelWidth="auto">
        <TextField autoFocus name="name" />
        {type === 'site'
          ? <Select name="level" />
          : null}
        <NumberField name="rankNum" />
        <ExternalComponent
          system={{
            scope: 'itsm',
            module: 'ImageSetter',
          }}
          name="logo"
          record={dataSet.current}
        />
        <TextArea name="description" rows={1} autoSize={{ minRows: 1, maxRows: 4 }} resize="height" />
      </Form>
    </div>
  );
});
