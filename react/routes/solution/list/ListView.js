import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { TabPage, Content, axios } from '@yqcloud/apps-master';
import { Table, Modal } from 'choerodon-ui/pro';
import { TableStatus, ClickText, useFilter, Button, TableHoverAction } from '@zknow/components';
import Store from './stores';
import FormView from './FormView';

const { Column } = Table;
const modalKey = Modal.key();

function ListView() {
  const {
    listDataset,
    intl,
    history,
    type,
    tenantId,
  } = useContext(Store);

  useFilter(listDataset);

  function openModal(baseId) {
    listDataset.create({ baseId });
    Modal.open({
      title: intl.formatMessage({ id: baseId ? 'baseCreate' : 'createMenuSolution' }),
      children: (
        <FormView
          dataSet={listDataset}
          intl={intl}
          type={type}
        />
      ),
      key: modalKey,
      drawer: false,
      destroyOnClose: true,
      afterClose: () => {
        if (listDataset?.current?.getState() !== 'sync') {
          listDataset.remove(listDataset.current);
        }
      },
    });
  }

  function renderName({ record, name, value }) {
    return (
      <ClickText
        record={record}
        valueField={name}
        history={history}
        path={`/iam/site/solution/detail/${record.get('id')}`}
      >
        {value}
      </ClickText>
    );
  }

  function renderEnabledFlag({ value }) {
    return (
      <TableStatus
        status={value}
        enabledText={intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' })}
        disabledText={intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' })}
      />
    );
  }

  return (
    <TabPage>
      <Content style={{ padding: 0 }}>
        <Table
          pristine
          dataSet={listDataset}
          queryFieldsLimit={100}
          queryBarProps={{
            title: intl.formatMessage({ id: 'iam.solution.desc.menu.solution', defaultMessage: '解决方案' }),
          }}
        >
          <Column name="name" renderer={renderName} />
          <Column name="code" />
          <Column name="description" />
          <Column name="enabledFlag" align="left" renderer={renderEnabledFlag} />
        </Table>
      </Content>
    </TabPage>
  );
}

export default observer(ListView);
