import React from 'react';
import { Route, Switch } from 'react-router-dom';
import { nomatch } from '@yqcloud/apps-master';
import { asyncRouter } from '@zknow/utils';

const List = asyncRouter(() => import('./list'));
const Detail = asyncRouter(() => import('./detail'));

const DomainIndex = (props) => {
  const { match } = props;

  return (
    <Switch>
      <Route path={`${match.url}/detail/:id`} component={Detail} />
      <Route path={`${match.url}`} component={List} />
      <Route path="*" component={nomatch} />
    </Switch>
  );
};

export default DomainIndex;
