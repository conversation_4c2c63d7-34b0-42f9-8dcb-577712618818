const BusinessUnitSelectDataSet = ({ tenantId }) => {
  const urlPrefix = '/iam/yqc';
  return {
    autoQuery: false,
    autoLocateFirst: false,
    idField: 'id',
    parentField: 'parentId',
    paging: false,
    pageSize: 10,
    transport: {
      read: {
        url: `${urlPrefix}/${tenantId}/business_unit/query_by_parent_id`,
        method: 'get',
        transformResponse(response) {
          try {
            return JSON.parse(response).filter(x => x.enabledFlag);
          } catch (e) {
            return response;
          }
        },
      },
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'name', type: 'string' },
    ],
  };
};

export { BusinessUnitSelectDataSet };
