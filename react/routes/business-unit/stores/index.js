import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import BusinessUnitDataSet from './BusinessUnitDataSet';
import { BusinessUnitSelectDataSet } from './FieldDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: 'iam.businessUnit' })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
    } = props;
    const intlPrefix = 'businessUnit';
    const prefixCls = 'business-unit';

    const businessUnitSelectDataSet = useMemo(() => new DataSet(BusinessUnitSelectDataSet({ tenantId })), []);

    const businessUnitDataSet = useMemo(() => new DataSet(BusinessUnitDataSet({
      tenantId,
      intl,
      intlPrefix,
      businessUnitSelectDataSet,
      autoQuery: true,
    })), [tenantId]);

    const businessUnitDetailDataSet = useMemo(() => new DataSet(BusinessUnitDataSet({
      tenantId,
      intl,
      intlPrefix,
      businessUnitSelectDataSet,
      autoQuery: false,
    })), [tenantId]);

    const value = {
      ...props,
      tenantId,
      intlPrefix,
      prefixCls,
      businessUnitDataSet,
      businessUnitDetailDataSet,
      businessUnitSelectDataSet,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
