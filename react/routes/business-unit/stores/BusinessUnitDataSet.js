import { axios } from '@yqcloud/apps-master';
import { DataSet } from 'choerodon-ui/pro';
import { getQueryParams } from '@zknow/utils';

const BusinessUnitDataSet = ({ tenantId, intl, intlPrefix, autoQuery }) => {
  const urlPrefix = '/iam/yqc';
  const unitName = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const description = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const manager = intl.formatMessage({ id: 'iam.businessUnit.model.business.unit.manager.id', defaultMessage: '部门负责人' });
  const company = intl.formatMessage({ id: 'iam.businessUnit.model.business.unit.company.id', defaultMessage: '所属公司' });
  const parent = intl.formatMessage({ id: 'iam.businessUnit.model.business.unit.parent.id', defaultMessage: '上级部门' });
  const enabledFlag = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const global = intl.formatMessage({ id: 'iam.common.model.global', defaultMessage: '全局' });
  const domainLabel = intl.formatMessage({ id: 'iam.businessUnit.model.business.unit.domain.label', defaultMessage: '域' });
  const domainMgLabel = intl.formatMessage({ id: 'iam.businessUnit.model.business.unit.domain.manager', defaultMessage: '域管理' });

  const enabledFlagDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' }), value: 'true' },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' }), value: 'false' },
    ],
  });

  function validateName(value, name, record) {
    if (value) {
      return axios({
        url: `${urlPrefix}/${tenantId}/business_unit/check_name`,
        method: 'get',
        params: {
          id: record.get('id'),
          name: value,
        },
      }).then(res => {
        if (res) {
          return true;
        } else {
          return intl.formatMessage({ id: `${intlPrefix}.validate.${name}` });
        }
      });
    }
  }

  return {
    autoQuery,
    autoLocateFirst: false,
    dataKey: 'content',
    totalKey: 'totalElement',
    idField: 'id',
    parentField: 'frontParentId',
    autoQueryAfterSubmit: true,
    paging: autoQuery ? 'server' : false,
    pageSize: 10,
    selection: false,
    transport: {
      read: ({ data: { param, id, parentId }, data, params }) => {
        const queryUrl = `${urlPrefix}/${tenantId}/business_unit/page`;
        const searchUrl = `${urlPrefix}/${tenantId}/business_unit/query/business/param`;
        const detailUrl = `${urlPrefix}/${tenantId}/business_unit/${id}`;
        const childUrl = `${urlPrefix}/${tenantId}/business_unit/query_by_parent_id`;
        const getUrl = () => {
          if (id) {
            return detailUrl;
          } else if (param) {
            return searchUrl;
          } else if (parentId) {
            return childUrl;
          } else {
            return queryUrl;
          }
        };
        return {
          url: getUrl(),
          method: 'get',
          params,
          data: getQueryParams(data),
          transformResponse(response) {
            try {
              const content = !!id || !!param || !!parentId ? JSON.parse(response) : JSON.parse(response).content;
              if (id) {
                if (content.parentId === '0') {
                  content.parentId = null;
                }
              } else {
                content.forEach(o => {
                  if (o.parentId === '0') {
                    o.parentId = null;
                  }
                  o.frontParentId = o.parentId;
                });
              }
              return content;
            } catch (e) {
              return response;
            }
          },
        };
      },
      create: ({ data: [data] }) => ({
        url: `${urlPrefix}/${tenantId}/business_unit`,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => ({
        url: `${urlPrefix}/${tenantId}/business_unit`,
        method: 'put',
        data,
      }),
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'name', type: 'string', label: unitName, required: true, validator: validateName, isIntl: true },
      { name: 'description', type: 'string', label: description },
      {
        name: 'managerId',
        label: manager,
        textField: 'realName',
        valueField: 'id',
        type: 'object',
        lovCode: 'USER',
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            realName: data.personName || data.managerName,
          };
        },
      },
      {
        name: 'parentId',
        label: parent,
        textField: 'name',
        valueField: 'id',
        type: 'object',
        lovCode: 'BUSINESS_UNIT',
        transformRequest: (value) => {
          return value?.id || 0;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            name: data.parentUnitName,
          };
        },
      },
      {
        name: 'companyId',
        label: company,
        textField: 'name',
        valueField: 'id',
        type: 'object',
        lovCode: 'COMPANY',
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            name: data.companyName,
          };
        },
      },
      {
        name: 'domainEnabledFlag',
        type: 'boolean',
        label: domainMgLabel,
      },
      {
        name: 'domainId',
        type: 'object',
        label: domainLabel,
        textField: 'name',
        valueField: 'id',
        lovCode: 'DOMAIN',
        transformRequest: (value) => {
          return value?.id ? `${value?.id}` : null;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            name: (+value) === 0 ? `${global}` : data.domainName,
          };
        },
      },
      { name: 'domainName', type: 'string', label: domainLabel },
      { name: 'personName', type: 'string', label: manager },
      { name: 'companyName', type: 'string', label: company },
      { name: 'parentUnitName', type: 'string', label: parent },
      { name: 'enabledFlag', type: 'boolean', label: enabledFlag },
    ],
    queryFields: [
      { name: 'name', type: 'string', label: unitName },
      { name: 'description', type: 'string', label: description },
      { name: 'companyName', type: 'string', label: company },
      { name: 'personName', type: 'string', label: manager },
      { name: 'domainName', type: 'string', label: domainLabel },
      { name: 'enabledFlag', type: 'string', label: enabledFlag, options: enabledFlagDs },
    ],
  };
};

export default BusinessUnitDataSet;
