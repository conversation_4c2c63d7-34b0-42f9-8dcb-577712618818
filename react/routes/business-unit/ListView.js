import React, { useContext, useState, useMemo, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { TabPage, Content, axios } from '@yqcloud/apps-master';
import uniq from 'lodash/uniq';
import { ClickText, TableHoverAction, TableStatus, Button as YQButton, ModalTitle } from '@zknow/components';
import { Table, Icon, Modal, Spin, message } from 'choerodon-ui/pro';
import QueryBar from '@/components/query-bar';
import BatchButton from '@/components/batch-button';
import { AddForm, EditForm } from './form';
import Store from './stores';

const { Column } = Table;
const modalKey = Modal.key();

const BusinessUnit = () => {
  const {
    intl, intlPrefix, businessUnitDataSet, businessUnitDetailDataSet, businessUnitSelectDataSet,
    tenantId, AppState: { currentMenuType: { domainId, domainName } },
  } = useContext(Store);
  const [isSearch, setIsSearch] = useState(false);

  useEffect(() => {
    if (businessUnitDataSet.status === 'ready') {
      businessUnitSelectDataSet.query();
    }
  }, [businessUnitDataSet.status]);

  function openModal(type) {
    const dynamicProps = {
      create: {
        title: intl.formatMessage({ id: 'iam.businessUnit.desc.business.unit.create', defaultMessage: '新建业务部门' }),
        style: { width: '8rem', minHeight: 186, maxHeight: 688 },
      },
      edit: {
        style: { width: '5.2rem' },
      },
    };

    Modal.open({
      title: <ModalTitle title={intl.formatMessage({ id: 'zknow.common.model.department', defaultMessage: '部门' })} dataSet={businessUnitDataSet} />,
      children: type === 'create' ? (
        <AddForm dataSet={businessUnitDataSet} intl={intl} intlPrefix={intlPrefix} />
      ) : (
        <EditForm
          dataSet={businessUnitDetailDataSet}
          listDataSet={businessUnitDataSet}
          intl={intl}
          intlPrefix={intlPrefix}
          tenantId={tenantId}
        />
      ),
      key: modalKey,
      destroyOnClose: true,
      movable: true,
      drawer: type === 'edit',
      ...dynamicProps[type],
    });
  }

  function handleCreate({ record }) {
    const parentId = record ? record.get('id') : null;
    const name = record?.get('name') || null;
    let newUnit = {
      parentId,
      parentUnitName: name,
    };
    if (domainId) {
      newUnit = {
        ...newUnit,
        domainEnabledFlag: true,
        domainId,
        domainName,
      };
    }
    businessUnitDataSet.current = businessUnitDataSet.create(newUnit);
    openModal('create');
  }

  function handleModify(record) {
    const id = record.get('id');
    businessUnitDetailDataSet.removeAll();
    businessUnitDetailDataSet.setQueryParameter('id', id);
    businessUnitDetailDataSet.query().then(() => {
      businessUnitDetailDataSet.current = businessUnitDetailDataSet.get(0);
    });
    openModal('edit');
  }

  function renderCreate() {
    return (
      <YQButton funcType="raised" color="primary" onClick={handleCreate} icon="plus">
        {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
      </YQButton>
    );
  }

  function handleChangeEnabledFlag({ batch, enabledFlag, disabled }) {
    if (disabled) {
      return false;
    }
    const requestData = batch ? businessUnitDataSet.selected.map(r => r.toData()) : [businessUnitDataSet.current?.toData()];
    axios.put(`/iam/yqc/${tenantId}/business_unit/enable_or_invalid?enable_flag=${enabledFlag}`, requestData).then(res => {
      if (res && res.failed) {
        message.error(res.message);
      } else {
        message.success(intl.formatMessage({ id: 'zknow.common.success.update', defaultMessage: '更新成功' }));
        businessUnitDataSet.query();
      }
    }).catch(e => {
      message.error(e.message);
    });
  }

  function renderBatchAction() {
    const items = [
      {
        icon: 'icon-Expires',
        text: intl.formatMessage({ id: 'iam.common.desc.batch.disabled', defaultMessage: '批量失效' }),
        disabled: uniq(businessUnitDataSet.selected.map(r => r.get('enabledFlag'))).includes(false),
        handleClick: () => handleChangeEnabledFlag({ batch: true, enabledFlag: false, disabled: uniq(businessUnitDataSet.selected.map(r => r.get('enabledFlag'))).includes(false) }),
      },
      {
        icon: 'icon-read',
        text: intl.formatMessage({ id: 'iam.common.desc.batch.enabled', defaultMessage: '批量生效' }),
        disabled: uniq(businessUnitDataSet.selected.map(r => r.get('enabledFlag'))).includes(true),
        handleClick: () => handleChangeEnabledFlag({ batch: true, enabledFlag: true, disabled: uniq(businessUnitDataSet.selected.map(r => r.get('enabledFlag'))).includes(true) }),
      },
    ];
    return <BatchButton text={intl.formatMessage({ id: 'zknow.common.button.batch.action', defaultMessage: '批量操作' })} dataSet={businessUnitDataSet} items={items} />;
  }

  const buttons = useMemo(() => [
    renderCreate(),
    // renderBatchAction(),
  ], [businessUnitDataSet.selected.length]);

  function renderQueryBar(props) {
    const { queryDataSet } = props;
    function handleSearch() {
      if (queryDataSet.current?.get('param')) {
        setIsSearch(true);
      } else {
        setIsSearch(false);
      }
      businessUnitDataSet.query();
    }
    return (
      <QueryBar
        dataSet={businessUnitDataSet}
        queryDataSet={queryDataSet}
        name="param"
        handleSearch={handleSearch}
        placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
        buttons={buttons}
      />
    );
  }

  function renderName({ record }) {
    return <ClickText record={record} onClick={() => handleModify(record)} valueField="name" />;
  }

  // FIXME: 需要重构，不清楚为什么不用组件自带的treeAsync
  async function handleLoadSub({ record, dataSet }) {
    const urlPrefix = '/iam/yqc';
    const parentId = record.get('id');
    if (!record.children) {
      record.setState('loading', true);
      return axios.get(`${urlPrefix}/${tenantId}/business_unit/query_by_parent_id?parentId=${parentId}`).then(res => {
        if (!res?.failed) {
          res.forEach(o => {
            if (o.parentId === '0') {
              o.parentId = null;
            }
            o.frontParentId = o.parentId;
          });
          dataSet.appendData(res);
          record.setState('loading', false);
        } else {
          message.error(res?.message);
        }
      });
    }
  }

  function renderAction({ record }) {
    const enabledFlag = record?.get('enabledFlag');
    let actions = [];
    if (!enabledFlag) {
      const parentId = record.get('parentId');
      const realParentId = parentId && typeof parentId === 'object' ? parentId.id : parentId;
      if (realParentId !== '0' && realParentId) {
        // 只有父级生效时，子级才可以生效
        const parent = record.dataSet.find(unit => unit.get('id') === realParentId);
        if (parent && parent.get('enabledFlag')) {
          actions = [
            {
              name: intl.formatMessage({ id: 'zknow.common.action.make.valid', defaultMessage: '生效' }),
              icon: 'icon-read',
              onClick: () => handleChangeEnabledFlag({ batch: false, enabledFlag: true }),
            },
          ];
        }
      } else {
        // 顶级不做限制
        actions = [
          {
            name: intl.formatMessage({ id: 'zknow.common.action.make.valid', defaultMessage: '生效' }),
            icon: 'icon-read',
            onClick: () => handleChangeEnabledFlag({ batch: false, enabledFlag: true }),
          },
        ];
      }
    } else {
      actions = [
        {
          name: intl.formatMessage({ id: 'iam.businessUnit.desc.business.unit.create.sub', defaultMessage: '新建子业务部门' }),
          icon: 'add-one',
          onClick: async () => handleCreate({ record }),
        },
        {
          name: intl.formatMessage({ id: 'zknow.common.action.make.invalid', defaultMessage: '失效' }),
          icon: 'icon-Expires',
          onClick: () => handleChangeEnabledFlag({ batch: false, enabledFlag: false }),
        },
      ];
    }
    return <TableHoverAction record={record} actions={actions} intlBtnIndex={2} />;
  }

  const dynamicTableProps = isSearch ? {} : {
    treeLoadData: handleLoadSub,
  };

  const renderDomainName = ({ record }) => {
    return (+(record.get('domainId')?.id)) === 0 ? intl.formatMessage({ id: 'iam.common.model.global', defaultMessage: '全局' }) : record.get('domainName');
  };

  return (
    <TabPage>
      <Content style={{ padding: 0 }}>
        <Table
          dataSet={businessUnitDataSet}
          mode="tree"
          onRow={({ record: current }) => ({
            isLeaf: current.get('leaf'),
          })}
          autoHeight
          autoLocateFirst={false}
          {...dynamicTableProps}
          buttons={buttons}
          queryBarProps={{
            title: intl.formatMessage({ id: 'iam.businessUnit.desc.menu.title.business.unit', defaultMessage: '业务部门' }),
          }}
        >
          <Column name="name" renderer={renderName} />
          <Column name="companyName" tooltip="overflow" />
          <Column name="personName" width={160} />
          <Column name="domainName" renderer={renderDomainName} width={180} tooltip="overflow" />
          <Column name="description" tooltip="overflow" />
          <Column name="enabledFlag" width={160} renderer={({ value }) => <TableStatus status={value} />} />
          <Column renderer={({ record }) => renderAction({ record })} width={10} />
        </Table>
      </Content>
    </TabPage>
  );
};

export default observer(BusinessUnit);
