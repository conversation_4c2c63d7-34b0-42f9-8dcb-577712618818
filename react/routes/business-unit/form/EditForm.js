import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { axios } from '@yqcloud/apps-master';
import { Button } from '@zknow/components';
import { Form, Output, TextField, TextArea, Select, TreeSelect, Spin, Lov, CheckBox } from 'choerodon-ui/pro';

const EditForm = (props) => {
  const { dataSet, intl, tenantId, listDataSet, modal } = props;
  const [editable, setEditable] = useState(false);
  const [excludeParent, setExcludeParent] = useState([]);

  useEffect(() => {
    if (dataSet.current?.get('id')) {
      axios.get(`/iam/yqc/${tenantId}/business_unit/query_subs/${dataSet.current?.get('id')}`).then(res => {
        setExcludeParent(res);
      });
    }
  }, [dataSet.current?.get('id')]);

  useEffect(() => {
    modal.update({
      footer: (okBtn) => {
        if (editable) {
          return [
            okBtn,
            <Button onClick={handleCancel} funcType="raised">{intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}</Button>,
          ];
        } else {
          return [
            <Button icon="write" color="primary" onClick={() => setEditable(true)} funcType="raised">
              { intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' }) }
            </Button>,
          ];
        }
      },
    });
  }, [editable]);

  async function handleOk() {
    const validate = dataSet?.dirty;
    if (validate) {
      const res = await dataSet.submit();
      if (res) {
        listDataSet.query();
      } else {
        setEditable(false);
        return false;
      }
    } else {
      setEditable(false);
      return false;
    }
  }

  function handleCancel() {
    dataSet.reset();
    setEditable(false);
  }

  modal.handleOk(handleOk);
  modal.handleCancel(handleCancel);

  const form = <Form disabled={!editable} dataSet={dataSet} labelLayout="horizontal" labelAlign="right" labelWidth="auto" columns={1}>
    <TextField name="name" autoFocus />
    <Lov name="managerId" />
    <Lov name="parentId" renderer={() => {}} optionsFilter={(record) => !excludeParent.includes(record.get('id'))} />
    <Lov name="companyId" />
    <CheckBox name="domainEnabledFlag" />
    <Lov name="domainId" disabled={!dataSet?.current?.get('domainEnabledFlag')} />
    <TextArea name="description" newLine colSpan={2} rows={1} autoSize={{ minRows: 1, maxRows: 4 }} resize="height" />
  </Form>;
  const loading = (
    <div style={{ width: '100%', height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
      <Spin loading />
    </div>
  );

  return dataSet.status === 'loading' ? loading : form;
};

export default observer(EditForm);
