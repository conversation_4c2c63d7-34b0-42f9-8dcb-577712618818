import React from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, TextArea, Select, TreeSelect, Lov, CheckBox } from 'choerodon-ui/pro';

const AddForm = (props) => {
  const { dataSet, modal } = props;

  async function handleOk() {
    return dataSet.submit()
      .then(() => dataSet.query())
      .catch(() => dataSet.query());
  }

  function handleCancel() {
    dataSet.delete(dataSet.current, false);
  }

  modal.handleOk(handleOk);
  modal.handleCancel(handleCancel);

  return (
    <Form dataSet={dataSet} labelLayout="horizontal" labelAlign="right" labelWidth="auto" columns={2}>
      <TextField name="name" autoFocus />
      <Lov name="managerId" />
      <Lov name="parentId" renderer={() => { }} />
      <Lov name="companyId" />
      <CheckBox name="domainEnabledFlag" />
      <Lov name="domainId" disabled={!dataSet?.current?.get('domainEnabledFlag')} />
      <TextArea name="description" newLine colSpan={2} rows={1} autoSize={{ minRows: 1, maxRows: 4 }} resize="height" />
    </Form>
  );
};

export default observer(AddForm);
