import React, { useContext, useMemo, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import queryString from 'query-string';
import { Content, TabPage, axios } from '@yqcloud/apps-master';
import { message, Table } from 'choerodon-ui/pro';
import { ClickText, TableHoverAction, Button, useFilter } from '@zknow/components';
import Store from './stores';
import './index.less';

const { Column } = Table;

function Location() {
  const { intl, locationDS, prefixCls, tenantId, history, match, intlPrefix } = useContext(Store);
  const urlPrefix = '/iam';

  useFilter(locationDS);

  // 操作栏
  function renderAction({ record }) {
    const status = record?.get('status');
    return (
      <TableHoverAction
        record={record}
        actions={[
          {
            name: intl.formatMessage({ id: 'iam.location.action.subCreate', defaultMessage: '创建子位置' }),
            icon: 'add-one',
            onClick: async () => handleCreate({ type: 'createSub', record }),
          },
          {
            name: status === '0' ? intl.formatMessage({ id: 'iam.common.make.valid', defaultMessage: '生效' }) : intl.formatMessage({ id: 'iam.common.make.invalid', defaultMessage: '失效' }),
            icon: status === '0' ? 'icon-read' : 'icon-Expires',
            onClick: () => handleDisableLocation(record, status),
          },
        ]}
      />
    );
  }

  // 渲染状态
  const renderStatus = ({ record }) => (
    <span
      name={renderStatusDot(record?.toData()?.status)}
      className="location-status"
    >
      {renderStatusText(record?.toData()?.status)}
    </span>
  );

  // 渲染状态的点点
  const renderStatusDot = (value) => {
    switch (value) {
    case '0':
      return 'DISABLED';
    case '1':
      return 'ENABLED';
    default:
      break;
    }
  };

  // 渲染状态的值
  const renderStatusText = (value) => {
    switch (value) {
    case '0':
      return intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' });
    case '1':
      return intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' });
    default:
      break;
    }
    return '';
  };

  // 失效位置
  const handleDisableLocation = async (record, currentStatus) => {
    const data = record.toData();
    let flag = false;
    if (currentStatus === '0') {
      flag = true;
    } else {
      flag = false;
    }
    const array = [];
    array.push(data.id);
    const res = await axios.put(`${urlPrefix}/yqc/${tenantId}/location/invalid?enabled_flag=${flag}`, array);
    if (res?.failed) {
      message.error(res.message);
    } else {
      message.success(intl.formatMessage({ id: 'iam.common.success.put', defaultMessage: '更新成功' }));
      locationDS.query();
    }
  };

  function renderCreateBtn() {
    return (
      <Button funcType="raised" icon="Plus" color="primary" onClick={() => handleCreate({ type: 'create' })}>
        {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '创建' })}
      </Button>
    );
  }

  // 头部按钮位置
  const buttons = useMemo(() => (
    [
      renderCreateBtn(),
    ]
  ), []);

  function handleCreate({ record, type }) {
    const search = queryString.parse(history.location?.search);
    if (type === 'create') {
      search.createType = 'create';
      search.parentId = undefined;
      search.parentName = undefined;
      locationDS.create();
      history.push({
        pathname: `${match.path}/create`,
        search: queryString.stringify(search),
      });
    }
    if (type === 'createSub') {
      search.createType = 'createSub';
      search.parentId = record.get('id');
      search.parentName = record.get('locationName');
      locationDS.create();
      history.push({
        pathname: `${match.path}/create`,
        search: queryString.stringify(search),
      });
    }
  }

  function renderName({ record }) {
    return (
      <ClickText
        record={record}
        history={history}
        path={`${match.path}/${record?.get('id')}`}
        valueField="locationName"
      />
    );
  }

  return (
    <TabPage className="enterprise-location-main">
      <Content className="location-content" style={{ padding: 0 }}>
        <Table
          mode="tree"
          dataSet={locationDS}
          treeAsync
          onRow={({ record }) => ({
            isLeaf: record.get('isLeaf'),
          })}
          autoHeight
          autoLocateFirst={false}
          buttons={buttons}
          queryBarProps={{
            title: intl.formatMessage({ id: 'iam.location.title', defaultMessage: '位置' }),
          }}
        >
          <Column name="locationName" width={280} renderer={renderName} tooltip="overflow" />
          <Column name="roomNumber" />
          <Column name="addressDetail" width={300} tooltip="overflow" />
          <Column name="domainName" tooltip="overflow" />
          {/* 不知道有没有用❓ <Column name="contact" /> */}
          <Column width={100} name="status" renderer={({ record }) => renderStatus({ record })} />
          <Column width={10} renderer={({ record }) => renderAction({ record })} tooltip="none" />
        </Table>
      </Content>
    </TabPage>
  );
}

export default observer(Location);
