import React, { useState, useEffect, useMemo, useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { TreeSelect } from 'choerodon-ui';
import { axios } from '@yqcloud/apps-master';
import classNames from 'classnames';
import { availableTree } from '@/utils/index.js';
import './index.less';

const TreeNode = TreeSelect.TreeNode;

function LocationSelect(props) {
  // 树形结构数据
  const { name, record, currentValue, tenantId, defaultValue, placeholder } = props;
  const [isInitDone, setIsInitDone] = useState(false);
  const [locationTreeData, setLocationTreeData] = useState([]);
  const [treeListData, setTreeListData] = useState([]);
  const [disabledList, setDisabledList] = useState([]);
  const [selectValue, setSelectValue] = useState(defaultValue === '0' ? undefined : defaultValue);
  const urlPrefix = '/iam';

  const getLocationData = async () => {
    const res = await axios.get(`${urlPrefix}/yqc/${tenantId}/location?enabled_flag=true`);
    const filterRes = res?.filter((item) => item.status === '1' || item.status === 1);
    const treeData = availableTree('id', 'parentId', filterRes);
    setTreeListData(res);
    setLocationTreeData(treeData);
  };

  const getDisabledLocationData = async () => {
    if (currentValue) {
      const res = await axios.get(`${urlPrefix}/yqc/${tenantId}/location/${currentValue}/children?enabled_flag=true`);
      if (res?.failed) {
        setDisabledList([]);
      } else {
        setDisabledList(res);
      }
    }
  };

  useEffect(() => {
    getLocationData();
    getDisabledLocationData();
  }, [isInitDone]);

  useEffect(() => {
    if (defaultValue || defaultValue === 0 || defaultValue === '0') {
      getLocationInfoByLocationId(defaultValue, treeListData);
    }
  }, [treeListData]);

  const getLocationInfoByLocationId = (id, data = []) => {
    const b = data.filter((i) => i.id === id)[0];
    if (b) {
      setSelectValue(b.locationName);
    }
    return b;
  };

  const onChange = (value) => {
    record.set(name, value?.toString());
    setSelectValue(value);
  };

  /**
   * @description:
   * @param {*} parentId 父级ID
   * @param {*} beAddData 要被添加的数据
   * @param {*} mapData 初始化数据,要循环遍历的
   * @return {*}
   */
  const addTreeNode = (parentId, beAddData, mapData) => {
    mapData.forEach((item, index) => {
      // 如果有子节点
      if (item?.children?.length > 0) {
        addTreeNode(parentId, beAddData, item.children);
      } else if (item.id === parentId) {
        item.children = beAddData;
      }
    });
  };

  const renderTreeNode = (item) => {
    if (Array.isArray(item.children) && item.children?.length > 0) {
      return (
        <TreeNode
          key={item.id}
          dataRef={item}
          title={renderTreeNodeTitle(item)}
          isLeaf={false}
          disabled={disabledList?.includes(item.id)}
        >
          {item.children.map(renderTreeNode)}
        </TreeNode>
      );
    } else {
      return (
        <TreeNode
          key={item.id}
          dataRef={item}
          isLeaf={item?.leaf}
          title={renderTreeNodeTitle(item)}
          disabled={disabledList?.includes(item.id)}
        />
      );
    }
  };

  const mainClassName = classNames({
    'enterprise-fv-location-select-main': true,
    [props.className]: true,
  });

  return (
    <div className={mainClassName}>
      <TreeSelect
        className="enterprise-fv-location-select"
        dropdownClassName="enterprise-fv-location-select-dropdown"
        style={{ width: '100%' }}
        value={selectValue}
        dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
        onChange={onChange}
        allowClear
        placeholder={placeholder}
      >
        {locationTreeData?.map(renderTreeNode)}
      </TreeSelect>
    </div>
  );
}

// 渲染企业信息
const renderTreeNodeTitle = (data) => data?.locationName;

export default observer(LocationSelect);
