/*
 * @Author: x<PERSON><PERSON>ya
 * @Date: 2021-03-04 17:12:49
 * @Description:
 */

import React, { useState, useEffect, useMemo, useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, Select, NumberField, Output, message, CheckBox, Lov } from 'choerodon-ui/pro';
import { Content, TabPage, Header, axios } from '@yqcloud/apps-master';
import { Button } from '@zknow/components';
import { getQueryString } from '@/utils/index.js';

import Store from '../stores';
import './FormView.less';

const labelWidth = 100;

// FIXME: 需要重构，这个页面的写法一言难尽
const CreateView = (props) => {
  const { history, match } = props;
  const { intl, intlPrefix, tenantId, locationEditDS, AppState: { currentMenuType: { domainId, domainName } } } = useContext(Store);
  const [editable, setEditable] = useState(false);

  const { search } = history.location;
  const urlPrefix = '/iam';
  const createType = getQueryString('createType');

  useEffect(() => {
    if (match?.params?.id === 'create') {
      let newLocation = {};
      if (domainId) {
        newLocation = {
          // domainManageFlag: true,
          domainId,
          domainName,
        };
      }
      if (createType === 'createSub') {
        newLocation = {
          ...newLocation,
          parentId: getQueryString('parentId'),
          parentName: getQueryString('parentName') || undefined,
        };
        locationEditDS.create(newLocation);
      } else {
        locationEditDS.create(newLocation);
      }
      setEditable(true);
    } else {
      const res = getLocationInfoById(match?.params?.id);
      if (res) {
        locationEditDS.getField('parentId').set('lovPara', { excludeIds: match?.params?.id });
        setEditable(false);
      }
    }
  }, [locationEditDS?.status]);

  const getLocationInfoById = async (id) => {
    const res = await axios.get(`${urlPrefix}/yqc/${tenantId}/location/detail?location_id=${id}`);
    await locationEditDS.loadData([res]);
    return res;
  };

  // 返回表格页
  const backHome = () => {
    const urlSearch = history.location?.search?.replace('&createType=create', '')?.replace('&createType=edit', '');
    const defaultSearch = `tenantId=${getQueryString('tenantId')}&type=${getQueryString('type')}`;
    history.push({
      pathname: `${match.path}`.replace('/:id', ''),
      search: defaultSearch,
    });
  };

  /* handleSave */
  const handleSave = async () => {
    try {
      const res = await locationEditDS.validate();
      if (res) {
        const updateRes = await locationEditDS.submit();
        if (updateRes?.success) {
          setEditable(false);
          await getLocationInfoById(updateRes?.content[0]?.id);
        } else {
          setEditable(false);
        }
      } else {
        message.error(intl.formatMessage({ id: 'iam.common.validation.required', defaultMessage: '请填写必填项' }));
        // setEditable(false);
        return false;
      }
    } catch (err) {
      return false;
    }
  };

  // 渲染按钮
  const buttons = useMemo(() => {
    if (editable) {
      return (
        <div>
          <Button funcType="raised" color="primary" onClick={() => handleSave()}>{intl.formatMessage({ id: 'zknow.common.button.ok', defaultMessage: '确定' })}</Button>
          <Button
            funcType="raised"
            color="default"
            onClick={() => {
              setEditable(false);
              if (createType === 'create' || createType === 'createSub') {
                backHome();
              } else {
                locationEditDS.reset();
              }
            }}
          >
            {intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}
          </Button>
        </div>
      );
    }
    return (
      <div>
        <Button
          funcType="raised"
          color="primary"
          icon="Write"
          onClick={() => setEditable(true)}
        >
          {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '保存' })}
        </Button>
        <Button
          funcType="raised"
          color="secondary"
          icon="icon-Expires"
          onClick={() => handleDisabled()}
        >
          {intl.formatMessage({ id: 'zknow.common.action.make.invalid', defaultMessage: '失效' })}
        </Button>
      </div>
    );
  }, [editable]);

  const handleDisabled = async () => {
    const data = locationEditDS?.current?.toData();
    const array = [];
    const flag = false;
    array.push(data.id);
    const res = await axios.put(`${urlPrefix}/yqc/${tenantId}/location/invalid?enabled_flag=${flag}`, array);
    if (res?.failed) {
      message.error(res.message);
    } else {
      backHome();
    }
  };

  // 渲染表单内容
  const renderFormContent = () => {
    return (
      <React.Fragment>
        <Form
          disabled={!editable}
          header={intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' })}
          dataSet={locationEditDS}
          columns={2}
          labelLayout="horizontal"
          labelWidth="90"
        >
          <TextField colSpan={1} name="locationName" className="field-content" autoFocus />
          <TextField colSpan={1} name="roomNumber" className="field-content" restrict="_|A-Z|a-z|0-9" />
          <Lov name="parentId" />
          <Select searchable colSpan={1} name="functionalUse" className="field-content" />
          <Select searchable colSpan={1} name="type" className="field-content" />
          <NumberField colSpan={1} name="capacity" className="field-content" />
          {/* <CheckBox colSpan={1} name="domainManageFlag" />
          <Lov colSpan={1} disabled={!locationEditDS?.current?.get('domainManageFlag')} name="domainId" /> */}
        </Form>
        <Form
          disabled={!editable}
          header={intl.formatMessage({ id: 'iam.location.model.geographical', defaultMessage: '地理位置' })}
          dataSet={locationEditDS}
          columns={2}
          labelLayout="horizontal"
          labelWidth="90"
          className="yq-mt-16"
        >
          <Lov searchable colSpan={1} name="countryId" className="field-content" />
          <TextField colSpan={1} name="city" className="field-content" />
          <TextField colSpan={1} name="addressDetail" className="field-content" />
          <Select searchable colSpan={1} name="timeZone" className="field-content" />
          <NumberField colSpan={1} name="longtitude" className="field-content" />
          <NumberField colSpan={1} name="latitude" className="field-content" />
        </Form>
        <Form
          disabled={!editable}
          header={intl.formatMessage({ id: 'iam.location.desc.contactInformation', defaultMessage: '联系信息' })}
          dataSet={locationEditDS}
          columns={2}
          labelLayout="horizontal"
          labelWidth="90"
          className="yq-mt-16"
        >
          <Lov name="managerId" />
          <TextField colSpan={1} name="telephone" className="field-content" />
          <TextField colSpan={1} name="email" className="field-content" />
        </Form>
      </React.Fragment>
    );
  };

  const renderTitle = () => {
    if (match?.params?.id === 'detail') {
      return intl.formatMessage({ id: 'iam.location.desc.detail', defaultMessage: '位置详情' });
    } else if (match?.params?.id === 'create') {
      return intl.formatMessage({ id: 'iam.location.action.create', defaultMessage: '新建位置' });
    } else {
      return intl.formatMessage({ id: 'iam.location.action.edit', defaultMessage: '编辑位置' });
    }
  };

  return (
    <TabPage>
      <Header backPath={`/iam/location${search}`} dataSet={locationEditDS}>
        <h1>{renderTitle()}</h1>
        <div>
          {buttons}
        </div>
      </Header>
      <Content>
        <div className="location-create-main">
          {renderFormContent()}
        </div>
      </Content>
    </TabPage>
  );
};

export default observer(CreateView);
