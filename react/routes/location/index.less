@import '~choerodon-ui/dist/choerodon-ui.less';
@import '~choerodon-ui/lib/style/themes/default';

.enterprise-location-main {
  // padding: 16px;
  .location-content {
    padding: 24px;
    background-color: #fff;
    height: 100%;
    box-shadow: 0 1px 5px 0 rgba(38, 38, 38, 0.1);
    border-radius: 4px;
    .location-status[name='DISABLED']::before {
      content: "";
      width: 6px;
      height: 6px;
      border-radius: 50%;
      display: inline-block;
      margin: 0 8px 0 1px;
      background: #000;
      opacity: 0.25;
      position: relative;
      bottom: 1px;
    }
    .location-status[name='ENABLED']::before {
      content: "";
      width: 6px;
      height: 6px;
      border-radius: 50%;
      display: inline-block;
      margin: 0 8px 0 1px;
      background: #75c940;
      position: relative;
      bottom: 1px;
    }
  }
}

.enterprise-location-create-modal {
  width: 548px;
}
