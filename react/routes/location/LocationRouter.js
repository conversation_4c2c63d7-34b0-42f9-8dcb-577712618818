/*
 * @Author: x<PERSON>ore<PERSON>
 * @Date: 2021-03-04 16:51:13
 * @Description:
 */

import React from 'react';
import { Route, Switch } from 'react-router-dom';
import { nomatch } from '@yqcloud/apps-master';
import { asyncRouter } from '@zknow/utils';
import { StoreProvider } from './stores';

const Location = asyncRouter(() => import('./index'));
const Create = asyncRouter(() => import('./create/index'));

function LocationRouter({ match }) {
  return (
    <StoreProvider>
      <Switch>
        <Route path={`${match.url}`} component={Location} exact />
        <Route path={`${match.url}/:id`} component={Create} exact />
        <Route path="*" component={nomatch} />
      </Switch>
    </StoreProvider>
  );
}

export default LocationRouter;
