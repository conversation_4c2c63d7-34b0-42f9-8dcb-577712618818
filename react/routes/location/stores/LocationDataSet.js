import { axios } from '@yqcloud/apps-master';
import { getQueryParams } from '@zknow/utils';

export default ({ intlPrefix, intl, tenantId, autoLocateFirst }) => {
  const urlPrefix = '/iam';
  const locationName = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const roomNumber = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const detailLocation = intl.formatMessage({ id: 'iam.location.model.detailLocation', defaultMessage: '详细地址' });
  const managerId = intl.formatMessage({ id: 'iam.location.model.contact', defaultMessage: '联系人' });
  const status = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const functionalUse = intl.formatMessage({ id: 'iam.location.model.functionalUse', defaultMessage: '用途' });
  const faxNumber = intl.formatMessage({ id: 'iam.location.model.faxNumber', defaultMessage: '传真' });
  const parentLocation = intl.formatMessage({ id: 'iam.location.model.parentLocation', defaultMessage: '上级位置' });
  const type = intl.formatMessage({ id: 'zknow.common.model.type', defaultMessage: '类型' });
  const countryOrRegion = intl.formatMessage({ id: 'iam.location.model.countryOrRegion', defaultMessage: '国家或区域' });
  const locationCapacity = intl.formatMessage({ id: 'iam.location.model.locationCapacity', defaultMessage: '位置容量' });
  // const costCenter = intl.formatMessage({ id: `iam.location.model.costCenter` });
  const city = intl.formatMessage({ id: 'iam.location.model.city', defaultMessage: '城市' });
  const longtitude = intl.formatMessage({ id: 'iam.location.model.longtitude', defaultMessage: '经度' });
  const latitude = intl.formatMessage({ id: 'iam.location.model.latitude', defaultMessage: '纬度' });
  const tz = intl.formatMessage({ id: 'iam.location.model.timeZone', defaultMessage: '时区' });
  const telephone = intl.formatMessage({ id: 'iam.location.model.telephone', defaultMessage: '联系人电话' });
  const email = intl.formatMessage({ id: 'iam.location.model.email', defaultMessage: '联系人邮箱' });
  const addressDetail = intl.formatMessage({ id: 'iam.location.model.detailLocation', defaultMessage: '详细地址' });
  const domain = intl.formatMessage({ id: 'iam.location.model.domain', defaultMessage: '域' });
  // const domainManageFlag = intl.formatMessage({ id: 'iam.common.model.domain.manager', defaultMessage: '域管理' });
  const global = intl.formatMessage({ id: 'iam.common.desc.global', defaultMessage: '全局' });

  return {
    autoQuery: false,
    selection: false,
    paging: 'server',
    idField: 'id',
    parentField: 'frontParentId',
    // expandField: 'expand',
    autoLocateFirst,
    primaryKey: 'id',
    transport: {
      read: ({ data, params }) => {
        const { frontParentId } = data;
        let searchFlag = false;
        const postData = getQueryParams(data, ['frontParentId']);
        Object.keys(postData).forEach((v) => {
          if (v.indexOf('search_') !== -1) {
            searchFlag = true;
          }
        });
        const queryParams = frontParentId && frontParentId !== 0
          ? {
            page: 0,
            size: 999,
          } : params;
        return {
          url: `${urlPrefix}/yqc/${tenantId}/location/page?${searchFlag ? '' : `parentId=${frontParentId || 0}`}`,
          method: 'get',
          data: postData,
          params: queryParams,
          transformResponse: (resp) => {
            try {
              const respData = JSON.parse(resp);
              respData.content.forEach((v) => {
                if (v.parentId === '0') {
                  v.parentId = null;
                  v.frontParentId = null;
                } else {
                  v.frontParentId = v.parentId;
                }
              });
              return respData;
            } catch {
              return resp;
            }
          },
        };
      },
      create: ({ data: [data] }) => ({
        url: `${urlPrefix}/yqc/${tenantId}/location`,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => ({
        url: `${urlPrefix}/yqc/${tenantId}/location`,
        method: 'put',
        data,
      }),
      destroy: ({ data: [data] }) => ({
        url: `${urlPrefix}/yqc/${tenantId}/location`,
        method: 'delete',
        data,
      }),
    },
    fields: [
      { name: 'id', type: 'string', label: 'ID' },
      // 名称
      {
        name: 'locationName',
        type: 'string',
        label: locationName,
        required: true,
        isIntl: true,
        validator: async (value, fieldName, record) => {
          const id = record.get('id');
          const res = await axios.get(`${urlPrefix}/yqc/${tenantId}/location/check/location_name?location_name=${value}${id ? `&location_id=${id}` : ''}`);
          if (res) {
            return true;
          }
          return intl.formatMessage({ id: 'iam.location.validation.name', defaultMessage: '位置重复' });
        },
      },
      // 位置编码
      {
        name: 'roomNumber',
        type: 'string',
        label: roomNumber,
        required: true,
        format: 'uppercase',
        validator: async (value, fieldName, record) => {
          const reg = /^[0-9A-Z_]{1,}$/;
          if (reg.test(value)) {
            const id = record.get('id');
            const res = await axios.get(`${urlPrefix}/yqc/${tenantId}/location/check/room_number?room_number=${value}${id ? `&location_id=${id}` : ''}`);
            if (res) {
              return true;
            }
            return intl.formatMessage({ id: 'iam.location.validation.code', defaultMessage: '编码重复' });
          }
          return intl.formatMessage({ id: 'iam.common.validate.message.code', defaultMessage: '编码只能由大写字母，数字和下划线构成' });
        },
      },
      // 详细信息
      { name: 'detailLocation', type: 'string', label: detailLocation },
      // // 上级位置
      // { name: 'parentId', type: 'string', label: parentLocation },
      {
        name: 'parentId',
        label: parentLocation,
        textField: 'locationName',
        defaultValue: undefined,
        valueField: 'id',
        type: 'object',
        lovCode: 'LOCATION',
        transformRequest: (value) => {
          return value?.id || 0;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            locationName: value === '0' ? global : data.parentName,
          };
        },
      },
      // 上级位置
      { name: 'parentName', type: 'string', label: parentLocation },
      // 用途
      { name: 'functionalUse', label: functionalUse, lookupCode: 'LOCATION_FUNCTIONAL_USE' },
      // 类型
      { name: 'type', type: 'string', label: type, lookupCode: 'LOCATION_TYPE' },
      // 位置容量
      { name: 'capacity', type: 'number', label: locationCapacity, min: 0, step: 1 },
      // 成本中心
      // { name: 'budgetHolder', type: 'string', label: costCenter },
      // 时区
      { name: 'timeZone', type: 'string', label: tz, lookupCode: 'TIME_ZONE' },
      // 状态
      { name: 'status', type: 'string', label: status },
      // 国家和地区
      {
        name: 'countryId',
        type: 'object',
        label: countryOrRegion,
        textField: 'name',
        valueField: 'id',
        lovCode: 'COUNTRY',
        transformRequest: (value) => {
          return value?.id || 0;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            name: data.countryName,
          };
        },
      },
      // 城市
      { name: 'city', type: 'string', label: city },
      // 详细地址
      { name: 'addressDetail', type: 'string', label: addressDetail },
      // 精度
      { name: 'longtitude', type: 'string', label: longtitude, max: 180.000000, min: -180.000000, step: 0.000001 },
      // 维度
      { name: 'latitude', type: 'string', label: latitude, max: 90.000000, min: -90.000000, step: 0.000001 },
      {
        name: 'managerId',
        label: managerId,
        textField: 'realName',
        valueField: 'id',
        type: 'object',
        lovCode: 'USER',
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            realName: data.managerName,
          };
        },
      },
      // 电话
      { name: 'telephone', type: 'string', label: telephone, disabled: true },
      // 传真
      { name: 'faxNumber', type: 'string', label: faxNumber, disabled: true },
      // 邮箱
      { name: 'email', type: 'string', label: email, disabled: true },
      // 版本号
      { name: 'objectVersionNumber' },
      { name: 'domainName', type: 'string', label: domain },
      // {
      //   name: 'domainId',
      //   type: 'object',
      //   label: domain,
      //   textField: 'name',
      //   valueField: 'id',
      //   lovCode: 'DOMAIN',
      //   transformRequest: (value) => {
      //     return value?.id ? `${value?.id}` : undefined;
      //   },
      //   transformResponse: (value, data) => {
      //     return {
      //       id: value,
      //       name: (+value) === 0 ? `${global}` : data.domainName,
      //     };
      //   },
      // },
      // {
      //   name: 'domainManageFlag',
      //   type: 'boolean',
      //   label: domainManageFlag,
      // },
    ],
    events: {
      update: ({ record, name, value }) => {
        // 联系人附带
        if (name === 'managerId') {
          record.set('telephone', value.phone);
          record.set('email', value.email);
        }
      },
    },
    queryFields: [
      { name: 'locationName', type: 'string', label: locationName },
      { name: 'roomNumber', type: 'string', label: roomNumber },
      { name: 'addressDetail', type: 'string', label: addressDetail },
      { name: 'domainName', type: 'string', label: domain },
    ],
  };
};
