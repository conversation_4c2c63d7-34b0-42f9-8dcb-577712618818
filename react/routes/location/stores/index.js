import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import LocationDataSet from './LocationDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: ['iam.common', 'iam.location'] })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: orgId } },
    } = props;
    const intlPrefix = 'location';
    const prefixCls = 'location';
    const tenantId = orgId;
    const locationDS = useMemo(() => new DataSet(LocationDataSet({ intlPrefix, intl, tenantId, autoLocateFirst: false })), []);
    const locationEditDS = useMemo(() => new DataSet(LocationDataSet({ intlPrefix, intl, tenantId, autoLocateFirst: true })), []);
    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      locationDS,
      locationEditDS,
      tenantId,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
