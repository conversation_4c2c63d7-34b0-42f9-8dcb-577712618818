export default ({ intlPrefix, intl, tenantId }) => {
  const urlPrefix = '/iam';
  const locationName = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  return {
    autoQuery: true,
    selection: false,
    paging: false,

    transport: {
      read: {
        url: `${urlPrefix}/v1/${tenantId}/persons`,
        method: 'get',
      },
    },
    fields: [
      { name: 'id', type: 'string', label: 'ID' },
      // 名称
      {
        name: 'locationName',
        type: 'string',
        label: locationName,
        required: true,
      },
    ],
  };
};
