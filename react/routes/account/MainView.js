import React, { useContext, Fragment, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Table, Modal, message } from 'choerodon-ui/pro';
import { Icon, TableHoverAction, ClickText, Button, YqTable, TableStatus, StatusTag, useFilter } from '@zknow/components';
import { axios, TabPage, Content } from '@yqcloud/apps-master';
import Store from './stores';
import CreateModal from './CreateModal';
import './index.less';

const { Column } = Table;

const modalKey = Modal.key();

const MainView = () => {
  const {
    prefixCls, intl, intlPrefix, tenantId, accountDataSet, userInfoDataSet,
    history, match, AppState: { currentMenuType: { domainId, domainName } },
  } = useContext(Store);

  useFilter(accountDataSet);

  const sourceTag = (userSourceText) => {
    if (userSourceText) {
      return (
        <span className="account-source">
          {userSourceText}
        </span>
      );
    }
    return null;
  };

  const renderName = ({ record, value }) => {
    const userSourceValue = record.get('userSource');
    const flag = record.get('invitationStatus');
    // const userSourceText = record.dataSet.getField('userSource').getText(userSourceValue);
    const userSourceText = intl.formatMessage({ id: 'iam.account.model.waiting.add', defaultMessage: '待加入' });
    return (
      <div className={`${prefixCls}-table-name-content`}>
        <span>
          <ClickText
            record={record}
            valueField="realName"
            history={history}
            path={`${match.url}/detail/${record?.get('id')}`}
          />
        </span>
        {flag !== 'inviting' ? <></> : sourceTag(userSourceText)}
      </div>
    );
  };

  const renderStatus = ({ record }) => {
    const flag = record.getPristineValue('enabled');
    return (
      <TableStatus
        status={flag}
        enabledText={intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' })}
        disabledText={intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' })}
      />
    );
  };

  const renderLocked = ({ record }) => {
    const flag = !record.getPristineValue('locked');
    if (flag) {
      return (
        <StatusTag
          name="flag"
          color="#7BC95A"
        >
          {intl.formatMessage({ id: 'iam.common.model.normal', defaultMessage: '正常' })}
        </StatusTag>
      );
    }
    return (
      <StatusTag
        name="flag"
        color="#F8353F"
      >
        {intl.formatMessage({ id: 'iam.common.model.locked', defaultMessage: '锁定' })}
      </StatusTag>
    );
  };

  const handleConfirmOk = (record) => {
    const successMsg = intl.formatMessage({ id: 'zknow.common.success.invalid', defaultMessage: '失效成功' });
    axios.post(`/iam/hzero/v1/${tenantId}/users/${record.get('id')}/frozen`).then((res) => {
      if (res) {
        message.success(successMsg);
      }
      accountDataSet.query();
    }).catch((err) => {
      message.error(err?.message);
    });
  };

  const openConfirmModal = (record) => {
    const content = intl.formatMessage({ id: 'iam.account.desc.moveout.confirm.message', defaultMessage: '失效后，帐号将无法访问该企业！' });
    Modal.confirm({
      title: intl.formatMessage({ id: 'iam.account.desc.moveout.confirm', defaultMessage: '确认失效?' }),
      children: (
        <div>
          {content}
        </div>
      ),
      onOk: () => handleConfirmOk(record),
      key: modalKey,
      destroyOnClose: true,
    });
  };

  const handleEnabled = (record) => {
    axios.post(`/iam/hzero/v1/${tenantId}/users/${record.get('id')}/unfrozen`).then((res) => {
      if (res) {
        message.success(intl.formatMessage({ id: 'zknow.common.success.valid', defaultMessage: '生效成功' }));
      }
      accountDataSet.query();
    }).catch((err) => {
      message.error(err?.message);
    });
  };

  async function handleUnlock(record) {
    record.set('locked', false);
    await accountDataSet.submit();
    await accountDataSet.query();
  }

  const getButtonObject = (record) => {
    const buttons = [];
    const flag = record.getPristineValue('enabled');
    const locked = record.getPristineValue('locked');
    const invitationStatus = record.get('invitationStatus');
    if (locked) {
      buttons.push(
        {
          name: intl.formatMessage({ id: 'iam.common.desc.unlock', defaultMessage: '解锁' }),
          icon: 'unlock',
          onClick: () => handleUnlock(record),
        }
      );
    }
    if (invitationStatus && invitationStatus !== 'joined') {
      buttons.push(
        {
          name: intl.formatMessage({ id: 'iam.account.desc.invitation', defaultMessage: '邀请' }),
          icon: 'share-one',
          onClick: () => handleInvite(record),
        }
      );
    }
    if (flag) {
      buttons.push(
        {
          name: intl.formatMessage({ id: 'zknow.common.action.make.invalid', defaultMessage: '失效' }),
          icon: 'icon-Expires',
          onClick: () => openConfirmModal(record),
        }
      );
    } else {
      buttons.push(
        {
          name: intl.formatMessage({ id: 'zknow.common.action.make.valid', defaultMessage: '生效' }),
          icon: 'icon-read',
          onClick: () => handleEnabled(record),
        }
      );
    }

    return buttons;
  };

  async function handleInvite(record) {
    try {
      const personId = record.get('id');
      const userEmail = record.get('email');

      const data = { personId, userEmail };
      const res = await axios.post(`iam/yqc/v1/${tenantId}/users/invite`, data);

      if (res.failed) {
        throw new Error(res.message);
      } else {
        message.success(intl.formatMessage({ id: 'iam.account.desc.invite.success', defaultMessage: '邀请成功' }));
      }
    } catch (err) {
      message.error(err?.message);
    }
    await accountDataSet.query();
  }

  const handleClickCreateBtn = () => {
    let newUser = {};
    if (domainId) {
      newUser = {
        domainEnabledFlag: true,
        domainId,
        domainName,
      };
    }
    userInfoDataSet.create(newUser);
    Modal.open({
      className: 'account-create-modal',
      title: intl.formatMessage({ id: 'iam.account.desc.create', defaultMessage: '新建人员' }),
      children: (
        <CreateModal
          userInfoDataSet={userInfoDataSet}
          accountDataSet={accountDataSet}
          intl={intl}
          intlPrefix={intlPrefix}
          prefixCls={prefixCls}
        />
      ),
      style: { width: '8rem' },
      closable: true,
      key: modalKey,
      destroyOnClose: true,
    });
  };

  const renderTableAction = ({ dataSet, record }) => {
    const flag = record.getPristineValue('enabled');
    const actionBtnConfig = getButtonObject(record);

    return (
      <TableHoverAction
        record={record}
        intlBtnIndex={1}
        actions={actionBtnConfig}
      />
    );
  };

  function handleModify(record) {
    const id = record.get('id');
    history.push({
      pathname: `${match.url}/detail/${id}`,
      search: history.location?.search,
    });
  }

  function renderCreate() {
    return (
      <Button funcType="raised" color="primary" className={`${prefixCls}-primary-button`} onClick={handleClickCreateBtn}>
        {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
      </Button>
    );
  }

  const tableButtons = useMemo(() => [
    renderCreate(),
  ], []);

  return (
    <TabPage>
      <Content className={`${prefixCls}-content`} style={{ padding: 0 }}>
        <Table
          labelLayout="float"
          pristine
          dataSet={accountDataSet}
          placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
          autoHeight
          autoLocateFirst={false}
          butttons={[tableButtons]}
          queryBarProps={{
            title: intl.formatMessage({ id: 'iam.common.model.menu.title.account', defaultMessage: '账号' }),
          }}
        >
          <Column name="realName" renderer={renderName} width={200} lock />
          <Column name="email" width={250} />
          <Column name="phone" />
          <Column name="companyName" tooltip="overflow" />
          <Column name="domainName" tooltip="overflow" />
          <Column name="departmentName" tooltip="overflow" />
          <Column name="locked" renderer={renderLocked} />
          <Column align="left" name="enabled" renderer={renderStatus} />
          <Column width={80} renderer={renderTableAction} />
        </Table>
      </Content>
    </TabPage>
  );
};

export default observer(MainView);
