export default ({ intlPrefix, intl, tenantId, childRolesDataSet, userGroupDataSet, domainsDataSet }) => {
  const url = `iam/yqc/v1/${tenantId}/users/paging`;
  const updateUrl = `iam/yqc/v1/${tenantId}/users`;
  const status = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const realName = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const phone = intl.formatMessage({ id: 'zknow.common.model.phone', defaultMessage: '手机号' });
  const email = intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' });
  const role = intl.formatMessage({ id: 'zknow.common.model.role', defaultMessage: '角色' });
  const company = intl.formatMessage({ id: 'zknow.common.model.company', defaultMessage: '公司' });
  const department = intl.formatMessage({ id: 'zknow.common.model.department', defaultMessage: '部门' });
  const location = intl.formatMessage({ id: 'zknow.common.model.location', defaultMessage: '位置' });
  const domain = intl.formatMessage({ id: 'iam.account.model.domain', defaultMessage: '域' });
  const domainManage = intl.formatMessage({ id: 'iam.account.model.domain.manage', defaultMessage: '域管理' });
  const userId = intl.formatMessage({ id: 'iam.account.model.user.id', defaultMessage: '用户ID' });
  const loginName = intl.formatMessage({ id: 'iam.common.model.account.login.name', defaultMessage: '登录名' });
  const global = intl.formatMessage({ id: 'iam.common.model.global', defaultMessage: '全局' });
  const director = intl.formatMessage({ id: 'iam.common.model.director', defaultMessage: '主管' });
  const phonePattern = /^1(3\d|4[5-9]|5[0-35-9]|6[2567]|7[0-8]|8\d|9[0-35-9])\d{8}$/;
  // eslint-disable-next-line no-useless-escape
  // const emailPattern = /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/;
  const emailPattern = /^([a-zA-Z0-9_.-])+@([a-zA-Z0-9-])+((\.[a-zA-Z0-9-]{2,}){1,4})$/;

  const phoneValidator = (value, name, record) => {
    const phoneValue = record.get('phone');
    const emailValue = record.get('email');
    const internationalTelCodeValue = record.get('internationalTelCode');
    if (phoneValue) {
      if (internationalTelCodeValue === '+86') {
        if (phonePattern.test(phoneValue)) {
          return true;
        }
        return intl.formatMessage({ id: 'iam.common.model.account.phone.error', defaultMessage: '请填写正确的手机号' });
      }
    }
    if (phoneValue || emailValue) {
      return true;
    }
    return intl.formatMessage({ id: 'iam.account.model.phone.or.email', defaultMessage: '不能同时没有邮箱和手机号' });
  };

  const emailValidator = (value, name, record) => {
    const phoneValue = record.get('phone');
    const emailValue = record.get('email');
    if (emailValue) {
      if (emailPattern.test(emailValue)) {
        return true;
      }
      return intl.formatMessage({ id: 'iam.account.model.email.error', defaultMessage: '请填写正确的邮箱' });
    }
    if (phoneValue || emailValue) {
      return true;
    }
    return intl.formatMessage({ id: 'iam.account.model.phone.or.email', defaultMessage: '不能同时没有邮箱和手机号' });
  };

  return {
    autoQuery: false,
    selection: false,
    paging: false,
    primaryKey: 'id',
    transport: {
      read: ({ data }) => ({
        url: `${url}?userId=${data.userId}`,
        method: 'get',
        paramsSerializer: () => '',
      }),
      create: ({ data: [data] }) => ({
        url: `iam/yqc/v1/${tenantId}/users`,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => ({
        url: updateUrl,
        method: 'put',
        data,
      }),
      destroy: ({ data }) => ({
        url: `${url}/${data.id}`,
        method: 'delete',
      }),
    },
    fields: [
      { name: 'id', type: 'string', label: userId },
      { name: 'realName', label: realName, required: true, type: 'string' },
      { name: 'loginName', label: loginName, type: 'string' },
      { name: 'phone', label: phone, type: 'string', validator: phoneValidator },
      { name: 'email', label: email, required: true, type: 'email' },
      { name: 'enabled', type: 'boolean', label: status },
      { name: 'internationalTelCode', type: 'string', defaultValue: '+86', lookupCode: 'INTL_TEL_CODE' },
      { name: 'language', type: 'string', lookupUrl: 'hpfm/v1/languages/list', textField: 'description', valueField: 'code' },
      { name: 'languageName', type: 'string' },
      { name: 'timeZone', type: 'string', lookupUrl: 'hpfm/v1/lookup/queryCode?lookupTypeCode=TIME_ZONE' },
      { name: 'timeZoneMeaning', type: 'string' },
      { name: 'imageUrl', type: 'string' },
      { name: 'userSource', lookupCode: 'ACCOUNT_SOURCE' },
      { name: 'personName', type: 'string' },
      { name: 'objectVersionNumber', type: 'number' },
      {
        name: 'directorId',
        type: 'object',
        textField: 'realName',
        label: director,
        valueField: 'id',
        lovCode: 'USER',
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          return {
            id: data.directorId,
            realName: data.directorName,
          };
        },
      },
      {
        name: 'companyId',
        label: company,
        textField: 'name',
        valueField: 'id',
        type: 'object',
        lovCode: 'COMPANY',
        transformRequest: (value) => {
          return value?.id ? `${value?.id}` : null;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            name: data.companyName,
          };
        },
      },
      {
        name: 'departmentId',
        label: department,
        textField: 'name',
        valueField: 'id',
        type: 'object',
        lovCode: 'DEPARTMENT',
        transformRequest: (value) => {
          return value?.id ? `${value?.id}` : null;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            name: data.departmentName,
          };
        },
      },
      {
        name: 'locationId',
        type: 'object',
        label: location,
        textField: 'locationName',
        valueField: 'id',
        lovCode: 'LOCATION',
        transformRequest: (value) => {
          return value?.id ? `${value?.id}` : null;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            locationName: data.locationName,
          };
        },
      },
      {
        name: 'domainId',
        type: 'object',
        label: domain,
        textField: 'name',
        valueField: 'id',
        lovCode: 'DOMAIN',
        transformRequest: (value) => {
          return value?.id ? `${value?.id}` : null;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            name: (+value) === 0 ? `${global}` : data.domainName,
          };
        },
      },
      {
        name: 'domainEnabledFlag',
        type: 'boolean',
        label: domainManage,
      },
    ],
    children: {
      child: childRolesDataSet,
      group: userGroupDataSet,
      domain: domainsDataSet,
    },
  };
};
