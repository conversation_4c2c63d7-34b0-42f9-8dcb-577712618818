import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import AccountDataSet from './AccountDataSet';
import UserInfoDataSet from './UserInfoDataSet';
import ChildRolesDataSet from './ChildRolesDataSet';
import UserGroupDataSet from './UserGroupDataSet';
import DomainsDataSet from './DominsDataSet';
import TransDataSet from './TransDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: 'iam.account' })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
    } = props;

    const intlPrefix = 'account';
    const prefixCls = 'account';
    const accountDataSet = useMemo(() => new DataSet(AccountDataSet({ intlPrefix, intl, tenantId })), [tenantId]);
    const transDataSetRole = useMemo(() => new DataSet(TransDataSet({ intlPrefix, intl, tenantId, source: 'roles' })), [tenantId]);
    const transDataSetDomain = useMemo(() => new DataSet(TransDataSet({ intlPrefix, intl, tenantId, source: 'domains' })), [tenantId]);
    const childRolesDataSet = useMemo(() => new DataSet(ChildRolesDataSet({ intl, intlPrefix, tenantId })), [tenantId]);
    const domainsDataSet = useMemo(() => new DataSet(DomainsDataSet({ intl, intlPrefix, tenantId })), [tenantId]);
    const userGroupDataSet = useMemo(() => new DataSet(UserGroupDataSet({ intl, intlPrefix, tenantId })), [tenantId]);
    const userInfoDataSet = useMemo(() => new DataSet(UserInfoDataSet({ intlPrefix, intl, tenantId, childRolesDataSet, userGroupDataSet, domainsDataSet })), [tenantId]);
    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      tenantId,
      accountDataSet,
      userInfoDataSet,
      childRolesDataSet,
      userGroupDataSet,
      transDataSetRole,
      transDataSetDomain,
      domainsDataSet,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
