import qs from 'qs';
import { getQueryParams } from '@zknow/utils';

const ChildRolesDataSet = ({ intl, intlPrefix, tenantId, source }) => {
  const rolesPrefix = `/iam/yqc/v1/${tenantId}/memberRoles/selectAssignRolesForUser`;
  const rulesPrefix = `/iam/yqc/${tenantId}/domains/selectAssignDomainsForUser`;
  const nameLabel = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const codeLabel = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const descriptionLabel = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const operationLabel = intl.formatMessage({ id: 'zknow.common.button.action', defaultMessage: '操作' });
  const typeLabel = intl.formatMessage({ id: 'zknow.common.model.type', defaultMessage: '类型' });

  const defaultQueryFields = [
    { name: 'name', type: 'string', label: nameLabel },
    { name: 'code', type: 'string', label: codeLabel },
  ];
  const rolesFields = [
    { name: 'description', type: 'string', label: descriptionLabel },
  ];

  const queryFieldsArr = source === 'roles' ? [...defaultQueryFields, ...rolesFields] : defaultQueryFields;
  return {
    autoQuery: false,
    autoLocateFirst: false,
    dataKey: 'content',
    primaryKey: 'id',
    totalKey: 'totalElements',
    autoQueryAfterSubmit: true,
    paging: true,
    pageSize: 20,
    selection: 'multiple',
    // cacheSelection: true,
    transport: {
      read: ({ data: { id, front }, data }) => ({
        url: front === 'roles' ? `${rolesPrefix}?userId=${id}&unAssign=true` : `${rulesPrefix}?userId=${id}&unAssign=true`,
        method: 'get',
        paramsSerializer: (params) => {
          delete params.id;
          delete params.front;
          return qs.stringify(params);
        },
        data: getQueryParams(data),
      }),
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'name', type: 'string', label: nameLabel },
      { name: 'code', type: 'string', label: codeLabel },
      { name: 'description', type: 'string', label: descriptionLabel },
    ],
    queryFields: queryFieldsArr,
  };
};

export default ChildRolesDataSet;
