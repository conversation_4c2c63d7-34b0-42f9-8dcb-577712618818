import qs from 'qs';
import { getQueryParams } from '@zknow/utils';

const ChildRolesDataSet = ({ intl, intlPrefix, tenantId }) => {
  // const urlPrefix = '/iam/yqc/v1/roles/sub_role';
  const urlPrefix = `/iam/yqc/v1/${tenantId}/memberRoles/selectAssignRolesForUser`;
  const nameLabel = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const codeLabel = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const descriptionLabel = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });

  return {
    autoQuery: false,
    autoLocateFirst: false,
    dataKey: 'content',
    totalKey: 'totalElement',
    autoQueryAfterSubmit: true,
    paging: 'server',
    pageSize: 20,
    selection: false,
    transport: {
      read: ({ data: { id, unAssign = false }, data }) => ({
        url: `${urlPrefix}?userId=${id}&unAssign=${unAssign}`,
        method: 'get',
        paramsSerializer: (params) => {
          delete params.id;
          return qs.stringify(params);
        },
        data: getQueryParams(data),
      }),
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'name', type: 'string', label: nameLabel },
      { name: 'code', type: 'string', label: codeLabel },
      { name: 'description', type: 'string', label: descriptionLabel },
    ],
    queryFields: [
      { name: 'name', type: 'string', label: nameLabel },
      { name: 'code', type: 'string', label: codeLabel },
      { name: 'description', type: 'string', label: descriptionLabel },
    ],
  };
};

export default ChildRolesDataSet;
