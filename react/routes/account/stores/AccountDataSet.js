import { DataSet } from 'choerodon-ui/pro';
import { getQueryParams } from '@zknow/utils';

export default ({ intlPrefix, intl, tenantId }) => {
  const url = `iam/yqc/v1/${tenantId}/users`;
  const hzeroUrl = `iam/hzero/v1/${tenantId}/users`;
  const realName = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const personName = intl.formatMessage({ id: 'iam.account.model.associated.member', defaultMessage: '关联人员' });
  const phone = intl.formatMessage({ id: 'zknow.common.model.phone', defaultMessage: '手机号' });
  const email = intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' });
  const status = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const locked = intl.formatMessage({ id: 'iam.account.model.secure.status', defaultMessage: '安全状态' });
  const companyName = intl.formatMessage({ id: 'zknow.common.model.company', defaultMessage: '公司' });
  const department = intl.formatMessage({ id: 'zknow.common.model.department', defaultMessage: '部门' });
  const domain = intl.formatMessage({ id: 'iam.account.model.domain', defaultMessage: '域' });
  const invitation = intl.formatMessage({ id: 'iam.account.model.invitation.status', defaultMessage: '邀请状态' });

  const commonBooleanDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' }), value: true },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' }), value: false },
    ],
  });

  const enabledFlagDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' }), value: 'true' },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' }), value: 'false' },
    ],
  });

  const lockedDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.common.model.normal', defaultMessage: '正常' }), value: 'false' },
      { meaning: intl.formatMessage({ id: 'iam.common.model.locked', defaultMessage: '锁定' }), value: 'true' },
    ],
  });
  const invitationStatusDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.account.model.joined', defaultMessage: '已加入' }), value: 'joined' },
      { meaning: intl.formatMessage({ id: 'iam.account.model.waiting.add', defaultMessage: '待加入' }), value: 'inviting' },
    ],
  });
  return {
    autoQuery: false,
    selection: false,
    paging: true,
    autoLocateFirst: false,
    primaryKey: 'id',
    pageSize: 20,
    transport: {
      read: ({ data }) => ({
        url: `${url}/paging`,
        method: 'get',
        data: getQueryParams(data),
      }),
      create: ({ data }) => ({
        url,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => ({
        url: `${hzeroUrl}/${data.id}/unlocked`,
        method: 'post',
        data,
      }),
      destroy: ({ data }) => ({
        url: `${url}/${data.id}`,
        method: 'delete',
      }),
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'realName', type: 'string', label: realName },
      { name: 'phone', type: 'string', label: phone },
      { name: 'email', type: 'string', label: email },
      { name: 'enabled', type: 'boolean', label: status },
      { name: 'locked', type: 'boolean', label: locked },
      { name: 'companyName', type: 'string', label: companyName },
      { name: 'departmentName', type: 'string', label: department },
      { name: 'personName', type: 'string', label: personName },
      { name: 'domainName', type: 'string', label: domain },
      { name: 'userSource', lookupCode: 'ACCOUNT_SOURCE', label: domain },
    ],
    queryFields: [
      { name: 'realName', type: 'string', label: realName },
      { name: 'phone', type: 'string', label: phone },
      { name: 'email', type: 'string', label: email },
      { name: 'enabled', type: 'string', label: status, options: enabledFlagDs },
      { name: 'companyName', type: 'string', label: companyName },
      { name: 'departmentName', type: 'string', label: department },
      { name: 'locked', type: 'string', label: locked, options: lockedDs },
      { name: 'invitationStatus', type: 'string', label: invitation, options: invitationStatusDs },
    ],
  };
};
