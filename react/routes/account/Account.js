import React from 'react';
import { Route, Switch, withRouter } from 'react-router-dom';
import { nomatch } from '@yqcloud/apps-master';
import MainView from './MainView.js';
import Detail from './detail';

const Account = ({ match }) => (
  <Switch>
    <Route path={`${match.url}/detail/:id`} component={Detail} />
    <Route path={`${match.url}`} component={MainView} />
    <Route path="*" component={nomatch} />
  </Switch>
);
export default withRouter(Account);
