import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, Select, CheckBox, Lov } from 'choerodon-ui/pro';
import { MobileField, Icon } from '@zknow/components';

const modalNoticeStyle = {
  marginTop: '-0.16rem',
  marginBottom: '0.05rem',
  color: '#2979FF',
  fontSize: '0.12rem',
};
const iconStyle = {
  fontSize: '0.14rem',
  color: '##2979FF',
  marginRight: '0.04rem',
  verticalAlign: 'middle',
};
export default observer((props) => {
  const [isDisabled, setIsDisabled] = useState(false);
  const { modal, userInfoDataSet, accountDataSet, intl, intlPrefix, prefixCls } = props;

  async function handleOk() {
    try {
      if (await userInfoDataSet.submit()) {
        await accountDataSet.query();
      } else {
        return false;
      }
    } catch (err) {
      return false;
    }
  }

  modal.handleOk(() => handleOk());
  modal.handleCancel(() => {
    userInfoDataSet.reset();
  });

  return (
    <React.Fragment>
      <div className="account-create-modal-notice" style={{ ...modalNoticeStyle }}><Icon type="help" style={{ ...iconStyle }} /><span>{intl.formatMessage({ id: 'iam.account.desc.add.account.warning', defaultMessage: '新建人员将通过邮箱发送邀请' })}</span></div>
      <Form columns={2} labelLayout="horizontal" labelAlign="right" labelWidth="auto" dataSet={userInfoDataSet}>
        <TextField autoFocus name="realName" />
        <TextField name="email" />
        <TextField name="phone" />
        <Lov name="companyId" />
        <Lov name="departmentId" />
        <Lov name="locationId" />
        <CheckBox name="domainEnabledFlag" />
        <Lov disabled={!userInfoDataSet?.current?.get('domainEnabledFlag')} name="domainId" />
      </Form>
    </React.Fragment>
  );
});
