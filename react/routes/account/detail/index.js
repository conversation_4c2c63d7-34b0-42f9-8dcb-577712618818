import React, { useState, useContext, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, TextArea, Output, Select, message, Lov, CheckBox } from 'choerodon-ui/pro';
import { TabPage, Header, Content, axios } from '@yqcloud/apps-master';
import { Button } from '@zknow/components';
import { Tabs } from 'choerodon-ui';
import ChildRoles from './ChildRoles';
import UserGroup from './UserGroup';
import Domains from './Domains';
import Store from '../stores';

import './index.less';

const { TabPane } = Tabs;

const DetailForm = (props) => {
  const { match: { params: { id } } } = props;
  const [editable, setEditable] = useState(false);

  // 首次切换tab渲染较慢
  const [init, setInit] = useState(true);
  const { userInfoDataSet, intl, intlPrefix, prefixCls, history, accountDataSet, tenantId } = useContext(Store);
  const search = history.location?.search;
  const currentUserInfo = userInfoDataSet?.current;

  const currentRole = accountDataSet.current;
  useEffect(() => {
    if (id) {
      refresh();
    }
  }, [id]);

  function refresh() {
    userInfoDataSet.setQueryParameter('userId', id);
    // console.log(accountDataSet);
    // userInfoDataSet.current = accountDataSet.current;
    userInfoDataSet.query().then(() => {
      userInfoDataSet.current = userInfoDataSet.get(0);
    });
  }

  function toggleEdit() {
    setEditable(!editable);
  }

  function handleCancel() {
    refresh();
    toggleEdit();
  }

  async function handleSave() {
    try {
      if (await userInfoDataSet.submit()) {
        await userInfoDataSet.query();
        toggleEdit();
      } else {
        toggleEdit();
        return false;
      }
    } catch (err) {
      // console.log(err);
      return false;
    }
  }

  async function handleInvite() {
    const record = userInfoDataSet.current;
    try {
      const personId = record.get('id');
      const userEmail = record.get('email');

      const data = { personId, userEmail };
      const res = await axios.post(`iam/yqc/v1/${tenantId}/users/invite`, data);

      if (res.failed) {
        throw new Error(res.message);
      } else {
        message.success(intl.formatMessage({ id: 'iam.account.desc.invite.success', defaultMessage: '邀请成功' }));
      }
    } catch (err) {
      message.error(err?.message);
    }
    await accountDataSet.query();
  }

  const actionButtons = () => {
    const invitationStatus = userInfoDataSet?.current?.get('invitationStatus');
    return editable ? (
      <div className={`${prefixCls}-form-buttons`}>
        <Button key="save" funcType="raised" onClick={handleSave} color="primary">
          {intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}
        </Button>
        <Button key="cancel" funcType="raised" onClick={handleCancel}>
          {intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}
        </Button>
      </div>
    ) : (
      <div className={`${prefixCls}-form-buttons`}>
        {(invitationStatus && invitationStatus !== 'joined') && (
          <Button key="invitation" funcType="raised" icon="share-one" onClick={() => handleInvite()} color="default">
            {intl.formatMessage({ id: 'iam.account.desc.invitation', defaultMessage: '邀请' })}
          </Button>
        )}
        <Button key="edit" funcType="raised" onClick={toggleEdit} color="default" icon="write">
          {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
        </Button>
      </div>
    );
  };

  return (
    <TabPage>
      <Header
        backPath={`/iam/account${search}`}
        dataSet={userInfoDataSet}
      >
        <h1>{intl.formatMessage({ id: 'iam.account.desc.detail', defaultMessage: '人员详情' })}</h1>
        <div>
          {actionButtons()}
        </div>
      </Header>
      <Content className="accout-content">
        <Form dataSet={userInfoDataSet} labelLayout="horizontal" labelAlign="right" labelWidth="auto" columns={2} disabled={!editable}>
          <TextField name="realName" />
          <TextField disabled name="loginName" />
          <TextField name="email" />
          <TextField name="phone" />
          <Lov name="companyId" />
          <Lov name="departmentId" />
          <CheckBox name="domainEnabledFlag" />
          <Lov name="domainId" disabled={!userInfoDataSet?.current?.get('domainEnabledFlag')} />
          <Lov name="locationId" />
          <Lov name="directorId" />
        </Form>
        <Tabs defaultActiveKey="1" type="card" className={`${prefixCls}-detail-tabs`} style={{ marginTop: '0.24rem' }} onChange={() => { setInit(false); }}>
          <TabPane tab={intl.formatMessage({ id: 'zknow.common.model.role', defaultMessage: '角色' })} key="1">
            <ChildRoles editable roleId={id} handleRefresh={refresh} currentRole={currentRole} />
          </TabPane>
          <TabPane tab={intl.formatMessage({ id: 'iam.account.desc.tab.person.group', defaultMessage: '人员组' })} key="2" forceRender={init}>
            <UserGroup roleId={id} handleRefresh={refresh} />
          </TabPane>
          <TabPane tab={intl.formatMessage({ id: 'iam.account.desc.tab.domain', defaultMessage: '可见域' })} key="3" forceRender={init}>
            <Domains editable roleId={id} handleRefresh={refresh} currentRole={currentRole} />
          </TabPane>
        </Tabs>
      </Content>
    </TabPage>
  );
};

export default observer(DetailForm);
