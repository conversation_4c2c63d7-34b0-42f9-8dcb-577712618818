import React, { useContext, useRef, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Table, Lov, message, Modal } from 'choerodon-ui/pro';
import { axios } from '@yqcloud/apps-master';
import { Button, TableHoverAction, YqTable } from '@zknow/components';
import EnabledFlag from '@/components/enabled-flag';
import FormView from './FormView';
import Store from '../stores';

const { Column } = Table;
const modalKey = Modal.key();
const AccountList = (props) => {
  const { editable, roleId, handleRefresh } = props;
  const context = useContext(Store);
  const { domainsDataSet, intl, intlPrefix, transDataSetDomain, tenantId } = context;
  const mdModalStyle = useMemo(() => ({ width: 800 }), []);

  function handleCreate() {
    openModal('create');
    transDataSetDomain.setQueryParameter('id', roleId);
    transDataSetDomain.setQueryParameter('front', 'domains');
    transDataSetDomain.query().then(() => {
    });
  }

  const createButton = () => (
    <Button funcType="raised" color="primary" onClick={handleCreate} disabled={!editable}>
      {intl.formatMessage({ id: 'zknow.common.button.append', defaultMessage: '添加' })}
    </Button>
  );

  function openModal(type) {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.account.desc.domains.add', defaultMessage: '添加域' }),
      children: (
        <FormView
          type={type}
          parentRoleId={roleId}
          context={context}
          dataSet={transDataSetDomain}
          curDataSet={domainsDataSet}
          front="domains"
        />
      ),
      key: modalKey,
      drawer: false,
      style: mdModalStyle,
      destroyOnClose: true,
    });
  }

  const buttons = useMemo(() => {
    return createButton();
  }, [editable]);

  function handleRemove(record) {
    const urlPrefix = `/iam/yqc/${tenantId}/domains/removeDomainsForUser?userId=${roleId}&domainId=${record.get('id')}`;
    axios.delete(`${urlPrefix}`).then(res => {
      if (res && res.failed) {
        message.error(res.message);
      } else {
        const newData = domainsDataSet.toData().filter((v) => v.id !== record.get('id'));
        domainsDataSet.loadData(newData);
        domainsDataSet.current = domainsDataSet.get(0);
        message.success(intl.formatMessage({ id: 'zknow.common.success.remove', defaultMessage: '移除成功' }));
      }
    }).catch(e => {
      message.error(e.message);
    });
  }

  function renderAction({ record }) {
    const actions = [
      {
        name: intl.formatMessage({ id: 'zknow.common.button.remove', defaultMessage: '移除' }),
        icon: 'delete',
        onClick: () => handleRemove(record),
      },
    ];
    return editable ? <TableHoverAction record={record} actions={actions} /> : null;
  }

  return (
    <div>
      <Table
        labelLayout="float"
        dataSet={domainsDataSet}
        placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
        className={`${intlPrefix}-table`}
        autoHeight
        autoLocateFirst={false}
        buttons={[buttons]}
        queryBarProps={{
          title: intl.formatMessage({ id: 'iam.account.desc.tab.domain', defaultMessage: '可见域' }),
        }}
      >
        <Column name="name" />
        <Column name="code" width={300} />
        {/* <Column name="enabled" renderer={({ value }) => <EnabledFlag enabledFlag={value} />} /> */}
        <Column width={1} renderer={renderAction} />
      </Table>
    </div>
  );
};

export default observer(AccountList);
