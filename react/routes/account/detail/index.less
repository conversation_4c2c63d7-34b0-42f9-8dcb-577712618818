@import '~choerodon-ui/lib/style/themes/default';

.flex-center {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.iam-role {
  &-header {
    .flex-center();
  }

  &-back-button {
    display: flex;
    align-items: center;
  }

  &-detail-tabs {
    margin-top: 24rem;

    .c7n-tabs-nav {
      margin-left: 0 !important;
    }
  }
}
.accout-content{
  .c7n-pro-table-body{
    height: calc(100% - 5rem)!important;
  }
  // .c7n-tabs,.c7n-tabs-content,.c7n-tabs-tabpane{
  //   height: 100%;
  // }
}

.modal-table {
  margin: -24px;
  .c7n-pro-table-body {
    height: calc(100vh - 500px);
  }
}