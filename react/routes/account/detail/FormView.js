import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { axios } from '@yqcloud/apps-master';
import { ExternalComponent, Icon, YqTable } from '@zknow/components';
import { Button, Table, Tooltip, message } from 'choerodon-ui/pro';
// import { Table } from 'choerodon-ui';
const { Column } = Table;
const defaultCodeStyle = {
  'max-width': '1.8rem',
  overflow: 'hidden',
  'text-overflow': 'ellipsis',
  'white-space': 'nowrap',
};

export default observer((props) => {
  const {
    modal, dataSet, type, curDataSet, parentRoleId, front,
    context: { intl, intlPrefix, tenantId, commonDataSet },
  } = props;

  modal.handleOk(async () => {
    try {
      if (front === 'sub-roles') {
        const postData = dataSet.selected.map((v) => {
          return v.get('id');
        });
        const resp = await axios.post(`/iam/yqc/v1/${tenantId}/memberRoles/assignRolesForUser?userId=${parentRoleId}`, JSON.stringify(postData));
        if (!resp.failed) {
          curDataSet.query();
        } else {
          return false;
        }
      } else {
        const postData = dataSet.selected.map((v) => {
          return v.get('id');
        });
        const resp = await axios.post(`/iam/yqc/${tenantId}/domains/assignDomainsForUser?userId=${parentRoleId}`, JSON.stringify(postData));
        if (!resp.failed) {
          curDataSet.query();
        }
      }
    } catch (err) {
      message.error(err?.message);
      curDataSet.query();
      return false;
    }
  });

  function handleCancel() {
    dataSet.reset();
    dataSet.queryDataSet.clear();
    return false;
  }

  const Footer = observer(({
    okBtn,
    cancelBtn,
  }) => {
    const myCancelBtn = <Button funcType="raised" onClick={handleCancel}>{intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}</Button>;
    return [okBtn, myCancelBtn];
  });

  if (front === 'sub-roles') {
    return (
      <div className="modal-table">
        <Table
          labelLayout="float"
          style={{ maxHeight: 500 }}
          dataSet={dataSet}
          autoHeight
          placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
          className={`${intlPrefix}-table`}
          autoLocateFirst={false}
          queryBarProps={{
            title: intl.formatMessage({ id: 'iam.account.desc.child.roles.add', defaultMessage: '添加角色' }),
            queryFieldsStyle: {
              name: { width: 100 },
              code: { width: 140 },
              description: { width: 200 },
            },
          }}
          onRow={({ record }) => ({
            onClick: (e) => {
              if (record.isSelected) {
                dataSet.unSelect(record);
              } else {
                dataSet.select(record);
              }
            },
          })}
        >
          <Column name="name" />
          <Column name="code" />
          <Column name="description" />
        </Table>
      </div>
    );
  }
  return (
    <div className="modal-table">
      <Table
        labelLayout="float"
        style={{ maxHeight: 500 }}
        dataSet={dataSet}
        autoHeight
        autoLocateFirst={false}
        placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
        className={`${intlPrefix}-table`}
        onRow={({ record }) => ({
          onClick: (e) => {
            if (record.isSelected) {
              dataSet.unSelect(record);
            } else {
              dataSet.select(record);
            }
          },
        })}
        queryBarProps={{
          title: intl.formatMessage({ id: 'iam.account.desc.domains.add', defaultMessage: '添加域' }),
        }}
      >
        <Column name="name" />
        <Column name="code" />
      </Table>
    </div>
  );
});
