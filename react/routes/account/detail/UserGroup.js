import React, { useContext, useRef, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Table, Lov, message, Modal } from 'choerodon-ui/pro';
import { axios } from '@yqcloud/apps-master';
import { Button, TableHoverAction, YqTable } from '@zknow/components';
import EnabledFlag from '@/components/enabled-flag';
import FormView from './FormView';
import Store from '../stores';

const { Column } = Table;
const modalKey = Modal.key();
const AccountList = (props) => {
  const { editable, roleId, handleRefresh } = props;
  const context = useContext(Store);
  const { userGroupDataSet, intl, intlPrefix, transDataSetRole, tenantId } = context;
  const mdModalStyle = useMemo(() => ({ width: 800 }), []);

  function handleCreate() {
    openModal('create');
    transDataSetRole.setQueryParameter('front', 'roles');
    transDataSetRole.setQueryParameter('id', roleId);
    transDataSetRole.query().then(() => {
    });
  }

  const createButton = () => (
    <Button funcType="raised" color="primary" onClick={handleCreate} disabled={!editable}>
      {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
    </Button>
  );

  function openModal(type) {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.account.desc.child.roles.add', defaultMessage: '添加角色' }),
      children: (
        <FormView
          type={type}
          parentRoleId={roleId}
          context={context}
          dataSet={transDataSetRole}
          curDataSet={userGroupDataSet}
          front="sub-roles"
        />
      ),
      key: modalKey,
      drawer: false,
      style: mdModalStyle,
      destroyOnClose: true,
    });
  }

  const buttons = useMemo(() => {
    return createButton();
  }, [editable]);

  function handleRemove(record) {
    const urlPrefix = `/iam/yqc/v1/${tenantId}/member-roles/${record.get('id')}`;
    axios.delete(`${urlPrefix}`).then(res => {
      if (res && res.failed) {
        message.error(res.message);
      } else {
        const newData = userGroupDataSet.toData().filter((v) => v.id !== record.get('id'));
        userGroupDataSet.loadData(newData);
        userGroupDataSet.current = userGroupDataSet.get(0);
        // handleRefresh();
      }
    }).catch(e => {
      message.error(e.message);
    });
  }

  function renderAction({ record }) {
    const actions = [
      {
        name: intl.formatMessage({ id: 'zknow.common.button.remove', defaultMessage: '移除' }),
        icon: 'delete',
        onClick: () => handleRemove(record),
      },
    ];
    return editable ? <TableHoverAction record={record} actions={actions} /> : null;
  }

  return (
    <div>
      <Table
        labelLayout="float"
        dataSet={userGroupDataSet}
        placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
        className={`${intlPrefix}-table`}
        autoHeight
        autoLocateFirst={false}
        queryBarProps={{
          title: intl.formatMessage({ id: 'iam.account.desc.tab.person.group', defaultMessage: '人员组' }),
        }}
      >
        <Column name="name" />
        <Column name="description" />
        {/* <Column width={1} renderer={renderAction} /> */}
      </Table>
    </div>
  );
};

export default observer(AccountList);
