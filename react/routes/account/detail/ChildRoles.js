import React, { useContext, useRef, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Table, Lov, message, Modal } from 'choerodon-ui/pro';
import { axios } from '@yqcloud/apps-master';
import { Button, TableHoverAction, YqTable } from '@zknow/components';
import EnabledFlag from '@/components/enabled-flag';
import FormView from './FormView';
import Store from '../stores';

const { Column } = Table;
const modalKey = Modal.key();
const AccountList = (props) => {
  const { editable, roleId, handleRefresh } = props;
  const context = useContext(Store);
  const { childRolesDataSet, intl, intlPrefix, transDataSetRole, tenantId } = context;
  const mdModalStyle = useMemo(() => ({ width: 800 }), []);

  function handleCreate() {
    openModal('create');
    transDataSetRole.setQueryParameter('id', roleId);
    transDataSetRole.setQueryParameter('front', 'roles');
    transDataSetRole.query().then(() => {
    });
  }

  const createButton = () => (
    <Button funcType="raised" color="primary" onClick={handleCreate} disabled={!editable}>
      {intl.formatMessage({ id: 'zknow.common.button.append', defaultMessage: '添加' })}
    </Button>
  );

  function openModal(type) {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.account.desc.child.roles.add', defaultMessage: '添加角色' }),
      children: (
        <FormView
          type={type}
          parentRoleId={roleId}
          context={context}
          dataSet={transDataSetRole}
          curDataSet={childRolesDataSet}
          front="sub-roles"
        />
      ),
      key: modalKey,
      drawer: false,
      style: mdModalStyle,
      destroyOnClose: true,
      onClose: () => {
        transDataSetRole.setQueryParameter('condition', '');
      },
    });
  }

  const buttons = useMemo(() => {
    return createButton();
  }, [editable]);

  function handleRemove(record) {
    const urlPrefix = `/iam/yqc/v1/${tenantId}/memberRoles/removeRolesForUser?userId=${roleId}&roleId=${record.get('id')}`;
    axios.delete(`${urlPrefix}`).then(res => {
      if (res && res.failed) {
        message.error(res.message);
      } else {
        const newData = childRolesDataSet.toData().filter((v) => v.id !== record.get('id'));
        childRolesDataSet.loadData(newData);
        childRolesDataSet.current = childRolesDataSet.get(0);
        message.success(intl.formatMessage({ id: 'zknow.common.success.remove', defaultMessage: '移除成功' }));
      }
    }).catch(e => {
      message.error(e.message);
    });
  }

  function renderAction({ record }) {
    const actions = [
      {
        name: intl.formatMessage({ id: 'zknow.common.button.remove', defaultMessage: '移除' }),
        icon: 'delete',
        onClick: () => handleRemove(record),
      },
    ];
    return editable ? <TableHoverAction record={record} actions={actions} /> : null;
  }

  return (
    <div>
      <Table
        labelLayout="float"
        dataSet={childRolesDataSet}
        placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
        className={`${intlPrefix}-table`}
        autoHeight
        autoLocateFirst={false}
        buttons={[buttons]}
        queryBarProps={{
          title: intl.formatMessage({ id: 'zknow.common.model.role', defaultMessage: '角色' }),
        }}
      >
        <Column name="name" />
        <Column name="code" width={300} />
        <Column name="description" />
        {/* <Column name="enabled" renderer={({ value }) => <EnabledFlag enabledFlag={value} />} /> */}
        <Column width={1} renderer={renderAction} />
      </Table>
    </div>
  );
};

export default observer(AccountList);
