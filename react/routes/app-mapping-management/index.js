import React, { useEffect } from 'react';
import { Route, Switch } from 'react-router-dom';
import { nomatch } from '@yqcloud/apps-master';
import { StoreProvider } from './stores';
import Detail from './detail';
import List from './list';

export default (props) => {
  return (
    <StoreProvider {...props}>
      <Switch>
        <Route path={`${props.match.url}/detail/:id`} component={Detail} />
        <Route path={`${props.match.url}`} component={List} />
        <Route path="*" component={nomatch} />
      </Switch>
    </StoreProvider>
  );
};
