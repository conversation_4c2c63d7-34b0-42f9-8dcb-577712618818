import React, { createContext, useEffect, useMemo, useState } from 'react';
import { DataSet, message } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import axios from 'axios';
import { observer, useLocalStore } from 'mobx-react-lite';
import { formatterCollections } from '@zknow/utils';
import ListDataSet from './ListDataSet';
import DetailDataSet from './DetailDataSet';
import C7NMappingDataSet from './C7NMappingDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState', 'HeaderStore')(formatterCollections({ code: 'iam.appMappingManagement' })(injectIntl(
  observer((props) => {
    const {
      children,
      AppState: {
        currentMenuType: { tenantId, type, domain: tenantDomain },
        getUserInfo: { tenantId: userTenantId },
      },
      location: { search },
      intl,
    } = props;
    const openAppStore = useLocalStore(() => ({
      mappingCodeOptions: [],
      larkFields: [],
      itemFields: [],
      sourceValues: {},
      targetValue: {},
      targetOptions: {},
      get getMappingCodeOptions() {
        return openAppStore.mappingCodeOptions.slice();
      },
      setMappingCodeOptions(options) {
        openAppStore.mappingCodeOptions = options;
      },
      get getLarkFields() {
        return openAppStore.larkFields.slice();
      },
      setLarkFields(fields) {
        openAppStore.larkFields = fields;
      },
      get getItemFields() {
        return openAppStore.itemFields.slice();
      },
      setItemFields(fields) {
        openAppStore.itemFields = fields;
      },
      get getSourceValues() {
        return openAppStore.sourceValues;
      },
      setSourceValues(values) {
        openAppStore.sourceValues = values;
      },
      get getTargetValue() {
        return openAppStore.targetValue;
      },
      setTargetValue(value) {
        openAppStore.targetValue = value;
      },
      get getTargetOptions() {
        return openAppStore.targetOptions;
      },
      setTargetOptions(options) {
        openAppStore.targetOptions = options;
      },
      queryLarkFields(openAppId, code) {
        axios.get(`/ecos/v1/${tenantId}/openApps/${openAppId}/mapping/approval/${code}/fields`)
          .then(res => {
            if (Array.isArray(res)) {
              openAppStore.setLarkFields(res.map(item => ({
                ...item,
                fieldCode: item.code,
                fieldId: item.code,
                fieldName: item.name,
              })));
            } else {
              openAppStore.setLarkFields([]);
            }
          })
          .catch(e => {
            openAppStore.setLarkFields([]);
          });
      },
      queryItemFields(itemId) {
        axios.get(`/lc/v1/${tenantId}/views/item/fields?itemId=${itemId}`)
          .then(res => {
            if (Array.isArray(res)) {
              openAppStore.setItemFields(res);
            } else {
              openAppStore.setItemFields([]);
            }
          })
          .catch(e => {
            openAppStore.setItemFields([]);
          });
      },
      queryTargetValues(openAppId, code, fieldCode) {
        // 查询飞书字段值使用，服务项字段值不走这个方法
        axios.get(`/ecos/v1/${tenantId}/openApps/${openAppId}/mapping/approval/${code}/fields/${fieldCode}`)
          .then(res => {
            if (res?.code && !res?.failed) {
              openAppStore.setTargetValue({ ...openAppStore.getTargetValue, [fieldCode]: res });
              // 现在只能映射 select 一种类型
              if (res?.type === 'radioV2') {
                openAppStore.setTargetOptions({
                  ...openAppStore.getTargetOptions,
                  [fieldCode]: (res?.values || []).map(item => ({ id: item.value, value: item.valueName })),
                });
              }
            } else {
              openAppStore.setTargetValue({ ...openAppStore.getTargetValue, [fieldCode]: null });
              openAppStore.setTargetOptions({ ...openAppStore.getTargetOptions, [fieldCode]: [] });
            }
          })
          .catch(e => {
            openAppStore.setTargetValue({ ...openAppStore.getTargetValue, [fieldCode]: null });
            openAppStore.setTargetOptions({ ...openAppStore.getTargetOptions, [fieldCode]: [] });
          });
      },
      queryMappingCodeOptions(openAppId) {
        axios.get(`/ecos/v1/${tenantId}/openApps/${openAppId}/mapping/approval`)
          .then(res => {
            if (Array.isArray(res)) {
              openAppStore.setMappingCodeOptions(res);
            } else {
              openAppStore.setMappingCodeOptions([]);
            }
          })
          .catch(e => {
            openAppStore.setMappingCodeOptions([]);
          });
      },
    }));

    const listDataSet = useMemo(() => new DataSet(ListDataSet({ intl, tenantId })), []);
    // TODO: 将详情页面的 dataSet 移到 detail 层级去
    const c7nMappingDataSet = useMemo(() => new DataSet(C7NMappingDataSet({ intl, tenantId, openAppStore })), []);
    const detailDataSet = useMemo(() => new DataSet(DetailDataSet({ intl, tenantId, c7nMappingDataSet, openAppStore })), []);

    const value = {
      ...props,
      tenantId,
      type,
      tenantDomain,
      userTenantId,
      listDataSet,
      detailDataSet,
      c7nMappingDataSet,
      openAppStore,
    };

    return <Store.Provider value={value}>{children}</Store.Provider>;
  }),
),));
