import { DataSet, message } from 'choerodon-ui/pro';

export default ({ intl, tenantId, openAppStore }) => {
  const mappingRuleTypeDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.appMappingManagement.model.c7n.mapping.mapping.rule.type.field', defaultMessage: '字段映射' }), value: 'DIRECT' }, // 字段映射
      { meaning: intl.formatMessage({ id: 'iam.appMappingManagement.model.c7n.mapping.mapping.rule.type.value', defaultMessage: '值映射' }), value: 'MAPPING_TABLE' }, // 值映射
    ],
  });

  const isConstantDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.appMappingManagement.model.c7n.mapping.constant', defaultMessage: '固定值' }), value: 'CONSTANT' }, // 固定值
      { meaning: intl.formatMessage({ id: 'iam.appMappingManagement.model.c7n.mapping.function', defaultMessage: '函数' }), value: 'EXPRESSION' }, // 函数表达式
    ],
  });

  const onlyConstantDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.appMappingManagement.model.c7n.mapping.constant', defaultMessage: '固定值' }), value: 'CONSTANT' }, // 固定值
    ],
  });

  const syncDirectionDs = new DataSet({
    data: [{ meaning: intl.formatMessage({ id: 'iam.appMappingManagement.model.c7n.mapping.yq2.c7n', defaultMessage: '燕千云同步猪齿鱼' }), value: 'yq2C7n' }],
  });
  return {
    autoQuery: false, // 自动请求
    selection: false,
    paging: false,
    autoLocateFirst: true,
    dataToJSON: 'all',
    fields: [
      {
        name: 'sourceFieldDetail', // 燕千云字段
        type: 'object',
        ignore: 'always',
      },
      {
        name: 'sourceField',
        type: 'string',
        dynamicProps: {
          bind: ({ record }) => (record.get('syncDirection') === 'LARK_YQ' ? 'sourceFieldDetail.value.code' : 'sourceFieldDetail.path'),
        },
      },
      {
        name: 'sourceFieldName',
        type: 'string',
        dynamicProps: {
          bind: ({ record }) => (record.get('syncDirection') === 'LARK_YQ' ? 'sourceFieldDetail.value.name' : 'sourceFieldDetail.name'),
        },
      },
      {
        name: 'sourceFieldRelationId',
        type: 'string',
        dynamicProps: {
          bind: ({ record }) => (record.get('syncDirection') === 'LARK_YQ' ? 'sourceFieldDetail.value.relationLovId' : 'sourceFieldDetail.relationLovId'),
        },
      },
      {
        name: 'targetFieldDetail', // 猪齿鱼字段
        type: 'object',
        ignore: 'always',
      },
      {
        name: 'targetField',
        type: 'string',
        bind: 'targetFieldDetail.value.fieldCode',
      },
      {
        name: 'targetFieldId',
        type: 'string',
        bind: 'targetFieldDetail.value.fieldId',
      },
      {
        name: 'targetFieldName',
        type: 'string',
        bind: 'targetFieldDetail.value.fieldName',
      },
      {
        name: 'sourceMetaInfo.expression',
        type: 'string',
      },
      {
        name: 'mappingRuleType', // 映射类型
        type: 'string',
        options: mappingRuleTypeDs,
        defaultValue: 'DIRECT',
      },
      {
        name: 'sourceDataType',
        defaultValue: 'FIELD', // 写死的
      },
      {
        name: 'mappingTable.type', // 燕千云值 是什么类型的字段 LOV/LOOKUP/SELECT
        type: 'string',
      },
      {
        name: 'mappingTable.lookupCode', // LOOKUP类型的字段的lookupCode
        type: 'string',
      },
      {
        name: 'mappingTable.sourceValueLookup', // Select状态下的燕千云值（快码）
        type: 'object',
        dynamicProps: {
          lookupCode: ({ record }) => {
            return record.get('mappingTable.lookupCode');
          },
        },
        transformRequest: (value, record) => { // 装填新数据时的处理
          if (value) {
            return {
              code: value?.code,
              meaning: value?.meaning,
            };
          }
          return value;
        },
        transformResponse: (value, data) => { // read时的处理
          if (data?.mappingTable?.sourceValue && data.mappingTable.type === 'LOOKUP') {
            return {
              code: data?.mappingTable?.sourceValue,
              meaning: data?.mappingTable?.sourceValueName,
            };
          }
          return undefined;
        },
      },
      {
        name: 'mappingTable.sourceValueSelect', // Select状态下的燕千云值（自定义选项集）
        type: 'object',
        transformRequest: (value, record) => { // 装填新数据时的处理
          if (value) {
            return {
              value: value.value,
              meaning: value.meaning,
            };
          }
          return value;
        },
        transformResponse: (value, data) => { // read时的处理
          if (data?.mappingTable?.sourceValue && data.mappingTable.type === 'SELECT') {
            return {
              value: data?.mappingTable?.sourceValue,
              meaning: data?.mappingTable?.sourceValueName,
            };
          }

          return undefined;
        },
      },
      {
        name: 'mappingTable.sourceValueLov', // LOV状态下的燕千云值
        lovCode: 'OBJECT_OPTIONS',
        // multiple: true,
        transformResponse: (value, data) => { // read时的处理
          if (data?.mappingTable?.sourceValue && data.mappingTable.type === 'LOV') {
            return {
              id: data?.mappingTable?.sourceValue,
              name: data?.mappingTable?.sourceValueName,
            };
          }
          if (data?.mappingTable?.sourceValueDetail) { // 旧数据
            return { ...data.mappingTable?.sourceValueDetail };
          }
          return undefined;
        },
        transformRequest: (value, record) => { // 装填新数据时的处理
          if (value) {
            return {
              id: value.id,
              name: value.name,
            };
          }
          return value;
        },
        dynamicProps: {
          lovQueryAxiosConfig: ({ record }) => {
            const { sourceFieldRelationId: relationLovId } = record?.toData() || {};
            if (relationLovId) {
              return {
                method: 'post',
                url: `/lc/v1/engine/${tenantId}/options/${relationLovId}/queryWithCondition`,
                transformResponse: (res) => {
                  try {
                    const data = JSON.parse(res) || {};
                    data?.content?.forEach((item) => {
                      if (!item.real_name && !item.name) {
                        item.name = item.id;
                      }
                      if (item.real_name) {
                        item.name = item.real_name;
                      }
                      if (!item.description) {
                        item.description = item.code;
                      }
                    });
                    return data;
                  } catch (e) {
                    // eslint-disable-next-line no-console
                    console.error(e);
                  }
                },
              };
            }
          },
        },
        type: 'object',
        textField: 'name',
      },
      {
        name: 'mappingTable.targetValueDetail', // 猪齿鱼值
        type: 'object',
        ignore: 'always',
        transformResponse: (value, data) => {
          if (data?.mappingTable?.targetValueName) {
            return {
              meaning: data?.mappingTable?.targetValueName,
            };
          }
          return undefined;
        },
      },
      {
        name: 'mappingTable.targetValue', // 猪齿鱼值
        bind: 'mappingTable.targetValueDetail.value.id',
        type: 'string',
      },
      {
        name: 'mappingTable.targetValueName',
        bind: 'mappingTable.targetValueDetail.value.value',
        type: 'string',
      },
      {
        name: 'fieldType', // 固定值/表达式
        dynamicProps: {
          options: ({ record }) => {
            const mappingRuleType = record?.get('mappingRuleType');
            if (mappingRuleType === 'MAPPING_TABLE') { // 业务逻辑：值映射只支持固定值
              return onlyConstantDs;
            } else {
              return isConstantDs;
            }
          },
        },
        type: 'string',
        defaultValue: 'CONSTANT',
      },
      {
        name: 'syncDirection', // 仅前端
        type: 'string',
        defaultValue: 'yq2C7n',
        options: syncDirectionDs,
      },
    ],
    events: {
      update: ({ name, value, record, dataSet }) => {
        // 尽量不动猪齿鱼的映射实现吧
        const isLark = dataSet.parent?.current?.get?.('openApp.type') === 'lark';
        if (isLark) {
          switch (name) {
            case 'mappingRuleType':
              record.set({
                fieldType: 'CONSTANT',
                sourceFieldDetail: undefined,
                targetFieldDetail: undefined,
                'mappingTable.sourceValueLov': undefined,
                'mappingTable.sourceValueLookup': undefined,
                'mappingTable.sourceValueSelect': undefined,
                'mappingTable.targetValueDetail': undefined,
              });
              break;
            case 'fieldType':
              record.set({
                sourceFieldDetail: undefined,
                targetFieldDetail: undefined,
                'mappingTable.sourceValueLov': undefined,
                'mappingTable.sourceValueLookup': undefined,
                'mappingTable.sourceValueSelect': undefined,
                'mappingTable.targetValueDetail': undefined,
              });
              break;
            case 'sourceFieldDetail': {
              const sourceData = record.get('syncDirection') === 'YQ_LARK' ? value : value?.value;
              const { widgetType, widgetConfig, path, code } = sourceData || {};

              // 多选暂时不做
              if (['Select', 'Radio'].includes(widgetType)) { // 下拉单选、单选框
                if (widgetConfig.dataSource === 'optionSet') { // 自定义选项集
                  record.set({
                    'mappingTable.type': 'SELECT',
                    'mappingTable.sourceValueLookup': undefined,
                    'mappingTable.sourceValueSelect': undefined,
                  });
                  if (widgetConfig.options.length > 0) {
                    // 映射左侧字段值
                    openAppStore.setSourceValues({ ...openAppStore.getSourceValues, [path || code]: widgetConfig.options });
                  }
                } else if (widgetConfig.dataSource === 'lookup') { // 已有选项集/快码
                  record.set({
                    'mappingTable.type': 'LOOKUP',
                    'mappingTable.sourceValueLov': undefined,
                    'mappingTable.sourceValueSelect': undefined,
                    'mappingTable.lookupCode': widgetConfig.lookupCode,
                  });
                }
              } else if (widgetType === 'MasterDetail') {
                record.set({
                  'mappingTable.type': 'LOV',
                  'mappingTable.sourceValueLookup': undefined,
                  'mappingTable.sourceValueSelect': undefined,
                });
              } else {
                record.set({
                  'mappingTable.type': '',
                  'mappingTable.sourceValueLov': undefined,
                  'mappingTable.sourceValueLookup': undefined,
                  'mappingTable.sourceValueSelect': undefined,
                });
              }
              break;
            }
            case 'targetFieldDetail': {
              if (record.get('mappingRuleType') === 'MAPPING_TABLE') {
                record.set({
                  'mappingTable.targetValueDetail': undefined,
                  'mappingTable.targetValue': undefined,
                  'mappingTable.targetValueName': undefined,
                });
                const openAppId = dataSet.parent?.current?.get?.('openApp.id');
                const openMappingCode = dataSet.parent?.current?.get?.('openMappingCode');
                const fieldCode = value?.value?.code;
                openAppStore.setTargetValue({ ...openAppStore.getTargetValue, [fieldCode]: null });
                openAppStore.setTargetOptions({ ...openAppStore.getTargetOptions, [fieldCode]: [] });

                // 在值映射的情况下，飞书的字段值需要单独接口查询
                if (fieldCode && openAppId && openMappingCode) {
                  openAppStore.queryTargetValues(openAppId, openMappingCode, fieldCode);
                } else {
                  openAppStore.setTargetValue({ ...openAppStore.getTargetValue, [fieldCode]: null });
                  openAppStore.setTargetOptions({ ...openAppStore.getTargetOptions, [fieldCode]: [] });
                }
              }
              break;
            }
            default:
          }
        }
        switch (name) {
          case 'mappingTable.sourceValueLookup':
            record.set({
              'mappingTable.sourceValue': value ? value.code : undefined,
              'mappingTable.sourceValueName': value ? value.meaning || value.name : undefined,
            });
            break;
          case 'mappingTable.sourceValueSelect':
            record.set({
              'mappingTable.sourceValue': value ? value.value : undefined,
              'mappingTable.sourceValueName': value ? value.meaning || value.name : undefined,
            });
            break;
          case 'mappingTable.sourceValueLov':
            record.set({
              'mappingTable.sourceValue': value ? value.id : undefined,
              'mappingTable.sourceValueName': value ? value.meaning || value.name : undefined,
            });
            break;
          default:
        }
      },
    },
  };
};
