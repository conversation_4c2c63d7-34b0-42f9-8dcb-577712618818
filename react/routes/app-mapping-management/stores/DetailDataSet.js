import { DataSet, message } from 'choerodon-ui/pro';
import compact from 'lodash/compact';

export default ({ intl, tenantId, c7nMappingDataSet, openAppStore }) => {
  const readUrl = `iam/yqc/${tenantId}/field_mapping`;
  const submitUrl = `iam/yqc/${tenantId}/field_mapping`;

  const statusOptionDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' }), value: true },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' }), value: false },
    ],
  });

  const handleTransformJson = (originData) => {
    // 先把多选的lov拆成单选的格式，因为后端需要单选的
    // const data = [];
    // originData.forEach((v) => {
    //   v.mappingTable.sourceValueDetail.forEach((e, index) => {
    //     data.push({
    //       ...v,
    //       mappingTable: {
    //         sourceValue: v.mappingTable.sourceValue[index],
    //         sourceValueName: v.mappingTable.sourceValueName[index],
    //         sourceValueDetail: v.mappingTable.sourceValueDetail[index],
    //         targetValue: v.mappingTable.targetValue,
    //         targetValueDetail: v.mappingTable.targetValueDetail,
    //         targetValueName: v.mappingTable.targetValueName,
    //       } });
    //   });
    // });
    // 这个函数的意义在于用reduce方法对数组进行合并操作，把数据转化为后端需要的样子
    const transformedData = (originData || []).filter(item => item._status !== 'delete').reduce((accumulator, current) => {
      // existing: 对于current项，在已经累加的数组accumulator中是否存在有sourceField和targetField都相同的项？
      const existing = accumulator.find((v) => v.mappingRuleType === 'MAPPING_TABLE' && v.sourceField === current.sourceField && v.targetField === current.targetField,);
      if (current.mappingRuleType === 'MAPPING_TABLE') {
        current.targetMetaInfo = { // 后端需要
          realValueMatchRule: {
            matchField: 'id',
            resultField: 'id',
          },
        };
      }
      if (existing && current.mappingRuleType === 'MAPPING_TABLE') {
        // current项不是独立的
        if (!Array.isArray(existing.mappingTable)) {
          existing.mappingTable = [existing.mappingTable];
        }
        existing.mappingTable.push(current.mappingTable);
      } else {
        // current项是独立的
        current.mappingTable = [current.mappingTable];
        accumulator.push(current);
      }
      return accumulator;
    }, []);
    return transformedData;
  };

  const reversedJsonData = (response) => {
    const { openAppType, jsonData } = response;
    const originData = JSON.parse(jsonData);
    const data = openAppType === 'choerodon' ? originData?.fieldMapping : originData;
    // 这个函数的意义在把后端数据改成前端需要的数据
    const transformedData = [];
    compact(data || []).forEach((item) => {
      if (item.mappingTable && Array.isArray(item.mappingTable) && item.mappingTable.length > 0) {
        item.mappingTable.forEach((tableItem) => {
          transformedData.push({
            ...item,
            mappingTable: { ...tableItem },
          });
        });
      } else {
        transformedData.push(item);
      }
    });
    return transformedData;
  };

  const reformedJsonData = (originJsonData) => {
    // 这个函数意义在于添加前面的metaInfo部分，把前端数据改造成后端需要的数据
    // 飞书映射不需要 metaInfo
    const res = {
      metaInfo: {
        sourceSystem: 'yqcloud',
        targetSystem: 'choerodon',
        version: '3',
        conditions: {
          前置为等待开发: "#origin?.statusVO?.name == '等待开发'",
          前置为技术设计中: "#origin?.statusVO?.name == '技术设计中'",
        },
      },
      fieldMapping: handleTransformJson(originJsonData),
    };
    return res;
  };
  return {
    autoQuery: false, // 自动请求
    selection: false,
    paging: true,
    primaryKey: null, // 返回的是数组时才用到
    autoLocateFirst: true,
    pageSize: 20,
    queryFields: [
      {
        name: 'name',
        type: 'string',
        label: intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' }),
      },
      {
        name: 'code',
        type: 'string',
        label: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }),
        format: 'uppercase',
      },
      {
        name: 'description',
        type: 'string',
        label: intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' }),
      },
      {
        name: 'enabledFlag',
        type: 'boolean',
        label: intl.formatMessage({ id: 'state' }),
        options: statusOptionDs,
      },
    ],
    transport: {
      read: ({ data: { id } }) => {
        if (id === '0') {
          // 新建接口不请求
        } else if (id) {
          return {
            url: `${readUrl}/${id}`,
            method: 'get',
            // data: getQueryParams(data),
            transformResponse: (res) => {
              const r = JSON.parse(res);
              if (r?.failed) {
                message.error(r.message);
                return {};
              }
              // 对jsonData的处理
              const jsonData = reversedJsonData(r);
              if (r.openAppId) {
                openAppStore.queryMappingCodeOptions(r.openAppId);
                if (r.openMappingCode) {
                  openAppStore.queryLarkFields(r.openAppId, r.openMappingCode);
                }
              }
              if (r.itemId) {
                openAppStore.queryItemFields(r.itemId);
              }
              return { ...r, jsonData };
            },
          };
        }
      },
      create: ({ data: [data], dataSet }) => {
        const { id, jsonData } = data;
        const jsonObj = dataSet?.current?.get('openApp.type') === 'choerodon' ? reformedJsonData(jsonData) : handleTransformJson(jsonData);
        data.jsonData = JSON.stringify(jsonObj);

        return {
          url: submitUrl,
          method: id === '0' ? 'post' : 'put',
          data,
        };
      },
      update: ({ data: [data], dataSet }) => {
        const { jsonData } = data;
        // NOTE： 猪齿鱼同步必须使用下面这个，jsonData 中会过滤 ignore 为 always 的字段
        const [{ jsonData: originJsonData }] = dataSet.toData();
        const jsonObj = dataSet?.current?.get('openApp.type') === 'choerodon' ? reformedJsonData(originJsonData) : handleTransformJson(jsonData);
        data.jsonData = JSON.stringify(jsonObj);

        return {
          url: `${submitUrl}`,
          method: 'put',
          data,
        };
      },
    },

    fields: [
      {
        name: 'id',
        type: 'string',
      },
      {
        name: 'name',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.appMappingManagement.model.c7n.mapping.mapping.name', defaultMessage: '映射名称' }),
        required: true,
      },
      {
        name: 'objectVersionNumber',
        type: 'string',
      },
      {
        name: 'businessObject',
        type: 'object',
        label: intl.formatMessage({ id: 'zknow.common.model.businessObject', defaultMessage: '业务对象' }),
        required: true,
        lovCode: 'BUSINESS_OBJECT',
        ignore: 'always',
      },
      {
        name: 'businessObjectId',
        type: 'string',
        bind: 'businessObject.id',
      },
      {
        name: 'businessObjectCode',
        type: 'string',
        bind: 'businessObject.code',
      },
      {
        name: 'businessObjectName',
        type: 'string',
        bind: 'businessObject.name',
      },
      {
        name: 'openApp',
        type: 'object',
        label: intl.formatMessage({ id: 'iam.appMappingManagement.model.c7n.mapping.open.app', defaultMessage: '三方应用' }), // 三方应用
        lovCode: 'THIRD_PARTY_APPLICATION',
        lovPara: {
          useForMapping: 1,
        },
        ignore: 'always',
        required: true,
      },
      {
        name: 'openAppId',
        type: 'string',
        bind: 'openApp.id',
      },
      {
        name: 'openAppName',
        type: 'string',
        bind: 'openApp.name',
      },
      {
        name: 'openAppType',
        type: 'string',
        bind: 'openApp.type',
        ignore: 'always',
      },
      {
        name: 'project', // 目标项目
        type: 'object',
        label: intl.formatMessage({ id: 'iam.appMappingManagement.model.c7n.mapping.project', defaultMessage: '目标项目' }),
        ignore: 'always',
        transformResponse: (value, data) => {
          return {
            meaning: data.projectName,
          };
        },
      },
      {
        name: 'projectId',
        type: 'string',
        bind: 'project.value.id',
      },
      {
        name: 'projectName',
        type: 'string',
        bind: 'project.value.name',
      },

      {
        name: 'issueType', // 工作项类型
        label: intl.formatMessage({ id: 'iam.appMappingManagement.model.c7n.mapping.issue.type', defaultMessage: '工作项类型' }),
        type: 'object',
        ignore: 'always',
        transformResponse: (value, data) => {
          return {
            meaning: data.issueTypeName,
          };
        },
        dynamicProps: {
          required: ({ record }) => record.get('openAppType') === 'choerodon',
        },
      },
      {
        name: 'issueTypeId',
        type: 'string',
        bind: 'issueType.value.id',
      },
      {
        name: 'enterCondition',
        type: 'string',
      },
      {
        name: 'issueTypeName',
        type: 'string',
        bind: 'issueType.value.name',
      },
      {
        name: 'description',
        type: 'string',
        label: intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' }),
      },
      {
        // 现在只有一种【审批类型】，所以不需要展示在页面上
        name: 'openMappingType',
        type: 'string',
        lookupCode: 'APP_MAPPING_OPEN_TYPE',
        label: intl.formatMessage({ id: 'iam.appMappingManagement.model.openMappingType', defaultMessage: '三方类型' }),
        dynamicProps: {
          required: ({ record }) => record.get('openAppType') === 'lark',
        },
      },
      {
        name: 'openMappingCode',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.appMappingManagement.model.openMappingCode', defaultMessage: '选择审批流' }),
        dynamicProps: {
          required: ({ record }) => record.get('openAppType') === 'lark',
        },
      },
      {
        name: 'direction',
        type: 'string',
        lookupCode: 'APP_FIELD_MAPPING_DIRECTION',
        label: intl.formatMessage({ id: 'iam.appMappingManagement.model.direction', defaultMessage: '同步方向' }),
        dynamicProps: {
          required: ({ record }) => record.get('openAppType') === 'lark',
        },
      },
      {
        name: 'mappingTarget',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.appMappingManagement.model.mappingTarget', defaultMessage: '调用内容' }),
        dynamicProps: {
          required: ({ record }) => record.get('openAppType') === 'lark' && record.get('direction') === 'LARK_YQ',
        },
        defaultValue: 'ITEM',
      },
      {
        name: 'itemId',
        type: 'object',
        lovCode: 'SC_ITEM',
        label: intl.formatMessage({ id: 'iam.appMappingManagement.model.item', defaultMessage: '服务项' }),
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          if (value) {
            return {
              id: value,
              name: data?.itemName,
            };
          }
          return undefined;
        },
        dynamicProps: {
          required: ({ record }) => record.get('openAppType') === 'lark' && record.get('direction') === 'LARK_YQ',
          // lovPara: ({ record }) => {
          //   const businessObjectCode = record.get('businessObject.code');
          //   return businessObjectCode ? ({ ticketTypeCode: businessObjectCode }) : null;
          // },
        },
      },
      {
        name: 'itemName',
        type: 'string',
      },
    ],
    children: {
      jsonData: c7nMappingDataSet,
    },
    events: {
      update: ({ name, value, record }) => {
        switch (name) {
          case 'openApp':
            record.set({
              openMappingCode: undefined,
              project: undefined,
              issueType: undefined,
            });
            c7nMappingDataSet.loadData([]);
            break;
          case 'project':
            record.set({
              issueType: undefined,
            });
            break;
          case 'direction':
            c7nMappingDataSet.loadData([]);
            break;
          case 'itemId':
            if (record.get('direction') === 'LARK_YQ') {
              c7nMappingDataSet.loadData([]);
            }
            if (value?.id) {
              openAppStore.queryItemFields(value.id);
            } else {
              openAppStore.setItemFields([]);
            }
            break;
          case 'openMappingCode':
            c7nMappingDataSet.loadData([]);
            if (!value) {
              openAppStore.setLarkFields([]);
            } else {
              openAppStore.queryLarkFields(record.get('openApp.id'), value);
            }
            break;
          default:
        }
      },
    },
  };
};
