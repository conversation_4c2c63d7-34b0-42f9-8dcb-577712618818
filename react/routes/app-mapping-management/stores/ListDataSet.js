import { DataSet, message } from 'choerodon-ui/pro';

export default ({ intl, tenantId }) => {
  const readUrl = `iam/yqc/${tenantId}/field_mapping`;
  const submitUrl = `iam/yqc/${tenantId}/field_mapping`;

  const statusOptionDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' }), value: true },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' }), value: false },
    ],
  });

  return {
    autoQuery: true, // 自动请求
    selection: false,
    paging: true,
    primaryKey: 'id',
    autoLocateFirst: false,
    pageSize: 20,
    queryFields: [
      {
        name: 'search_name',
        type: 'string',
        label: intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' }),
      },
      {
        name: 'search_businessObjectName',
        type: 'string',
        label: intl.formatMessage({ id: 'zknow.common.model.businessObject', defaultMessage: '业务对象' }),
      },
    ],
    transport: {
      read: ({ data }) => {
        if (data.fuzzy_params) {
          return {
            url: `${readUrl}?search_fuzzy_params=${data.fuzzy_params}`,
            method: 'get',
          };
        }
        return {
          url: readUrl,
          method: 'get',
        };
      },
      create: ({ data: [data] }) => {
        return {
          url: submitUrl,
          method: 'post',
          data,
        };
      },
      update: ({ data: [data] }) => {
        return {
          url: submitUrl,
          method: 'put',
          data,
        };
      },
    },

    fields: [
      {
        name: 'enabledFlag',
        type: 'boolean',
        label: intl.formatMessage({ id: 'zknow.common.action.make.valid', defaultMessage: '生效' }),
      },
      {
        name: 'name',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.appMappingManagement.model.c7n.mapping.mapping.name', defaultMessage: '映射名称' }),
        required: true,
      },
      {
        name: 'businessObject',
        type: 'object',
        required: true,
      },
      {
        name: 'businessObjectId',
        type: 'string',
        bind: 'businessObject.id',
      },
      {
        name: 'businessObjectName',
        type: 'string',
        bind: 'businessObject.name',
        label: intl.formatMessage({ id: 'zknow.common.model.businessObject', defaultMessage: '业务对象' }),
      },
      {
        name: 'openApp',
        type: 'object',
        required: true,
      },
      {
        name: 'openAppId',
        type: 'string',
        bind: 'openApp.id',
      },
      {
        name: 'openAppName', // 三方应用
        type: 'string',
        bind: 'openApp.name',
        label: intl.formatMessage({ id: 'iam.appMappingManagement.model.c7n.mapping.open.app', defaultMessage: '三方应用' }),
      },
      {
        name: 'project', // 目标项目
        type: 'object',
        label: intl.formatMessage({ id: 'iam.appMappingManagement.model.c7n.mapping.project', defaultMessage: '目标项目' }),
      },
      {
        name: 'projectId',
        type: 'string',
        bind: 'project.id',
      },
      {
        name: 'projectName',
        type: 'string',
        bind: 'project.name',
      },
      {
        name: 'issueType', // 工作项类型
        label: intl.formatMessage({ id: 'iam.appMappingManagement.model.c7n.mapping.issue.type', defaultMessage: '工作项类型' }),
        type: 'object',
        // required: true,
      },
      {
        name: 'issueTypeId',
        type: 'string',
        bind: 'issueType.id',
      },
      {
        name: 'issueTypeName',
        type: 'string',
      },
      {
        name: 'description',
        type: 'string',
        label: intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' }),
      },
    ],
  };
};
