import React, { useContext } from 'react';
import { Content, TabPage, axios, Header } from '@yqcloud/apps-master';
import { Table } from 'choerodon-ui/pro';
import {
  ClickText,
  TableStatus,
  Button,
  TableHoverAction,
} from '@zknow/components';
import Store from '../stores';

const { Column } = Table;
export default function Detail(props) {
  const context = useContext(Store);
  const { intl, listDataSet, history, match } = context;

  const handleCreate = () => {
    history.push({
      pathname: `${match.url}/detail/0`,
      search: history.location?.search,
    });
  };

  const renderEnabledFlag = ({ record }) => {
    const flag = record.getPristineValue('enabledFlag');
    return (
      <TableStatus
        status={flag}
        enabledText={intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' })}
        disabledText={intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' })}
      />
    );
  };

  const renderTableAction = ({ dataSet, record }) => {
    const flag = record.getPristineValue('enabledFlag');

    const handleChangeEnable = async () => {
      record.set('enabledFlag', !flag);
      await listDataSet.submit();
      await listDataSet.query();
    };
    const actions = [
      {
        name: flag ? intl.formatMessage({ id: 'zknow.common.button.disable', defaultMessage: '不启用' }) : intl.formatMessage({ id: 'zknow.common.button.enable', defaultMessage: '启用' }),
        icon: flag ? 'icon-Expires' : 'icon-read',
        onClick: () => handleChangeEnable(),
      },
    ];

    return <TableHoverAction record={record} actions={actions} intlBtnIndex={0} />;
  };
  const renderName = ({ record }) => (
    <ClickText
      history={history}
      record={record}
      path={`${match.path}/detail/${record.get('id')}`}
      valueField="name"
    />
  );

  const renderCreateButton = () => {
    return (
      <Button
        funcType="raised"
        color="primary"
        icon="plus"
        onClick={handleCreate}
      >
        {intl.formatMessage({ id: 'iam.appMappingManagement.desc.create', defaultMessage: '创建' })}
      </Button>
    );
  };
  return (
    <TabPage>
      <Content style={{ padding: 0 }}>
        <Table
          dataSet={listDataSet}
          pristine
          autoHeight
          buttons={[renderCreateButton()]}
        >
          <Column name="name" width={300} lock="left" renderer={renderName} />
          <Column name="description" width={220} lock="left" />
          <Column name="businessObjectName" width={220} />
          <Column name="openAppName" width={300} />
          <Column name="enabledFlag" align="right" renderer={renderEnabledFlag} />
          <Column width={100} renderer={renderTableAction} tooltip="none" />
        </Table>
      </Content>
    </TabPage>
  );
}
