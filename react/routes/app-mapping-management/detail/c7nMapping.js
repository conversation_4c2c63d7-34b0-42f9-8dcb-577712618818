import React, { useContext, useEffect, useCallback, useRef, useMemo, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Icon, Button, ExternalComponent, YqCodeMirror } from '@zknow/components';
import {
  message,
  Form,
  TextField,
  Select,
  Lov,
  Modal,
} from 'choerodon-ui/pro';
import classnames from 'classnames';
import { axios } from '@yqcloud/apps-master';
import Store from '../stores';
import styles from './AppMappingDetail.module.less';
import C7nFieldValues from './c7nFieldValues';

const FieldTree = (props) => (
  <ExternalComponent {...props} system={{ scope: 'lc', module: 'FieldTree' }} />
);

function C7nMapping({ isEdit }) {
  const context = useContext(Store);
  const { detailDataSet, c7nMappingDataSet, intl, tenantId } = context;
  useEffect(() => {
    if (detailDataSet?.current && c7nMappingDataSet && c7nMappingDataSet.length === 0) {
      c7nMappingDataSet.create({});
    }
  }, [c7nMappingDataSet, detailDataSet?.current]);

  const openAppId = useMemo(() => {
    return detailDataSet?.current?.get('openAppId');
  }, [detailDataSet, detailDataSet?.current?.get('openAppId')]);
  const projectId = useMemo(() => {
    return detailDataSet?.current?.get('projectId');
  }, [detailDataSet, detailDataSet?.current?.get('projectId')]);
  const issueTypeId = useMemo(() => {
    return detailDataSet?.current?.get('issueTypeId');
  }, [detailDataSet, detailDataSet?.current?.get('issueTypeId')]);
  const receiveC7nFields = async () => {
    if (openAppId && projectId && issueTypeId) {
      const res = await axios.get(`/iam/yqc/${tenantId}/field_mapping/fields?openAppId=${openAppId}&projectId=${projectId}&issueTypeId=${issueTypeId}`);
      const resWithName = res?.map((item) => {
        item.name = `${item.fieldName || '-'}(${item.fieldCode || '-'})`;
        return item;
      });
      setFields(resWithName || []);
    }
  };
  const [fields, setFields] = useState([]);
  const [sourceValue, setSourceValue] = useState([]);
  useEffect(() => {
    receiveC7nFields();
  }, [issueTypeId, projectId, openAppId]);
  const handleDuplicate = (record) => {
    const targetData = record.toData();
    c7nMappingDataSet.create({
      ...targetData,
      mappingTable: { ...targetData.mappingTable, sourceValue: undefined, targetValue: undefined, sourceValueName: undefined, targetValueName: undefined },
      // 只清空value和name 而不清空type lookupCode等字段
    });
  };
  const handleDelete = (record) => {
    c7nMappingDataSet.delete(record);
  };
  const handleAddLine = () => {
    c7nMappingDataSet.create({});
  };
  const treeRef = useRef();

  const openModal = useCallback(
    (businessObjId, businessObjCode, shouldDisableCheckbox, handleOk) => {
      if (businessObjId && businessObjCode) {
        Modal.open({
          title: intl.formatMessage({ id: 'iam.appMappingManagement.desc.c7n.mapping.choose.field', defaultMessage: '选择字段' }),
          children: (
            <FieldTree
              businessObjectId={businessObjId}
              businessObjectCode={businessObjCode}
              fieldCmpType="fieldCmpType"
              checkable
              keyField="path"
              dragable={false}
              treeRef={treeRef}
              defaultCheckedKeys={[]}
              searchable
              singleCheck
              displayCode
              shouldDisableCheckbox={shouldDisableCheckbox}
            />
          ),
          // key: modalKey,
          drawer: false,
          style: { width: 800 },
          destroyOnClose: true,
          onOk: () => handleOk(),
        });
      } else {
        message.error(intl.formatMessage({ id: 'iam.appMappingManagement.desc.c7n.mapping.choose.business.obj.first', defaultMessage: '请先填写业务对象' }));
      }
    },
    [],
  );

  const shouldDisableCheckbox = (_record) => {
    const widgetType = _record?.get('widgetType');
    if (widgetType === 'MasterDetail') {
      return !_record?.get('relationLovId');
    }
    if (['Select', 'Radio'].includes(widgetType)) { // 下拉单选、单选框
      return false; // 可用
    }
    if (['MultipleSelect', 'SelectBox'].includes(widgetType)) { // 下拉多选、多选框
      return true; // 目前不可用（置灰）（未来可能改变）
    }
    return true; // 不可用（置灰）
  };

  const handleChooseSourceField = (e, record) => {
    const mappingRuleType = record.get('mappingRuleType');

    const handleOk = () => {
      const { dataSet: treeDataSet } = treeRef.current || {};
      const data = treeDataSet.getState('selected').slice()?.[0]?.toData();
      record.set('sourceFieldDetail', data);
    };
    openModal(
      detailDataSet.current.get('businessObjectId'),
      detailDataSet.current.get('businessObjectCode'),
      mappingRuleType === 'DIRECT' ? undefined : shouldDisableCheckbox,
      handleOk,
    );
  };
  const fieldRenderer = (record) => {
    if (!record.get('sourceField')) return null;
    return (
      <div>{`${record.get('sourceFieldName')}(${record.get(
        'sourceField',
      )})`}</div>
    );
  };
  const handleClear = (record) => {
    // 清空其他字段
    record.set('sourceFieldDetail', '');
    record.set('targetFieldDetail', '');
    record.set('mappingTable.sourceValueLov', '');
    record.set('mappingTable.sourceValueLookup', '');
    record.set('mappingTable.sourceValueSelect', '');
    record.set('mappingTable.targetValueDetail', '');
  };

  const selectorRender = ({ value }) => {
    const field = value?.value;
    if (field?.name) return (<div>{field.name}</div>);
    if (field) return (<div>{field?.fieldName || '-'}({field?.fieldCode || '-'})</div>);
    return null;
  };

  useEffect(() => {
    const record = c7nMappingDataSet?.current;
    const sourceFieldDetail = record?.get('sourceFieldDetail');
    const { widgetType, widgetConfig } = sourceFieldDetail || {};
    // 多选暂时不做
    if (['Select', 'Radio'].includes(widgetType)) { // 下拉单选、单选框
      if (widgetConfig.dataSource === 'optionSet') { // 自定义选项集
        // setType('SELECT');
        record?.set('mappingTable.type', 'SELECT');
        record?.set('mappingTable.sourceValueLov', '');
        record?.set('mappingTable.sourceValueLookup', '');
        if (widgetConfig.options.length > 0) {
          setSourceValue(widgetConfig.options);
        }
      } else if (widgetConfig.dataSource === 'lookup') { // 已有选项集/快码
        record?.set('mappingTable.type', 'LOOKUP');
        record?.set('mappingTable.sourceValueLov', '');
        record?.set('mappingTable.sourceValueSelect', '');
        record?.set('mappingTable.lookupCode', widgetConfig.lookupCode);
        // setType('LOOKUP');
      }
    } else if (widgetType === 'MasterDetail') {
      record?.set('mappingTable.type', 'LOV');
      record?.set('mappingTable.sourceValueSelect', '');
      record?.set('mappingTable.sourceValueLookup', '');
    }
  }, [c7nMappingDataSet?.current?.get('sourceFieldDetail')]);

  const renderSourceValue = (_record) => {
    if (_record?.get('mappingRuleType') === 'DIRECT') return null;
    const type = _record.get('mappingTable.type') || 'LOV';
    // 燕千云值
    if (type === 'SELECT') {
      return (
        <Select
          record={_record}
          disabled={!isEdit}
          name="mappingTable.sourceValueSelect"
          className={classnames(styles.oneItem)}
        >
          {sourceValue.map((item) => {
            return (
              <Select.Option value={item.value}>{item.meaning}</Select.Option>
            );
          })}
        </Select>
      );
    } else if (type === 'LOV') {
      return (
        <Lov
          disabled={!isEdit}
          record={_record}
          name="mappingTable.sourceValueLov"
          className={classnames(styles.oneItem)}
        />
      );
    } else if (type === 'LOOKUP') {
      return (
        <Select
          disabled={!isEdit}
          record={_record}
          name="mappingTable.sourceValueLookup"
          className={classnames(styles.oneItem)}
        />
      );
    }
    return null;
  };

  const handleChangeRecord = (record) => {
    c7nMappingDataSet.current = record;
  };
  return (
    <div className={styles.c7nForm}>
      <Form
        dataSet={c7nMappingDataSet}
        columns={1}
        labelLayout="horizontal"
        header={intl.formatMessage({ id: 'iam.appMappingManagement.desc.c7n.mapping.field.mapping', defaultMessage: '字段或状态映射' })}
        className={styles.form}
      >
        <div className={classnames(styles.oneLine, styles.underline)}>
          <div className={styles.shortItem}>{intl.formatMessage({ id: 'iam.appMappingManagement.desc.c7n.mapping.mapping.rule.type', defaultMessage: '映射类型' })}</div>
          <div className={styles.shortItem} />
          <div className={styles.constantItem}>
            <div className={styles.oneItem}><span className={styles.red}>*</span>{intl.formatMessage({ id: 'iam.appMappingManagement.desc.c7n.mapping.yq.field', defaultMessage: '燕千云字段' })}</div>
            <div className={styles.oneItem}>(<span className={styles.red}>*</span>{intl.formatMessage({ id: 'iam.appMappingManagement.desc.c7n.mapping.value', defaultMessage: '值' })})
            </div>
          </div>
          <div className={styles.longItem}>{intl.formatMessage({ id: 'iam.appMappingManagement.desc.c7n.mapping.sync.direction', defaultMessage: '同步方向' })}</div>
          <div className={styles.constantItem}>
            <div className={styles.oneItem}><span className={styles.red}>*</span>{intl.formatMessage({ id: 'iam.appMappingManagement.desc.c7n.mapping.c7n.field', defaultMessage: '猪齿鱼字段' })}
            </div>
            <div className={styles.oneItem}>({intl.formatMessage({ id: 'iam.appMappingManagement.desc.c7n.mapping.value', defaultMessage: '值' })})</div>
          </div>
          <div className={styles.lastBtn} />
        </div>
        {c7nMappingDataSet.map((record) => {
          return (
            <div className={styles.oneLine} onClick={() => handleChangeRecord(record)}>
              <Select
                disabled={!isEdit}
                record={record}
                name="mappingRuleType" // 映射类型
                className={styles.shortItem}
                onChange={(e) => {
                  if (e === 'MAPPING_TABLE') { // 业务逻辑：值映射只支持固定值
                    record?.set('fieldType', 'CONSTANT');
                  }
                  handleClear(record);
                }}
              />
              <Select
                disabled={!isEdit || record?.get('mappingRuleType') === 'MAPPING_TABLE'}
                record={record}
                name="fieldType" // 固定值/表达式
                className={styles.shortItem}
                onChange={() => handleClear(record)}
              />
              {record?.get('fieldType') === 'CONSTANT' ? <div className={styles.constantItem}>
                <TextField
                  disabled={!isEdit}
                  record={record}
                  name="sourceFieldName" // 燕千云字段
                  className={classnames(
                    record?.get('mappingRuleType') === 'MAPPING_TABLE' && styles.oneItem, // 值映射
                    record?.get('mappingRuleType') === 'DIRECT' && styles.doubleItem, // 字段映射
                  )}
                  suffix={<Icon type="search" />}
                  onClick={(e) => handleChooseSourceField(e, record)}
                  onChange={() => {
                    record.set('mappingTable.sourceValueLov', '');
                    record.set('mappingTable.sourceValueLookup', '');
                    record.set('mappingTable.sourceValueSelect', '');
                  }}
                  renderer={() => fieldRenderer(record)}
                  placeholder={intl.formatMessage({ id: 'iam.appMappingManagement.desc.c7n.mapping.choose.field', defaultMessage: '选择字段' })}
                />
                {renderSourceValue(record)}
              </div> : <div className={styles.constantItem}>
                <YqCodeMirror mode="button" businessObjectId={detailDataSet?.current?.get('businessObjectId')} record={record} name="sourceMetaInfo.expression" />
              </div>}

              <Select
                disabled={!isEdit}
                record={record}
                name="syncDirection"
                className={styles.longItem}
              />
              <div className={styles.constantItem}>
                <Select
                  disabled={!isEdit}
                  record={record}
                  name="targetFieldDetail" // 猪齿鱼字段
                  className={classnames(
                    record?.get('mappingRuleType') === 'MAPPING_TABLE' && styles.oneItem, // 值映射
                    record?.get('mappingRuleType') === 'DIRECT' && styles.doubleItem, // 字段映射
                  )}
                  renderer={selectorRender}
                >
                  {fields.map((field) => {
                    return (
                      <Select.Option value={field}>
                        {field?.name}
                      </Select.Option>
                    );
                  })}
                </Select>
                {record?.get('mappingRuleType') !== 'DIRECT' && <C7nFieldValues isEdit={isEdit} record={record} />}
              </div>

              <div
                className={classnames(
                  styles.lastBtn,
                  !isEdit && styles.hiddenItem,
                )}
              >
                <div
                  className={styles.inlineBtn}
                  onClick={() => handleDuplicate(record)}
                >
                  {intl.formatMessage({ id: 'zknow.common.button.copy', defaultMessage: '复制' })}
                </div>
                <div className={styles.inlineBtn} onClick={handleAddLine}>
                  {intl.formatMessage({ id: 'zknow.common.button.append', defaultMessage: '添加' })}
                </div>
                <Icon
                  onClick={() => handleDelete(record)}
                  type="close"
                  size={14}
                />
              </div>
            </div>
          );
        })}
        {c7nMappingDataSet.length === 0 && (<div
          className={classnames(
            styles.lastBtn,
            !isEdit && styles.hiddenItem,
          )}
        >
          <div className={styles.inlineBtn} onClick={handleAddLine}>
            {intl.formatMessage({ id: 'zknow.common.button.append', defaultMessage: '添加' })}
          </div>
        </div>)}
      </Form>
    </div>
  );
}

export default observer(C7nMapping);
