import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { observer } from 'mobx-react-lite';
import {
  Select,
} from 'choerodon-ui/pro';
import { axios } from '@yqcloud/apps-master';
import styles from './AppMappingDetail.module.less';
import Store from '../stores';

function C7nFieldValues(props) {
  const context = useContext(Store);
  const { detailDataSet, intl, tenantId, c7nMappingDataSet } = context;
  const { isEdit, record } = props;

  const initFlag = useRef(true);
  const fieldId = useMemo(() => {
    return record?.get('targetFieldId');
  }, [record, record?.get('targetFieldId')]);
  const openAppId = useMemo(() => {
    return detailDataSet?.current?.get('openAppId');
  }, [detailDataSet, detailDataSet?.current?.get('openAppId')]);
  const projectId = useMemo(() => {
    return detailDataSet?.current?.get('projectId');
  }, [detailDataSet, detailDataSet?.current?.get('projectId')]);
  const issueTypeId = useMemo(() => {
    return detailDataSet?.current?.get('issueTypeId');
  }, [detailDataSet, detailDataSet?.current?.get('issueTypeId')]);
  const targetFieldId = useMemo(() => {
    return c7nMappingDataSet?.current?.get('targetFieldId');
  }, [c7nMappingDataSet, c7nMappingDataSet?.current?.get('targetFieldId')]);

  useEffect(() => {
    receiveC7nFieldValues();
  }, [fieldId, targetFieldId]);

  const [fieldValues, setFieldValues] = useState([]);
  const receiveC7nFieldValues = async () => {
    if (tenantId && openAppId && projectId && fieldId) {
      const res = await axios.get(`/iam/yqc/${tenantId}/field_mapping/fieldValues?openAppId=${openAppId}&projectId=${projectId}&fieldId=${fieldId}&issueTypeId=${issueTypeId}`);
      if (res?.fieldOptions) {
        setFieldValues(res?.fieldOptions);
      } else {
        record.set('mappingTable.targetValueDetail', '');
        setFieldValues([]);
      }
    }
  };

  return (
    <Select
      disabled={!isEdit}
      record={record}
      name="mappingTable.targetValueDetail" // 猪齿鱼值
      className={styles.oneItem}
    >
      {fieldValues?.map((fieldValue) => {
        return (
          <Select.Option value={fieldValue}>
            {fieldValue?.value}
          </Select.Option>
        );
      })}
    </Select>
  );
}

export default observer(C7nFieldValues);
