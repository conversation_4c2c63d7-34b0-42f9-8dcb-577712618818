import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import classnames from 'classnames';
import Store from '../../stores';
import styles from '../AppMappingDetail.module.less';

const LarkMappingHeader = () => {
  const { detailDataSet, intl } = useContext(Store);
  return (
    <div className={classnames(styles.oneLine, styles.underline)}>
      <div className={styles.shortItem}>{intl.formatMessage({
        id: 'iam.appMappingManagement.desc.c7n.mapping.mapping.rule.type',
        defaultMessage: '映射类型',
      })}</div>
      <div className={styles.shortItem} />
      <div className={styles.constantItem}>
        <div className={styles.oneItem}><span className={styles.red}>*</span>{intl.formatMessage({
          id: `iam.appMappingManagement.desc.c7n.mapping.${detailDataSet.current?.get('direction') === 'YQ_LARK' ? 'yq' : 'lark'}.field`,
          defaultMessage: detailDataSet.current?.get('direction') === 'YQ_LARK' ? '燕千云字段' : '飞书字段',
        })}</div>
        <div className={styles.oneItem}>(<span className={styles.red}>*</span>{intl.formatMessage({
          id: 'iam.appMappingManagement.desc.c7n.mapping.value',
          defaultMessage: '值',
        })})
        </div>
      </div>
      <div className={styles.constantItem}>
        <div className={styles.oneItem}><span className={styles.red}>*</span>{intl.formatMessage({
          id: `iam.appMappingManagement.desc.c7n.mapping.${detailDataSet.current?.get('direction') === 'YQ_LARK' ? 'lark' : 'yq'}.field`,
          defaultMessage: detailDataSet.current?.get('direction') === 'YQ_LARK' ? '飞书字段' : '燕千云字段',
        })}
        </div>
        <div className={styles.oneItem}>({intl.formatMessage({
          id: 'iam.appMappingManagement.desc.c7n.mapping.value',
          defaultMessage: '值',
        })})
        </div>
      </div>
      <div className={styles.lastBtn} />
    </div>
  );
};

export default observer(LarkMappingHeader);
