import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Form } from 'choerodon-ui/pro';
import classnames from 'classnames';
import Store from '../../stores';
import styles from '../AppMappingDetail.module.less';
import LarkMappingItem from './LarkMappingItem';
import LarkMappingHeader from './LarkMappingHeader';

function LarkMapping({ isEdit, larkBusinessObjectId }) {
  const { detailDataSet, c7nMappingDataSet, intl } = useContext(Store);

  const handleAddLine = () => {
    c7nMappingDataSet.create({
      syncDirection: detailDataSet.current?.get('direction'),
    });
  };

  return (
    <div className={styles.c7nForm}>
      <Form
        dataSet={c7nMappingDataSet}
        columns={1}
        labelLayout="horizontal"
        header={intl.formatMessage({
          id: 'iam.appMappingManagement.desc.c7n.mapping.field.mapping',
          defaultMessage: '字段或状态映射',
        })}
        className={styles.form}
      >
        <LarkMappingHeader />
        {c7nMappingDataSet.map((record) => (
          <LarkMappingItem
            isEdit={isEdit}
            record={record}
            handleAddLine={handleAddLine}
            larkBusinessObjectId={larkBusinessObjectId}
          />
        ))}
        {c7nMappingDataSet.length === 0 && isEdit && (
          <div className={classnames(styles.lastBtn, { [styles.hiddenItem]: !isEdit })}>
            <div className={styles.inlineBtn} onClick={handleAddLine}>
              {intl.formatMessage({ id: 'zknow.common.button.append', defaultMessage: '添加' })}
            </div>
          </div>
        )}
      </Form>
    </div>
  );
}

export default observer(LarkMapping);
