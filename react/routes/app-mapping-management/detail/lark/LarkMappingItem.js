import React, { useContext, useEffect, useRef, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Icon, ExternalComponent, YqCodeMirror } from '@zknow/components';
import {
  message,
  Form,
  TextField,
  Select,
  Lov,
  Modal,
} from 'choerodon-ui/pro';
import classnames from 'classnames';
import Store from '../../stores';
import styles from '../AppMappingDetail.module.less';

const LarkMappingItem = observer(({ isEdit, record, handleAddLine, larkBusinessObjectId }) => {
  const { detailDataSet, c7nMappingDataSet, intl, openAppStore } = useContext(Store);
  const treeRef = useRef();

  const handleChangeRecord = () => {
    c7nMappingDataSet.current = record;
  };

  const handleDuplicate = () => {
    const targetData = record.toData();
    c7nMappingDataSet.create({
      ...targetData,
      mappingTable: {
        ...targetData.mappingTable,
        sourceValue: undefined,
        targetValue: undefined,
        sourceValueName: undefined,
        targetValueName: undefined,
      },
    });
  };
  const handleDelete = () => {
    c7nMappingDataSet.delete(record, false);
  };

  const fieldRenderer = () => {
    return record.get('sourceField') ? (
      <div>{`${record.get('sourceFieldName')}(${record.get('sourceField')})`}</div>
    ) : null;
  };

  const selectorRender = ({ value }) => {
    const field = value?.value;
    if (field?.name) {
      return (<div>{field.name}</div>);
    } else if (field) {
      return (<div>{field?.fieldName || '-'}({field?.fieldCode || '-'})</div>);
    }
    return null;
  };

  const openModal = (businessObjId, businessObjCode, shouldDisableCheckbox) => {
    if (businessObjId && businessObjCode) {
      Modal.open({
        title: intl.formatMessage({
          id: 'iam.appMappingManagement.desc.c7n.mapping.choose.field',
          defaultMessage: '选择字段',
        }),
        children: (
          <div>
            <ExternalComponent
              businessObjectId={businessObjId}
              businessObjectCode={businessObjCode}
              fieldCmpType="fieldCmpType"
              checkable
              keyField="path"
              dragable={false}
              treeRef={treeRef}
              defaultCheckedKeys={[]}
              searchable
              singleCheck
              displayCode
              shouldDisableCheckbox={shouldDisableCheckbox}
              system={{ scope: 'lc', module: 'FieldTree' }}
            />
          </div>
        ),
        drawer: false,
        style: { width: 800 },
        className: styles.fieldTreeModal,
        destroyOnClose: true,
        onOk: () => {
          const data = treeRef.current?.dataSet?.getState('selected')?.slice?.()?.[0]?.toData?.();
          record.set('sourceFieldDetail', data);
        },
      });
    } else {
      message.error(intl.formatMessage({
        id: 'iam.appMappingManagement.desc.c7n.mapping.choose.business.obj.first',
        defaultMessage: '请先填写业务对象',
      }));
    }
  };

  const shouldDisableCheckbox = (_record) => {
    const widgetType = _record?.get('widgetType');
    if (widgetType === 'MasterDetail') {
      return !_record?.get('relationLovId');
    }
    if (['Select', 'Radio'].includes(widgetType)) { // 下拉单选、单选框
      return false; // 可用
    }
    if (['MultipleSelect', 'SelectBox'].includes(widgetType)) { // 下拉多选、多选框
      return true; // 目前不可用（置灰）（未来可能改变）
    }
    return true; // 其余情况暂不可用（置灰）
  };

  const handleChooseSourceField = () => {
    const mappingRuleType = record.get('mappingRuleType');
    openModal(detailDataSet.current.get('businessObjectId'), detailDataSet.current.get('businessObjectCode'), mappingRuleType === 'DIRECT' ? undefined : shouldDisableCheckbox);
  };

  const renderYqValue = (isTarget) => {
    if (record.get('mappingRuleType') === 'DIRECT') {
      return null;
    }
    const fieldCode = isTarget ? record.get('sourceFieldDetail.value.code') : record.get('sourceFieldDetail.path');
    const type = record.get('mappingTable.type');
    // 燕千云值
    if (type === 'SELECT') {
      return (
        <Select
          record={record}
          disabled={!isEdit}
          name="mappingTable.sourceValueSelect"
          className={classnames(styles.oneItem)}
        >
          {(openAppStore.getSourceValues[fieldCode] || []).map((item) => (
            <Select.Option value={item.value}>{item.meaning}</Select.Option>
          ))}
        </Select>
      );
    } else if (type === 'LOV') {
      return (
        <Lov
          disabled={!isEdit}
          record={record}
          name="mappingTable.sourceValueLov"
          className={classnames(styles.oneItem)}
        />
      );
    } else if (type === 'LOOKUP') {
      return (
        <Select
          disabled={!isEdit}
          record={record}
          name="mappingTable.sourceValueLookup"
          className={classnames(styles.oneItem)}
        />
      );
    }
    return <TextField disabled />;
  };

  const renderLarkValue = () => {
    if (record.get('mappingRuleType') === 'DIRECT') {
      return null;
    }
    const targetFieldCode = record.get('targetFieldDetail.value.fieldCode');
    const allLarkFields = openAppStore.getLarkFields;
    const targetFields = allLarkFields.find(field => field.code === targetFieldCode);
    if (targetFields?.type === 'radioV2') {
      const storageOptions = openAppStore.getTargetOptions?.[targetFieldCode]?.length ? openAppStore.getTargetOptions[targetFieldCode] : null;
      const targetOptions = targetFields?.values?.length ? targetFields.values.map(item => ({ id: item.value, value: item.valueName })) : null;
      return (
        <Select
          disabled={!isEdit}
          record={record}
          name="mappingTable.targetValueDetail"
          className={styles.oneItem}
        >
          {(storageOptions || targetOptions || []).map((option) => (
            <Select.Option value={option}>
              {option?.value}
            </Select.Option>
          ))}
        </Select>
      );
    }
    return <TextField disabled />;
  };

  const renderYqField = () => {
    if (record.get('fieldType') === 'CONSTANT') {
      return (
        <div className={styles.constantItem}>
          <TextField
            disabled={!isEdit || !detailDataSet.current?.get('openMappingCode')}
            record={record}
            name="sourceFieldName"
            className={classnames({
              [styles.oneItem]: record.get('mappingRuleType') === 'MAPPING_TABLE',
              [styles.doubleItem]: record.get('mappingRuleType') === 'DIRECT',
            })}
            suffix={<Icon type="search" />}
            onClick={handleChooseSourceField}
            renderer={fieldRenderer}
            placeholder={intl.formatMessage({
              id: 'iam.appMappingManagement.desc.c7n.mapping.choose.field',
              defaultMessage: '选择字段',
            })}
          />
          {renderYqValue()}
        </div>
      );
    }
    return (
      <div className={styles.constantItem}>
        <YqCodeMirror
          mode="button"
          businessObjectId={detailDataSet?.current?.get('businessObjectId')}
          record={record}
          name="sourceMetaInfo.expression"
        />
      </div>
    );
  };

  const renderYqTargetField = () => {
    if (record.get('fieldType') === 'CONSTANT') {
      return (
        <div className={styles.constantItem}>
          <Select
            disabled={!isEdit || !detailDataSet.current?.get('openMappingCode')}
            record={record}
            name="sourceFieldDetail"
            className={classnames(
              record.get('mappingRuleType') === 'MAPPING_TABLE' && styles.oneItem, // 值映射
              record.get('mappingRuleType') === 'DIRECT' && styles.doubleItem, // 字段映射
            )}
            renderer={selectorRender}
          >
            {(openAppStore.getItemFields || []).map((field) => (
              <Select.Option value={field}>
                {field?.name}
              </Select.Option>
            ))}
          </Select>
          {renderYqValue(true)}
        </div>
      );
    }

    return (
      <div className={styles.constantItem}>
        <YqCodeMirror
          mode="button"
          businessObjectId={detailDataSet?.current?.get('businessObjectId')}
          record={record}
          name="sourceMetaInfo.expression"
        />
      </div>
    );
  };

  const renderLarkField = () => (
    <div className={styles.constantItem}>
      <Select
        disabled={!isEdit || !detailDataSet.current?.get('openMappingCode')}
        record={record}
        name="targetFieldDetail"
        className={classnames(
          record.get('mappingRuleType') === 'MAPPING_TABLE' && styles.oneItem, // 值映射
          record.get('mappingRuleType') === 'DIRECT' && styles.doubleItem, // 字段映射
        )}
        renderer={selectorRender}
      >
        {(openAppStore.getLarkFields || []).map((field) => (
          <Select.Option value={field}>
            {field?.name}
          </Select.Option>
        ))}
      </Select>
      {renderLarkValue()}
    </div>
  );

  return (
    <div
      className={styles.oneLine}
      onClick={handleChangeRecord}
    >
      <Select
        disabled={!isEdit}
        record={record}
        name="mappingRuleType" // 映射类型
        className={styles.shortItem}
      />
      <Select
        disabled={!isEdit || record.get('mappingRuleType') === 'MAPPING_TABLE'}
        record={record}
        name="fieldType" // 固定值/表达式
        className={styles.shortItem}
      />
      {detailDataSet.current?.get('direction') === 'YQ_LARK' ? (
        <>
          {renderYqField()}
          {renderLarkField()}
        </>
      ) : (
        <>
          {renderLarkField()}
          {renderYqTargetField()}
        </>
      )}
      <div className={classnames(styles.lastBtn, { [styles.hiddenItem]: !isEdit })}>
        <div
          className={styles.inlineBtn}
          onClick={handleDuplicate}
        >
          {intl.formatMessage({ id: 'zknow.common.button.copy', defaultMessage: '复制' })}
        </div>
        <div className={styles.inlineBtn} onClick={handleAddLine}>
          {intl.formatMessage({ id: 'zknow.common.button.append', defaultMessage: '添加' })}
        </div>
        <Icon
          style={{ cursor: 'pointer' }}
          onClick={handleDelete}
          type="close"
          size={14}
        />
      </div>
    </div>
  );
});

export default LarkMappingItem;
