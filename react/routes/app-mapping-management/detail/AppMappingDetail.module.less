@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.border {
  border-radius: 5px;
  border: 1px solid rgba(203, 210, 220, 0.5);
  padding: 16px;
  box-sizing: border-box;
  min-height: 32px;
}

.form {
  margin-top: 16px;

  :global {
    .c7n-pro-field, .c7n-pro-field.c7n-row {
      display: flex;
    }
  }
}

.c7nForm {
  :global {
    .c7n-pro-field, .c7n-pro-field.c7n-row {
      display: flex;
    }

    .c7n-pro-field-wrapper {
      width: 100%;
    }
  }
}

.underline {
  border-bottom: 1px #cbd2dc solid;
  height: 40px;
  padding-bottom: 5px;
  margin-bottom: 5px;
}

.oneLine {
  :global {
    .c7n-pro-field-wrapper {
      width: 100%;
    }
  }

  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;

  .shortItem {
    width: 100px;
    flex-grow: 0;
    margin-right: 20px;
    flex-shrink: 0;
  }

  .red {
    color: red;
  }

  .oneItem {
    width: 100px;
    flex-grow: 2;
    margin-right: 20px;
    flex-shrink: 0;
    //flex-basis: 100px;
  }

  .doubleItem {
    width: 220px;
    flex-grow: 2;
    margin-right: 20px;
    flex-shrink: 0;
  }

  .disappearItem {
    display: none;
  }

  .hiddenItem {
    visibility: hidden;
  }

  .longItem {
    flex-grow: 1;
    margin-right: 20px;
    width: 150px;
  }

  .constantItem {
    width: 230px;
    flex-grow: 2;
    margin-right: 20px;
    display: flex;
    align-items: center;
  }

  .lastBtn {
    width: 130px;
    flex-grow: 0;
    flex-shrink: 0;
    display: flex;
    //background-color: #1ab335;
    justify-content: space-around;
    align-items: center;
  }
}

.inlineBtn {
  display: inline-block;
  height: 0.2rem;
  padding: 0 0.08rem;
  cursor: pointer;
  background: @primary-x;
  border-radius: 0.02rem;
  border: 0.01rem solid @minor-color;
  font-size: 0.12rem;
  font-weight: 400;
  color: @primary-color;
}

.fieldTreeModal {
  :global {
    .lc-field-tree-field {
      max-width: 100% !important;
    }
  }
}
