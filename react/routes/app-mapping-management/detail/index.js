import React, { useEffect, useContext, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Content, TabPage, axios, Header } from '@yqcloud/apps-master';
import { Button, ExternalComponent } from '@zknow/components';
import { Spin, Form, TextField, Lov, Select, TextArea } from 'choerodon-ui/pro';
import Store from '../stores';
import styles from './AppMappingDetail.module.less';
import C7nMapping from './c7nMapping';
import LarkMapping from './lark';

const Detail = observer((props) => {
  const {
    match,
    match: {
      params: { id },
    },
    history,
    history: {
      location: { search },
    },
  } = props;
  const context = useContext(Store);
  const { intl, detailDataSet, tenantId, openAppStore, listDataSet } = context;
  const backUrl = match.path.substr(0, match.path.length - 11);
  const [isEdit, setEdit] = useState(false);
  const [projects, setProjects] = useState([]);
  const [issueType, setIssueType] = useState([]);
  const [conditions, setConditions] = useState([]);
  // 飞书审批任务和审批实例需要使用不同的业务对象
  const [larkBusinessObjectId, setLarkBusinessObjectId] = useState('');
  const [larkTaskBusinessObjectId, setLarkTaskBusinessObjectId] = useState('');

  // 请求飞书业务对象 id，直接请求即可，没必要限制
  useEffect(() => {
    (async () => {
      const instanceObject = axios.get(`lc/v1/${tenantId}/business_objects/code/ECOS_LARK_APPROVAL_INSTANCE`).catch(e => {});
      const taskObject = axios.get(`lc/v1/${tenantId}/business_objects/code/ECOS_LARK_APPROVAL_TASK`).catch(e => {});
      const instanceRes = await instanceObject;
      const taskRes = await taskObject;
      setLarkBusinessObjectId(instanceRes?.id || '');
      setLarkTaskBusinessObjectId(taskRes?.id || '');
    })();
  }, []);

  const refresh = async (_id) => {
    detailDataSet.setQueryParameter('id', _id);
    if (_id === '0') {
      setEdit(true);
      detailDataSet.create({ id: _id });
    } else {
      setEdit(false);
      await detailDataSet.query();
      detailDataSet.current?.set('id', _id);
      const initConditions = detailDataSet?.current?.get('enterCondition');
      if (initConditions) {
        setConditions(JSON.parse(initConditions));
      }
    }
  };

  useEffect(() => {
    if (id) {
      refresh(id);
    }
  }, [id]);

  const handleReceiveProjects = async (type) => {
    const openAppId = detailDataSet?.current?.get('openAppId');
    const projectId = detailDataSet?.current?.get('projectId');
    if (openAppId) {
      if (type === 'project') {
        // 请求猪齿鱼项目列表
        axios.get(`/iam/yqc/${tenantId}/field_mapping/projects?openAppId=${openAppId}`)
          .then(res => {
            setProjects(Array.isArray(res) ? res : []);
          })
          .catch(e => {
            setProjects([]);
          });
      } else if (type === 'issueType') {
        // 请求工作项
        axios.get(`/iam/yqc/${tenantId}/field_mapping/issueTypes?openAppId=${openAppId}${projectId ? `&projectId=${projectId}` : ''}`)
          .then(res => {
            setIssueType(Array.isArray(res) ? res : []);
          })
          .catch(e => {
            setIssueType([]);
          });
      }
    }
  };

  const handleConfirm = async () => {
    if (conditions) {
      detailDataSet.current.set('enterCondition', JSON.stringify(conditions));
    }
    const validateRes = await detailDataSet.validate();
    if (validateRes) {
      const res = await detailDataSet.submit();
      if (id !== '0') {
        await refresh(id);
      } else if (id === '0' && res?.success) {
        const newId = res.content[0]?.id;
        history.push({
          pathname: `${match.url.slice(0, -1)}${newId}`,
          search,
        });
      }
    }
  };

  const headerButtons = () => {
    return isEdit ? (
      <>
        <Button
          key="confirm"
          onClick={handleConfirm}
          funcType="raised"
          color="primary"
        >
          {intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}
        </Button>
        {id !== '0' && (
          <Button
            key="cancel"
            onClick={() => {
              refresh(id);
              setEdit(false);
            }}
            funcType="raised"
          >
            {intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}
          </Button>
        )}
      </>
    ) : (
      <Button
        color="primary"
        key="edit"
        icon="icon-edit"
        onClick={() => setEdit(true)}
        funcType="raised"
      >
        {intl?.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
      </Button>
    );
  };

  const handleOpenAppChange = (value) => {
    if (!value?.id) {
      setProjects([]);
      setIssueType([]);
      openAppStore.setMappingCodeOptions([]);
    } else if (value?.type === 'lark') {
      openAppStore.queryMappingCodeOptions(value.id);
    } else if (value?.type === 'choerodon') {
      handleReceiveProjects('project');
      handleReceiveProjects('issueType');
    }
  };

  const handleProjectChange = () => {
    handleReceiveProjects('issueType');
  };

  const handleItemChange = (value) => {};

  const renderConditions = () => {
    if (conditions?.length === 0 && !isEdit) {
      return <div>{intl.formatMessage({ id: 'zknow.common.model.noData', defaultMessage: '暂无数据' })}</div>;
    }

    let tableId = detailDataSet.current?.get('businessObjectId');
    const isLark = detailDataSet?.current?.get('openAppType') === 'lark';
    if (isLark) {
      const openMappingType = detailDataSet?.current?.get('openMappingType');
      if (detailDataSet?.current?.get('direction') === 'LARK_YQ') {
        tableId = openMappingType === 'APPROVAL_TASK' ? larkTaskBusinessObjectId : larkBusinessObjectId;
      }
    }

    return (
      <ExternalComponent
        system={{ scope: 'itsm', module: 'YqCondition' }}
        fieldTableData={[]}
        conditionData={conditions}
        // 飞书同步燕千云，则触发条件是飞书，业务对象需要取飞书
        tableId={tableId}
        onChange={(data) => setConditions(data)}
        readOnly={!isEdit}
        front="messageTemplate" // 需要“更新”字段
      />
    );
  };

  return (
    <TabPage>
      <Header
        backPath={`${backUrl}${search}`}
        dataSet={detailDataSet}
        onBackPathBtnClick={() => {
          listDataSet.query();
          history.push(`${backUrl}${search}`);
        }}
      >
        <h1>{intl.formatMessage({ id: 'iam.appMappingManagement.desc.c7n.mapping.mapping.detail', defaultMessage: '映射详情' })}</h1>
        <div>{headerButtons()}</div>
      </Header>
      <Content>
        <Spin dataSet={detailDataSet}>
          <Form
            labelWidth="auto"
            labelLayout="horizontal"
            columns={2}
            header={intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' })}
            dataSet={detailDataSet}
          >
            <TextField disabled={!isEdit} autoFocus name="name" />
            <Lov disabled={id !== '0'} name="businessObject" />
            <Lov
              disabled={!isEdit}
              name="openApp"
              onChange={handleOpenAppChange}
            />
            {detailDataSet?.current?.get('openAppType') === 'choerodon' && [
              <Select
                disabled={(!isEdit) || (projects?.length === 0)}
                name="project"
                onChange={handleProjectChange}
              >
                {projects.map((preProject) => (
                  <Select.Option value={preProject}>
                    {preProject.name}
                  </Select.Option>
                ))}
              </Select>,
              <Select disabled={(!isEdit) || (issueType?.length === 0)} name="issueType">
                {issueType.map((preType) => (
                  <Select.Option value={preType}>
                    {preType.name}
                  </Select.Option>
                ))}
              </Select>,
            ]}
            {detailDataSet?.current?.get('openAppType') === 'lark' && [
              <Select name="openMappingType" disabled={!isEdit} />,
              <Select disabled={!isEdit} name="openMappingCode">
                {openAppStore.getMappingCodeOptions.filter(op => op.enabledFlag).map(op => (
                  <Select.Option value={op.approvalCode}>
                    {op.name}
                  </Select.Option>
                ))}
              </Select>,
              <Select name="direction" disabled={!isEdit} />,
              ...(detailDataSet.current?.get('direction') === 'LARK_YQ' ? [
                <Select name="mappingTarget" disabled>
                  <Select.Option value="ITEM">{intl.formatMessage({ id: 'iam.appMappingManagement.model.mappingTarget.item', defaultMessage: '服务项' })}</Select.Option>
                  <Select.Option value="TEMPLATE">{intl.formatMessage({ id: 'iam.appMappingManagement.model.mappingTarget.template', defaultMessage: '工单模板' })}</Select.Option>
                </Select>,
                <Lov disabled={!isEdit} name="itemId" onChange={handleItemChange} />,
              ] : []),
            ]}
            <TextArea
              disabled={!isEdit}
              name="description"
              newLine
              colSpan={2}
              rows={1}
              autoSize={{ minRows: 1, maxRows: 4 }}
              resize="height"
            />
          </Form>
          <Form
            labelWidth="auto"
            labelLayout="horizontal"
            header={intl.formatMessage({ id: 'iam.appMappingManagement.desc.c7n.mapping.condition', defaultMessage: '触发条件' })}
            dataSet={detailDataSet}
            className={styles.form}
          >
            <div className={styles.border}>
              {renderConditions()}
            </div>
          </Form>
          {detailDataSet?.current?.get('openAppType') === 'choerodon' && <C7nMapping isEdit={isEdit} />}
          {detailDataSet?.current?.get('openAppType') === 'lark' && detailDataSet?.current?.get('direction') && (
            <LarkMapping
              isEdit={isEdit}
              larkBusinessObjectId={larkBusinessObjectId}
            />
          )}
        </Spin>
      </Content>
    </TabPage>
  );
});

export default Detail;
