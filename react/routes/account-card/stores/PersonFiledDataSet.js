import React, { useState, useEffect } from 'react';
import { axios } from '@yqcloud/apps-master';

export default ({ 
  intlPrefix, 
  intl, 
  tenantId, 
}) => {
  const urlPrefix = `/lc/v1/${tenantId}/card_configs/fields`;
  return {
    autoQuery: true,
    paging: false,
    selection: false,
    autoLocateFirst: true,
    transport: {
      read: ({ data }) => {
        return {
          url: `${urlPrefix}`,
          method: 'get',
        };
      },
    },
  };
};
