import React, { createContext, useMemo, useRef } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import { formatterCollections } from '@zknow/utils';
import AccountCardDataSet from './AccountCardDataSet';
import PersonFiledDataSet from './PersonFiledDataSet';
import ConfigDataSet from './ConfigDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState', 'HeaderStore')(formatterCollections({ code: 'iam.accountCard' })(injectIntl(
  observer((props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId }, userInfo: { language } },
      HeaderStore: {
        getTenantConfig: { supportLanguages },
      },
    } = props;
    const intlPrefix = 'iam.accountCard';
    const prefixCls = 'iam-account-card';

    const personFiledDataSet = useMemo(() => new DataSet(PersonFiledDataSet({ 
      tenantId, 
      intlPrefix, 
      intl,
    })), []);

    const configDataSet = useMemo(() => new DataSet(ConfigDataSet({ 
      personFiledDataSet,
      language,
    })), [personFiledDataSet, language]);

    const accountCardDataSet = useMemo(() => new DataSet(AccountCardDataSet({ 
      tenantId, 
      intlPrefix, 
      intl,
      configDataSet,
    })), []);

    const value = {
      ...props,
      tenantId,
      intlPrefix,
      prefixCls,
      accountCardDataSet,
      personFiledDataSet,
      configDataSet,
      language,
      supportLanguages,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
)));
