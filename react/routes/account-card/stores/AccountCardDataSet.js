import { v4 as uuidv4 } from 'uuid';

export default ({
  intlPrefix,
  intl,
  tenantId,
  configDataSet,
}) => {
  const urlPrefix = `/lc/v1/${tenantId}/card_configs`;
  return {
    autoQuery: false,
    paging: false,
    selection: false,
    autoLocateFirst: true,
    transport: {
      read: ({ data }) => {
        return {
          url: `${urlPrefix}`,
          method: 'get',
        };
      },
    },
    fields: [{
      name: 'badgeFlag',
      type: 'boolean',
    }],
    events: {
      beforeLoad: ({ dataSet, data }) => {
        try {
          const config = JSON.parse(data[0]?.config) || [];
          const newConfig = config.map((i) => {
            if (!i.id) {
              i.id = uuidv4();
            }
            return i;
          });
          data[0].config = JSON.stringify(newConfig);
          return data;
        } catch {
          //
        }
      },
      load: ({ dataSet }) => {
        try {
          const config = JSON.parse(dataSet.get(0).get('config')) || [];
          configDataSet.loadData(config);
        } catch {
          //
        }
      },
    },
  };
};
