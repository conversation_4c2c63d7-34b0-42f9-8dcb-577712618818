export default ({
  personFiledDataSet,
}) => {
  return {
    autoQuery: false,
    paging: false,
    selection: false,
    autoLocateFirst: true,
    field: [
      // 配置字段id
      { name: 'id', type: 'string' },
      // 配置字段code
      { name: 'code', type: 'string' },
      // 配置字段名称
      { name: 'name', type: 'string' },
      // // 显示字段
      // { name: 'label.en_US' },
      // { name: 'label.zh_CN' },
    ],
    events: {
      update({ name, record, value }) {
        if (name === 'code') {
          // 卡片预览默认值设置
          const filed = personFiledDataSet?.toData()?.find((i) => i.code === value);
          if (filed) {
            const nameField = filed?.widgetConfig?.relationLovNameFieldCode ? `${filed?.code}:${filed?.widgetConfig?.relationLovNameFieldCode}` : filed?.code;
            record.set('name', filed?.name);
            record?.set('nameField', nameField);
          }
        }
      },
    },
  };
};
