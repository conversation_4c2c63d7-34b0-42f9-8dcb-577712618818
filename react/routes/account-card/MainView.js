import React, { useContext, useMemo, useEffect, useState } from 'react';
import { Content, Header, TabPage } from '@yqcloud/apps-master';
import { observer } from 'mobx-react-lite';
import { Spin, message, CheckBox } from 'choerodon-ui/pro';
import { Button, Icon } from '@zknow/components';
import { axios } from '@yqcloud/apps-master';
import FiledTable from './components/filed-table';
import PreviewCard from './components/card';
import Store from './stores';
import './index.less';

const MainView = (props) => {
  const context = useContext(Store);
  const {
    intl,
    prefixCls,
    tenantId,
    accountCardDataSet,
    configDataSet,
  } = context;

  const [editing, setEditing] = useState(false);

  useEffect(() => {
    accountCardDataSet.query();
  }, []);

  // 渲染按钮
  const buttons = useMemo(() => {
    if (!editing) {
      return (
        <Button
          key="edit"
          funcType="raised"
          onClick={() => {
            setEditing(true);
          }}
          color="primary"
          icon="write"
        >
          {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
        </Button>
      );
    }
    return (
      <React.Fragment>
        <Button
          funcType="raised"
          style={{ float: 'right' }}
          onClick={() => handleCancel()}
        >
          {intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}
        </Button>
        <Button
          funcType="raised"
          color="primary"
          style={{ float: 'right', marginRight: '0.12rem' }}
          onClick={() => handleSave()}
        >
          {intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}
        </Button>
      </React.Fragment>
    );
  }, [editing]);

  async function handleCancel() {
    //
    try {
      const config = JSON.parse(accountCardDataSet?.current?.get('config'));
      configDataSet.loadData(config);
    } catch {
      //
    }
    setEditing(false);
  }

  async function handleSave() {
    const config = (configDataSet?.toData?.() || []).filter(c => c.code);
    const cardData = accountCardDataSet?.current?.toData();
    cardData.config = JSON.stringify(config);
    const res = await axios.put(`/lc/v1/${tenantId}/card_configs`, JSON.stringify(cardData));
    if (res?.failed) {
      message.error(res?.message);
    } else {
      message.success(intl.formatMessage({ id: 'zknow.common.success.save', defaultMessage: '保存成功' }));
      accountCardDataSet.query();
      setEditing(false);
    }
  }

  function renderLayout() {
    return (
      <div className={`${prefixCls}-main`}>
        <div className={`${prefixCls}-main-left`}>
          <div className="header-title">{intl.formatMessage({
            id: 'iam.accountCard.desc.account.card.detail',
            defaultMessage: '详情展示字段',
          })}</div>
          <FiledTable
            editing={editing}
          />
          <div className="header-title mt">{intl.formatMessage({
            id: 'iam.accountCard.desc.account.card.other',
            defaultMessage: '其他展示信息',
          })}</div>
          <div className={`${prefixCls}-badge`}>
            <CheckBox
              dataSet={accountCardDataSet}
              name="badgeFlag"
              disabled={!editing}
            >
              {intl.formatMessage({ id: 'iam.accountCard.desc.account.card.badge', defaultMessage: '个人勋章' })}
            </CheckBox>
          </div>
        </div>
        <div className={`${prefixCls}-main-flex-right`}>
          <div className={`${prefixCls}-main-right`}>
            <div className="header-title">{intl.formatMessage({ id: 'iam.accountCard.desc.account.card.preview', defaultMessage: '名片预览' })}</div>
            <PreviewCard />
          </div>
        </div>
      </div>
    );
  }

  const renderMain = () => {
    if (accountCardDataSet?.getState('loading')) {
      return <div style={{ width: '100%', height: '150px', textAlign: 'center', paddingTop: '100px' }}><Spin /></div>;
    }

    return (
      <TabPage className={prefixCls}>
        <Header dataSet={accountCardDataSet}>
          <h1>{intl.formatMessage({ id: 'iam.accountCard.title', defaultMessage: '人员名片' })}</h1>
          <div className="btn-groups">{buttons}</div>
        </Header>
        <Content className={`${prefixCls}-content`}>
          {renderLayout()}
        </Content>
      </TabPage>
    );
  };

  return renderMain();
};

export default observer(MainView);
