/* eslint-disable jsx-a11y/alt-text */
import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Select, Tooltip } from 'choerodon-ui/pro';
import classnames from 'classnames';
import PersonCard from '@/assets/images/person-card.png';
import Store from '../../stores';
import './index.less';

const { Option } = Select;

const badges = ['copper', 'gold', 'purple', 'silver', 'copper'];

const UserCard = (props) => {
  const context = useContext(Store);
  const {
    intl,
    configDataSet,
    language,
    accountCardDataSet,
  } = context;

  function renderHeader() {
    return (
      <div className="preview-main-header">
        <img className="preview-main-header-img" src={PersonCard} />
        <div className="preview-main-header-name">
          {intl.formatMessage({ id: 'iam.accountCard.desc.username', defaultMessage: '燕小千' })}
        </div>
      </div>
    );
  }

  function renderContent() {
    const labelCLassName = classnames({
      'tooltip-content-cell': true,
      'tooltip-content-cell-title': true,
    });
    const valueCLassName = classnames({
      'tooltip-content-cell': true,
      'tooltip-content-cell-value': true,
    });
    return (
      <div className="preview-main-content">
        {configDataSet?.map(i => {
          return (
            <div className="preview-main-content-row">
              <div className={labelCLassName} title={i?.get(`label.${language}`)}>{i?.get(`label.${language}`)}</div>
              <div className={valueCLassName}>{i?.get(language) || intl.formatMessage({ id: 'iam.accountCard.desc.sample', defaultMessage: '示例值' })}</div>
            </div>
          );
        })}
        {accountCardDataSet?.current?.get('badgeFlag') && (
          <div className="preview-main-content-row">
            <div className={labelCLassName}>{intl.formatMessage({
              id: 'iam.accountCard.desc.account.card.badges',
              defaultMessage: '勋章',
            })}</div>
            <div className="tooltip-content-badge">
              <div className="tooltip-content-badge-cell">
                {badges.map((badge, i) => (
                  <Tooltip title={intl.formatMessage({ id: 'iam.accountCard.desc.sample', defaultMessage: '示例值' })}>
                    <span className="tooltip-content-badge-item"><img
                      src={`${window._env_.ICON_SERVER}/static/itsm/medal/${badge}-${i + 1}.svg`}
                      alt=""
                    /></span>
                  </Tooltip>
                ))}
                <div className="tooltip-content-badge-cell-after">{intl.formatMessage({
                  id: 'iam.accountCard.desc.sample.badge.count',
                  defaultMessage: '共{count}个',
                }, { count: '99+' })}</div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="preview-main">
      {renderHeader()}
      {renderContent()}
    </div>
  );
};

export default observer(UserCard);
