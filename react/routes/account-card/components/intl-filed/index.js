/* eslint-disable jsx-a11y/alt-text */
import React, { useContext, useState, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { TextField, Icon, Modal, DataSet, Form } from 'choerodon-ui/pro';
import classnames from 'classnames';
import Locale from '../../stores/locale';
import Store from '../../stores';
import './index.less';

const modalKey = Modal.key();

const IntlField = (props) => {
  const {
    name,
    record,
    style = {},
  } = props;
  const context = useContext(Store);
  const {
    intl,
    prefixCls,
    intlPrefix,
    tenantId,
    language,
    supportLanguages,
  } = context;

  const languages = supportLanguages.map(r => r.code);
  const intlDataSet = useMemo(() => new DataSet({
    fields: supportLanguages.map(r => ({ name: r.code, label: r.name })),
    data: [record?.get('label') || {}],
  }), [record?.get('label')]);

  function handleClick() {
    Modal.open({
      title: intl.formatMessage({ id: 'zknow.common.model.intl', defaultMessage: '多语言' }),
      children: (
        <Form dataSet={intlDataSet} labelWidth="auto">
          {languages.map((i) => <TextField name={i} />)}
        </Form>
      ),
      key: modalKey,
      drawer: false,
      destroyOnClose: true,
      onOk: () => {
        supportLanguages.forEach((r) => {
          record?.set(`label.${r.code}`, intlDataSet?.current?.get(r.code));
        });
        intlDataSet.reset();
      },
      onCancel: () => {
        intlDataSet.reset();
      },
    });
  }

  return (
    <TextField 
      name={name}
      record={record}
      style={style}
      suffix={<Icon className="prefixCls-intl" type="language" onClick={() => handleClick()} />} 
    />
  );
};

export default observer(IntlField);
