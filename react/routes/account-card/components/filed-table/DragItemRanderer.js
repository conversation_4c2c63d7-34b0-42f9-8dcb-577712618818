import React, { useContext, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Icon, Button } from '@zknow/components';
import { Select, Output, TextField } from 'choerodon-ui/pro';
import classnames from 'classnames';
import { action } from 'mobx';
import IntlField from '../intl-filed';
import Store from '../../stores';
import './index.less';

const { Option } = Select;

const DragItemRanderer = (props) => {
  const {
    editing,
  } = props;
  const context = useContext(Store);
  const {
    intl,
    prefixCls,
    intlPrefix,
    tenantId,
    accountCardDataSet,
    personFiledDataSet,
    configDataSet,
    language,
  } = context;

  const ISelect = accountCardDataSet?.getState('editing') ? Select : Output;
  const ITextField = accountCardDataSet?.getState('editing') ? IntlField : Output;

  function handleDelete(e, item) {
    if (e) e.stopPropagation();
    const id = item?.get('id');
    const data = configDataSet?.toData();
    if (data?.map(i => i.id.includes(id))) {
      data.splice(data.findIndex(j => j.id === id), 1);
    }
    configDataSet.loadData(data);
  }

  function renderConfig(item) {
    if (!accountCardDataSet?.getState('editing')) {
      return <ISelect record={item} style={{ width: '100%' }} name="name" />;
    }
    return (
      <ISelect
        record={item}
        style={{ width: '100%' }}
        name="code"
        searchable
      >
        {(personFiledDataSet?.toData()?.filter(i => i.name) || [])?.map((i) => {
          return (
            <Option
              value={i.code}
              key={i.code}
              disabled={((configDataSet?.toData() || [])?.map(j => j.code))?.includes(i.code)}
            >
              {i.name}
            </Option>
          );
        })}
      </ISelect>
    );
  }

  function renderShow(item) {
    return (
      <ITextField
        prefixCls={prefixCls}
        style={{ width: '100%' }}
        name={`label.${language}`}
        record={item}
      />
    );
  }

  const dragCLassName = classnames({
    'tooltip-content-cell': true,
    'tooltip-content-cell-drag': true,
    'tooltip-content-cell-drag-readonly': !accountCardDataSet?.getState('editing'),
  });
  const configCLassName = classnames({
    'tooltip-content-cell': true,
    'tooltip-content-cell-config': true,
  });
  const showCLassName = classnames({
    'tooltip-content-cell': true,
    'tooltip-content-cell-show': true,
  });
  const deleteCLassName = classnames({
    'tooltip-content-cell': true,
    'tooltip-content-cell-delete': true,
    'tooltip-content-cell-delete-readonly': !accountCardDataSet?.getState('editing'),
  });

  function renderMain() {
    return configDataSet?.map((item, index) => {
      const className = classnames({
        'tooltip-content-row': true,
        'tooltip-content-row-body': true,
        'tooltip-content-row-readonly': !accountCardDataSet?.getState('editing'),
        'tooltip-content-row-last': index === configDataSet?.length - 1,
      });
      return (
        <div className={className} key={item?.get('id')}>
          <div className="tooltip-content-row-right">
            <div className={dragCLassName}>
              <Icon type="drag" />
            </div>
            <div className={configCLassName}>
              {renderConfig(item)}
            </div>
            <div className={showCLassName}>
              {renderShow(item)}
            </div>
          </div>
          <div className="tooltip-content-row-left">
            <div className={deleteCLassName}><Icon onClick={(e) => handleDelete(e, item)} type="close" /></div>
          </div>
        </div>
      );
    });
  }

  return renderMain();
};

export default observer(DragItemRanderer);
