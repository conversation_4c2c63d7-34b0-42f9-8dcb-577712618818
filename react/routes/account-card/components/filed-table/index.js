import React, { useContext, useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Icon, Button } from '@zknow/components';
import { Select, Output, TextField } from 'choerodon-ui/pro';
import classnames from 'classnames';
import { action } from 'mobx';
import ReactSortable from 'react-sortablejs';
import { v4 as uuidv4 } from 'uuid';
import DragItemRanderer from './DragItemRanderer';
import Store from '../../stores';
import './index.less';

const { Option } = Select;

const FiledTable = (props) => {
  const {
    editing,
  } = props;
  const context = useContext(Store);
  const {
    intl,
    prefixCls,
    intlPrefix,
    tenantId,
    accountCardDataSet,
    personFiledDataSet,
    configDataSet,
  } = context;

  useEffect(() => {
    accountCardDataSet?.setState('editing', editing);
  }, [editing]);

  function renderAddButton() {
    if (!editing) return null;
    return (
      <Button
        className="table-add-button"
        key="add"
        funcType="raised"
        color="primary"
        icon="plus"
        onClick={() => {
          configDataSet.create({
            id: uuidv4(),
          });
        }}
      >
        {intl.formatMessage({ id: 'zknow.common.button.add', defaultMessage: '新增' })}
      </Button>
    );
  }

  async function handleUpdate(evt) {
    const { oldIndex, newIndex } = evt;
    const currentButtons = configDataSet.filter((v) => v.status !== 'delete');
    const oldButton = currentButtons[oldIndex];
    const realNewIndex = currentButtons[newIndex].index;
    const realOldIndex = oldButton.index;
    await configDataSet.splice(
      realOldIndex > realNewIndex ? realNewIndex : realNewIndex + 1,
      0,
      oldButton
    );
    oldButton.status = 'update';
  }

  // 渲染提示内容
  function renderTable() {
    const dragCLassName = classnames({
      'tooltip-content-cell': true,
      'tooltip-content-cell-drag': true,
    });
    const configCLassName = classnames({
      'tooltip-content-cell': true,
      'tooltip-content-cell-config': true,
    });
    const showCLassName = classnames({
      'tooltip-content-cell': true,
      'tooltip-content-cell-show': true,
    });

    const sortableOption = {
      animation: 150,
      fallbackOnBody: true,
      swapThreshold: 0.65,
      filter: '.components-tag',
    };

    return (
      <div className="filed-table-tooltip-main">
        <div className="tooltip-content">
          <div className="tooltip-content-row tooltip-content-row-header">
            <div className="tooltip-content-row-right">
              <div className={dragCLassName}> <Icon style={{ visibility: 'hidden' }} type="drag" /></div>
              <div className={configCLassName}>{intl.formatMessage({ id: 'iam.accountCard.desc.account.card.config.field', defaultMessage: '配置字段' })}</div>
              <div className={showCLassName}>{intl.formatMessage({ id: 'iam.accountCard.desc.account.card.show.field', defaultMessage: '显示字段' })}</div>
            </div>
          </div>
          <div className="table-body">
            <div className="tooltip-content-row">
              {
                accountCardDataSet?.getState('editing') ? (
                  <ReactSortable
                    key="tooltip-content-row-sorttable"
                    style={{ width: '100%' }}
                    options={{
                      ...sortableOption,
                      ghostClass: 'ghost',
                      group: {
                        name: 'Section',
                        pull: false,
                        put: ['Section'],
                      },
                      onUpdate: action(handleUpdate),
                    }}
                  >
                    <DragItemRanderer {...props} />
                  </ReactSortable>
                ) : (
                  <div>
                    <DragItemRanderer {...props} />
                  </div>
                )
              }
            </div>
            {renderAddButton()}
          </div>
        </div>
      </div>
    );
  }

  return renderTable();
};

export default observer(FiledTable);
