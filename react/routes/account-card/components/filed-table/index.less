.filed-table-tooltip-main {
  .tooltip-content {
    width: 100%;
    border: 1px solid #E5E6EB;
    &-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      &-header {
        padding: 12px 18px 12px 6px;
        border-bottom: solid 1px #E5E6EB;
        font-size: 14px;
        font-weight: 500;
        color: #12274D;
        line-height: 22px;
      }
      &-body {
        cursor: move;
        padding: 0px;
        margin-bottom: 8px;
      }
      &-last {
        margin-bottom: 0px;
      }
      &-readonly {
        cursor: auto;
      }
      &-right {
        display: flex;
        align-items: center;
      }
      &-left {
        .yqcloud-icon-park-wrapper {
          color: #12274D;
          cursor: pointer;
        }
      }
    }
    &-cell {
      &-drag {
        margin-right: 12px;
        &-readonly {
          .yqcloud-icon-park-wrapper {
            visibility: hidden;
          }
        }
        .yqcloud-icon-park-wrapper {
          position: relative;
          top: 3px;
        }
      }
      &-config {
        width: 357px;
        margin-right: 8px;
      }
      &-show {
        width: 357px;
      }
      &-delete {
        &-readonly {
          display: none;
        }
      }
    }

    .table-body {
      padding: 12px 18px 12px 6px;
      // 添加按钮
      .table-add-button {
        margin-left: 28px;
        margin-top: 8px;
      }
    }
  }
}