export default ({ intlPrefix, intl, tenantId }) => {
  const url = `/iam/yqc/v1/${tenantId}/interface-login`;

  return {
    autoQuery: true,
    selection: false,
    fields: [
      {
        name: 'enabledFlag',
        type: 'boolean',
        label: intl.formatMessage({ id: 'iam.interfaceLogin.model.interface.login.enabled.flag', defaultMessage: '接口认证登录' }),
        defaultValue: false,
      },
      {
        name: 'serverAddress',
        required: true,
        type: 'string',
        label: intl.formatMessage({ id: 'iam.interfaceLogin.model.interface.login.server.address', defaultMessage: '接口地址' }),
      },
      {
        name: 'loginNameField',
        required: true,
        type: 'string',
        label: intl.formatMessage({ id: 'iam.interfaceLogin.model.interface.login.login.name.field', defaultMessage: '登录名' }),
      },
      {
        name: 'loginPasswordField',
        required: true,
        type: 'string',
        label: intl.formatMessage({ id: 'iam.interfaceLogin.model.interface.login.login.password.field', defaultMessage: '密码' }),
      },
      {
        name: 'account',
        type: 'string',
        ignore: 'always',
        label: intl.formatMessage({ id: 'iam.interfaceLogin.model.interface.login.account', defaultMessage: '账号' }),
      },
      {
        name: 'loginPassword',
        type: 'string',
        ignore: 'always',
        label: intl.formatMessage({ id: 'iam.interfaceLogin.model.interface.login.login.password.field', defaultMessage: '密码' }),
      },
    ],
    transport: {
      read: {
        url,
        method: 'GET',
        params: {},
      },
      create: ({ data: [data] }) => ({
        url,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => ({
        url,
        method: 'put',
        data,
      }),
    },
    events: {
      load: ({ dataSet }) => {
        if (!dataSet.current?.get('id')) {
          dataSet.create();
        }
      },
    },
  };
};
