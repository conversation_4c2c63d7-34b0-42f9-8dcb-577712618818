import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import { formatterCollections } from '@zknow/utils';
import InterFaceLoginDataSet from './InterFaceLoginDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: 'iam.interfaceLogin' })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { tenantId } },
    } = props;
    const intlPrefix = 'iam.interface.login';
    const prefixCls = 'iam-interface-login';

    const interFaceLoginDataSet = useMemo(() => new DataSet(InterFaceLoginDataSet({ intlPrefix, intl, tenantId })), []);

    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      tenantId,
      interFaceLoginDataSet,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
