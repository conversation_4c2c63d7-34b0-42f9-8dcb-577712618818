import React, { useContext, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { message } from 'choerodon-ui';
import { Form, TextField, Password, Modal, Output } from 'choerodon-ui/pro';
import axios from 'axios';
import { Constants } from '@zknow/utils';
import { Button, TableStatus } from '@zknow/components';
import { Content, Header, TabPage } from '@yqcloud/apps-master';

import Store from './stores';

const { MULTIPLE_FORM_TOP } = Constants;

const MainView = () => {
  const { intl, prefixCls, interFaceLoginDataSet, tenantId } = useContext(Store);

  const [editing, setEditing] = useState(false);
  const record = interFaceLoginDataSet.current;

  const extraSaveBtn = (
    <>
      <Button
        color="primary"
        onClick={async () => {
          await interFaceLoginDataSet.submit();
          setEditing(false);
          interFaceLoginDataSet.query();
        }}
      >
        {intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}
      </Button>
      <Button
        color="secondary"
        onClick={async () => {
          setEditing(false);
          record.reset();
        }}
      >
        {intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}
      </Button>
    </>
  );

  const testModal = () => {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.interfaceLogin.desc.interface.login.test', defaultMessage: '认证测试' }),
      children: (
        <Form dataSet={interFaceLoginDataSet}>
          <TextField name="account" required />
          <Password name="loginPassword" required />
        </Form>
      ),
      okText: intl.formatMessage({ id: 'iam.interfaceLogin.desc.interface.login.test.login', defaultMessage: '测试登录' }),
      onOk: async () => {
        const params = {
          account: record?.get('account'),
          loginPassword: record?.get('loginPassword'),
        };
        const testRes = await axios.post(`/iam/yqc/v1/${tenantId}/interface-login/${record.get('id')}/test-connect`, params);
        if (testRes?.failed) {
          message.error(testRes?.message);
        } else {
          message.success(intl.formatMessage({ id: 'iam.interfaceLogin.desc.interface.login.test.success', defaultMessage: '测试成功' }));
        }
        return false;
      },
    });
  };

  const extraBtn = (
    <>
      <Button
        color="primary"
        icon="icon-edit"
        onClick={() => {
          setEditing(true);
        }}
      >
        {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
      </Button>
      <Button
        color="secondary"
        icon={record?.get('enabledFlag') ? 'ReduceOne' : 'CheckOne'}
        onClick={async () => {
          const validate = await interFaceLoginDataSet.validate();
          if (validate) {
            const statusRes = await axios.put(`/iam/yqc/v1/${tenantId}/interface-login/${record?.get('enabledFlag') ? 'disable' : 'enable'}`, { ...record.toData() });
            if (statusRes?.failed) {
              message.error(statusRes?.message);
            } else {
              interFaceLoginDataSet.query();
            }
          } else {
            message.error(intl.formatMessage({ id: 'iam.interfaceLogin.desc.interface.login.status.message', defaultMessage: '启用失败，请补充接口认证信息' }));
          }
        }}
      >
        {
          record?.get('enabledFlag')
            ? intl.formatMessage({ id: 'iam.interfaceLogin.login.disabled', defaultMessage: '禁用接口认证' })
            : intl.formatMessage({ id: 'iam.interfaceLogin.login.enabled', defaultMessage: '启用接口认证' })
        }
      </Button>
      <Button
        color="secondary"
        icon="icon-link"
        onClick={async () => {
          const validate = await interFaceLoginDataSet.validate();
          if (validate) {
            testModal();
          } else {
            message.error(intl.formatMessage({ id: 'iam.interfaceLogin.desc.interface.login.test.message', defaultMessage: '请补充接口认证信息' }));
          }
        }}
      >
        {intl.formatMessage({ id: 'iam.interfaceLogin.desc.interface.login.test', defaultMessage: '认证测试' })}
      </Button>
    </>
  );

  const renderEnabled = ({ value }) => {
    return (
      <TableStatus
        status={value}
        enabledText={intl.formatMessage({ id: 'iam.interfaceLogin.desc.interface.login.enable', defaultMessage: '已启用' })}
        disabledText={intl.formatMessage({ id: 'iam.interfaceLogin.desc.interface.login.disable', defaultMessage: '未启用' })}
      />
    );
  };

  return (
    <TabPage>
      <Header dataSet={interFaceLoginDataSet}>
        <h1>{intl.formatMessage({ id: 'iam.interfaceLogin.desc.interface.login.page.title', defaultMessage: '接口认证登录' })}</h1>
        <div>{editing ? extraSaveBtn : extraBtn}</div>
      </Header>
      <Content className={prefixCls}>
        <Form
          record={record}
          columns={2}
          disabled
        >
          <Output name="enabledFlag" renderer={renderEnabled} />
        </Form>
        <Form
          record={record}
          header={intl.formatMessage({ id: 'iam.interfaceLogin.desc.interface.login.auth.info', defaultMessage: '认证信息' })}
          columns={2}
          disabled={!editing}
          className={MULTIPLE_FORM_TOP}
        >
          <TextField name="serverAddress" colSpan={2} />
          <TextField name="loginNameField" newLine />
          <TextField name="loginPasswordField" />
        </Form>
      </Content>
    </TabPage>
  );
};

export default observer(MainView);
