@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.security-policy-main {
  height: 100%;
}

.security-policy-tabs {
  width: 100%;

  .c7n-tabs-bar {
    margin-bottom: 0.24rem;
  }
}

.security-policy-buttons {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 0.24rem;
  padding: 0 15%;
  min-width: 12rem;
}

.security-policy-content {
  height: 100%;
  background-color: #fff;

  .icon-edit {
    margin-right: 0.04rem;
    font-size: 0.14rem;
  }

  .button-grey {
    color: #595959;
    font-size: 0.14rem;
  }
}

.security-policy-form-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 15%;
  min-width: 12rem;

  &-wrapper {
    width: 100%;
    padding: 1px;
  }

  .c7n-pro-field-wrapper {
    padding: 0;
  }
}

.security-policy-card {
  background-color: #f7f9fc;
  border-radius: 0.04rem;
  padding: 0.08rem 0.24rem;
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.12rem;

  .card-title {
    font-size: 0.14rem;
    font-weight: 500;
    line-height: 0.2rem;

    &-help {
      font-size: 0.12rem;
      font-weight: 400;
      color: @yq-text-6;
      line-height: 0.2rem;
    }
  }

  .card-right {
    display: flex;
    align-items: center;
    .card-value-help {
      font-size: 0.16rem;
      color: @yq-text-4;
      margin-left: 0.08rem;
      vertical-align: text-bottom;
    }

    &-suffix {
      line-height: 0.32rem;
    }
  }

  .card-value-input {
    width: 1.1rem;
    margin: 0 0.08rem;
  }

  .card-value-output {
    font-size: 0.24rem;
    font-weight: 500;
    color: @text-color;
    line-height: 0.32rem;
    margin: 0 0.08rem;
  }

  .complexity-suffix {
    display: flex;
    align-items: center;
    line-height: 0.32rem;
    font-size: 0.14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);

    .box-group {
      display: inline-flex;
      align-items: center;
      margin-left: 0.08rem;
    }
  }

  .output-content {
    display: inline-flex;
    align-items: center;
  }
}
