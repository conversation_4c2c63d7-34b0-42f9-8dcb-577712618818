import React, { useState, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import { message } from 'choerodon-ui/pro';
import { Button } from '@zknow/components';

const ButtonGroup = ({
  onChangeEdit,
  intl,
  dataSet,
}) => {
  const [isEdit, setIsEdit] = useState(false);

  const handleOk = async (ds) => {
    try {
      const characterTypeCount = ds.current.get('characterTypeCount');
      const checkLength = ['lowercaseCount', 'uppercaseCount', 'digitsCount', 'specialCharCount'].filter((item) => ds.current.get(item)).length;
      if (characterTypeCount > checkLength) {
        message.error(intl.formatMessage({ id: 'iam.security.length.passwordComplexityRequirements', defaultMessage: '密码复杂度要求必须包含{count}种字符' }, { count: characterTypeCount }));
        return false;
      } else if (await ds.submit()) {
        await ds.query();
        setIsEdit(false);
        onChangeEdit(false);
      } else {
        return false;
      }
    } catch (err) {
      return false;
    }
  };

  const handleClickSaveBtn = useCallback(async () => {
    await handleOk(dataSet);
  }, [dataSet.current]);

  const handleClickCancelBtn = useCallback(() => {
    setIsEdit(false);
    onChangeEdit(false);
    dataSet.reset();
  }, []);

  const handleClickEditBtn = useCallback(() => {
    setIsEdit(true);
    onChangeEdit(true);
  }, [isEdit]);

  if (isEdit) {
    return (
      <div>
        <Button key="confirm" funcType="raised" color="primary" onClick={handleClickSaveBtn}>
          {intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}
        </Button>
        <Button key="cancel" funcType="raised" onClick={handleClickCancelBtn}>
          {intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}
        </Button>
      </div>
    );
  }

  return (
    <div>
      <Button key="edit" icon="icon-edit" funcType="raised" color="primary" onClick={handleClickEditBtn}>
        {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
      </Button>
    </div>
  );
};

export default observer(ButtonGroup);
