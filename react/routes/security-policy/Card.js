import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Tooltip } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import Store from './stores';
import './index.less';

const Card = (props) => {
  const { title, titleHelp, value, valueHelp, valueSuffix } = props;
  const { prefixCls } = useContext(Store);

  return (
    <div className={`${prefixCls}-card`}>
      <div>
        <div className="card-title">{title}</div>
        <div className="card-title-help">{titleHelp}</div>
      </div>
      <div className="card-right">
        {value}
        <div className="card-right-suffix">{valueSuffix}</div>
        {valueHelp && <div className="card-right-suffix">
          <Tooltip placement="top" title={valueHelp}>
            <Icon className="card-value-help" type="Help" />
          </Tooltip>
        </div>}
      </div>
    </div>
  );
};

export default observer(Card);
