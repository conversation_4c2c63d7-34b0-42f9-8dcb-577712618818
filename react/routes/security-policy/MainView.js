import React, { useContext, Fragment, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { TabPage, Content, Header } from '@yqcloud/apps-master';
import Store from './stores';
import PasswordSettings from './PasswordSettings';
import ButtonGroup from './ButtonGroup';
import './index.less';

const MainView = () => {
  const { prefixCls, intl, intlPrefix, securityPolicyDataSet } = useContext(Store);
  const [IsEdit, setIsEdit] = useState(false);

  const handleClickEdit = (flag) => setIsEdit(flag);

  return (
    <Fragment>
      <TabPage>
        <Header>
          <h1>{intl.formatMessage({ id: 'iam.security.title', defaultMessage: '安全策略' })}</h1>
          <ButtonGroup
            onChangeEdit={handleClickEdit}
            dataSet={securityPolicyDataSet}
            prefixCls={prefixCls}
            intl={intl}
            intlPrefix={intlPrefix}
          />
        </Header>
        <Content className={`${prefixCls}-content`}>
          <PasswordSettings isEdit={IsEdit} />
        </Content>
      </TabPage>
    </Fragment>
  );
};

export default observer(MainView);
