import React, { useContext, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, NumberField, CheckBox, Switch, Select } from 'choerodon-ui/pro';
import DivisionTitle from '../../components/division-title';
import Card from './Card';
import Store from './stores';

const PasswordSettings = ((props) => {
  const { prefixCls, intl, intlPrefix, securityPolicyDataSet } = useContext(Store);
  const { isEdit } = props;
  const helpIconStyle = useMemo(() => ({ color: 'rgba(0, 0, 0, 0.25)', fontSize: '0.16rem', marginLeft: '0.04rem' }), []);

  // 密码有效期
  const validityPeriod = intl.formatMessage({ id: 'iam.security.model.passwordValidityPeriod', defaultMessage: '密码有效期' });
  const validityPeriodInfo = intl.formatMessage({ id: 'iam.security.tips.passwordValidityPeriod', defaultMessage: '可填写0-365，"0"表示永不过期' });
  const validityPeriodHelp = intl.formatMessage({ id: 'iam.security.help.passwordValidityPeriod', defaultMessage: '密码过期后需要重置密码，再使用新密码登录' });
  const validityPeriodSuffix = intl.formatMessage({ id: 'iam.security.unit.passwordValidityPeriod', defaultMessage: '天' });
  const validityPeriodValue = securityPolicyDataSet?.current?.get('passwordUpdateRate');
  // 密码到期提醒
  const passwordReminderPeriod = intl.formatMessage({ id: 'iam.security.model.passwordExpirationReminder', defaultMessage: '密码到期提醒' });
  const passwordReminderPeriodInfo = intl.formatMessage({ id: 'iam.security.tips.passwordExpirationReminder', defaultMessage: '可填写0-365不能大于密码有效期天数，"0"表示不提醒' });
  const passwordReminderPeriodHelp = intl.formatMessage({ id: 'iam.security.help.passwordExpirationReminder', defaultMessage: '密码到期前提醒用户' });
  const passwordReminderPeriodSuffix = intl.formatMessage({ id: 'iam.security.unit.passwordExpirationReminder', defaultMessage: '天前' });
  const passwordReminderPeriodValue = securityPolicyDataSet?.current?.get('passwordReminderPeriod');
  // 密码复杂要求
  const complexityRequirements = intl.formatMessage({ id: 'iam.security.model.passwordComplexityRequirements', defaultMessage: '密码复杂要求' });
  const complexityRequirementsSuffix = intl.formatMessage({ id: 'iam.security.unit.passwordComplexityRequirements', defaultMessage: '种字符类型' });
  const complexityRequirementsHelp = intl.formatMessage({ id: 'iam.security.help.passwordComplexityRequirements', defaultMessage: '密码特殊设置' });
  const complexityRequirementsValueHelp = intl.formatMessage({ id: 'iam.security.help.passwordComplexityValue', defaultMessage: '特殊字符包括：[ ]~~@#$%&*\\-_=+l/()<>,.;:!' });
  const complexityRequirementsValue = securityPolicyDataSet?.current?.get('characterTypeCount');
  // 密码最小长度
  const minLength = intl.formatMessage({ id: 'iam.security.model.passwordMinLength', defaultMessage: '密码最小长度' });
  const minLengthInfo = intl.formatMessage({ id: 'iam.security.tips.passwordMinLength', defaultMessage: '可填写5-50' });
  const minLengthSuffix = intl.formatMessage({ id: 'iam.security.unit.passwordMinLength', defaultMessage: '个字符' });
  const minLengthHelp = intl.formatMessage({ id: 'iam.security.help.passwordMinLength', defaultMessage: '密码输入最小长度' });
  const minLengthValue = securityPolicyDataSet?.current?.get('minLength');
  // 禁用历史密码
  const disabledHistory = intl.formatMessage({ id: 'iam.security.model.passwordDisabledHistory', defaultMessage: '禁用历史密码' });
  const disabledHistoryInfo = intl.formatMessage({ id: 'iam.security.tips.passwordDisabledHistory', defaultMessage: '可填写0-24, "0"表示不禁用任何历史密码' });
  const disabledHistoryPrefix = intl.formatMessage({ id: 'iam.security.prefix.passwordDisabledHistory', defaultMessage: '最近设置过的' });
  const disabledHistorySuffix = intl.formatMessage({ id: 'iam.security.suffix.passwordDisabledHistory', defaultMessage: '个密码不能作为新密码' });
  const disabledHistoryHelp = intl.formatMessage({ id: 'iam.security.help.passwordDisabledHistory', defaultMessage: '历史密码不能作为新密码' });
  const disabledHistoryValue = securityPolicyDataSet?.current?.get('notRecentCount');

  // 密码复杂要求类型
  const includeMust = intl.formatMessage({ id: 'iam.security.desc.include.must', defaultMessage: '必须包含' });
  const atLeastMust = intl.formatMessage({ id: 'iam.security.desc.include.at.least', defaultMessage: '至少包含' });
  const uppercaseLetter = intl.formatMessage({ id: 'iam.security.desc.uppercase.letter', defaultMessage: '大写字母' });
  const uppercaseCount = securityPolicyDataSet?.current?.get('uppercaseCount');
  const lowercaseLetter = intl.formatMessage({ id: 'iam.security.desc.lowercase.letter', defaultMessage: '小写字母' });
  const lowercaseCount = securityPolicyDataSet?.current?.get('lowercaseCount');
  const number = intl.formatMessage({ id: 'iam.security.desc.number', defaultMessage: '数字' });
  const digitsCount = securityPolicyDataSet?.current?.get('digitsCount');
  const symbol = intl.formatMessage({ id: 'iam.security.desc.symbol', defaultMessage: '特殊符号' });
  const specialCharCount = securityPolicyDataSet?.current?.get('specialCharCount');

  // 密码错误最大次数
  const maxErrorTime = intl.formatMessage({ id: 'iam.security.model.passwordMaxErrorTime', defaultMessage: '密码错误最大次数' });
  const maxErrorTimeHelp = intl.formatMessage({ id: 'iam.security.help.passwordMaxErrorTime', defaultMessage: '登录超过错误最大次数后，会锁定帐号' });
  const maxErrorTimeSuffix = intl.formatMessage({ id: 'iam.security.unit.passwordValidityPeriod', defaultMessage: '次' });
  const maxErrorTimeValue = securityPolicyDataSet?.current?.get('maxErrorTime');
  // 登录密码错误
  const maxCheckCaptcha = intl.formatMessage({ id: 'iam.security.model.passwordMaxCheckCaptcha', defaultMessage: '登录密码错误' });
  const maxCheckCaptchaInfo = intl.formatMessage({ id: 'iam.security.tips.passwordMaxCheckCaptcha', defaultMessage: '"0"表示登录时必须要图形验证码' });
  const maxCheckCaptchaSuffix = intl.formatMessage({ id: 'iam.security.unit.passwordMaxCheckCaptcha', defaultMessage: '次后，显示图形验证码' });
  const maxCheckCaptchaHelp = intl.formatMessage({ id: 'iam.security.help.passwordMaxCheckCaptcha', defaultMessage: '登录密码错误后显示图形验证码' });
  const maxCheckCaptchaValue = securityPolicyDataSet?.current?.get('maxCheckCaptcha');
  // 帐号锁定时长
  const lockedExpireTime = intl.formatMessage({ id: 'iam.security.model.lockedExpireTime', defaultMessage: '帐号锁定时长' });
  const lockedExpireTimeSuffix = intl.formatMessage({ id: 'iam.security.unit.lockedExpireTime', defaultMessage: '秒' });
  const lockedExpireTimeHelp = intl.formatMessage({ id: 'iam.security.help.lockedExpireTime', defaultMessage: '帐号锁定超过锁定时长后，自动解锁' });
  const lockedExpireTimeValue = securityPolicyDataSet?.current?.get('lockedExpireTime');
  // 登录有效时长
  const accessTokenValidity = intl.formatMessage({ id: 'iam.security.model.accessTokenValidity', defaultMessage: '登录有效时长' });
  const accessTokenValidityInfo = intl.formatMessage({ id: 'iam.security.tips.accessTokenValidity', defaultMessage: '可填写0-999， "0"表示登录状态一直有效' });
  const accessTokenValiditySuffix = intl.formatMessage({ id: 'iam.security.unit.accessTokenValidity', defaultMessage: '小时' });
  const accessTokenValidityHelp = intl.formatMessage({ id: 'iam.security.help.accessTokenValidity', defaultMessage: '保持登录状态超过有效时长后，需要用户重新登录' });
  const accessTokenValidityValue = securityPolicyDataSet?.current?.get('accessTokenValidity');

  // 登录页是否记住密码
  const savePwdFlag = intl.formatMessage({ id: 'iam.security.model.flagSavePassword', defaultMessage: '登录页记住密码' });
  const savePwdFlagInfo = intl.formatMessage({ id: 'iam.security.tips.flagSavePassword', defaultMessage: '登录时可选择是否要记住密码' });
  const savePwdFlagSuffix = intl.formatMessage({ id: 'iam.security.unit.flagSavePassword', defaultMessage: '登录时是否记住密码' });
  const savePwdFlagValue = securityPolicyDataSet?.current?.get('rememberPasswordFlag');

  // 关闭浏览器强制登出
  const forceLogoutFlag = intl.formatMessage({ id: 'iam.security.model.flagForceLogout', defaultMessage: '关闭浏览器强制登出' });
  const forceLogoutFlagInfo = intl.formatMessage({ id: 'iam.security.tips.flagForceLogout', defaultMessage: '关闭浏览器后自动退出登录' });
  const forceLogoutFlagSuffix = intl.formatMessage({ id: 'iam.security.unit.flagForceLogout', defaultMessage: '关闭浏览器时强制登出' });
  const forceLogoutFlagValue = securityPolicyDataSet?.current?.get('forceLogoutFlag');

  // 首次登陆强制修改密码
  const forceModifyPassword = intl.formatMessage({ id: 'iam.security.model.forceModifyPassword', defaultMessage: '首次登录强制修改密码' });
  const forceModifyPasswordInfo = intl.formatMessage({ id: 'iam.security.tips.forceModifyPassword', defaultMessage: '用户首次登录系统根据配置控制是否需要修改密码' });
  const forceModifyPasswordSuffix = intl.formatMessage({ id: 'iam.security.unit.forceModifyPassword', defaultMessage: '首次登录时是否需要修改密码才允许登陆' });
  const forceModifyPasswordValue = securityPolicyDataSet?.current?.get('forceModifyPassword');

  // 开启验证码登录
  const verificationLoginFlag = intl.formatMessage({ id: 'iam.security.model.flagVerificationLoginFlag', defaultMessage: '验证码登录' });
  const verificationLoginFlagInfo = intl.formatMessage({ id: 'iam.security.tips.flagVerificationLoginFlag', defaultMessage: '登录页可通过手机号接收验证码进行登录' });
  const verificationLoginFlagValue = securityPolicyDataSet?.current?.get('verificationLoginFlag');

  // 登录页是否隐藏忘记
  const loginHidePassword = intl.formatMessage({ id: 'iam.security.model.passwordHide', defaultMessage: '找回密码' });
  const loginHidePasswordInfo = intl.formatMessage({ id: 'iam.security.tips.passwordHide', defaultMessage: '开启后用户可在登录页点击“忘记密码”找回密码' });
  const loginHidePasswordValue = securityPolicyDataSet?.current?.get('hidePasswordFlag');

  // 是否强制绑定手机号码
  const forcePhoneBind = intl.formatMessage({ id: 'iam.security.model.forceBindPhone', defaultMessage: '登录时检测账号是否已绑定手机号' });
  const forcePhoneBindInfo = intl.formatMessage({ id: 'iam.security.tips.forceBindPhone', defaultMessage: '需绑定手机号后才能登入' });
  const forcePhoneBindValue = securityPolicyDataSet?.current?.get('forcePhoneBind');

  // 是否强制验证邮箱
  const forceEmailVerify = intl.formatMessage({ id: 'iam.security.model.forceVerifyEmail', defaultMessage: '登录时检测邮箱是否已验证' });
  const forceEmailVerifyInfo = intl.formatMessage({ id: 'iam.security.tips.forceVerifyEmail', defaultMessage: '需验证邮箱后才能登入' });
  const forceEmailVerifyValue = securityPolicyDataSet?.current?.get('userCheckedFlag');

  const renderCharacters = useMemo(() => {
    const characterArr = [];
    let characterText = '';
    if (uppercaseCount) {
      characterArr.push(uppercaseLetter);
    }
    if (lowercaseCount) {
      characterArr.push(lowercaseLetter);
    }
    if (digitsCount) {
      characterArr.push(number);
    }
    if (specialCharCount) {
      characterArr.push(symbol);
    }
    if (characterArr.length > 0) {
      characterText = characterArr.join('，');
    }
    return characterText;
  }, [uppercaseCount, lowercaseCount, digitsCount, specialCharCount]);

  if (isEdit) {
    return (
      <div className={`${prefixCls}-form-content`}>
        <Form labelLayout="none" dataSet={securityPolicyDataSet}>
          {/* password */}
          <div>
            <DivisionTitle style={{ marginBottom: '0.16rem' }} title={intl.formatMessage({ id: 'iam.security.model.passwordSettings', defaultMessage: '密码策略' })} />
            <Card
              title={validityPeriod}
              titleHelp={validityPeriodHelp}
              value={<NumberField autoFocus className="card-value-input" name="passwordUpdateRate" />}
              valueSuffix={validityPeriodSuffix}
              valueHelp={validityPeriodInfo}
            />
            <Card
              title={passwordReminderPeriod}
              titleHelp={passwordReminderPeriodHelp}
              value={<NumberField className="card-value-input" name="passwordReminderPeriod" />}
              valueSuffix={passwordReminderPeriodSuffix}
              valueHelp={passwordReminderPeriodInfo}
            />
            <Card
              title={minLength}
              titleHelp={minLengthHelp}
              value={<NumberField className="card-value-input" name="minLength" />}
              valueSuffix={minLengthSuffix}
              valueHelp={minLengthInfo}
            />
            <Card
              title={complexityRequirements}
              titleHelp={complexityRequirementsHelp}
              value={(
                <div>
                  {atLeastMust}<NumberField className="card-value-input" name="characterTypeCount" />
                </div>
              )}
              valueSuffix={(
                <div className="complexity-suffix">
                  <div>{complexityRequirementsSuffix},{includeMust}:</div>
                  <div className="box-group">
                    <CheckBox className={`${prefixCls}-password-form-item-checkbox`} name="uppercaseCount">{uppercaseLetter}</CheckBox>
                    <CheckBox className={`${prefixCls}-password-form-item-checkbox`} name="lowercaseCount">{lowercaseLetter}</CheckBox>
                    <CheckBox className={`${prefixCls}-password-form-item-checkbox`} name="digitsCount">{number}</CheckBox>
                    <CheckBox className={`${prefixCls}-password-form-item-checkbox`} name="specialCharCount">{symbol}</CheckBox>
                  </div>
                </div>
              )}
              valueHelp={complexityRequirementsValueHelp}
            />
            <Card
              title={disabledHistory}
              titleHelp={disabledHistoryHelp}
              value={(
                <div>
                  {disabledHistoryPrefix}<NumberField className="card-value-input" name="notRecentCount" />
                </div>
              )}
              valueSuffix={disabledHistorySuffix}
              valueHelp={disabledHistoryInfo}
            />
            <Card
              title={forceModifyPassword}
              titleHelp={forceModifyPasswordInfo}
              value={<Switch name="forceModifyPassword" />}
              valueHelp={forceModifyPasswordSuffix}
            />
            <Card
              title={loginHidePassword}
              titleHelp={loginHidePasswordInfo}
              value={<Switch name="hidePasswordFlag" />}
            />
            {/* login */}
            <DivisionTitle style={{ margin: '0.24rem 0 0.16rem 0' }} title={intl.formatMessage({ id: 'iam.security.model.loginSettings', defaultMessage: '登录策略' })} />
            <Card
              title={forcePhoneBind}
              titleHelp={forcePhoneBindInfo}
              value={<Switch name="forcePhoneBind" />}
            />
            <Card
              title={forceEmailVerify}
              titleHelp={forceEmailVerifyInfo}
              value={<Switch name="userCheckedFlag" />}
            />
            <Card
              title={maxErrorTime}
              titleHelp={maxErrorTimeHelp}
              value={<Select clearButton={false} className="card-value-input" name="maxErrorTime" />}
              valueSuffix={maxErrorTimeSuffix}
            />
            <Card
              title={lockedExpireTime}
              titleHelp={lockedExpireTimeHelp}
              value={<NumberField className="card-value-input" name="lockedExpireTime" />}
              valueSuffix={lockedExpireTimeSuffix}
            />
            <Card
              title={maxCheckCaptcha}
              titleHelp={maxCheckCaptchaHelp}
              value={(
                <div>
                  {maxCheckCaptcha}<NumberField className="card-value-input" name="maxCheckCaptcha" />
                </div>
              )}
              valueSuffix={maxCheckCaptchaSuffix}
              valueHelp={maxCheckCaptchaInfo}
            />
            <Card
              title={accessTokenValidity}
              titleHelp={accessTokenValidityHelp}
              value={<NumberField className="card-value-input" name="accessTokenValidity" />}
              valueSuffix={accessTokenValiditySuffix}
              valueHelp={accessTokenValidityInfo}
            />
            <Card
              title={savePwdFlag}
              titleHelp={savePwdFlagInfo}
              value={<Switch name="rememberPasswordFlag" />}
              valueHelp={savePwdFlagSuffix}
            />
            <Card
              title={forceLogoutFlag}
              titleHelp={forceLogoutFlagInfo}
              value={<Switch name="forceLogoutFlag" />}
              valueHelp={forceLogoutFlagSuffix}
            />
            <Card
              title={verificationLoginFlag}
              titleHelp={verificationLoginFlagInfo}
              value={<Switch name="verificationLoginFlag" />}
              valueHelp={verificationLoginFlag}
            />
          </div>
        </Form>
      </div>
    );
  }

  return (
    <div className={`${prefixCls}-form-content`}>
      <div className={`${prefixCls}-form-content-wrapper`}>
        {/* password */}
        <DivisionTitle style={{ marginBottom: '0.16rem' }} title={intl.formatMessage({ id: 'iam.security.model.passwordSettings', defaultMessage: '密码策略' })} />
        <Card
          title={validityPeriod}
          titleHelp={validityPeriodHelp}
          value={(
            <div className="output-content">
              <span className="card-value-output">{validityPeriodValue}</span>
              <span className="card-value-suffix">{validityPeriodSuffix}</span>
            </div>
          )}
          valueHelp={validityPeriodInfo}
        />
        <Card
          title={passwordReminderPeriod}
          titleHelp={passwordReminderPeriodHelp}
          value={(
            <div className="output-content">
              <span className="card-value-output">{passwordReminderPeriodValue}</span>
              <span className="card-value-suffix">{passwordReminderPeriodSuffix}</span>
            </div>
          )}
          valueHelp={passwordReminderPeriodInfo}
        />
        <Card
          title={minLength}
          titleHelp={minLengthHelp}
          value={(
            <div className="output-content">
              <span className="card-value-output">{minLengthValue}</span>
              <span className="card-value-suffix">{minLengthSuffix}</span>
            </div>
          )}
          valueHelp={minLengthInfo}
        />
        <Card
          title={complexityRequirements}
          titleHelp={complexityRequirementsHelp}
          value={(
            <div className="output-content">
              <span className="card-value-suffix">{atLeastMust}</span>
              <span className="card-value-output">{complexityRequirementsValue}</span>
              <span className="card-value-suffix">{complexityRequirementsSuffix},{includeMust}:{renderCharacters}</span>
            </div>
          )}
          valueHelp={maxCheckCaptchaInfo}
        />
        <Card
          title={disabledHistory}
          titleHelp={disabledHistoryHelp}
          value={(
            <div className="output-content">
              <span className="card-value-suffix">{disabledHistoryPrefix}</span>
              <span className="card-value-output">{disabledHistoryValue}</span>
              <span className="card-value-suffix">{disabledHistorySuffix}</span>
            </div>
          )}
          valueHelp={disabledHistoryInfo}
        />
        <Card
          title={forceModifyPassword}
          titleHelp={forceModifyPasswordInfo}
          value={(
            <div className="output-content">
              <span className="card-value-output">{intl.formatMessage({ id: forceModifyPasswordValue ? 'zknow.common.status.yes' : 'zknow.common.status.no', defaultMessage: forceModifyPasswordValue ? '是' : '否' })}</span>
            </div>
          )}
          valueHelp={forceModifyPasswordSuffix}
        />
        <Card
          title={loginHidePassword}
          titleHelp={loginHidePasswordInfo}
          value={(
            <div className="output-content">
              <span className="card-value-output">{intl.formatMessage({ id: loginHidePasswordValue ?  'zknow.common.status.no' : 'zknow.common.status.yes', defaultMessage: loginHidePasswordValue ? '否' : '是' })}</span>
            </div>
          )}
        // valueHelp={forceModifyPasswordSuffix}
        />
        <DivisionTitle style={{ margin: '0.24rem 0 0.16rem 0' }} title={intl.formatMessage({ id: 'iam.security.model.loginSettings', defaultMessage: '登录策略' })} />
        <Card
          title={forcePhoneBind}
          titleHelp={forcePhoneBindInfo}
          value={(
            <div className="output-content">
              <span className="card-value-output">{intl.formatMessage({ id: forcePhoneBindValue ? 'zknow.common.status.yes' : 'zknow.common.status.no', defaultMessage: forcePhoneBindValue ? '是' : '否' })}</span>
            </div>
          )}
        />
        <Card
          title={forceEmailVerify}
          titleHelp={forceEmailVerifyInfo}
          value={(
            <div className="output-content">
              <span className="card-value-output">{intl.formatMessage({ id: forceEmailVerifyValue ? 'zknow.common.status.yes' : 'zknow.common.status.no', defaultMessage: forceEmailVerifyValue ? '是' : '否' })}</span>
            </div>
          )}
        />
        <Card
          title={maxErrorTime}
          titleHelp={maxErrorTimeHelp}
          value={(
            <div className="output-content">
              <span className="card-value-output">
                {maxErrorTimeValue === '100'
                  ? intl.formatMessage({ id: 'iam.security.desc.noLimit', defaultMessage: '无限制' })
                  : maxErrorTimeValue}
              </span>
              {maxErrorTimeValue !== '100'
                ? <span className="card-value-suffix">{maxErrorTimeSuffix}</span>
                : null}
            </div>
          )}
        />
        <Card
          title={lockedExpireTime}
          titleHelp={lockedExpireTimeHelp}
          value={(
            <div className="output-content">
              <span className="card-value-output">{lockedExpireTimeValue}</span>
              <span className="card-value-suffix">{lockedExpireTimeSuffix}</span>
            </div>
          )}
        />
        <Card
          title={maxCheckCaptcha}
          titleHelp={maxCheckCaptchaHelp}
          value={(
            <div className="output-content">
              <span className="card-value-suffix">{maxCheckCaptcha}</span>
              <span className="card-value-output">{maxCheckCaptchaValue}</span>
              <span className="card-value-suffix">{maxCheckCaptchaSuffix}</span>
            </div>
          )}
          valueHelp={maxCheckCaptchaInfo}
        />
        <Card
          title={accessTokenValidity}
          titleHelp={accessTokenValidityHelp}
          value={(
            <div className="output-content">
              <span className="card-value-output">{accessTokenValidityValue}</span>
              <span className="card-value-suffix">{accessTokenValiditySuffix}</span>
            </div>
          )}
          valueHelp={accessTokenValidityInfo}
        />
        <Card
          title={savePwdFlag}
          titleHelp={savePwdFlagInfo}
          value={(
            <div className="output-content">
              <span className="card-value-output">{intl.formatMessage({ id: savePwdFlagValue ? 'zknow.common.status.yes' : 'zknow.common.status.no', defaultMessage: savePwdFlagValue ? '是' : '否' })}</span>
            </div>
          )}
          valueHelp={savePwdFlagSuffix}
        />
        <Card
          title={forceLogoutFlag}
          titleHelp={forceLogoutFlagInfo}
          value={(
            <div className="output-content">
              <span className="card-value-output">{intl.formatMessage({ id: forceLogoutFlagValue ? 'zknow.common.status.yes' : 'zknow.common.status.no', defaultMessage: forceLogoutFlagValue ? '是' : '否' })}</span>
            </div>
          )}
          valueHelp={forceLogoutFlagSuffix}
        />
        <Card
          title={verificationLoginFlag}
          titleHelp={verificationLoginFlagInfo}
          value={(
            <div className="output-content">
              <span className="card-value-output">{intl.formatMessage({ id: verificationLoginFlagValue ? 'zknow.common.status.yes' : 'zknow.common.status.no', defaultMessage: verificationLoginFlagValue ? '是' : '否' })}</span>
            </div>
          )}
          valueHelp={verificationLoginFlagInfo}
        />
      </div>
    </div>
  );
});

export default observer(PasswordSettings);
