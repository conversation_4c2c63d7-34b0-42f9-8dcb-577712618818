import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import SecurityPolicyDataSet from './SecurityPolicyDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: ['iam.common', 'iam.security'] })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId } },
    } = props;
    const intlPrefix = 'security.policy';
    const prefixCls = 'security-policy';
    const securityPolicyDataSet = useMemo(() => new DataSet(SecurityPolicyDataSet({ intlPrefix, intl, orgId: organizationId })), []);
    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      orgId: organizationId,
      securityPolicyDataSet,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
