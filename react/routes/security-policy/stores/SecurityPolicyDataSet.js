export default ({ intlPrefix, intl, orgId }) => {
  const url = `iam/yqc/${orgId}/password-policies`;

  return {
    autoQuery: true,
    selection: false,
    paging: false,
    primaryKey: 'id',
    transport: {
      read: () => ({
        url,
        method: 'get',
      }),
      create: ({ data: [data] }) => ({
        url,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => ({
        url,
        method: 'put',
        data,
      }),
      destroy: ({ data }) => ({
        url: `${url}/${data.id}`,
        method: 'delete',
      }),
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'passwordUpdateRate', type: 'number', step: 1, min: 0, max: 365, required: true },
      { name: 'passwordReminderPeriod', type: 'number', step: 1, min: 0, max: 365 },
      { name: 'minLength', type: 'number', step: 1, min: 5, max: 50, required: true },
      { name: 'characterTypeCount', type: 'number', step: 1, min: 3, max: 4, required: true },
      { name: 'digitsCount', type: 'boolean' },
      { name: 'lowercaseCount', type: 'boolean' },
      { name: 'uppercaseCount', type: 'boolean' },
      { name: 'specialCharCount', type: 'boolean' },
      { name: 'notRecentCount', type: 'number', step: 1, min: 0, max: 24, required: true },
      { name: 'maxErrorTime', type: 'string', lookupCode: 'MAXIMUM_PASSWORD_ERROR_TIMES', defaultValue: '100' },
      { name: 'maxCheckCaptcha', type: 'number', step: 1, min: 0, max: 10, required: true },
      { name: 'lockedExpireTime', type: 'number', step: 1, min: 0, required: true },
      { name: 'accessTokenValidity', type: 'number', step: 1, min: 0, max: 999, required: true },
      { name: 'rememberPasswordFlag', type: 'boolean', defaultValue: true },
      { name: 'forceLogoutFlag', type: 'boolean' },
      { name: 'forceModifyPassword', type: 'boolean' },
      { name: 'hidePasswordFlag', type: 'boolean', defaultValue: false, trueValue: false, falseValue: true },
      { name: 'forcePhoneBind', type: 'boolean', defaultValue: false },
      { name: 'userCheckedFlag', type: 'boolean', defaultValue: false },
    ],
  };
};
