import React, { useEffect } from 'react';
import { Route, Switch } from 'react-router-dom';
import { nomatch } from '@yqcloud/apps-master';
import { StoreProvider } from './stores';
import Detail from './detail';
import MainView from './MainView.js';

export default (props) => {
  return (
    <StoreProvider {...props}>
      <Switch>
        <Route path={`${props.match.url}/detail/:id`} component={Detail} />
        <Route path={`${props.match.url}`} component={MainView} />
        <Route path="*" component={nomatch} />
      </Switch>
    </StoreProvider>
  );
};
