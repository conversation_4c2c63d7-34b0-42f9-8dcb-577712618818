import React, { useState, useContext, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { v4 as uuidv4 } from 'uuid';
import { axios } from '@yqcloud/apps-master';
import { ClickText, Icon, StatusTag, Button } from '@zknow/components';
import { Modal, message } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { color as colorUtils } from '@zknow/utils';
import TicketView from './TicketView';
import wechatImg from './assets/wechat.svg';
import wechatEnterpriseImg from './assets/wechat_enterprise.svg';
import dingdingImg from './assets/dingding.svg';
import larkImg from './assets/lark.svg';
import oauth2Img from './assets/oauth2.svg';
import yqImg from './assets/yqchat.png';
import miniProgramImg from './assets/wx-xcx.svg';
import ChoerodonImg from './assets/choerodon-logo.svg';
import TeamsImg from './assets/teams.svg';
import Store from './stores';
import { randomString } from '../../utils';

import './index.less';

const imgMap = {
  wechat: wechatImg,
  wechat_open_account: wechatImg,
  wechat_enterprise: wechatEnterpriseImg,
  ding_talk: dingdingImg,
  dingding: dingdingImg,
  lark: larkImg,
  other: oauth2Img,
  wechat_agent: wechatImg,
  quick_authentication: yqImg,
  wechat_mini_program: miniProgramImg,
  choerodon: ChoerodonImg,
  microsoft: TeamsImg,
};

const modalKey = Modal.key();

export default observer(({ records, type, showApplist }) => {
  const context = useContext(Store);
  const {
    prefixCls, intlPrefix, intl, history, match, infoDataSet, nameMap,
    type: levelType, tenantId, openKey, setOpenKey, solutionId,
  } = context;

  const [show, setShow] = useState(false);

  useEffect(() => {
    setShow(showApplist);
  }, [showApplist]);

  const curPrefixCls = `${prefixCls}-content-item`;

  const handleCreate = (e) => {
    e.stopPropagation();
    if (records.length >= 1 && ['wechat_agent', 'lark', 'choerodon', 'microsoft'].includes(type)) {
      message.info(intl.formatMessage({ id: 'iam.openLoginConfig.desc.same.type.only.allow.create.one.application', defaultMessage: '该类型三方只允许新建一个应用' }));
      return;
    }
    if (type === 'quick_authentication') {
      const record = infoDataSet.create({
        type,
        code: randomString(),
        appId: uuidv4(),
        appSecret: randomString(16),
        jsonConfig: {
          openFunctions: [],
          name: intl.formatMessage({ id: 'iam.openLoginConfig.desc.ticket.platform', defaultMessage: '问题反馈平台' }),
          displayName: intl.formatMessage({ id: 'iam.openLoginConfig.desc.ticket.platform', defaultMessage: '问题反馈平台' }),
          icon: 'https://api.yqcloud.com/hfle/yqc/v1/0/files/download-by-key?fileKey=207965165382135808%2Ff958eb9f412f4e77ac356cafb885c2ee%40help.png',
        },
      });

      Modal.open({
        title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.create', defaultMessage: '新建应用' }),
        closeable: true,
        key: modalKey,
        children: <TicketView record={record} setOpenKey={setOpenKey} />,
        style: { width: 520 },
      });
    } else {
      // 如果不传solutionId，则跳转路由时会重新请求solution导致页面刷新两次
      history.push(`${match.url}/detail/0?type=${type}&solutionId=${solutionId}`);
    }
  };

  const handleShowMore = () => {
    setOpenKey(show ? '' : type);
    setShow(!show);
  };

  /**
   * 修改或删除app
   * @param item
   * @param operate
   * @returns {Promise<void>}
   */
  async function operateOpenApp(item, operate) {
    let url = levelType === 'site' ? 'iam/yqc/open_apps' : `iam/yqc/${tenantId}/open_apps`;
    try {
      let res;
      if (operate === 'delete') {
        res = await axios.delete(url, { data: item });
      } else {
        url = `${url}/${item?.enabledFlag ? 'disabled' : 'enabled'}`;
        res = await axios.post(url, item);
      }
      if (res.failed) {
        message.error(res?.message);
      } else {
        setOpenKey(type);
        await infoDataSet.query();
      }
    } catch (e) {
      message.error(e?.message);
    }
  }

  function handleDisabled(item) {
    Modal.confirm({
      title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.app.disabled.tip', defaultMessage: '确定停用该应用' }),
      children: (
        <div>
          {intl.formatMessage({ id: 'iam.openLoginConfig.desc.app.disabled.description', defaultMessage: '该操作不可逆，停用之后，该应用与燕千云之间的集成将中断，该应用将无法进行快捷提单！同时无法查看以往提单记录，请谨慎操作！' })}
        </div>
      ),
      onOk: () => operateOpenApp(item),
      key: modalKey,
      destroyOnClose: true,
    });
  }

  function handleDelete(item) {
    Modal.confirm({
      title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.app.delete.tip', defaultMessage: '确定删除该应用' }),
      children: (
        <div>
          {intl.formatMessage({ id: 'iam.openLoginConfig.desc.app.delete.description', defaultMessage: '该操作不可逆，删除之后，应用配置将无法恢复，请谨慎操作！' })}
        </div>
      ),
      onOk: () => operateOpenApp(item, 'delete'),
      key: modalKey,
      destroyOnClose: true,
    });
  }

  /**
   * 渲染快捷提单行按钮
   * @param item
   * @returns {string}
   */
  const renderButtons = (item) => {
    if (item?.enabledFlag) {
      return (
        <Button
          // color="primary"
          funcType="flat"
          key="disabled"
          icon="ReduceOne"
          onClick={() => handleDisabled(item)}
        >
          {intl.formatMessage({ id: 'zknow.common.button.deactivated', defaultMessage: '停用' })}
        </Button>
      );
    } else if (item.type === 'wechat_open_account' || item.type === 'other' || item.type === 'wechat_agent' || item.type === 'quick_authentication') {
      // 微信公众号、其他、微信客服、嵌入式 才有移除
      return [
        <Button
          // color="primary"
          funcType="flat"
          key="enabled"
          icon="CheckOne"
          onClick={() => operateOpenApp(item)}
        >
          {intl.formatMessage({ id: 'zknow.common.button.enable', defaultMessage: '启用' })}
        </Button>,
        <Button
          // color="primary"
          funcType="flat"
          key="delete"
          icon="Delete"
          onClick={() => handleDelete(item)}
        >
          {intl.formatMessage({ id: 'zknow.common.button.remove', defaultMessage: '移除' })}
        </Button>,
      ];
    } else {
      return (
        <Button
          // color="primary"
          funcType="flat"
          key="enabled"
          icon="CheckOne"
          onClick={() => operateOpenApp(item)}
        >
          {intl.formatMessage({ id: 'zknow.common.button.enable', defaultMessage: '启用' })}
        </Button>
      );
    }
  };

  const showQuickType = (item) => {
    const { quickType } = item;
    if (!quickType) {
      return intl.formatMessage({ id: 'iam.openLoginConfig.detail.quickType.embedded.recognition.failure', defaultMessage: '未能识别' });
    }
    if (quickType.includes('SDK')) {
      return intl.formatMessage({ id: 'iam.openLoginConfig.detail.quickType.SDKEmbedded', defaultMessage: 'SDK嵌入式' });
    }
    if (quickType.includes('EBS')) {
      return intl.formatMessage({ id: 'iam.openLoginConfig.detail.quickType.EBSEmbedded', defaultMessage: 'EBS嵌入式' });
    }
    if (quickType.includes('CLIENT')) {
      return intl.formatMessage({ id: 'iam.openLoginConfig.detail.quickType.SAPEmbedded', defaultMessage: 'SAP嵌入式' });
    }
    if (quickType.includes('HZERO')) {
      return intl.formatMessage({ id: 'iam.openLoginConfig.detail.quickType.HZEROPlugin', defaultMessage: 'HZERO插件' });
    }
    return intl.formatMessage({ id: `iam.openLoginConfig.detail.quickType.${item.quickType}.embedded`, defaultMessage: 'Netsuite嵌入式' });
  };

  const renderApplication = (item) => {
    return (
      <div className={`${curPrefixCls}-app-item`}>
        <div className={`${curPrefixCls}-app-item-left`}>
          <ClickText style={{ marginRight: '20px' }} record={infoDataSet.get(0)} path={`${match.url}/detail/${item.id}`} history={history}>
            {item?.name || '-'}
          </ClickText>
          <span className={`${curPrefixCls}-app-item-tag ${item.enabledFlag ? '' : 'app-item-disabled'}`}>
            <Tag>
              {item.enabledFlag ? intl.formatMessage({ id: 'iam.openLoginConfig.list.activated', defaultMessage: '已启用' }) : intl.formatMessage({ id: 'iam.openLoginConfig.list.deactivated', defaultMessage: '已停用' })}
            </Tag>
          </span>
          {item.quickType ? (
            <Tag className={`${curPrefixCls}-app-item-typeTag`}>
              {showQuickType(item)}
            </Tag>
          ) : (
            <></>
          )}
        </div>
        {renderButtons(item)}
        {/* {type === 'quick_authentication' ? renderButtons(item) : <Icon type="right" theme="outline" size="14" fill="#333" />} */}
      </div>
    );
  };

  const renderAppNumbers = () => {
    if (records.length > 0) {
      return <div className={`${curPrefixCls}-num`}>{`${records.length}${intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.apps', defaultMessage: '个应用' })}`}</div>;
    } else {
      return <div className={[`${curPrefixCls}-num`, `${curPrefixCls}-none`].join(' ')}>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.search.not.added', defaultMessage: '未添加' })}</div>;
    }
  };

  return (
    <div className={curPrefixCls}>
      <div className={`${curPrefixCls}-header`} onClick={handleShowMore}>
        <img alt="" src={imgMap[type]} className={`${curPrefixCls}-img`} />
        <div className={`${curPrefixCls}-name`}>{nameMap[type]}</div>
        {renderAppNumbers()}
        <div className={`${curPrefixCls}-create`}>
          <div className={`${curPrefixCls}-create-add`} onClick={handleCreate}>
            {intl.formatMessage({ id: 'zknow.common.button.append', defaultMessage: '添加' })}
          </div>
          {records.length > 0 && (
            <div className={`${curPrefixCls}-create-showmore`} onClick={handleShowMore}>
              <Icon type="down" theme="outline" size="14" fill="#fff" />
            </div>
          )}
        </div>
      </div>
      {(show || type === openKey) && records.length > 0 && (
        <div className={`${curPrefixCls}-app`}>
          {records.map(renderApplication)}
        </div>
      )}
    </div>
  );
});
