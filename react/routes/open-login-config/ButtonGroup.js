import React, { useState, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import { Icon, Button } from '@zknow/components';
import { Modal, message } from 'choerodon-ui/pro';
import { axios } from '@yqcloud/apps-master';

const modalKey = Modal.key();

const ButtonGroup = ({
  onChangeEdit,
  intl,
  dataSet,
  intlPrefix,
  prefixCls,
}) => {
  const [isEdit, setIsEdit] = useState(false);
  const record = dataSet?.current;

  async function loadDetail() {
    try {
      const res = await axios.get(`${record.dataSet.props.transport.read.url}/${record.get('id')}`);
      if (res.failed) {
        throw new Error(res.message);
      } else {
        record.init(res);
      }
    } catch (err) {
      message.error(err.message);
    }
  }

  const handleOk = async (ds) => {
    try {
      if (await ds.submit()) {
        loadDetail();
        setIsEdit(false);
        onChangeEdit(false);
      } else {
        const res = await dataSet.validate();
        if (res) {
          setIsEdit(false);
          onChangeEdit(false);
        }
        return false;
      }
    } catch (err) {
      return false;
    }
  };

  const handleClickSaveBtn = useCallback(() => {
    handleOk(dataSet);
  }, [dataSet.current]);

  const handleClickCancelBtn = useCallback(() => {
    setIsEdit(false);
    onChangeEdit(false);
    dataSet.reset();
  }, []);

  const handleClickEditBtn = useCallback(() => {
    setIsEdit(true);
    onChangeEdit(true);
  }, [isEdit]);

  if (isEdit) {
    return (
      <div>
        <Button key="confirm" funcType="raised" color="primary" onClick={handleClickSaveBtn}>
          {intl.formatMessage({ id: 'zknow.common.button.ok', defaultMessage: '确定' })}
        </Button>
        <Button key="cancel" funcType="raised" onClick={handleClickCancelBtn}>
          {intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}
        </Button>
      </div>
    );
  }

  return (
    <div>
      <Button key="edit" icon="icon-edit" funcType="raised" onClick={handleClickEditBtn}>
        {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
      </Button>
    </div>
  );
};

export default observer(ButtonGroup);
