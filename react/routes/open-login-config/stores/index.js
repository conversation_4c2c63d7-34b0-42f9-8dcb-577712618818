import React, { createContext, useEffect, useMemo, useState } from 'react';
import { DataSet, message } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import queryString from 'query-string';
import { formatterCollections } from '@zknow/utils';
import InfoDataSet from './OpenAppDataSet';
import UserDataSet from './UserDataSet';
import DepartmentDataSet from './DepartmentDataSet';
import OpenFunctionDataSet from './OpenFunctionDataSet';
import OpenClientFunctionDataSet from './OpenClientFunctionDataSet';
import FieldsDataSet from './FieldsDataSet';
import FieldListDataSet from './FieldListDataSet';
import ChoerodonUserDataSet from './ChoerodonUserDataSet';
import NotifyDataSet from './NotifyDataSet';
import { checkPassword } from '../../../utils';
import SyncHistoryDataSet from './SyncHistoryDataSet';
import UserBindInfoDataSet from './UserBindInfoDataSet';
import UserAutoBindDataSet from './UserAutoBindDataSet';
import WechatOpenAccountListDs from './WechatOpenAccountListDs';
import BindHistoryDataSet from './BindHistoryDataSet';
import ToDoConfigDataSet from './ToDoConfigDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState', 'HeaderStore')(formatterCollections({
  code: 'iam.openLoginConfig',
})(injectIntl(
  observer((props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { tenantId, type, domain: tenantDomain }, getUserInfo: { tenantId: userTenantId } },
      location: { search },
    } = props;
    const { solutionId, type: appType } = queryString.parse(search);
    const organizationId = tenantId;
    const intlPrefix = 'iam.open-login-config';
    const prefixCls = 'iam-open-login-config';
    const [openKey, setOpenKey] = useState('');

    const nameMap = {
      ding_talk: intl.formatMessage({ id: 'iam.openLoginConfig.model.dingding', defaultMessage: '钉钉' }),
      lark: intl.formatMessage({ id: 'iam.openLoginConfig.model.lark', defaultMessage: '飞书' }),
      wechat_enterprise: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.enterprise', defaultMessage: '企业微信' }),
      wechat_open_account: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.wechat.open.account', defaultMessage: '微信公众号' }),
      other: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.oauth2', defaultMessage: '其他Oauth2.0' }),
      wechat_agent: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.wechat.agent', defaultMessage: '微信客服' }),
      quick_authentication: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.quick.authentication', defaultMessage: '嵌入式应用' }),
      wechat_mini_program: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.wechat.mini.program', defaultMessage: '小程序' }),
      choerodon: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.choerodon', defaultMessage: '猪齿鱼' }),
      microsoft: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.teams', defaultMessage: 'Teams' }),
    };

    const openCodeMapping = {
      user: {
        lark: 'LARK_USER_FIELD',
        ding_talk: 'DING_TALK_USER_FIELD',
        wechat_enterprise: 'ENTERPRISE_WECHAT_USER_FIELD',
        microsoft: 'TEAMS_USER_FIELD',
      },
      department: {
        lark: 'LARK_DEPARTMENT_FIELD',
        ding_talk: 'DING_TALK_DEPARTMENT_FIELD',
        wechat_enterprise: 'ENTERPRISE_WECHAT_DEPARTMENT_FIELD',
        microsoft: 'TEAMS_DEPARTMENT_FIELD',
      },
    };

    const keepValueMapping = {
      user: ['real_name', 'login_name'],
      department: ['name', 'code', 'parent_id'],
    };

    const fieldListDataSet = useMemo(() => new DataSet(FieldListDataSet({ tenantId })), []);

    const fieldsSDKDataSet = useMemo(() => new DataSet(FieldsDataSet({ intl, fieldListDataSet, openFunctionDataSet })), [fieldListDataSet]);

    const fieldsDataSet = useMemo(() => new DataSet(FieldsDataSet({ intl, fieldListDataSet })), [fieldListDataSet]);

    const openFunctionDataSet = useMemo(() => new DataSet(OpenFunctionDataSet({ intl, fieldsDataSet: fieldsSDKDataSet, tenantId, tenantDomain, intlPrefix })), []);

    const openClientFunctionDataSet = useMemo(() => new DataSet(OpenClientFunctionDataSet({ intl, tenantId, fieldsDataSet, tenantDomain })), []);

    const notifyDataSet = useMemo(() => new DataSet(NotifyDataSet({ intl, intlPrefix })), []);
    const todoConfigDataSet = useMemo(() => new DataSet(ToDoConfigDataSet({ tenantId, intl })), []);
    const infoDataSet = useMemo(() => new DataSet(InfoDataSet({ intlPrefix, intl, organizationId, type, list: true, nameMap })), []);

    const infoDetailDataSet = useMemo(() => new DataSet(InfoDataSet({ intlPrefix, intl, organizationId, type, tenantId, openFunctionDataSet, openClientFunctionDataSet, notifyDataSet, checkPassword, userTenantId, nameMap, todoConfigDataSet })), []);

    const userDataSet = useMemo(() => new DataSet(UserDataSet({ openCodeMapping, keepValueMapping, intl, intlPrefix, appType })), [infoDetailDataSet?.status]);

    const userAutoBindDataSet = useMemo(() => new DataSet(UserAutoBindDataSet({ openCodeMapping, keepValueMapping, intl, intlPrefix, appType })), [infoDetailDataSet?.status]);

    const departmentDataSet = useMemo(() => new DataSet(DepartmentDataSet({ openCodeMapping, keepValueMapping, intl, intlPrefix, appType })), [infoDetailDataSet?.status]);

    const syncHistoryDataSet = useMemo(() => new DataSet(SyncHistoryDataSet({ intl, intlPrefix, tenantId, openAppId: infoDetailDataSet?.current?.get('id') })), [infoDetailDataSet?.current?.get('id')]);

    const bindHistoryDataSet = useMemo(() => new DataSet(BindHistoryDataSet({ intl, intlPrefix, tenantId, openAppId: infoDetailDataSet?.current?.get('id') })), [infoDetailDataSet?.current?.get('id')]);
    const defaultDataSet = useMemo(() => new DataSet({
      autoLocateFirst: false,
      paging: false,
      autoCreate: true,
      fields: [
        { name: 'userInfo', type: 'object', lovCode: 'OBJECT_FIELD', lovPara: { object_code: 'IAM_USER' } },
        { name: 'departmentInfo', type: 'object', lovCode: 'OBJECT_FIELD', lovPara: { object_code: 'ORG_DEPARTMENT' } },
      ],
    }), []);

    const userBindInfoDataSet = useMemo(() => new DataSet(UserBindInfoDataSet({ intl, intlPrefix, tenantId, infoDetailDataSet })), [infoDetailDataSet?.current?.get('id')]);

    const choerodonUserDataSet = useMemo(() => new DataSet(ChoerodonUserDataSet({
      intl,
      intlPrefix,
      tenantId,
      infoDetailDataSet,
      appItemId: infoDetailDataSet?.current?.get('id'),
    })), [infoDetailDataSet?.current?.get('id')]);

    const wechatOpenAccountListDs = useMemo(() => new DataSet(WechatOpenAccountListDs({ intlPrefix, intl, tenantId, appId: infoDetailDataSet?.current?.get('id') })), [infoDetailDataSet?.current?.get('id')]);
    useEffect(() => {
      const _appType = infoDetailDataSet?.current?.get('type');
      if (_appType) {
        userDataSet.setState('appType', _appType);
        departmentDataSet.setState('appType', _appType);
        userAutoBindDataSet.setState('appType', _appType);
      }
      if (!userDataSet.get(0)) {
        const userBindData = infoDetailDataSet?.current?.get('bindConfig.jsonData') || '[]';
        const bindData = typeof userBindData === 'string' ? JSON.parse(userBindData) : [];
        const data = infoDetailDataSet?.current?.get('config.jsonData') || '{ "user": [], "department": [], "userBind": [] }';
        try {
          const jsonData = typeof data === 'string' ? JSON.parse(data) : '{ "user": [], "department": [], "userBind": [] }';
          const { user, department, userBind } = jsonData;
          if (user.length > 0) {
            userDataSet.data = user.map(u => {
              const { fieldCode, fieldName, syncUserDepartments, openName, openCode, updateFlag, constantValue } = u;
              return { userInfo: { fieldCode, fieldName }, syncUserDepartments, openName, openCode, updateFlag, constantValue };
            });
          } else {
            userDataSet.loadData([]);
          }
          if (department.length > 0) {
            departmentDataSet.data = department.map(u => {
              const { fieldCode, fieldName, openName, openCode, updateFlag, constantValue } = u;
              return { departmentInfo: { fieldCode, fieldName }, openName, openCode, updateFlag, constantValue };
            });
          } else {
            departmentDataSet.loadData([]);
          }
          if (bindData?.length > 0) {
            userAutoBindDataSet.data = bindData.map(u => {
              const { fieldCode, fieldName, openName, openCode, constantValue } = u;
              return { userInfo: { fieldCode, fieldName }, openName, openCode, constantValue };
            });
          } else {
            userAutoBindDataSet.loadData([]);
          }
        } catch (e) {
          // message.error(error); 这样写会导致message报错，后续都不能抛提示了
          message.error(e.message);
        }
      }
    }, [infoDetailDataSet.status]);

    const value = {
      ...props,
      tenantId,
      intlPrefix,
      prefixCls,
      infoDataSet,
      infoDetailDataSet,
      syncHistoryDataSet,
      organizationId,
      type,
      nameMap,
      keepValueMapping,
      userDataSet,
      departmentDataSet,
      defaultDataSet,
      openKey,
      setOpenKey,
      openFunctionDataSet,
      openClientFunctionDataSet,
      notifyDataSet,
      fieldsDataSet,
      solutionId,
      choerodonUserDataSet,
      fieldListDataSet,
      fieldsSDKDataSet,
      userBindInfoDataSet,
      userAutoBindDataSet,
      wechatOpenAccountListDs,
      bindHistoryDataSet,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
)));
