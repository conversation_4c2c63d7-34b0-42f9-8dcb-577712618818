import { DataSet } from 'choerodon-ui/pro';

export default ({ tenantId, historyId, type, intlPrefix, intl }) => {
  const nameLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.account.member.name', defaultMessage: '姓名' });
  const bindResultLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.bind.result', defaultMessage: '绑定结果' });

  const handleStatusOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.bind.success', defaultMessage: '绑定成功' }), value: '1' }, // 绑定成功
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.bind.failure', defaultMessage: '绑定失败' }), value: '0' }, // 绑定失败
    ],
  });

  return {
    name: type,
    paging: true,
    autoQuery: true,
    autoLocateFirst: true,
    selection: false,
    transport: {
      read: () => ({
        url: `/ecos/v1/${tenantId}/openApps/bind/histories/${historyId}/users`,
        method: 'get',
      }),
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'historyId', type: 'string' },
      { name: 'openId', type: 'string', label: intl.formatMessage({ id: 'iam.common.model.account.login.name', defaultMessage: '登录名' }) },
      { name: 'openName', type: 'string', label: nameLabel },
      { name: 'bindInfo', type: 'object' },
      { name: 'bindResultFlag', type: 'boolean' },
      { name: 'bindResult', type: 'string', label: bindResultLabel },
      { name: 'handleStatus', type: 'string' },
      { name: 'bindLog', type: 'string', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.log', defaultMessage: '日志' }) },
      { name: 'userId', type: 'string' },
      { name: 'failResult', type: 'object', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.bind.failure.reason', defaultMessage: '失败原因' }) },
    ],
    queryFields: [
      { name: 'openName', type: 'string', label: nameLabel },
      { name: 'bindResultFlag', type: 'string', label: bindResultLabel, options: handleStatusOptions },
    ],
  };
};
