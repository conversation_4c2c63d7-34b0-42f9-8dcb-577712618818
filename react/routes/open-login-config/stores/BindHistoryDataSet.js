import { DataSet } from 'choerodon-ui/pro';

export default ({ intl, intlPrefix, tenantId, openAppId }) => {
  const executorLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.executor', defaultMessage: '执行人' });
  const bindWayLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.bind.way', defaultMessage: '绑定方式' });
  const statusLabel = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });

  const syncTypeOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.manual.bind', defaultMessage: '手动绑定' }), value: 'M' }, // 手动绑定
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.auto.bind', defaultMessage: '自动绑定' }), value: 'A' }, // 自动绑定
    ],
  });

  const statusOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.bind.binding', defaultMessage: '绑定中' }), value: '0' }, // 绑定中
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.bind.complete', defaultMessage: '绑定完成' }), value: '1' }, // 绑定完成
    ],
  });

  return {
    paging: true,
    autoQuery: false,
    autoLocateFirst: true,
    selection: false,
    transport: {
      read: ({ data }) => {
        const { syncBeginTime, ...query } = data;
        if (syncBeginTime) {
          const { start, end } = syncBeginTime;
          if (start) Object.assign(query, { syncTimeStart: start });
          if (end) Object.assign(query, { syncTimeEnd: end });
        }
        return {
          url: `/ecos/v1/${tenantId}/openApps/bind/${openAppId}/histories`,
          method: 'get',
          data: query,
        };
      },
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'openUserCount', type: 'number', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.bind.all.count', defaultMessage: '总人数' }) },
      { name: 'noBindUserCount', type: 'number', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.bind.un.bind.count', defaultMessage: '未绑定人数' }) },
      { name: 'bindType', type: 'string', label: bindWayLabel },
      { name: 'bindStatusFlag', type: 'number' }, // 1 绑定完成 0 绑定中
      { name: 'bindResult', type: 'object', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.bind.result', defaultMessage: '绑定结果' }) }, // 仅前端使用
      { name: 'bindStatus', type: 'string', label: statusLabel },
      { name: 'bindLog', type: 'string', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.log', defaultMessage: '日志' }) },
      { name: 'executor', type: 'string', label: executorLabel },
      { name: 'remainingCount', type: 'string' },
      { name: 'creationDate', type: 'dateTime', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.choerodon.creation.date', defaultMessage: '绑定时间' }) },
    ],
    queryFields: [
      { name: 'bindType', type: 'string', label: bindWayLabel, options: syncTypeOptions },
      { name: 'bindStatusFlag', type: 'string', label: statusLabel, options: statusOptions },
      { name: 'executor', type: 'string', label: executorLabel },
      { name: 'creationDateFrom', type: 'dateTime', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.bind.creation.date.from', defaultMessage: '绑定时间从' }) },
      { name: 'creationDateTo', type: 'dateTime', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.bind.creation.date.to', defaultMessage: '绑定时间至' }) },
    ],
  };
};
