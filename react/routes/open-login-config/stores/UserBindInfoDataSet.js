import { DataSet } from 'choerodon-ui/pro';

export default ({ intl, intlPrefix, tenantId, infoDetailDataSet }) => {
  const openAppCode = infoDetailDataSet.get(0)?.get('code');
  const booleanDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.common.model.bind', defaultMessage: '绑定' }), value: 'bind' },
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.unbind', defaultMessage: '未绑定' }), value: 'unbind' },
      { meaning: intl.formatMessage({ id: 'zknow.common.desc.all', defaultMessage: '全部' }), value: 'all' },
    ],
  });
  return {
    paging: true,
    autoQuery: false,
    autoLocateFirst: true,
    selection: false,
    transport: {
      read: ({ data }) => {
        const searchParams = {
          openAppCode,
          // bindFlag: true,
          fuzzy_params: data.fuzzy_params,
        };
        if (data.bindFlag === 'bind') {
          searchParams.bindFlag = true;
        }
        if (data.bindFlag === 'unbind') {
          searchParams.bindFlag = false;
        }
        if (data.bindFlag === 'all') {
          // searchParams.bindFlag = undefined;
        }
        return {
          url: `/ecos/v1/${tenantId}/userOpenAccountInfo/list`,
          method: 'get',
          data: searchParams,
        };
      },
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'openDepartmentName', type: 'string', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.department.name', defaultMessage: '三方部门' }) },
      { name: 'openName', type: 'string', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.name', defaultMessage: '三方人员姓名' }) },
      { name: 'openId', type: 'string', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.id', defaultMessage: '三方人员id' }) },
      { name: 'yqcloudUser', type: 'object', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.yqcloud.user', defaultMessage: '燕千云用户' }), lovCode: 'NO_BIND_USER', lovPara: { openAppCode } },
      { name: 'bindFlag', type: 'boolean', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.bind.flag', defaultMessage: '绑定状态' }) },
    ],
    queryFields: [
      { name: 'bindFlag', type: 'string', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.bind.flag', defaultMessage: '绑定状态' }), options: booleanDs },
    ],
  };
};
