import { DataSet } from 'choerodon-ui/pro';

export default ({ tenantId, intl }) => {
  const configTemplateDs = new DataSet({
    paging: false,
    dataToJSON: 'normal',
    fields: [
      { name: 'businessTypeName', type: 'string' },
      { name: 'businessObjectId', type: 'string' },
      { name: 'code', type: 'string' },
      { name: 'content.zh_CN', type: 'string', trim: 'none' },
      { name: 'content.en_US', type: 'string', trim: 'none' },
    ],
  });

  return {
    // name: type,
    paging: true,
    autoLocateFirst: true,
    selection: false,
    fields: [
      { name: 'id', type: 'string' },
      { name: 'todoFlag', type: 'boolean', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.ding.talk.todo.todo.flag', defaultMessage: '单据生成待办' }) },
      {
        name: 'todoCreatorCode',
        type: 'object',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.ding.talk.todo.todo.creator.code', defaultMessage: '生成待办人员' }),
        lovCode: 'TICKET_TYPE_FIELDS_WITH_USER',
        textField: 'name',
        multiple: true,
      },
      {
        name: 'taskChangeStatus',
        type: 'number',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.ding.talk.todo.task.change.status', defaultMessage: '执行人变更后待办状态' }),
        help: intl.formatMessage({ id: 'iam.openLoginConfig.model.ding.talk.todo.task.change.status.help', defaultMessage: '执行人变更后待办状态，指某个人员不满足生成待办角色后，已存在待办区的待办是完成还是删除' }),
      },
      {
        name: 'taskFinalStatus',
        type: 'number',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.ding.talk.todo.task.final.status', defaultMessage: '进入最终状态待办状态' }),
        help: intl.formatMessage({ id: 'iam.openLoginConfig.model.ding.talk.todo.task.final.status.help', defaultMessage: '进入最终状态后待办状态，指某个人员不满足生成待办角色后，已存在待办区的待办是完成还是删除' }),
      },
      { name: 'deadline', type: 'number', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.ding.talk.todo.deadline', defaultMessage: '待办持续时间' }) },
      { name: 'notifiedFlag', type: 'boolean', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.ding.talk.todo.notified.flag', defaultMessage: '开启通知' }) },
      {
        name: 'businessType',
        type: 'object',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.ding.talk.todo.business.type', defaultMessage: '选择业务类型' }),
        lovCode: 'SVS_TICKET_TYPE',
        optionsProps: (props) => {
          props.queryFields.forEach(i => {
            i.name = `search_${i.name}`;
          });
          return props;
        },
        dynamicProps: {
          lovQueryAxiosConfig: ({ record }) => {
            const { businessType } = record?.toData() || {};
            return ({
              method: 'get',
              url: `lc/v1/${tenantId}/ticket-type`,
              transformResponse: (res) => {
                // 这段的意义：因为后端没有保存“选择业务类型”businessType这个lov，所以这个lov是根据“配置模板”configTemplate来的，所以在接受lov数据的时候要排除一下已经选过的内容，否则就会重复选中。
                // TODO 可以考虑将这个字段保存到后端，这样就避免了在前端做判断
                const data = JSON.parse(res) || {};
                const filtered = data?.content?.filter((newGot) => {
                  let isUnique = true;
                  if (businessType?.length > 0) {
                    businessType.forEach((alreadyGot) => {
                      if (newGot.code === alreadyGot.code) {
                        isUnique = false;
                      }
                    });
                  }
                  return isUnique;
                });
                data.content = filtered;
                return data;
              },
            });
          },
        },
      }, // 纯前端字段
    ],
    children: {
      configTemplate: configTemplateDs,
    },
    queryFields: [
      // { name: 'openName', type: 'string', label: nameLabel },
      // { name: 'bindResultFlag', type: 'string', label: bindResultLabel, options: handleStatusOptions },
    ],
  };
};
