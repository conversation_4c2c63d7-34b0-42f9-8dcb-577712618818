export default ({ intlPrefix, intl, tenantId, type, appId = '0' }) => {
  const readUrl = `ecos/v1/${tenantId}/weChat/message/template?openAppId=${appId}`;
  const putUrl = `/ecos/v1/${tenantId}/weChat/message/sync/template?openAppId=${appId}`;

  return {
    autoQuery: true, // 自动请求
    selection: false,
    paging: true,
    primaryKey: 'id',
    autoLocateFirst: false,
    pageSize: 20,
    transport: {
      read: () => ({
        url: readUrl,
        method: 'get',
      }),
      update: () => ({
        url: putUrl,
        method: 'put',
      }),
    },

    fields: [
      { name: 'id', type: 'string' },
      {
        name: 'title',
        type: 'string',
        label: intl.formatMessage({ id: 'zknow.common.model.title', defaultMessage: '标题' }),
      },
      {
        name: 'content',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.desc.wechat.open.account.content', defaultMessage: '内容' }),
      },
      {
        name: 'example',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.desc.wechat.open.account.example', defaultMessage: '例子' }),
      },
      {
        name: 'template_id',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.desc.wechat.open.account.template.id', defaultMessage: '模板ID' }),
      },
      {
        name: 'primary_industry',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.desc.wechat.open.account.primary.industry', defaultMessage: '一级类目/行业' }),
      },
      {
        name: 'deputy_industry',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.desc.wechat.open.account.deputy.industry', defaultMessage: '二级类目/行业' }),
      },
      {
        name: 'field',
        type: 'string',
        label: intl.formatMessage({ id: 'zknow.common.model.field', defaultMessage: '字段' }),
      },
    ],
  };
};
