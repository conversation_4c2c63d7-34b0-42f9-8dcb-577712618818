import { simplifyField } from '../lovConfig/utils';

export default ({ intl, fieldListDataSet, openFunctionDataSet }) => {
  const fieldCode = intl.formatMessage({ id: 'iam.openLoginConfig.model.field.code', defaultMessage: '字段编码' });
  const defaultValue = intl.formatMessage({ id: 'iam.openLoginConfig.model.default.value', defaultMessage: '默认值' });

  return {
    paging: false,
    autoQuery: false,
    autoLocateFirst: true,
    selection: false,
    fields: [
      {
        name: 'fieldId',
        type: 'object',
        textField: 'name',
        valueField: 'id',
        dynamicProps: {
          lovCode: ({ record }) => {
            return record.get('isVariableField') ? 'VARIABLE_FIELD' : 'BUSINESS_OBJECT_FIELD';
          },
          lovPara: ({ dataSet, record }) => {
            let umdPara = {};
            if (dataSet.getState('udmTenantId')) {
              umdPara = {
                _udm: 'a0b923820dcc509a',
                udmTenantId: dataSet.getState('udmTenantId'),
              };
            }
            return {
              variable_view_id: record.get('variableViewId'),
              business_object_id: record.get('businessObjectId'),
              ...umdPara,
            };
          },
        },
        transformRequest: (value) => {
          return value?.id || value;
        },
        transformResponse: (value, data) => {
          if (value) {
            return {
              id: value.id,
              name: data?.fieldName,
            };
          }
          return undefined;
        },
      },
      { name: 'code', type: 'string', label: fieldCode },
      { name: 'defaultValue', type: 'string', label: defaultValue },
    ],
    events: {
      update: ({ record, name, value }) => {
        if (name === 'fieldId') {
          record.set('fieldCode', value.code);
          record.set('fieldName', value.name);
          record.set('defaultValue', '');

          if (value.code) {
            const fieldData = fieldListDataSet.find(r => r.get('code') === value.code)?.toData();
            if (fieldData) {
              const { widgetType } = fieldData;
              record.set('widgetType', widgetType);
              record.set('fieldData', simplifyField(fieldData));
            }
          }
        }
      },
    },
  };
};
