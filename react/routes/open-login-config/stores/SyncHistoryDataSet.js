import { DataSet } from 'choerodon-ui/pro';

export default ({ intl, intlPrefix, tenantId, openAppId }) => {
  const syncTypeLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.sync.method', defaultMessage: '同步方式' });
  const syncBeginTimeLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.sync.time', defaultMessage: '同步时间' });
  const syncStatusMeaningLabel = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const executorLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.executor', defaultMessage: '执行人' });
  const resultLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.sync.results', defaultMessage: '同步结果' });

  const syncBeginTimePlaceHolder = [`${syncBeginTimeLabel} ${intl.formatMessage({ id: 'zknow.common.desc.from', defaultMessage: '从' })}`, intl.formatMessage({ id: 'iam.openLoginConfig.model.to', defaultMessage: '到' })];

  const syncTypeOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.sync.hand', defaultMessage: '手动同步' }), value: 'M' }, // 手动同步
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.sync.auto', defaultMessage: '自动同步' }), value: 'A' }, // 定时同步
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.sync.realtime', defaultMessage: '实时同步' }), value: 'RealTime' }, // 实时同步
    ],
  });

  const statusOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.synchronizing', defaultMessage: '同步中' }), value: 0 }, // 同步中
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.partially.failed', defaultMessage: '部分失败' }), value: -2 }, // 部分失败
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.sync.completed', defaultMessage: '同步完成' }), value: 1 }, // 同步完成
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.sync.failed', defaultMessage: '同步失败' }), value: -1 }, // 同步失败
    ],
  });

  return {
    paging: true,
    autoQuery: false,
    autoLocateFirst: true,
    selection: false,
    transport: {
      read: ({ data }) => {
        const { syncBeginTime, ...query } = data;
        if (syncBeginTime) {
          const { start, end } = syncBeginTime;
          if (start) Object.assign(query, { syncTimeStart: start });
          if (end) Object.assign(query, { syncTimeEnd: end });
        }
        return {
          url: `/ecos/v1/${tenantId}/openApps/sync/${openAppId}/histories`,
          method: 'get',
          data: query,
        };
      },
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'syncType', type: 'object', label: syncTypeLabel, options: syncTypeOptions },
      { name: 'syncBeginTime', type: 'dateTime', label: syncBeginTimeLabel },
      { name: 'syncStatusMeaning', type: 'string', label: syncStatusMeaningLabel },
      { name: 'syncStatusFlag', type: 'number', options: statusOptions },
      { name: 'result', type: 'string', label: resultLabel },
      { name: 'totalUserCount', type: 'string' },
      { name: 'totalDeptCount', type: 'string' },
      { name: 'successUserCount', type: 'string' },
      { name: 'successDeptCount', type: 'string' },
      { name: 'errorUserCount', type: 'string' },
      { name: 'errorDepartmentCount', type: 'string' },
      { name: 'executor', type: 'string', label: executorLabel },
      { name: 'errorLog', type: 'string', label: intl.formatMessage({ id: 'iam.openLoginConfig.model.log', defaultMessage: '日志' }) },
    ],
    queryFields: [
      { name: 'syncType', type: 'string', label: syncTypeLabel, options: syncTypeOptions },
      { name: 'syncBeginTime', type: 'dateTime', range: ['start', 'end'], label: syncBeginTimeLabel, placeholder: syncBeginTimePlaceHolder },
      { name: 'syncStatusFlag', type: 'number', label: syncStatusMeaningLabel, options: statusOptions },
      { name: 'executor', type: 'string', label: executorLabel },
    ],
  };
};
