const appTypeDefaultValueMap = (intl, appType) => {
  const data = {
    wechat_enterprise: [
      {
        userInfo: {
          fieldCode: 'login_name',
          fieldName: intl.formatMessage({ id: 'iam.common.model.account.login.name', defaultMessage: '登录名' }),
        },
        openCode: 'userid',
        openName: intl.formatMessage({ id: 'iam.common.model.menu.title.account', defaultMessage: '账号' }),
        updateFlag: true,
      },
      {
        userInfo: {
          fieldCode: 'real_name',
          fieldName: intl.formatMessage({ id: 'iam.openLoginConfig.model.interface.auth.username', defaultMessage: '用户名' }),
        },
        openCode: 'name',
        openName: intl.formatMessage({ id: 'iam.openLoginConfig.model.user.data.member.name', defaultMessage: '成员名称' }),
        updateFlag: true,
      },
      {
        userInfo: {
          fieldCode: 'phone',
          fieldName: intl.formatMessage({ id: 'zknow.common.model.phone', defaultMessage: '手机号' }),
        },
        openCode: 'mobile',
        openName: intl.formatMessage({ id: 'zknow.common.model.phone', defaultMessage: '手机号' }),
        updateFlag: true,
      },
      {
        userInfo: {
          fieldCode: 'location_id',
          fieldName: intl.formatMessage({ id: 'zknow.common.model.location', defaultMessage: '位置' }),
        },
        openCode: 'address',
        openName: intl.formatMessage({ id: 'zknow.common.model.location', defaultMessage: '位置' }),
        updateFlag: true,
      },
      {
        userInfo: {
          fieldCode: 'email',
          fieldName: intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' }),
        },
        openCode: 'email',
        openName: intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' }),
        updateFlag: true,
      },
      {
        userInfo: {
          fieldCode: 'department_id',
          fieldName: intl.formatMessage({ id: 'zknow.common.model.department', defaultMessage: '部门' }),
        },
        openCode: 'main_department',
        openName: intl.formatMessage({ id: 'zknow.common.model.department', defaultMessage: '部门' }),
        updateFlag: true,
        syncUserDepartments: false,
      },
      {
        userInfo: {
          fieldCode: 'director_id',
          fieldName: intl.formatMessage({ id: 'iam.common.model.director', defaultMessage: '主管' }),
        },
        openCode: 'direct_leader',
        openName: intl.formatMessage({ id: 'iam.common.model.director', defaultMessage: '主管' }),
        updateFlag: true,
      },
    ],
    ding_talk: [
      {
        userInfo: {
          fieldCode: 'login_name',
          fieldName: intl.formatMessage({ id: 'iam.common.model.account.login.name', defaultMessage: '登录名' }),
        },
        openName: intl.formatMessage({ id: 'iam.common.model.account.login.name', defaultMessage: '登录名' }),
        openCode: 'userid',
        updateFlag: true,
      },
      {
        userInfo: {
          fieldCode: 'real_name',
          fieldName: intl.formatMessage({ id: 'iam.openLoginConfig.model.interface.auth.username', defaultMessage: '用户名' }),
        },
        openName: intl.formatMessage({ id: 'iam.openLoginConfig.model.user.data.user.real.name', defaultMessage: '用户名称' }),
        openCode: 'name',
        updateFlag: true,
      },
      {
        userInfo: {
          fieldCode: 'department_id',
          fieldName: intl.formatMessage({ id: 'zknow.common.model.department', defaultMessage: '部门' }),
        },
        openName: intl.formatMessage({ id: 'zknow.common.model.department', defaultMessage: '部门' }),
        openCode: 'department_id',
        updateFlag: true,
        syncUserDepartments: false,
      },
      {
        userInfo: {
          fieldCode: 'director_id',
          fieldName: intl.formatMessage({ id: 'iam.common.model.director', defaultMessage: '主管' }),
        },
        openName: intl.formatMessage({ id: 'iam.common.model.director', defaultMessage: '主管' }),
        openCode: 'manager_userid',
        updateFlag: true,
      },
      {
        userInfo: {
          fieldCode: 'email',
          fieldName: intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' }),
        },
        openName: intl.formatMessage({ id: 'iam.openLoginConfig.model.user.data.user.email', defaultMessage: '员工邮箱' }),
        openCode: 'email',
        updateFlag: true,
      },
      {
        userInfo: {
          fieldCode: 'location_id',
          fieldName: intl.formatMessage({ id: 'zknow.common.model.location', defaultMessage: '位置' }),
        },
        openName: intl.formatMessage({ id: 'iam.openLoginConfig.model.user.data.work.place', defaultMessage: '办公地点' }),
        openCode: 'work_place',
        updateFlag: true,
      },
      {
        userInfo: {
          fieldCode: 'phone',
          fieldName: intl.formatMessage({ id: 'zknow.common.model.phone', defaultMessage: '手机号' }),
        },
        openName: intl.formatMessage({ id: 'zknow.common.model.phone', defaultMessage: '手机号' }),
        openCode: 'mobile',
        updateFlag: true,
      },
    ],
    lark: [
      {
        userInfo: {
          fieldCode: 'login_name',
          fieldName: intl.formatMessage({ id: 'iam.common.model.account.login.name', defaultMessage: '登录名' }),
        },
        openName: intl.formatMessage({ id: 'iam.common.model.account.login.name', defaultMessage: '登录名' }),
        openCode: 'user_id',
        updateFlag: true,
      },
      {
        userInfo: {
          fieldCode: 'real_name',
          fieldName: intl.formatMessage({ id: 'iam.openLoginConfig.model.interface.auth.username', defaultMessage: '用户名' }),
        },
        openName: intl.formatMessage({ id: 'iam.openLoginConfig.model.interface.auth.username', defaultMessage: '用户名' }),
        openCode: 'name',
        updateFlag: true,
      },
      {
        userInfo: {
          fieldCode: 'phone',
          fieldName: intl.formatMessage({ id: 'zknow.common.model.phone', defaultMessage: '手机号' }),
        },
        openName: intl.formatMessage({ id: 'zknow.common.model.phone', defaultMessage: '手机号' }),
        openCode: 'mobile',
        updateFlag: true,
      },
      {
        userInfo: {
          fieldCode: 'location_id',
          fieldName: intl.formatMessage({ id: 'zknow.common.model.location', defaultMessage: '位置' }),
        },
        openName: intl.formatMessage({ id: 'iam.common.model.company.country', defaultMessage: '国家或地区' }),
        openCode: 'SAML',
        updateFlag: true,
      },
      {
        userInfo: {
          fieldCode: 'email',
          fieldName: intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' }),
        },
        openName: intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' }),
        openCode: 'email',
        updateFlag: true,
      },
      {
        userInfo: {
          fieldCode: 'department_id',
          fieldName: intl.formatMessage({ id: 'zknow.common.model.department', defaultMessage: '部门' }),
        },
        openName: intl.formatMessage({ id: 'iam.openLoginConfig.model.user.data.department', defaultMessage: '所属部门' }),
        openCode: 'department_id',
        updateFlag: true,
        syncUserDepartments: false,
      },
      {
        userInfo: {
          fieldCode: 'director_id',
          fieldName: intl.formatMessage({ id: 'iam.common.model.director', defaultMessage: '主管' }),
        },
        openName: intl.formatMessage({ id: 'iam.common.model.director', defaultMessage: '主管' }),
        openCode: 'leader_user_id',
        updateFlag: true,
      },
    ],
  };
  return data[appType];
};
export default ({ keepValueMapping, openCodeMapping, intl, appType }) => ({
  autoQuery: false,
  autoLocateFirst: true,
  data: appTypeDefaultValueMap(intl, appType),
  fields: [
    {
      name: 'userInfo',
      type: 'object',
      lovCode: 'OBJECT_FIELD',
      textField: 'fieldName',
      lovPara: { object_code: 'IAM_USER' },
    },
    {
      name: 'syncUserDepartments',
      type: 'boolean',
      defaultValue: false,
    },
    {
      name: 'openName',
      type: 'string',
      dynamicProps: {
        lookupCode: ({ dataSet }) => openCodeMapping.user[dataSet.getState('appType')],
        required: ({ record }) => keepValueMapping.user.includes(record.get('userInfo.fieldCode')),
      },
    },
    { name: 'openCode', type: 'string' },
    { name: 'updateFlag', type: 'boolean', defaultValue: true },
    { name: 'constantValue' },
    { name: 'constantWidgetType' },
  ],
  events: {
    load: ({ dataSet }) => {
      if (dataSet.length === 0 && Object.keys(appTypeDefaultValueMap).includes(dataSet.getState('appType'))) {
        dataSet.loadData(appTypeDefaultValueMap[dataSet.getState('appType')]);
      }
    },
  },
});
