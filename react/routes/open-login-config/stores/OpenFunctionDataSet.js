import { DataSet, message } from 'choerodon-ui/pro';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';
import queryString from 'query-string';
import { getEnv } from '@zknow/utils';

// 用于SDK HZERO
export default ({ intl, fieldsDataSet, tenantId, tenantDomain, intlPrefix }) => {
  const nameLabel = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const displayName = intl.formatMessage({ id: 'iam.openLoginConfig.model.display.name', defaultMessage: '展示名称' });
  const icon = intl.formatMessage({ id: 'zknow.common.model.icon', defaultMessage: '图标' });
  const link = intl.formatMessage({ id: 'iam.openLoginConfig.model.link', defaultMessage: '链接' });
  const channel = intl.formatMessage({ id: 'iam.openLoginConfig.model.feedback.channel', defaultMessage: '反馈渠道' });
  const status = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const serviceItem = intl.formatMessage({ id: 'iam.openLoginConfig.model.service.item', defaultMessage: '服务项' });
  const workbench = intl.formatMessage({ id: 'iam.openLoginConfig.model.service.workbench', defaultMessage: '服务工作台' });
  const view = intl.formatMessage({ id: 'zknow.common.model.view', defaultMessage: '视图' });
  const openType = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.type', defaultMessage: '打开方式' });
  const viewSize = intl.formatMessage({ id: 'iam.openLoginConfig.model.view.size', defaultMessage: '视图大小' });
  const screenShot = intl.formatMessage({ id: 'iam.openLoginConfig.model.screen.shot', defaultMessage: '开启截屏' });
  const feedbackLinkType = intl.formatMessage({ id: 'iam.openLoginConfig.model.feedback.link.type', defaultMessage: '跳转设置' });
  const screenShotField = intl.formatMessage({ id: 'iam.openLoginConfig.model.screen.shot.field', defaultMessage: '截屏字段' });
  const featureLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.feature', defaultMessage: '功能' });
  const botLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.smart.assistant', defaultMessage: '智能助理' });
  const methodLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.interview.method', defaultMessage: '访问方式' });
  const botViewSizeLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.dialog.size', defaultMessage: '对话框大小' });
  const knowledgeLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.knowledge', defaultMessage: '知识' });
  const iconSizeLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.model.icon.size', defaultMessage: '图标尺寸' });
  const iconCornerLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.model.icon.corner', defaultMessage: '图标切角' });

  const enableOptionDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' }), value: 'true' },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' }), value: 'false' },
    ],
  });

  const sizeOptionDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.large', defaultMessage: '大（1200）' }), value: 1200 },
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.middle', defaultMessage: '中（800）' }), value: 800 },
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.small', defaultMessage: '小（520）' }), value: 520 },
    ],
  });

  const openOptionDs = new DataSet({
    data: [
      // { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.right.drawer', defaultMessage: '右侧弹出' }), value: 'RIGHT' },
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.middle.drawer', defaultMessage: '中间弹出' }), value: 'MIDDLE' },
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.new.page', defaultMessage: '新页面' }), value: 'NEW' },
    ],
  });

  const featuresOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.create.ticket', defaultMessage: '创建工单' }), value: 'serviceItem' }, // 创建工单
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.view.ticket', defaultMessage: '查看工单' }), value: 'view' }, // 查看工单
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.access.assistant', defaultMessage: '访问智能助理' }), value: 'bot' }, // 访问智能助理
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.view.knowledge', defaultMessage: '查看知识' }), value: 'knowledge' }, // 查看知识
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.link', defaultMessage: '链接' }), value: 'link' }, // 链接
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.workbench', defaultMessage: '服务工作台' }), value: 'workbench' }, // 服务工作台
    ],
  });

  const methodOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.anonymous.access', defaultMessage: '匿名访问' }), value: 'anonymous' }, // 匿名访问
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.general.access', defaultMessage: '普通访问' }), value: 'general' }, // 普通访问
    ],
  });

  const botViewSizeOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.robot.view.size.big', defaultMessage: '大（800 x 800）' }), value: 'large' },
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.robot.view.size.middle', defaultMessage: '中（600 x 800）' }), value: 'middle' },
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.robot.view.size.small', defaultMessage: '小（400 x 800）' }), value: 'small' },
    ],
  });

  return {
    paging: false,
    autoQuery: false,
    autoLocateFirst: true,
    selection: false,
    fields: [
      { name: 'name', type: 'string', label: nameLabel, required: true },
      { name: 'displayName', type: 'string', label: displayName, required: true },
      { name: 'icon', type: 'string', label: icon, required: true },
      { name: 'link', type: 'string', label: link },
      { name: 'channel', type: 'string', label: channel, defaultValue: 'pc', transformResponse: (value) => value || 'pc' },
      { name: 'enabledFlag', type: 'boolean', label: status, defaultValue: true },
      {
        name: 'feature', // 功能
        textField: 'meaning',
        valueField: 'value',
        options: featuresOptions,
        required: true,
        label: featureLabel,
        transformResponse: (value, data) => {
          // 这里是为了兼容旧数据的
          if (value) return value;
          if (data.serviceItem) return 'serviceItem';
          if (data.workbench) return 'workbench';
          if (data.view) return 'view';
          return null;
        },
      },
      {
        name: 'bot',
        type: 'object',
        lovCode: 'INTELLIGENT_BOT',
        label: botLabel, // 访问智能助理
        dynamicProps: {
          required: ({ record }) => (record.get('feature') === 'bot'),
        },
      },
      {
        name: 'method', // 访问方式
        type: 'string',
        label: methodLabel,
        options: methodOptions,
        transformResponse: (value) => value || 'anonymous',
      },
      {
        name: 'knowledge', // 知识
        type: 'object',
        lovCode: 'KB_ELEMENT',
        label: knowledgeLabel,
        dynamicProps: {
          required: ({ record }) => (record.get('feature') === 'knowledge'),
        },
      },
      {
        name: 'botViewSize',
        type: 'string',
        label: botViewSizeLabel,
        options: botViewSizeOptions,
        transformResponse: (value) => value || 'middle',
      },
      {
        name: 'workbench',
        type: 'object',
        label: workbench, // 服务项
        lovCode: 'WORKBENCH_ALL_ENABLED',
        lovPara: {
          publishState: 'PUBLISHED',
        },
        textField: 'name',
        valueField: 'id',
        dynamicProps: {
          required: ({ record }) => (record.get('feature') === 'workbench'),
        },
      },
      {
        name: 'serviceItem',
        type: 'object',
        label: serviceItem, // 服务项
        lovCode: 'SERVICE_ITEM_WITH_SHARED',
        lovPara: {
          publishState: 'PUBLISHED',
        },
        textField: 'name',
        valueField: 'id',
        dynamicProps: {
          required: ({ record }) => (record.get('feature') === 'serviceItem'),
        },
      },
      {
        name: 'view',
        type: 'object',
        label: view,
        // lovCode: 'PAGE_VIEW',
        textField: 'name',
        valueField: 'id',
        lovPara: { search_viewType: 'TABLE' },
        dynamicProps: {
          lovCode: ({ record }) => (record.get('channel') === 'pc' ? 'PC_PAGE_VIEW' : 'H5_PAGE_VIEW'),
          required: ({ record }) => (record.get('feature') === 'view'),
        },
        transformRequest: (value) => {
          if (value) {
            return {
              id: value.id,
              name: value.name,
              businessObjectId: value.businessObjectId,
            };
          }
          return undefined;
        },
      },
      {
        name: 'openType',
        type: 'string',
        options: openOptionDs,
        defaultValue: 'MIDDLE',
        label: openType,
      },
      {
        name: 'viewSize',
        type: 'number',
        options: sizeOptionDs,
        defaultValue: 800,
        label: viewSize,
      },
      { name: 'screenShotFlag', type: 'boolean', label: screenShot },
      {
        name: 'feedbackLinkType', // sdk 反馈页的查看详情
        type: 'string',
        label: feedbackLinkType,
        lookupCode: 'SDK_PAGE_REDIRECT_TYPE',
        defaultValue: 'SELF_SYSTEM',
        transformResponse: (value, data) => (value || 'SELF_SYSTEM'),
      },
      {
        name: 'screenShotFieldCode',
        label: screenShotField,
        type: 'object',
        textField: 'name',
        valueField: 'code',
        dynamicProps: {
          lovCode: ({ record }) => {
            return record.get('serviceItem.variableFlag') ? 'VARIABLE_FIELD' : 'BUSINESS_OBJECT_FIELD';
          },
          lovPara: ({ record }) => {
            let umdPara = {};
            if (record?.get('serviceItem.udmItemFlag') === 1) {
              umdPara = {
                _udm: 'a0b923820dcc509a',
                udmTenantId: record?.get('serviceItem.udmProviderId'),
              };
            }
            return {
              variable_view_id: record.get('serviceItem.id') || record.get('variableViewId'),
              business_object_id: record.get('businessObjectId'),
              widget_type: 'Upload',
              ...umdPara,
            };
          },
        },
        transformRequest: (value) => {
          return value?.code || value;
        },
        transformResponse: (value, data) => {
          if (value) {
            return {
              code: value,
              name: data?.screenShotFieldName,
            };
          }
          return undefined;
        },
      },
      {
        name: 'iconSize',
        type: 'string',
        label: iconSizeLabel,
        options: new DataSet({
          paging: false,
          data: [
            { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.options.icon.size.small', defaultMessage: '小尺寸' }), value: 'SMALL_SIZE' },
            { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.options.icon.size.large', defaultMessage: '大尺寸' }), value: 'LARGE_SIZE' },
          ],
        }),
        defaultValue: 'SMALL_SIZE',
        transformResponse: (value) => (value || 'SMALL_SIZE'),
        help: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.help.icon.size', defaultMessage: '小尺寸图标显示为24*24大小，大尺寸显示为40*40大小。小尺寸模式下可调节切角的方式。若您的图标需要显示为方形，则可选择方角切角方式。' }),
      },
      {
        name: 'iconCorner',
        type: 'string',
        label: iconCornerLabel,
        options: new DataSet({
          paging: false,
          data: [
            { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.options.icon.corner.square', defaultMessage: '方角' }), value: 'SQUARE_CORNER' },
            { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.options.icon.corner.round', defaultMessage: '圆角' }), value: 'ROUND_CORNER' },
          ],
        }),
        defaultValue: 'SQUARE_CORNER',
        transformResponse: (value) => (value || 'SQUARE_CORNER'),
      },
    ],
    queryFields: [
      { name: 'name', type: 'string', label: nameLabel },
    ],
    children: {
      fields: fieldsDataSet,
      mapFields: new DataSet({}),
    },
    events: {
      update: async (props) => {
        const { record, dataSet, name, value, oldValue } = props;
        const currentChannel = record.get('channel'); // 反馈渠道
        const quickToken = record.dataSet?.parent?.current?.get('appId');
        const quickType = record.dataSet?.parent?.current?.get('quickType');
        const commonParams = { tenantId, quickToken, quickType, quickId: record.get('id') };
        if (name === 'icon') {
          if (value.endsWith('.svg')) {
            record.set(name, oldValue);
            message.info(intl.formatMessage({ id: 'iam.openLoginConfig.model.not.support.svg', defaultMessage: '暂不支持svg格式图片' }));
          }
        }
        if (name === 'feature') {
          // 切换功能时，将服务项、视图和智能助理的字段都清空
          record.set('serviceItem', undefined);
          record.set('view', undefined);
          record.set('bot', undefined);
          record.set('knowledge', undefined);
        }
        if (name === 'workbench') {
          const workbenchId = record.get('workbench.id');
          const linkParams = `?tenantId=${tenantId}&quickToken=${quickToken}&quickId=${record.get('id')}&workbenchFlag=true`;
          record.set('link', `${window.location.origin}/#/lc/feedback/${workbenchId}${linkParams}`);
        }
        if (name === 'serviceItem') {
          record.set('variableViewId', value?.ticketTypeId ? undefined : value?.variableViewId);
          record.set('businessObjectId', value?.ticketTypeId ? value?.businessObjectId : undefined);
          // 修复旧数据
          if (!record.get('id')) {
            record.set('id', uuidv4());
          }
          const linkParams = `?tenantId=${tenantId}&quickToken=${quickToken}&quickId=${record.get('id')}${value?.ticketTypeId ? '&viewFlag=true' : ''}${record.get('serviceItem.udmItemFlag') === 1 ? `&udmItemFlag=true&udmProviderId=${record.get('serviceItem.udmProviderId')}` : ''}`;
          // 更新链接
          if (record.get('channel') === 'mobile') {
            record.set('link', `${window._env_.MB_HOST}/pages/create-order/index?tenant=${tenantDomain}&noBreadcrumb=true&itemId=${value.id}&hiddenHeader=true`);
          } else if (record.get('serviceItem.udmItemFlag') === 1) {
            record.set('link', `${window.location.origin}/#/lc/feedback/${value.variableViewId}/${value.id}${linkParams}`);
          } else {
            const data = await axios(`itsm/v1/${tenantId}/service_items/${value.id}`);
            if (data.type === 'CONTENT_ITEM' && data.contentType === 'LINK_URL') {
              record.set('link', data.linkUrl);
              return;
            } else if (data.type === 'CONTENT_ITEM' && data.contentType === 'KNOWLEDGE') {
              record.set('link', `${window.location.origin}/#/itsm/portal/knowledge?menu=knowledge&knowledgeId=${data?.elementId}`);
              return;
            } else if (value?.variableViewId && value?.id) {
              record.set('link', `${window.location.origin}/#/lc/feedback/${value.variableViewId}/${value.id}${linkParams}`);
            } else {
              record.set('link', `${window.location.origin}`);
            }
          }
          // 清空截屏字段
          record.set('screenShotFieldCode', undefined);
          record.set('screenShotFieldName', undefined);
          if (dataSet?.children?.fields) {
            dataSet.children.fields.removeAll(true);
          }
        }
        if (name === 'view') {
          record.set('variableViewId', undefined);
          record.set('businessObjectId', value?.businessObjectId);
          // 修复旧数据
          if (!record.get('id')) {
            record.set('id', uuidv4());
          }
          const linkParams = `?tenantId=${tenantId}&quickToken=${quickToken}&quickId=${record.get('id')}&viewType=${value?.viewType}`;
          // 更新链接
          if (value?.id) {
            if (record.get('channel') === 'mobile') {
              record.set('link', `${window._env_.MB_HOST}/pages/task-detail/index?tenant=${tenantDomain}&viewCode=${value.code}&hiddenHeader=true`);
            } else {
              record.set('link', `${window.location.origin}/#/lc/feedback/${value.id}${linkParams}`);
            }
          } else {
            record.set('link', undefined);
          }
          // 清空截屏字段
          record.set('screenShotFieldCode', undefined);
          record.set('screenShotFieldName', undefined);
          if (dataSet?.children?.fields) {
            dataSet.children.fields.removeAll(true);
          }
        }
        if (name === 'bot' || name === 'method') {
          // 机器人的链接会自适应窗口大小，不区分移动端和pc端
          record.set('variableViewId', undefined);
          record.set('businessObjectId', undefined);
          const method = record.get('method') || 'anonymous';
          const { id } = record.get('bot');
          if (method === 'anonymous') { // 匿名访问
            record.set('link', id ? `${window._env_.BOTPRESS_DOMAIN || window._env_.botpress_domain}/s/${id}?${queryString.stringify(commonParams)}` : undefined);
          } else { // 普通访问
            Object.assign(commonParams, { botId: id });
            record.set('link', `${window.location.origin}/#/intelligent/bot?${queryString.stringify(commonParams)}`);
          }
        }
        if (name === 'knowledge') {
          const { id } = record.get('knowledge');
          Object.assign(commonParams, { portal: true, menu: 'knowledge', knowledgeId: id });
          if (currentChannel === 'pc') {
            record.set('link', `${window.location.origin}/#/itsm/portal/knowledge?${queryString.stringify(commonParams)}`);
          } else {
            record.set('link', `${getEnv('MB_HOST')}/pages/knowledge-detail/index?tenant=${tenantDomain}&id=${id}`);
          }
        }
        if (name === 'name') {
          record.set('displayName', value);
        }
        if (name === 'screenShotFieldCode') {
          record.set('screenShotFieldName', value?.name);
        }
      },
    },
  };
};
