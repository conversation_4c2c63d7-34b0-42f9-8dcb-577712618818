import { DataSet } from 'choerodon-ui/pro';
import { v4 as uuidv4 } from 'uuid';
import { getEnv } from '@zknow/utils';
import queryString from 'query-string';

// 用于SAP EBS
export default ({ intl, tenantId, fieldsDataSet, tenantDomain }) => {
  const statusLabel = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const linkLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.link', defaultMessage: '链接' });
  const nameLabel = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const channelLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.feedback.channel', defaultMessage: '反馈渠道' });
  const channelHelp = intl.formatMessage({ id: 'iam.openLoginConfig.model.channel.help', defaultMessage: '客户端类型应用，仅支持PC端使用' });
  const featureLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.feature', defaultMessage: '功能' });
  const serviceItemLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.service.item', defaultMessage: '服务项' });
  const viewLabel = intl.formatMessage({ id: 'zknow.common.model.view', defaultMessage: '视图' });
  const viewHelp = intl.formatMessage({ id: 'iam.openLoginConfig.model.view.help', defaultMessage: '视图类型仅可选择PC的列表视图' });
  const botLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.smart.assistant', defaultMessage: '智能助理' });
  const methodLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.interview.method', defaultMessage: '访问方式' });
  const knowledgeLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.knowledge', defaultMessage: '知识' });
  const feedbackLinkType = intl.formatMessage({ id: 'iam.openLoginConfig.model.feedback.link.type', defaultMessage: '跳转设置' });

  const channelOptions = new DataSet({
    data: [
      { meaning: 'PC', value: 'pc' }, // pc
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.channel.mobile', defaultMessage: '移动端' }), value: 'mobile' }, // 移动端
    ],
  });

  const featuresOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.create.ticket', defaultMessage: '创建工单' }), value: 'create' }, // 创建工单
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.view.ticket', defaultMessage: '查看工单' }), value: 'view' }, // 查看工单
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.access.assistant', defaultMessage: '访问智能助理' }), value: 'bot' }, // 智能助理
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.view.knowledge', defaultMessage: '查看知识' }), value: 'knowledge' }, // 查看知识
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.link', defaultMessage: '链接' }), value: 'link' }, // 链接
    ],
  });

  const methodOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.anonymous.access', defaultMessage: '匿名访问' }), value: 'anonymous' }, // 匿名访问
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.general.access', defaultMessage: '普通访问' }), value: 'general' }, // 普通访问
    ],
  });

  return {
    paging: false,
    autoQuery: false,
    autoLocateFirst: true,
    selection: false,
    fields: [
      { name: 'id', type: 'string', defaultValue: uuidv4() },
      { name: 'enabledFlag', type: 'boolean', label: statusLabel, defaultValue: true },
      { name: 'link', type: 'string', label: linkLabel },
      { name: 'name', type: 'string', required: true, label: nameLabel },
      { name: 'feature', textField: 'meaning', valueField: 'value', options: featuresOptions, required: true, label: featureLabel },
      {
        name: 'channel',
        type: 'string',
        label: channelLabel,
        options: channelOptions,
        help: channelHelp,
        transformResponse: (value) => value || 'pc',
      },
      {
        name: 'serviceItem',
        type: 'object',
        lovCode: 'SERVICE_ITEM_WITH_SHARED',
        label: serviceItemLabel,
        dynamicProps: {
          required: ({ record }) => record.get('feature') === 'create',
        },
      },
      {
        name: 'view',
        type: 'object',
        label: viewLabel,
        lovPara: { search_viewType: 'TABLE' },
        help: viewHelp,
        dynamicProps: {
          required: ({ record }) => record.get('feature') === 'view',
          lovCode: ({ record }) => (record.get('channel') === 'pc' ? 'PC_PAGE_VIEW' : 'H5_PAGE_VIEW'),
        },
        // transformRequest: (value) => {
        //   return value ? { id: value.id, name: value.name, businessObjectId: value.businessObjectId } : undefined;
        // },
      },
      {
        name: 'bot',
        type: 'object',
        lovCode: 'INTELLIGENT_BOT',
        label: botLabel,
        dynamicProps: {
          required: ({ record }) => record.get('feature') === 'bot',
        },
      },
      {
        name: 'method',
        type: 'string',
        label: methodLabel,
        options: methodOptions,
        transformResponse: (value) => value || 'anonymous',
      },
      {
        name: 'knowledge',
        type: 'object',
        lovCode: 'KB_ELEMENT',
        label: knowledgeLabel,
        dynamicProps: {
          required: ({ record }) => record.get('feature') === 'knowledge',
        },
      },
      {
        name: 'link',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.link', defaultMessage: '链接' }),
        dynamicProps: {
          required: ({ record }) => record.get('feature') === 'link',
        },
      },
      {
        name: 'feedbackLinkType', // sdk 反馈页的查看详情
        type: 'string',
        label: feedbackLinkType,
        lookupCode: 'SDK_PAGE_REDIRECT_TYPE',
        defaultValue: 'SELF_SYSTEM',
        transformResponse: (value, data) => (value || 'SELF_SYSTEM'),
      },
    ],
    queryFields: [
      { name: 'name', type: 'string', label: nameLabel },
    ],
    children: {
      fields: fieldsDataSet,
      mapFields: new DataSet({}),
    },
    events: {
      update: ({ record, name }) => {
        const feature = record.get('feature');
        const channel = record.get('channel'); // 反馈渠道
        const quickToken = record.dataSet?.parent?.current?.get('appId');
        const quickType = record.dataSet?.parent?.current?.get('quickType');
        const commonParams = { tenantId, quickToken, quickType, quickId: record.get('id') };
        if (name === 'channel' && feature === 'view' && record.get('view')) {
          // 改变了反馈渠道 && 功能是视图 && 视图有值 = 清空视图的值
          record.set('view', undefined);
        }
        if (feature === 'create') { // 服务项 -- 创建工单
          const { variableViewId, id, ticketTypeId } = record.get('serviceItem') || {};
          Object.assign(commonParams, { feature: 'create' });
          if (ticketTypeId) {
            Object.assign(commonParams, { viewFlag: true });
          }
          if (channel === 'pc') {
            record.set('link', variableViewId && id ? `${window.location.origin}/#/lc/feedback/${variableViewId}/${id}?${queryString.stringify(commonParams)}` : undefined);
          } else {
            record.set('link', id ? `${getEnv('MB_HOST')}/pages/create-order/index?tenant=${tenantDomain}&itemId=${id}` : '');
          }
        } else if (feature === 'view') { // 视图 -- 查看工单
          const { viewType, id } = record.get('view');
          Object.assign(commonParams, { viewType, feature: 'view' });
          if (channel === 'pc') {
            record.set('link', id ? `${window.location.origin}/#/lc/feedback/${id}?${queryString.stringify(commonParams)}` : undefined);
          } else {
            record.set('link', id ? `${getEnv('MB_HOST')}/pages/task-detail/index/?tenant=${tenantDomain}&viewId=${id}` : '');
          }
        } else if (feature === 'bot') { // 智能助理
          const method = record.get('method') || 'anonymous';
          const { id } = record.get('bot');
          if (method === 'anonymous') { // 匿名访问
            record.set('link', id ? `${window._env_.BOTPRESS_DOMAIN || window._env_.botpress_domain}/s/${id}?${queryString.stringify(commonParams)}` : undefined);
          } else { // 普通访问
            Object.assign(commonParams, { botId: id });
            record.set('link', `${window.location.origin}/#/intelligent/bot?${queryString.stringify(commonParams)}`);
          }
        } else if (feature === 'knowledge') { // 知识
          const { id } = record.get('knowledge');
          Object.assign(commonParams, { portal: true, menu: 'knowledge', knowledgeId: id });
          if (channel === 'pc') {
            record.set('link', `${window.location.origin}/#/itsm/portal/knowledge?${queryString.stringify(commonParams)}`);
          } else {
            record.set('link', `${getEnv('MB_HOST')}/pages/knowledge-detail/index?tenant=${tenantDomain}&id=${id}`);
          }
        }
      },
    },
  };
};
