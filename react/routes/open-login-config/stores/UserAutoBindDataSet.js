const appTypeDefaultValueMap = (intl, appType) => {
  const data = {
    wechat_enterprise: [
      {
        userInfo: {
          fieldCode: 'login_name',
          fieldName: intl.formatMessage({ id: 'iam.common.model.account.login.name', defaultMessage: '登录名' }),
        },
        openCode: 'userid',
        openName: intl.formatMessage({ id: 'iam.common.model.menu.title.account', defaultMessage: '账号' }),
      },
    ],
    ding_talk: [
      {
        userInfo: {
          fieldCode: 'login_name',
          fieldName: intl.formatMessage({ id: 'iam.common.model.account.login.name', defaultMessage: '登录名' }),
        },
        openName: intl.formatMessage({ id: 'iam.common.model.account.login.name', defaultMessage: '登录名' }),
        openCode: 'userid',
      },
    ],
    lark: [
      {
        userInfo: {
          fieldCode: 'login_name',
          fieldName: intl.formatMessage({ id: 'iam.common.model.account.login.name', defaultMessage: '登录名' }),
        },
        openName: intl.formatMessage({ id: 'iam.common.model.account.login.name', defaultMessage: '登录名' }),
        openCode: 'user_id',
      },
    ],
  };
  return data[appType];
};
export default ({ keepValueMapping, openCodeMapping, intl, appType }) => ({
  autoQuery: false,
  autoLocateFirst: true,
  data: appTypeDefaultValueMap(intl, appType),
  fields: [
    {
      name: 'userInfo',
      type: 'object',
      lovCode: 'OBJECT_FIELD',
      textField: 'fieldName',
      lovPara: { object_code: 'IAM_USER' },
    },
    {
      name: 'openName',
      type: 'string',
      dynamicProps: {
        lookupCode: ({ dataSet }) => openCodeMapping.user[dataSet.getState('appType')],
        required: ({ record }) => keepValueMapping.user.includes(record.get('userInfo.fieldCode')),
      },
    },
    { name: 'openCode', type: 'string' },
    { name: 'updateFlag', type: 'boolean', defaultValue: true },
    { name: 'constantValue' },
    { name: 'constantWidgetType' },
  ],
  events: {
    load: ({ dataSet }) => {
      if (dataSet.length === 0 && Object.keys(appTypeDefaultValueMap).includes(dataSet.getState('appType'))) {
        dataSet.loadData(appTypeDefaultValueMap[dataSet.getState('appType')]);
      }
    },
  },
});
