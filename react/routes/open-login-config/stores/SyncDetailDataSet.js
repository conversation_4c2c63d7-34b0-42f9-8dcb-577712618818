import { DataSet } from 'choerodon-ui/pro';

export default ({ tenantId, historyId, type, intlPrefix, intl }) => {
  const openIdLabel = type === 'users' ? intl.formatMessage({ id: 'iam.openLoginConfig.model.account.loginName', defaultMessage: '登录名' }) : intl.formatMessage({ id: 'iam.openLoginConfig.model.account.department.id', defaultMessage: '部门ID' });
  const openNameLable = type === 'users' ? intl.formatMessage({ id: 'iam.openLoginConfig.model.account.member.name', defaultMessage: '姓名' }) : intl.formatMessage({ id: 'iam.openLoginConfig.model.account.department.name', defaultMessage: '登录名' });
  const handleStatusMeaningLable = intl.formatMessage({ id: 'iam.openLoginConfig.model.processing.method', defaultMessage: '处理方式' });
  const syncStatusMeaningLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.sync.results', defaultMessage: '同步结果' });
  const errorLogLable = intl.formatMessage({ id: 'iam.openLoginConfig.model.yqcloud.iam.ldap.sync.log', defaultMessage: '错误日志' });

  const statusOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.synchronizing', defaultMessage: '同步中' }), value: 0 }, // 同步中
      // { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.partially.failed', defaultMessage: '部分失败' }), value: -2 }, // 部分失败
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.sync.completed', defaultMessage: '同步完成' }), value: 1 }, // 同步完成
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.sync.failed', defaultMessage: '同步失败' }), value: -1 }, // 同步失败
    ],
  });

  const handleStatusOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.not.deal.with', defaultMessage: '无更新' }), value: 0 }, // 无更新
      { meaning: intl.formatMessage({ id: 'zknow.common.button.add', defaultMessage: '新增' }), value: 1 }, // 新增
      { meaning: intl.formatMessage({ id: 'zknow.common.condition.isUpdate', defaultMessage: '更新' }), value: 2 }, // 更新
      { meaning: intl.formatMessage({ id: 'zknow.common.action.make.invalid', defaultMessage: '失效' }), value: 3 }, // 失效
    ],
  });

  return {
    name: type,
    paging: true,
    autoQuery: true,
    autoLocateFirst: true,
    selection: false,
    transport: {
      read: () => ({
        url: `/ecos/v1/${tenantId}/openApps/sync/histories/${historyId}/${type}`,
        method: 'get',
      }),
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'openId', type: 'string', label: openIdLabel },
      { name: 'openName', type: 'string', label: openNameLable },
      { name: 'handleStatus', type: 'number', label: handleStatusMeaningLable, options: handleStatusOptions },
      { name: 'handleStatusMeaning', type: 'string', label: handleStatusMeaningLable },
      { name: 'syncStatus', type: 'number', label: syncStatusMeaningLabel, options: statusOptions },
      { name: 'syncStatusMeaning', type: 'string', label: syncStatusMeaningLabel },
      { name: 'errorLog', type: 'string', label: errorLogLable },
    ],
    queryFields: [
      { name: 'syncStatus', type: 'number', label: syncStatusMeaningLabel, options: statusOptions },
      { name: 'handleStatus', type: 'number', label: handleStatusMeaningLable, options: handleStatusOptions },
    ],
  };
};
