import { getQueryParams } from '@zknow/utils';

export default ({ intl, intlPrefix, tenantId, appItemId }) => {
  const realNameLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.choerodon.real.name', defaultMessage: '绑定人员' });
  const emailLabel = intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' });
  const loginNameLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.choerodon.login.name', defaultMessage: '登录名' });
  const creationDateLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.choerodon.creation.date', defaultMessage: '绑定时间' });
  const choerodonUserAccountLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.choerodon.choerodon.user.account', defaultMessage: '猪齿鱼账号' });
  return {
    paging: true,
    dataKey: 'content',
    autoQuery: false,
    autoLocateFirst: false,
    selection: false,
    transport: {
      read: ({ data }) => {
        if (!appItemId) return null;
        return {
          url: `/iam/v1/${tenantId}/choerodon_user/${appItemId}`,
          method: 'get',
          data: getQueryParams(data),
        };
      },
    },
    fields: [
      { name: 'realName', label: realNameLabel },
      { name: 'email', label: emailLabel },
      { name: 'loginName', label: loginNameLabel },
      { name: 'creationDate', label: creationDateLabel },
      { name: 'choerodonUserAccount', label: choerodonUserAccountLabel },
    ],
    queryFields: [
      { name: 'realName', type: 'string', label: realNameLabel },
      { name: 'email', type: 'string', label: emailLabel },
      { name: 'loginName', type: 'string', label: loginNameLabel },
      { name: 'creationDate', type: 'string', label: creationDateLabel },
      { name: 'choerodonUserAccount', type: 'string', label: choerodonUserAccountLabel },
    ],
    events: {

    },
  };
};
