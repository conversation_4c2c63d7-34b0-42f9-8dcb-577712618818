export default ({ intl, intlPrefix }) => {
  return {
    paging: false,
    autoQuery: false,
    autoLocateFirst: true,
    selection: false,
    fields: [
      {
        name: 'content',
        type: 'string',
        defaultValue: intl.formatMessage({ id: `${intlPrefix}.sendContentDefaultValue` }),
        transformResponse: (value) => (value || intl.formatMessage({ id: `${intlPrefix}.sendContentDefaultValue` })),
      },
      {
        name: 'range',
        type: 'object',
        dynamicProps: {
          lovPara: ({ record }) => ({ openAppId: record.dataSet?.parent?.current?.get('config.openAppId') }),
          lovCode: ({ record }) => {
            const appType = record.dataSet?.parent?.current?.get('type');
            if (appType === 'ding_talk') return 'DING_SOCIAL_DEPARTMENT_LIST';
            else return 'WECOM_DEPARTMENT_LIST';
          },
        },
      },
    ],
  };
};
