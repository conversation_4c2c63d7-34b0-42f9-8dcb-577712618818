export default (props) => {
  const { tenantId } = props;

  function getUrl(boId, viewId) {
    if (boId) {
      return `/lc/v1/${!tenantId ? '' : `${tenantId}/`}object_fields/all/${boId}`;
    } else if (viewId) {
      return `/lc/v1/${!tenantId ? '' : `${tenantId}/`}variable_fields/${viewId}`;
    }
    return '';
  }

  return {
    autoQuery: false,
    paging: false,
    selection: false,
    transport: {
      read: ({ data: { boId, viewId, variableFlag } }) => ({
        url: getUrl(boId, viewId, variableFlag),
        method: 'get',
      }),
    },
  };
};
