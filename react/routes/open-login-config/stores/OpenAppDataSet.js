import { DataSet, message } from 'choerodon-ui/pro';
import qs from 'qs';
import { axios } from '@yqcloud/apps-master';
import { getEnv } from '@zknow/utils';

export default ({
  intlPrefix,
  intl,
  organizationId,
  type,
  list,
  tenantId,
  openFunctionDataSet,
  openClientFunctionDataSet,
  notifyDataSet,
  checkPassword,
  userTenantId,
  nameMap,
  todoConfigDataSet,
}) => {
  const typeOptionDs = new DataSet({
    data: type === 'site' ? [
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat', defaultMessage: '微信' }), value: 'wechat' },
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.oauth2', defaultMessage: '其他Oauth2.0' }), value: 'other' },
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.wechat.open.account', defaultMessage: '微信公众号' }), value: 'wechat_open_account' },
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.wechat.mini.program', defaultMessage: '小程序' }), value: 'wechat_mini_program' },
    ] : [
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.enterprise', defaultMessage: '企业微信' }), value: 'wechat_enterprise' },
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.dingding', defaultMessage: '钉钉' }), value: 'ding_talk' },
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.lark', defaultMessage: '飞书' }), value: 'lark' },
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.oauth2', defaultMessage: '其他Oauth2.0' }), value: 'other' },
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.wechat.open.account', defaultMessage: '微信公众号' }), value: 'wechat_open_account' },
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.wechat.agent', defaultMessage: '微信客服' }), value: 'wechat_agent' },
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.choerodon', defaultMessage: '猪齿鱼' }), value: 'choerodon' },
    ],
  });
  const yesNoOptionDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' }), value: true },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' }), value: false },
    ],
  });
  const enabledOptionDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.button.enable', defaultMessage: '启用' }), value: true },
      { meaning: intl.formatMessage({ id: 'zknow.common.button.deactivated', defaultMessage: '停用' }), value: false },
    ],
  });

  const url = type === 'site' ? `iam/yqc/open_apps${list ? '/list' : ''}` : `iam/yqc/${organizationId}/open_apps${list ? '/list' : ''}`;

  const icon = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.icon', defaultMessage: '应用图标' });
  const code = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const name = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const typeLabel = intl.formatMessage({ id: 'zknow.common.model.type', defaultMessage: '类型' });
  const quickTypeLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.quick.type', defaultMessage: '应用类型' });
  const idLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.id', defaultMessage: '应用ID' });
  const enabledFlag = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const enabledPasswordFlag = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.enabled.password.flag', defaultMessage: '启用初始密码' });
  const password = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.password', defaultMessage: '初始化密码' });
  const appId = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.app.id', defaultMessage: '客户端ID' });
  const appSecret = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.app.secret', defaultMessage: '客户端密码' });
  const agentId = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.agent.id', defaultMessage: 'AgentId' });
  const authUrl = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.auth.url', defaultMessage: '认证地址' });
  const tokenUrl = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.token.url', defaultMessage: '获取token地址' });
  const userInfoUrl = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.user.info.url', defaultMessage: '获取用户信息地址' });
  const scope = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.scope', defaultMessage: '认证作用域' });
  const uuidField = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.uuid.field', defaultMessage: '用户唯一标识' });
  const enabledLogin = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.enabled.login', defaultMessage: '作为登录方式' });
  const timingFlag = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.timing.flag', defaultMessage: '启用定时同步' });
  const dingTalkRobotFlag = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.ding.talk.robot.flag', defaultMessage: '启用酷应用' });
  const dingTalkRobotCode = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.ding.talk.robot.code', defaultMessage: '机器人编码' });
  const realTimeSyncFlag = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.real.time.sync.flag', defaultMessage: '开启实时同步' });
  const dingTalkCardId = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.ding.talk.card.id', defaultMessage: '卡片模板ID' });

  const loginNameField = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.login.name', defaultMessage: '登录名' });
  const realNameField = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.real.name', defaultMessage: '姓名' });
  const emailField = intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' });
  const phoneField = intl.formatMessage({ id: 'zknow.common.model.phone', defaultMessage: '手机号' });
  const locationField = intl.formatMessage({ id: 'zknow.common.model.location', defaultMessage: '位置' });
  const departmentField = intl.formatMessage({ id: 'zknow.common.model.department', defaultMessage: '部门' });
  const directorField = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.manager', defaultMessage: '主管' });
  const startSyncTime = intl.formatMessage({ id: 'zknow.common.model.startTime', defaultMessage: '开始时间' });
  const endSyncTime = intl.formatMessage({ id: 'zknow.common.model.endTime', defaultMessage: '结束时间' });
  const frequency = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.sync.interval', defaultMessage: '同步间隔' });
  const description = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const userInfoInterface = intl.formatMessage({ id: 'iam.openLoginConfig.model.user.info.interface', defaultMessage: '人员信息接口' });
  const displayName = intl.formatMessage({ id: 'iam.openLoginConfig.model.display.name', defaultMessage: '展示名称' });
  const iconLabel = intl.formatMessage({ id: 'zknow.common.model.icon', defaultMessage: '图标' });
  const orgCodeLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.choerodon.org.code', defaultMessage: '组织编码' });
  const envUrlLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.choerodon.env.url', defaultMessage: '网关地址' });
  const domainUrlLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.choerodon.domain.url', defaultMessage: '猪齿鱼域名' });
  const fieldMappingConfigLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.choerodon.field.mapping.config', defaultMessage: '映射配置' });
  const syncC7nJournalFlagLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.choerodon.sync.c7n.journal.flag', defaultMessage: '是否同步回复至猪齿鱼' });

  const notifyFlagLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.notify.flag', defaultMessage: '通知窗口' });
  const enableWechatEnterpriseBotFlagLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.enable.wechat.enterprise.bot.flag', defaultMessage: '启用企微助理' });
  const enableDingTalkBotFlagLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.enable.ding.talk.bot.flag', defaultMessage: '启用钉助理' });
  const notifyBotLabel = intl.formatMessage({ id: `${intlPrefix}.wechatEnterpriseBot` });
  const loginWithoutPassword = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.login.without.password', defaultMessage: '开启免密登录' });
  const openFieldMappingFlag = intl.formatMessage({ id: 'iam.openLoginConfig.model.c7n.mapping.open.field.mapping.flag', defaultMessage: '是否开启可视化配置' });
  const iconSizeLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.model.icon.size', defaultMessage: '图标尺寸' });
  const iconCornerLabel = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.model.icon.corner', defaultMessage: '图标切角' });
  const corpId = intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.model.corpId', defaultMessage: '目录(租户) ID' });

  const noticeScopeOptions = new DataSet(({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.desc.all', defaultMessage: '全部' }), value: 'ALL' },
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.success.only', defaultMessage: '仅成功' }), value: 'SUCCESS' },
      { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.failure.and.partial.success', defaultMessage: '失败与部分成功' }), value: 'NOT_SUCCESS' },
    ],
  }));

  const dingdingTodoTransform2Backend = (data) => {
    if (data.todoConfig?.length === 0) {
      delete data.todoConfig;
    }
    // 1 将数组改成对象
    if (data.todoConfig?.[0]) {
      data.todoConfig = data.todoConfig[0];
    }
    if (data.todoConfig?.configTemplate || typeof data.todoConfig?.configTemplate === 'object') {
      const { businessType } = data.todoConfig || {};
      // 由businessType构造新的configTemplate，目的是删掉之前的在businessType上已删除的项
      const configTemplate = businessType?.map((item) => {
        return ({
          businessTypeName: item.name,
          businessObjectId: item.businessObjectId,
          code: item.code,
          content: data.todoConfig?.configTemplate?.find((e) => e.code === item.code)?.content || {},
        });
      });
      // configTemplate字符串化
      data.todoConfig.configTemplate = JSON.stringify(configTemplate || []);
    }
    // 3 todoCreatorCode字符串化
    if (data.todoConfig?.todoCreatorCode) {
      data.todoConfig.todoCreatorCode = JSON.stringify(data.todoConfig.todoCreatorCode);
    }
    if (data.todoConfig?.todoFlag) {
      data.todoFlag = true;
    }
    return data;
  };

  const dingdingTodoTransform2Frontend = (res) => {
    // 需要todoConfig里的让数据结构符合前端需要
    // configTemplate对象化
    try {
      if (res.todoConfig?.configTemplate && typeof res.todoConfig?.configTemplate === 'string') {
        res.todoConfig.configTemplate = JSON.parse(res.todoConfig?.configTemplate);
        // 分离出businessType
        if (res.todoConfig?.configTemplate?.length) {
          res.todoConfig.businessType = res.todoConfig?.configTemplate?.map((item) => {
            return { name: item.businessTypeName || 'UNNAMED', code: item.code, businessObjectId: item.businessObjectId };
          });
        }
      }
      // todoCreatorCode字符串变数组
      if (res.todoConfig?.todoCreatorCode && typeof res.todoConfig.todoCreatorCode === 'string') {
        res.todoConfig.todoCreatorCode = JSON.parse(res.todoConfig?.todoCreatorCode);
      }
      // 对象变数组（最后一步）
      if (res.todoConfig) {
        // 生成todoFlag
        res.todoConfig.todoFlag = res.todoFlag;
        res.todoConfig = [res.todoConfig];
      }
      return res;
    } catch (err) {
      message.error(err);
      return res;
    }
  };
  const validateCode = (value, tip) => {
    const reg = /^[0-9A-Z_]{1,}$/;
    return reg.test(value) || tip;
  };
  const phonePattern = /^[1][3,4,5,7,8,9][0-9]{9}$/;

  const typeList = ['wechat_enterprise', 'lark', 'ding_talk'];
  const validateUrl = (value) => {
    // eslint-disable-next-line no-useless-escape
    const strRegex = '^(http|https)://([\\w.]+\\/?)\\S*'
                    + "?(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?" // ftp的user@
                    // eslint-disable-next-line no-useless-escape
                    + '(([0-9]{1,3}\.){3}[0-9]{1,3}' // IP形式的URL- **************
                    + '|' // 允许IP和DOMAIN（域名）
                    // eslint-disable-next-line no-useless-escape
                    + "([0-9a-zA-Z_!~*'()-]+\.)*" // 域名- www.
                    // eslint-disable-next-line no-useless-escape
                    + '([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z]\.' // 二级域名
                    + '[a-zA-Z]{2,6})' // first level domain- .com or .museum
                    + '(:[0-9]{1,4})?' // 端口- :80
                    + '((/?)|'
                    + "(/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+/?)$";
    const reg = new RegExp(strRegex);
    return reg.test(value) || intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.choerodon.domain.validate', defaultMessage: '请输入正确的地址！' });
  };

  // eslint-disable-next-line no-shadow
  const validatePolicy = (value, name, record) => {
    const phoneValue = record.get('phone');
    if (phoneValue) {
      if (phonePattern.test(phoneValue)) {
        return true;
      }
      return intl.formatMessage({ id: 'iam.common.model.account.phone.error', defaultMessage: '请填写正确的手机号' });
    }
    return true;
  };
  const validatePassword = async (value) => {
    const ret = await checkPassword(value, userTenantId, intl);
    return ret;
  };

  const dingTalkChatTransferDs = new DataSet({
    paging: false,
    fields: [
      {
        name: 'dingTalkChatTransferTicketServiceItem',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.transfer.service.item', defaultMessage: '转工单服务项' }),
        type: 'object',
        lovCode: 'SC_ITEM',
        ignore: 'always',
      },
      {
        name: 'dingTalkChatTransferTicketPrompt',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.prompt.for.service.item', defaultMessage: 'Prompt模板' }),
        type: 'object',
        lovCode: 'PROMPT_TEMPLATE',
        ignore: 'always',
      },
      {
        name: 'dingTalkChatTransferTicketServiceItemField',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.transfer.service.item.field', defaultMessage: '聊天记录存储字段' }),
        type: 'object',
        lovCode: 'SC_ITEM_FIELD',
        valueField: 'id',
        textField: 'name',
        ignore: 'always',
        dynamicProps: {
          lovPara: ({ record }) => {
            return { itemId: record.get('dingTalkChatTransferTicketServiceItem.id'), widgetType: 'Input,RichText,TextArea' };
          },
        },
      },
    ],
  });

  const feishuChatTransferDs = new DataSet({
    paging: false,
    fields: [
      {
        name: 'feishuTalkChatTransferTicketServiceItem',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.transfer.service.item', defaultMessage: '服务项' }),
        type: 'object',
        lovCode: 'SC_ITEM',
        ignore: 'always',
        computedProps: {
          required: ({ dataSet }) => dataSet.parent?.current?.get('feishuTalkChatItemRange') === 'PART',
        },
      },
      {
        name: 'feishuTalkChatTransferTicketServiceItemField',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.transfer.service.item.field', defaultMessage: '聊天记录存储字段' }),
        type: 'object',
        lovCode: 'SC_ITEM_FIELD',
        valueField: 'id',
        textField: 'name',
        ignore: 'always',
        dynamicProps: {
          lovPara: ({ record }) => {
            return { itemId: record.get('feishuTalkChatTransferTicketServiceItem.id'), widgetType: 'Input,RichText,TextArea' };
          },
        },
        transformRequest: (value) => {
          if (value) {
            return {
              id: value.id,
              name: value.name,
              code: value.code,
            };
          } else {
            return null;
          }
        }
      },
      {
        name: 'feishuTalkChatTransferTicketPromptFlag',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.transfer.smart.fill', defaultMessage: '智能填充' }),
        type: 'boolean',
        ignore: 'always',
      },
      {
        name: 'feishuTalkChatTransferTicketPrompt',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.prompt.for.service.item', defaultMessage: 'Prompt模板' }),
        type: 'object',
        lovCode: 'PROMPT_TEMPLATE',
        ignore: 'always',
        transformRequest: (value) => {
          if (value) {
            return {
              id: value.id,
              name: value.name,
              code: value.code,
            };
          } else {
            return null;
          }
        }
      },
    ],
  });

  const feishuChatTransferHeaderDs = new DataSet({
    paging: false,
    fields: [
      {
        name: 'feishuTalkChatItemRange',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.transfer.service.item.rang', defaultMessage: '服务项范围' }),
        type: 'string',
        ignore: 'always',
        defaultValue: 'ALL',
        options: new DataSet({
          data: [
            {
              meaning: '部分',
              value: 'PART',
            },
            {
              meaning: '全部',
              value: 'ALL',
            }
          ],
        }),
      },
      {
        name: 'feishuTalkChatTransferTicketServiceItemField',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.transfer.service.item.field', defaultMessage: '聊天记录存储字段' }),
        type: 'object',
        lovCode: 'SC_ITEM_FIELD',
        valueField: 'id',
        textField: 'name',
        ignore: 'always',
        dynamicProps: {
          lovPara: ({ record }) => {
            return { itemId: record.get('feishuTalkChatTransferTicketServiceItem.id'), widgetType: 'Input,RichText,TextArea', allItemFlag: true };
          },
        },
        transformRequest: (value) => {
          if (value) {
            return {
              id: value.id,
              name: value.name,
              code: value.code,
            };
          } else {
            return null;
          }
        }
      },
      {
        name: 'feishuTalkChatTransferTicketPromptFlag',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.transfer.smart.fill', defaultMessage: '智能填充' }),
        type: 'boolean',
        ignore: 'always',
      },
      {
        name: 'feishuTalkChatTransferTicketPrompt',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.prompt.for.service.item', defaultMessage: 'Prompt模板' }),
        type: 'object',
        lovCode: 'PROMPT_TEMPLATE',
        ignore: 'always',
        computedProps: {
          required: ({ record }) => record.get('feishuTalkChatTransferTicketPromptFlag'),
        },
        transformRequest: (value) => {
          if (value) {
            return {
              id: value.id,
              name: value.name,
              code: value.code,
            };
          } else {
            return null;
          }
        }
      },
      // {
      //   name: 'feishuTalkChatTransferAiItemFlag',
      //   type: 'boolean',
      //   label: '智能推荐服务项',
      //   ignore: 'always',
      // },
      // {
      //   name: 'feishuTalkChatTransferAiPrompt',
      //   label: '智能推荐Prompt',
      //   type: 'object',
      //   lovCode: 'PROMPT_TEMPLATE',
      //   ignore: 'always',
      //   computedProps: {
      //     required: ({ record }) => record.get('feishuTalkChatTransferAiItemFlag'),
      //   },
      // },
    ],
    children: {
      feishuChatTalkTransferItem: feishuChatTransferDs,
    }
  });

  return {
    autoQuery: false,
    selection: false,
    autoLocateFirst: true,
    autoQueryAfterSubmit: false,
    pageSize: 20,
    transport: {
      read: ({ data: { id } }) => ({
        url: id ? `${url}/${id}` : url,
        method: 'get',
        transformResponse: (res) => {
          res = JSON.parse(res);
          if (typeof res.jsonConfig === 'string') {
            try {
              res.jsonConfig = JSON.parse(res.jsonConfig);
            } catch (e) {
              //
            }
          }
          if (res?.jsonConfig?.dingTalkChatTransferTicketServiceItem) {
            res.dingTalkChatTransferTicketServiceItem = res.jsonConfig.dingTalkChatTransferTicketServiceItem;
          }
          if (res?.jsonConfig?.dingTalkChatTransferTicketServiceItemField) {
            res.dingTalkChatTransferTicketServiceItemField = res.jsonConfig.dingTalkChatTransferTicketServiceItemField;
          }
          if (res?.jsonConfig?.dingTalkChatTransferTicketPrompt) {
            res.dingTalkChatTransferTicketPrompt = res.jsonConfig.dingTalkChatTransferTicketPrompt;
          }
          res = dingdingTodoTransform2Frontend(res);
          return res;
        },
      }),
      create: ({ data: [data] }) => {
        if (data?.config) {
          delete data.config;
        }
        const uri = type === 'site' ? 'iam/yqc/open_apps' : `iam/yqc/${organizationId}/open_apps`;
        // 需要todoConfig里的让数据结构符合后端需要
        data = dingdingTodoTransform2Backend(data);
        if (data.type === 'quick_authentication') {
          delete data.config;
        }
        return {
          url: uri,
          method: 'post',
          data,
        };
      },
      update: ({ data: [data] }) => {
        // 需要todoConfig里的让数据结构符合后端需要
        data = dingdingTodoTransform2Backend(data);
        if (data.type === 'quick_authentication') {
          delete data.config;
        }
        return ({
          url: `${url}`,
          method: 'put',
          data,
        });
      },
    },
    fields: [
      { name: 'icon', type: 'string', label: icon, defaultValue: 'http://x.x/x.png', required: true },
      { name: 'code',
        type: 'string',
        label: code,
        format: 'uppercase',
        dynamicProps: { required: ({ record }) => record.get('type') === 'other' },
        validator: (value, n, record) => {
          if (record.get('type') === 'other') {
            const tip = intl.formatMessage({
              id: 'validate.message.code',
            });
            return validateCode(value, tip);
          }
          return true;
        } },
      { name: 'name', isIntl: true, type: 'string', label: name, required: true },
      { name: 'autoCreateUserFlag', defaultValue: true },
      { name: 'anonymousCreatorFlag', defaultValue: true },
      { name: 'type', type: 'string', label: typeLabel, required: true, options: typeOptionDs },
      {
        name: 'quickType',
        type: 'string',
        valueField: 'code',
        textField: 'meaning',
        label: quickTypeLabel,
        dynamicProps: {
          required: () => !window.location.href.includes('detail'), // 在详情页非必填项，在创建页必填
        },
        lookupCode: 'QUICK_AUTHENTICATION_APPLICATION_TYPE',
        transformResponse: (value) => (value === '0' ? 'SDK_QUICK_AUTHENTICATION' : value),
      },
      { name: 'gateway',
        type: 'string',
        label: envUrlLabel,
        transformResponse: () => getEnv('API_HOST'),
      },
      { name: 'id', type: 'string', label: idLabel },
      { name: 'appId',
        type: 'string',
        label: appId,
        required: true,
        computedProps: {
          help: ({ record }) => (record.get('type') === 'ding_talk' ? 'clientId' : 'appId'),
        },
      },
      {
        name: 'appSecret',
        type: 'string',
        label: appSecret,
        dynamicProps: {
          required: ({ record }) => record.get('type') !== 'quick_authentication',
          help: ({ record }) => (record.get('type') === 'ding_talk' ? 'clientSecret' : 'appSecret'),
        },
      },
      { name: 'agentId',
        type: 'string',
        label: agentId,
        dynamicProps: {
          required: ({ record }) => record.get('type') === 'wechat_enterprise' || record.get('type') === 'ding_talk',
        },
      },
      {
        name: 'jsonConfig.openFieldMappingFlag',
        label: openFieldMappingFlag,
        type: 'boolean',
      },
      { name: 'dingTalkCorpId', type: 'string', label: 'CorpId', dynamicProps: { required: ({ record }) => record.get('type') === 'ding_talk' } },
      { name: 'authUrl', type: 'string', label: authUrl, dynamicProps: { required: ({ record }) => record.get('type') === 'other' } },
      { name: 'tokenUrl', type: 'string', label: tokenUrl, dynamicProps: { required: ({ record }) => record.get('type') === 'other' } },
      { name: 'userInfoUrl', type: 'string', label: userInfoUrl, dynamicProps: { required: ({ record }) => record.get('type') === 'other' } },
      { name: 'scope', type: 'string', label: scope, dynamicProps: { required: ({ record }) => record.get('type') === 'other' } },
      { name: 'uuidField', type: 'string', label: uuidField, dynamicProps: { required: ({ record }) => record.get('type') === 'other' } },
      { name: 'enabledLogin', type: 'boolean', label: enabledLogin, options: yesNoOptionDs, defaultValue: true },
      { name: 'dingTalkRobotFlag', type: 'boolean', label: dingTalkRobotFlag, dynamicProps: { required: ({ record }) => record.get('type') === 'ding_talk' }, options: yesNoOptionDs, defaultValue: false },
      { name: 'dingTalkRobotCode', type: 'string', label: dingTalkRobotCode, dynamicProps: { required: ({ record }) => record.get('type') === 'ding_talk' && record.get('dingTalkRobotFlag') }, defaultValue: '' },
      { name: 'realTimeSyncFlag', type: 'boolean', label: realTimeSyncFlag, defaultValue: false },
      {
        name: 'eventToken',
        type: 'string',
        dynamicProps: {
          label: ({ record }) => {
            if (record.get('type') === 'ding_talk') return intl.formatMessage({ id: 'iam.openLoginConfig.model.ding.event.token', defaultMessage: '回调Token' });
            else if (record.get('type') === 'lark') return intl.formatMessage({ id: 'iam.openLoginConfig.model.lark.event.token', defaultMessage: '订阅Token' });
          },
        },
      },
      { name: 'eventKey',
        type: 'string',
        dynamicProps: {
          label: ({ record }) => {
            if (record.get('type') === 'ding_talk') return intl.formatMessage({ id: 'iam.openLoginConfig.model.ding.event.aes.key', defaultMessage: '回调AesKey' });
            else if (record.get('type') === 'lark') return intl.formatMessage({ id: 'iam.openLoginConfig.model.lark.event.key', defaultMessage: '加密Key' });
          },
        },
      },
      { name: 'dingTalkCardId', type: 'string', label: dingTalkCardId, dynamicProps: { required: ({ record }) => record.get('type') === 'ding_talk' && record.get('dingTalkRobotFlag') }, defaultValue: '' },
      { name: 'enabledFlag', type: 'boolean', label: enabledFlag, required: true, options: enabledOptionDs, defaultValue: true },
      // 作为通知接受窗口
      {
        name: 'noticeFlag',
        type: 'boolean',
        label: notifyFlagLabel,
        defaultValue: false,
        transformResponse: (value) => (value === undefined ? false : value),
      },
      // 是否启用企微/钉助理
      {
        name: 'assistantFlag',
        type: 'boolean',
        defaultValue: false,
        transformResponse: (value) => (value === undefined ? false : value),
        dynamicProps: {
          label: ({ record }) => {
            return record.get('type') === 'ding_talk' ? enableDingTalkBotFlagLabel : enableWechatEnterpriseBotFlagLabel;
          },
        },
      },
      // 是否显示快捷进入企微的图标
      {
        name: 'socialContactRedirectFlag',
        dynamicProps: {
          help: ({ record }) => {
            return intl.formatMessage({ id: 'iam.openLoginConfig.model.social.contact.redirect.flag.info', defaultMessage: '启用后，与{app}绑定的人员在登录燕千云移动端后，在人员头像后及人员卡片上，会展示{app}图标和快捷拨打电话按钮' }).replaceAll('{app}', nameMap[record.get('type')]);
          },
        },
        type: 'boolean',
        transformResponse: (value) => (value === undefined ? true : value),
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.social.contact.redirect.flag', defaultMessage: '快捷联系图标' }),
      },
      // 企微智能助理
      {
        name: 'notifyBot',
        type: 'object',
        label: notifyBotLabel,
        lovCode: 'INTELLIGENT_BOT',
        help: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.notify.bot.help', defaultMessage: '选择智能助理后，当前助理将根据当前智能助理的后台配置，提供功能、运行会话流程' }),
        dynamicProps: {
          required: ({ record }) => (['wechat_enterprise', 'ding_talk'].includes(record.get('type'))) && record.get('assistantFlag'),
        },
        transformResponse: (value, record) => {
          if (record?.botId) {
            return { id: record.botId, name: record.botName };
          } else {
            return value;
          }
        },
      },
      { name: 'botId', type: 'string' },
      { name: 'botName', type: 'string' },
      // 钉助理的模板ID 需要从钉钉那边获取，是一个字符串
      {
        name: 'templateId',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.template.id', defaultMessage: '模版ID' }),
        dynamicProps: {
          required: ({ record }) => (record.get('type') === 'ding_talk' && record.get('assistantFlag')),
        },
      },
      // 钉钉机器人编码 也是需要从钉钉那边获取，也是一个字符串
      {
        name: 'dingRobotCode',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.ding.talk.robot.code', defaultMessage: '钉钉机器人编码' }),
        dynamicProps: {
          required: ({ record }) => (record.get('type') === 'ding_talk' && record.get('assistantFlag')),
        },
      },
      {
        name: 'notificationConfig',
        type: 'object',
        lovPara: {
          businessObjectName: '同步历史',
        },
        lovCode: 'NOTIFICATION_CONFIG',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.notification.template', defaultMessage: '通知模版' }),
        transformResponse: (value, record) => {
          if (record?.notificationConfigId) {
            return { id: record.notificationConfigId, name: record.notificationConfigName };
          } else {
            return value;
          }
        },
      },
      { name: 'notificationConfigId', type: 'string' },
      { name: 'notificationConfigName', type: 'string' },
      { name: 'noticeScope', type: 'object', options: noticeScopeOptions, label: intl.formatMessage({ id: 'iam.openLoginConfig.model.synchronization.result.notification', defaultMessage: '同步结果通知' }) },
      {
        name: 'config.passwordFlag',
        type: 'boolean',
        label: enabledPasswordFlag,
        options: yesNoOptionDs,
        defaultValue: false,
        // dynamicProps: {
        //   required: ({ record }) => {
        //     return typeList.includes(record.get('type'));
        //   },
        // },
      },
      {
        name: 'config.passwordField',
        type: 'password',
        label: password,
        dynamicProps: {
          required: ({ record }) => {
            return typeList.includes(record.get('type')) && record.get('config.passwordFlag');
          },
          validator: ({ record }) => {
            if (typeList.includes(record.get('type')) && record.get('config.passwordFlag')) {
              return validatePassword;
            }
          },
        },
      },
      {
        name: 'config.loginNameField',
        type: 'string',
        label: loginNameField,
      },
      { name: 'config.realNameField', type: 'string', label: realNameField },
      { name: 'config.emailField', type: 'string', label: emailField },
      {
        name: 'config.phoneField',
        type: 'string',
        validator: validatePolicy,
        label: phoneField,
      },
      {
        name: 'config',
        transformRequest: (value, data) => {
          if (data.get('type') === 'other') {
            return null;
          }
          return value;
        },
      },
      { name: 'config.jsonData', type: 'string' },
      {
        name: 'config.locationField',
        type: 'string',
        label: locationField,
      },
      { name: 'config.departmentField',
        type: 'string',
        label: departmentField,
      },
      {
        name: 'config.directorField',
        type: 'string',
        label: directorField,
      },
      {
        name: 'config.objectVersionNumber',
        type: 'string',
      },
      {
        name: 'config.timingFlag',
        type: 'boolean',
        label: timingFlag,
      },
      {
        name: 'config.startSyncTime',
        type: 'datetime',
        label: startSyncTime,
        dynamicProps: {
          required: ({ record }) => record.get('config.timingFlag'),
        },
      },
      {
        name: 'config.endSyncTime',
        type: 'datetime',
        label: endSyncTime,
      },
      {
        name: 'wechatAgentConfig.description',
        type: 'string',
        label: intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' }),
      },
      {
        name: 'wechatAgentConfig.agentCheckToken',
        type: 'string',
        label: 'Token',
        dynamicProps: {
          required: ({ record }) => record.get('type') === 'wechat_agent',
        },
        help: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.agent.token.tips', defaultMessage: '前往微信客服官网后台，“开发配置-开启企业内部接入”，随机获取Token和EncodingAESKey填入输入框内。' }),
      },
      {
        name: 'wechatAgentConfig.agentCheckKey',
        type: 'string',
        label: 'EncodingAESKey',
        dynamicProps: {
          required: ({ record }) => record.get('type') === 'wechat_agent',
        },
        help: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.agent.token.tips', defaultMessage: '前往微信客服官网后台，“开发配置-开启企业内部接入”，随机获取Token和EncodingAESKey填入输入框内。' }),
      },
      {
        name: 'wechatAgentConfig.agentCallbackUrl',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.agent.address', defaultMessage: '回调URL' }),
        help: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.agent.address.tips', defaultMessage: '复制此地址，前往微信客服官网后台，“开发配置-开启企业内部接入”，将此地址复制到填写回调URL处。' }),
        defaultValue: `${window._env_.API_HOST}/intelligent/v1/${tenantId}/callback`,
      },
      {
        name: 'wechatAgentConfig.agentName',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.agent.name', defaultMessage: '账号名称' }),
        dynamicProps: {
          required: ({ record }) => record.get('type') === 'wechat_agent',
        },
        defaultValue: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.wechat.agent', defaultMessage: '微信客服' }),
      },
      {
        name: 'wechatAgentConfig.agentAvatar',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.agent.avatar', defaultMessage: '头像' }),
      },
      {
        name: 'wechatAgentConfig.agentWelcomeMsg',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.agent.welcome.msg', defaultMessage: '客服欢迎语' }),
        help: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.agent.welcome.msg.tips', defaultMessage: '微信用户与客服创建会话后，系统发送此说辞作为欢迎语' }),
        defaultValue: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.agent.welcome.msg.default.value', defaultMessage: '您好，很高兴为您服务' }),
      },
      {
        name: 'wechatAgentConfig.agentMenuBootMsg',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.agent.menu.boot.msg', defaultMessage: '菜单消息引导语' }),
        help: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.agent.menu.boot.msg.tips', defaultMessage: '微信用户与客服创建会话后，系统发送选择客服组菜单消息的引导语' }),
        defaultValue: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.agent.menu.boot.msg.default.value', defaultMessage: '您好，请根据你选择的业务类型选择一个客服组，为你提供专属服务' }),
      },
      {
        name: 'wechatAgentConfig.agentOfflineMsg',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.agent.offline.msg', defaultMessage: '客服不在线提示语' }),
        help: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.agent.offline.msg.tips', defaultMessage: '微信用户与客服创建会话后，用户选择的客服组无客服在线时，系统将发送此说辞' }),
        defaultValue: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.agent.offline.msg.default.value', defaultMessage: '当前客服组无在线客服，人工服务时间：周一到周日8:00-18:00，请稍后咨询' }),
      },
      {
        name: 'wechatAgentConfig.agentResolveMsg',
        type: 'string',
        label: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.agent.resolve.msg', defaultMessage: '客服结束语' }),
        help: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.agent.resolve.msg.tips', defaultMessage: '微信用户与客服结束会话后，系统发送此说辞作为结束语' }),
        defaultValue: intl.formatMessage({ id: 'iam.openLoginConfig.model.wechat.agent.resolve.msg.default.value', defaultMessage: '服务结束，欢迎再次咨询' }),
      },
      {
        name: 'config.frequency',
        lookupCode: 'SYNC_JOB_INTERVAL',
        type: 'string',
        label: frequency,
        // defaultValue: '1',
        dynamicProps: {
          required: ({ record }) => record.get('config.timingFlag'),
        },
      },
      { name: 'config.defaultRoleId', label: '角色', type: 'object', lovCode: 'ROLE', multiple: true, textField: 'name', valueField: 'id' },
      {
        name: 'jsonConfig',
        type: 'object',
        transformRequest: (_value, record) => {
          let value = _value;
          try {
            while (typeof value !== 'object') {
              value = JSON.parse(value);
            }
          } catch (e) {
            value = {};
          }
          return JSON.stringify({
            ...value,
            userInfoInterface: {
              interfaceName: value.userInfoInterface?.interfaceName,
              interfaceCode: value.userInfoInterface?.interfaceCode,
              serverCode: value.userInfoInterface?.serverCode,
            },
            dingTalkChatTransfers: record?.getCascadeRecords?.('jsonConfig.dingTalkChatTransfers')?.map?.(r => r.toData()),
            feishuTalkChatTransfers: record?.getCascadeRecords?.('jsonConfig.feishuTalkChatTransfers')?.map?.(r => r.toData()),
          });
        },
        transformResponse: (value, record) => {
          try {
            if (value && typeof value === 'object') {
              return value;
            }
            let realValue = value;
            if (typeof realValue !== 'object') {
              try {
                realValue = JSON.parse(realValue);
              } catch (e) {
                //
              }
            }
            return realValue;
          } catch (e) {
            return {};
          }
        },
      },
      {
        name: 'jsonConfig.userInfoInterface',
        type: 'object',
        lovCode: 'INTERFACE',
        label: userInfoInterface,
        textField: 'interfaceName',
        valueField: 'interfaceCode',
      },
      {
        name: 'jsonConfig.description',
        type: 'string',
        label: description,
      },
      {
        name: 'jsonConfig.name',
        type: 'string',
        label: name,
      },
      {
        name: 'jsonConfig.displayName',
        type: 'string',
        label: displayName,
        dynamicProps: {
          required: ({ record }) => record.get('type') === 'quick_authentication' && record.get('quickType') !== 'CLIENT',
        },
      },
      {
        name: 'jsonConfig.icon',
        type: 'string',
        label: iconLabel,
        dynamicProps: {
          required: ({ record }) => record.get('type') === 'quick_authentication' && record.get('quickType') !== 'CLIENT',
        },
      },
      {
        name: 'jsonConfig.orgCode',
        type: 'string',
        label: orgCodeLabel,
        format: 'uppercase',
        validator: async (value, fieldName, record) => {
          if (record.get(fieldName) === record.getPristineValue(fieldName)) {
            return true;
          }
          return true;
        },
        dynamicProps: {
          required: ({ record }) => record.get('type') === 'choerodon',
        },
      },
      {
        name: 'jsonConfig.envUrl',
        type: 'string',
        label: envUrlLabel,
        validator: (value, filedName, record) => {
          if (record.get('type') === 'choerodon') {
            return validateUrl(value);
          }
          return true;
        },
        dynamicProps: {
          required: ({ record }) => record.get('type') === 'choerodon',
        },
      },
      {
        name: 'jsonConfig.domainUrl',
        type: 'string',
        label: domainUrlLabel,
        validator: (value, filedName, record) => {
          if (record.get('type') === 'choerodon') {
            return validateUrl(value);
          }
          return true;
        },
        dynamicProps: {
          required: ({ record }) => record.get('type') === 'choerodon',
        },
      },
      // 猪齿鱼单据字段映射配置
      {
        name: 'jsonConfig.fieldMappingConfig',
        type: 'string',
        label: fieldMappingConfigLabel,
      },
      // 免密登录
      {
        name: 'freeLoginFlag',
        label: loginWithoutPassword,
        type: 'boolean',
      },
      // 回复同步猪齿鱼
      {
        name: 'jsonConfig.syncC7nJournalFlag',
        type: 'boolean',
        label: syncC7nJournalFlagLabel,
        options: yesNoOptionDs,
        defaultValue: false,
      },
      {
        name: 'bindConfig.frequency',
        type: 'object',
        lookupCode: 'SYNC_JOB_INTERVAL',
        transformRequest: (value) => {
          return value?.code || value;
        },
      },
      {
        name: 'jsonConfig.iconSize',
        type: 'string',
        label: iconSizeLabel,
        options: new DataSet({
          paging: false,
          data: [
            { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.options.icon.size.small', defaultMessage: '小尺寸' }), value: 'SMALL_SIZE' },
            { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.options.icon.size.large', defaultMessage: '大尺寸' }), value: 'LARGE_SIZE' },
          ],
        }),
        defaultValue: 'SMALL_SIZE',
        transformResponse: (value) => (value || 'SMALL_SIZE'),
        help: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.help.icon.size', defaultMessage: '小尺寸图标显示为24*24大小，大尺寸显示为40*40大小。小尺寸模式下可调节切角的方式。若您的图标需要显示为方形，则可选择方角切角方式。' }),
      },
      {
        name: 'jsonConfig.iconCorner',
        type: 'string',
        label: iconCornerLabel,
        options: new DataSet({
          paging: false,
          data: [
            { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.options.icon.corner.square', defaultMessage: '方角' }), value: 'SQUARE_CORNER' },
            { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.options.icon.corner.round', defaultMessage: '圆角' }), value: 'ROUND_CORNER' },
          ],
        }),
        defaultValue: 'SQUARE_CORNER',
        transformResponse: (value) => (value || 'SQUARE_CORNER'),
      },
      {
        name: 'jsonConfig.iconX',
        type: 'number',
        label: 'X',
        defaultValue: 30,
        min: 0,
      },
      {
        name: 'jsonConfig.iconY',
        type: 'number',
        label: 'Y',
        defaultValue: 30,
        min: 0,
      },
      // microsoft teams
      { name: 'corpId', type: 'string', label: corpId, dynamicProps: { required: ({ record }) => record.get('type') === 'microsoft' } },
    ],
    children: {
      'jsonConfig.openFunctions': openFunctionDataSet,
      'jsonConfig.openClientFunctions': openClientFunctionDataSet,
      'jsonConfig.notify': notifyDataSet,
      'jsonConfig.dingTalkChatTransfers': dingTalkChatTransferDs,
      'jsonConfig.feishuTalkChatTransfers': feishuChatTransferHeaderDs,
      todoConfig: todoConfigDataSet,
    },
    queryFields: [
      { name: 'search_name', type: 'string', label: name },
      { name: 'enabledFlag', type: 'boolean', label: enabledFlag, options: enabledOptionDs },
    ],
    events: {
      async indexChange({ record }) {
        if (record && record.get('id') && !['quick_authentication', 'choerodon'].includes(record.get('type'))) {
          try {
            const res = await axios.get(`${url}/${record.get('id')}`);
            if (res.failed) {
              throw new Error(res.message);
            } else {
              record.init(res);
            }
          } catch (err) {
            message.error(err.message);
          }
        }
      },
    },
  };
};
