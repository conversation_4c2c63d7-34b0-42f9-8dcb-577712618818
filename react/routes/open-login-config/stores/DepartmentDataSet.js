import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

const appTypeDefaultValueMap = (intl, appType) => {
  const data = {
    wechat_enterprise: [
      {
        departmentInfo: {
          fieldCode: 'name',
          fieldName: intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' }),
        },
        openName: intl.formatMessage({ id: 'iam.openLoginConfig.model.account.department.name', defaultMessage: '部门名称' }),
        openCode: 'name',
        updateFlag: true,
      },
      {
        departmentInfo: {
          fieldCode: 'parent_id',
          fieldName: intl.formatMessage({ id: 'iam.openLoginConfig.model.department.higher.office', defaultMessage: '上级部门' }),
        },
        openName: `${intl.formatMessage({ id: 'iam.openLoginConfig.model.department.parent', defaultMessage: '父部门' })}ID`,
        openCode: 'parentid',
        updateFlag: true,
      },
      {
        departmentInfo: {
          fieldCode: 'code',
          fieldName: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }),
        },
        openName: intl.formatMessage({ id: 'iam.openLoginConfig.model.account.department.id', defaultMessage: '部门ID' }),
        openCode: 'id',
        updateFlag: true,
      },
      {
        departmentInfo: {
          fieldCode: 'manager_id',
          fieldName: intl.formatMessage({ id: 'iam.openLoginConfig.model.account.department.manager', defaultMessage: '部门负责人' }),
        },
        openName: intl.formatMessage({ id: 'iam.openLoginConfig.model.account.department.manager', defaultMessage: '部门负责人' }),
        openCode: 'department_leader',
        updateFlag: true,
      },
    ],
    ding_talk: [
      {
        departmentInfo: {
          fieldCode: 'name',
          fieldName: intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' }),
        },
        openName: intl.formatMessage({ id: 'iam.openLoginConfig.model.account.department.name', defaultMessage: '部门名称' }),
        openCode: 'name',
        updateFlag: true,
      },
      {
        departmentInfo: {
          fieldCode: 'code',
          fieldName: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }),
        },
        openName: intl.formatMessage({ id: 'iam.openLoginConfig.model.account.department.id', defaultMessage: '部门ID' }),
        openCode: 'dept_id',
        updateFlag: true,
      },
      {
        departmentInfo: {
          fieldCode: 'parent_id',
          fieldName: intl.formatMessage({ id: 'iam.openLoginConfig.model.department.higher.office', defaultMessage: '上级部门' }),
        },
        openName: `${intl.formatMessage({ id: 'iam.openLoginConfig.model.department.parent', defaultMessage: '父部门' })}ID`,
        openCode: 'parent_id',
        updateFlag: true,
      },
      {
        departmentInfo: {
          fieldCode: 'manager_id',
          fieldName: intl.formatMessage({ id: 'iam.openLoginConfig.model.account.department.manager', defaultMessage: '部门负责人' }),
        },
        openName: intl.formatMessage({ id: 'iam.openLoginConfig.model.account.department.manager', defaultMessage: '部门负责人' }),
        openCode: 'dept_manager_userid_list',
        updateFlag: true,
      },
    ],
    lark: [
      {
        departmentInfo: {
          fieldCode: 'name',
          fieldName: intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' }),
        },
        openName: intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' }),
        openCode: 'name',
        updateFlag: true,
      },
      {
        departmentInfo: {
          fieldCode: 'manager_id',
          fieldName: intl.formatMessage({ id: 'iam.openLoginConfig.model.department.info.manager', defaultMessage: '管理者' }),
        },
        openName: intl.formatMessage({ id: 'iam.openLoginConfig.model.department.info.leader', defaultMessage: '部门主管' }),
        openCode: 'leader_user_id',
        updateFlag: true,
      },
      {
        departmentInfo: {
          fieldCode: 'parent_id',
          fieldName: intl.formatMessage({ id: 'iam.openLoginConfig.model.department.higher.office', defaultMessage: '上级部门' }),
        },
        openName: intl.formatMessage({ id: 'iam.openLoginConfig.model.department.parent', defaultMessage: '父部门' }),
        openCode: 'parent_department_id',
        updateFlag: true,
      },
      {
        departmentInfo: {
          fieldCode: 'code',
          fieldName: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }),
        },
        openName: intl.formatMessage({ id: 'iam.openLoginConfig.model.account.department.id', defaultMessage: '部门ID' }),
        openCode: 'open_department_id',
        updateFlag: true,
      },
    ],
  };
  return data[appType];
};
/**
 *
 * @param keepValueMapping
 * @param openCodeMapping
 * @returns DataSetProps
 */
export default ({ keepValueMapping, openCodeMapping, intl, intlPrefix, appType }) => ({
  autoQuery: false,
  autoLocateFirst: true,
  data: appTypeDefaultValueMap(intl, appType),
  fields: [
    {
      name: 'departmentInfo',
      type: 'object',
      lovCode: 'OBJECT_FIELD',
      textField: 'fieldName',
      lovPara: { object_code: 'ORG_DEPARTMENT' },
    },
    {
      name: 'openName',
      type: 'string',
      dynamicProps: {
        lookupCode: ({ dataSet }) => openCodeMapping.department[dataSet.getState('appType')],
        required: ({ record }) => keepValueMapping.department.includes(record.get('departmentInfo.fieldCode')),
      },
    },
    { name: 'openCode', type: 'string' },
  ],
  events: {
    load: ({ dataSet }) => {
      if (dataSet.length === 0 && Object.keys(appTypeDefaultValueMap).includes(dataSet.getState('appType'))) {
        dataSet.loadData(appTypeDefaultValueMap[dataSet.getState('appType')]);
      }
    },
  },
});
