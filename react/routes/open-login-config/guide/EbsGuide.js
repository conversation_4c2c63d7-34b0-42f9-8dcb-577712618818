import React from 'react';
import { observer } from 'mobx-react-lite';

const EbsGuide = () => {
  return (
    <iframe
      style={{ width: '100%', height: '100%', border: 'none', overflow: 'hidden' }}
      title="ebs"
      src="https://support.yqcloud.com/#/knowledge/share/support/ab12a1b5bd6c4bb59464c4d47e93439d?portal=true&solutionId=358734080981991424&tenantId=228549383619211264&menu=knowledge&fileKey=228549383619211264%2Fd39e63b43b154e518751291566f9e71f%40EBS%E5%AE%A2%E6%88%B7%E7%AB%AF%E5%AE%89%E8%A3%85%E8%AF%B4%E6%98%8E.docx&knowledgeId=445904601248141312"
    />
  );
};

export default observer(EbsGuide);
