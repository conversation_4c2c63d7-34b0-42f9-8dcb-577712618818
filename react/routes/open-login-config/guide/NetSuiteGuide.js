import React from 'react';
import { observer } from 'mobx-react-lite';

const EbsGuide = () => {
  return (
    <>
      <iframe
        style={{ width: '100%', height: '100%', border: 'none', overflow: 'hidden' }}
        title="ebs"
        src="https://support.yqcloud.com/#/knowledge/share/support/8178271b1344d9385a856eb2a56b6116?knowledgeId=536234902691381248&menu=knowledge&portal=true&solutionId=358734080981991424&tenantId=228549383619211264&spaceId=324937068922798080&sourceModule=KNOWLEDGE_CENTER&sourceFunction=OUTSIDE_LINK"
      />
      <iframe
        style={{ width: '100%', height: '100%', border: 'none', overflow: 'hidden' }}
        title="ebs"
        src="https://support.yqcloud.com/#/knowledge/share/support/2ee5b3b5b57552659c82a68c8ca0f5b8?knowledgeId=536234380987072512&menu=knowledge&portal=true&solutionId=358734080981991424&tenantId=228549383619211264&spaceId=324937068922798080&sourceModule=KNOWLEDGE_CENTER&sourceFunction=OUTSIDE_LINK"
      />
    </>
  );
};

export default observer(EbsGuide);
