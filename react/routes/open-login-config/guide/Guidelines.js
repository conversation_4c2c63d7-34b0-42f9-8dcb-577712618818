import React from 'react';
import copy from 'copy-to-clipboard';
import { message } from 'choerodon-ui/pro';
import { getEnv } from '@zknow/utils';
import { Icon } from '@zknow/components';

export default function ({ intl, prefixCls, dataSet }) {
  const record = dataSet?.current;
  const url = `${getEnv('API_HOST')}/iam/yqc/open_apps/quick/${record?.get('appId')}/feedback.js`;

  function copyText(text) {
    copy(text);
    message.success(intl.formatMessage({ id: 'zknow.common.success.copy', defaultMessage: '复制成功' }));
  }

  const indentation = '10px';

  return (
    <div className={`${prefixCls}-guidelines`}>
      <div className={`${prefixCls}-guidelines-title`}>
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.1', defaultMessage: '引入SDK' })}
      </div>
      <div className={`${prefixCls}-guidelines-description`}>
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.2', defaultMessage: '1.使用在线链接或通过链接下载文件到前端工程中' })}
      </div>
      <pre>
        <Icon
          type="Copy"
          className={`${prefixCls}-guidelines-copy`}
          onClick={() => copyText(url)}
        />
        {url}
      </pre>
      <div className={`${prefixCls}-guidelines-description`}>
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.3', defaultMessage: '2.在主页中引入 SDK' })}
      </div>
      <pre>
        <Icon
          type="Copy"
          className={`${prefixCls}-guidelines-copy`}
          onClick={() => copyText(`<!DOCTYPE html>
  <html>
  <head>
    <meta charset="utf-8" />
    <title>YQCloud</title>
  </head>
  <body>
    <script src="${url}"></script>
  </body>
</html>`)}
        />
        {`<!DOCTYPE html>
  <html>
  <head>
    <meta charset="utf-8" />
    <title>YQCloud</title>
  </head>
  <body>
    <script src="${url}"></script>
  </body>
</html>`}
      </pre>
      <div className={`${prefixCls}-guidelines-title`} style={{ height: '30px' }}>
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.4', defaultMessage: '3. 传递用户信息。该步骤有两种方法，可结合实际情况进行选择。' })}
      </div>
      <div className={`${prefixCls}-guidelines-title`}>
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.5', defaultMessage: '方法一：使用密钥加密传递用户信息' })}
      </div>

      <div className={`${prefixCls}-guidelines-description`} style={{ textIndent: indentation }}>
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.6', defaultMessage: '3.1 第三方服务后端构造如下 JSON 数据，JSON 中数据按照实际用户信息生成' })}

      </div>
      <pre>
        <Icon
          type="Copy"
          className={`${prefixCls}-guidelines-copy`}
          onClick={() => copyText(`{
  "loginName": "feedback", // ${intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.tips.1', defaultMessage: '登录名，作为唯一标识，保证每个租户不同，最多 128 位' })}
  "email": "<EMAIL>", // ${intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.tips.2', defaultMessage: '邮箱，用户邮箱用于后续邮件通知等功能' })}
  "realName": "${intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.tips.6', defaultMessage: '快捷提单用户' })}" // ${intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.tips.4', defaultMessage: '真实名称，于燕千云系统上显示的名称' })}
}`)}
        />
        {`{
  "loginName": "feedback", // ${intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.tips.1', defaultMessage: '登录名，作为唯一标识，保证每个租户不同，最多 128 位' })}
  "email": "<EMAIL>", // ${intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.tips.2', defaultMessage: '邮箱，用户邮箱用于后续邮件通知等功能' })}
  "realName": "${intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.tips.6', defaultMessage: '快捷提单用户' })}" // ${intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.tips.4', defaultMessage: '真实名称，于燕千云系统上显示的名称' })}
}`}
      </pre>
      <div className={`${prefixCls}-guidelines-description`} style={{ textIndent: indentation }}>
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.7', defaultMessage: '3.2 燕千云系统复制快捷提单的用户密钥，并使用 128 位 ECB 模式 PKCS7-PKCS5 填充的 AES 算法进行加密后使用 Base64 编码返回，示例 JAVA 代码如下：' })}

      </div>
      <pre>
        <Icon
          type="Copy"
          className={`${prefixCls}-guidelines-copy`}
          onClick={() => copyText(`public static String encrypt(String content, String secretKey) {
  SecretKeySpec key = new SecretKeySpec(secretKey.getBytes("UTF-8"), "AES");
  try {
    Cipher cipher = Cipher.getInstance("AES");
    cipher.init(1, key);
    return Base64.getEncoder().encodeToString(cipher.doFinal(content.getBytes("UTF-8")));
  } catch (Exception var4) {
    throw new EncryptionException("AES encrypt failed!", var4);
  }
}`)}
        />
        {`public static String encrypt(String content, String secretKey) {
  SecretKeySpec key = new SecretKeySpec(secretKey.getBytes("UTF-8"), "AES");
  try {
    Cipher cipher = Cipher.getInstance("AES");
    cipher.init(1, key);
    return Base64.getEncoder().encodeToString(cipher.doFinal(content.getBytes("UTF-8")));
  } catch (Exception var4) {
    throw new EncryptionException("AES encrypt failed!", var4);
  }
}`}
      </pre>
      <div className={`${prefixCls}-guidelines-description`} style={{ textIndent: indentation }}>
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.8', defaultMessage: '3.3 将生成的 Secret 查询给前端，前端保存到 Session 中，供 SDK 使用' })}

      </div>
      <pre>
        <Icon
          type="Copy"
          className={`${prefixCls}-guidelines-copy`}
          onClick={() => copyText('sessionStorage.setItem("YQ_USER_SECRET", secret);')}
        />
        sessionStorage.setItem(&quot;YQ_USER_SECRET&quot;, secret);
      </pre>
      <div className={`${prefixCls}-guidelines-title`}>
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.9', defaultMessage: '方法二：使用后端接口传递用户信息' })}

      </div>
      <div className={`${prefixCls}-guidelines-description`} style={{ textIndent: indentation }}>
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.10', defaultMessage: '3.1 第三方服务后端实现一个接口接收 url 查询参数 code 返回如下 JSON 数据' })}

      </div>
      <pre>
        <Icon
          type="Copy"
          className={`${prefixCls}-guidelines-copy`}
          onClick={() => copyText(`{
  "loginName": "feedback", // ${intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.tips.1', defaultMessage: '登录名，作为唯一标识，保证每个租户不同，最多 128 位' })}
  "phone": "18888888888", // ${intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.tips.5', defaultMessage: '手机号，根据登入名称未找到时会根据手机号查找用户' })}
  "email": "<EMAIL>", // ${intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.tips.3', defaultMessage: '邮箱，根据手机号未找到用户，会根据邮箱查询，用户邮箱用于后续邮件通知等功能' })}
  "realName": "${intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.tips.6', defaultMessage: '快捷提单用户' })}" // ${intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.tips.4', defaultMessage: '真实名称，于燕千云系统上显示的名称' })}
}`)}
        />
        {`{
  "loginName": "feedback", // ${intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.tips.1', defaultMessage: '登录名，作为唯一标识，保证每个租户不同，最多 128 位' })}
  "email": "<EMAIL>", // ${intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.tips.2', defaultMessage: '邮箱，用户邮箱用于后续邮件通知等功能' })}
  "realName": "${intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.tips.6', defaultMessage: '快捷提单用户' })}" // ${intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.tips.4', defaultMessage: '真实名称，于燕千云系统上显示的名称' })}
}`}
      </pre>
      <div className={`${prefixCls}-guidelines-description`} style={{ textIndent: indentation }}>
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.11', defaultMessage: '3.2 使用此接口地址到燕千云系统接口注册功能中注册一个接口并发布（如果第三方系统接口实现和第一步不同可以在此步骤进行响应映射）' })}

      </div>
      <div className={`${prefixCls}-guidelines-description`} style={{ textIndent: indentation }}>
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.12', defaultMessage: '3.3 在燕千云系统快捷提单设置中配置用户信息接口为前面创建的接口' })}

      </div>
      <div className={`${prefixCls}-guidelines-description`} style={{ textIndent: indentation }}>
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.13', defaultMessage: '3.4 将生成的 Secret 查询给前端，前端保存到 Session 中，供 SDK 使用' })}

      </div>
      <pre>
        <Icon
          type="Copy"
          className={`${prefixCls}-guidelines-copy`}
          onClick={() => copyText('sessionStorage.setItem("YQ_USER_CODE", code);')}
        />
        sessionStorage.setItem(&quot;YQ_USER_CODE&quot;, code);
      </pre>
      <div className={`${prefixCls}-guidelines-description`}>
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.14', defaultMessage: '另附：使用服务项创建单据时传递自定义默认值给提单页' })}

      </div>
      <div className={`${prefixCls}-guidelines-description`}>
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.15', defaultMessage: 'customFieldData就是自定义传递给单据的数据，可以将系统报错，系统url通过这种方式传递给单据，其中";short_description";是服务项上的字段名，";测试"; 是数据值' })}

      </div>
      <pre>
        <Icon
          type="Copy"
          className={`${prefixCls}-guidelines-copy`}
          onClick={() => copyText('sessionStorage.setItem("YQ_USER_CODE", code);')}
        />
        {`const customFieldData = {
  short_description: "${intl.formatMessage({ id: 'iam.openLoginConfig.desc.sdk.guidelines.tips.7', defaultMessage: '测试' })}",
};
sessionStorage.setItem("YQ_FIELD_DATA", encodeURIComponent(JSON.stringify(customFieldData)));`}
      </pre>
    </div>
  );
}
