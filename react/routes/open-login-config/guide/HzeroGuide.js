import React from 'react';
import copy from 'copy-to-clipboard';
import { message } from 'choerodon-ui/pro';
import { getEnv } from '@zknow/utils';
import { Icon } from '@zknow/components';

export default function ({ intl, prefixCls, dataSet }) {
  const record = dataSet?.current;
  const url = `${getEnv('API_HOST')}/iam/yqc/open_apps/quick/${record?.get('appId')}/feedback.js`;
  const pluginCode = `import React, {useEffect} from 'react';
import Yqcloud from 'hzerojs-plugin-yqcloud-jssdk';

const customSuspensionComponent = (style) => {
  // tokenPath 为hzero插件接口
  // apiHost 为燕千云对应环境的网关
  // token 为燕千云对应环境中三方应用的token
  // 以上三个参数均已根据此应用自动生成,正常情况无需修改
  const map = new Map([
    [
      'HZERO.ZKNOW',
      <Yqcloud tokenPath="/iam/v1/zknow/encript/quick" apiHost="${getEnv('API_HOST')}" token="${record?.get('appId')}"></Yqcloud>,
    ],
  ]);
  return map;
};
// 初始化
export const appInit = () => {

};

export default { customSuspensionComponent }`;
  const backendPluginCode = `
<!--添加依赖-->
<dependency>
    <groupId>com.zknow.starter</groupId>
    <artifactId>zknow-starter-sdk-hzero</artifactId>
    <version>0.10.0-SNAPSHOT</version>
</dependency>
<!--添加仓库地址-->
<repository>
    <id>YQCloudGroup</id>
    <url>http://nexus.saas.hand-china.com/repository/yqcloud-group</url>
    <releases>
        <enabled>true</enabled>
    </releases>
    <snapshots>
        <enabled>true</enabled>
    </snapshots>
</repository>
`;
  function copyText(text) {
    copy(text);
    message.success(intl.formatMessage({ id: 'zknow.common.success.copy', defaultMessage: '复制成功' }));
  }

  return (
    <div className={`${prefixCls}-guidelines`}>
      <div className={`${prefixCls}-guidelines-title`}>
        安装前端插件
      </div>
      <div className={`${prefixCls}-guidelines-description`}>
        1.使用npm下载燕千云hzero插件
      </div>
      <pre>
        <Icon
          type="Copy"
          className={`${prefixCls}-guidelines-copy`}
          onClick={() => copyText(url)}
        />
        npm install hzerojs-plugin-yqcloud-jssdk
      </pre>
      <div className={`${prefixCls}-guidelines-description`}>
        2.在app.tsx中引入插件
      </div>
      <pre>
        <Icon
          type="Copy"
          className={`${prefixCls}-guidelines-copy`}
          onClick={() => copyText(pluginCode)}
        />
        {pluginCode}
      </pre>
      <div className={`${prefixCls}-guidelines-description`}>
        3.前往hzero快捷入口配置菜单新建编码为 HZERO.ZKNOW 的快捷入口
      </div>
      <div className={`${prefixCls}-guidelines-title`}>
        后端
      </div>
      <div className={`${prefixCls}-guidelines-description`}>
        1.在后端服务（默认IAM服务）pom.xml中引入插件 (如果要在其他服务中引入，需要调整前端配置参数`tokenPath`)
      </div>
      <pre>
        <Icon
          type="Copy"
          className={`${prefixCls}-guidelines-copy`}
          onClick={() => copyText(backendPluginCode)}
        />
        {backendPluginCode}
      </pre>
      <div className={`${prefixCls}-guidelines-description`}>
        2.后端配置参数

      </div>
      <div className={`${prefixCls}-guidelines-description`}>
        `zknow.encrypt.prefix`：<font color="red">访问燕千云用户前缀，由于一个燕千云租户可能对应了多个OP本地版本的客户提单， 所以需要配置前缀来区分不同的客户，防止有多个本地OP的相同登入账号水平越权</font>
      </div>
      <div className={`${prefixCls}-guidelines-description`}>
        `zknow.encrypt.secretKey`: 配置在燕千云三方中拿到的人员信息密钥
      </div>

    </div>
  );
}
