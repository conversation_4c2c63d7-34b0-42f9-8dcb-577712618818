import React, { useState, useRef, useEffect } from 'react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import axios from 'axios';
import { inject } from 'mobx-react';
import { Timeline } from 'choerodon-ui';
import { observer } from 'mobx-react-lite';
import useInterval from '../../../../components/use-interval';

const STATUS_COLOR_MAP = {
  FINISHED: 'green',
  WAIT: 'blue',
  RUNNING: 'blue',
  WARNING: 'yellow',
  ERROR: 'red',
};
function ConfigCheckModal({ openAppId, tenantId, intl, type, infoDetailDataSet }) {
  const instanceIdRef = useRef();
  const [checkInfo, setCheckInfo] = useState({});
  const isRunning = checkInfo.status === 'RUNNING';
  async function startCheck() {
    if (type === 'choerodon') { // 猪齿鱼测试连通性
      const data = infoDetailDataSet?.current?.toData();
      data.todoConfig = undefined;
      try {
        const res = await axios.post(`/iam/v1/${tenantId}/choerodon_user/check_connect`, data);
        if (res?.failed) {
          setCheckInfo({
            message: res.message,
            success: false,
          });
        }
        if (res === true) {
          setCheckInfo({
            message: intl.formatMessage({ id: 'iam.openLoginConfig.desc.c7n.mapping.check.success', defaultMessage: '检查成功' }),
            success: true,
          });
        }
      } catch (err) {
        setCheckInfo(err);
      }
    } else {
      const res = await axios.post(`/ecos/v1/${tenantId}/openApps/${openAppId}/health`);
      instanceIdRef.current = res.id;
      await fetchCheckInfo();
    }
  }
  async function fetchCheckInfo() {
    const checkResult = await axios.get(`/ecos/v1/${tenantId}/openApps/health/${instanceIdRef.current}`);
    setCheckInfo(checkResult);
  }
  useEffect(() => {
    startCheck();
  }, []);
  useInterval(() => {
    checkInfo.id && checkInfo.status !== 'FINISHED' && fetchCheckInfo();
  }, checkInfo.id && checkInfo.status !== 'FINISHED' && 1000);

  if (type === 'choerodon') {
    return (
      <Timeline pending={isRunning && intl.formatMessage({ id: 'iam.openLoginConfig.desc.config.checking', defaultMessage: '配置检查中' })}>
        <Timeline.Item color="green">
          {intl.formatMessage({ id: 'iam.openLoginConfig.desc.c7n.mapping.start.check', defaultMessage: '开始检查' })}
        </Timeline.Item>
        <Timeline.Item color={checkInfo.success ? 'green' : 'red'}>
          {checkInfo.message}
        </Timeline.Item>
      </Timeline>
    );
  }

  return (
    <div>
      <Timeline pending={isRunning && intl.formatMessage({ id: 'iam.openLoginConfig.desc.config.checking', defaultMessage: '配置检查中' })}>
        {checkInfo.tasks?.map((task) => {
          return (
            <Timeline.Item color={STATUS_COLOR_MAP[task.status]}>
              <div>{task.name}-{intl.formatMessage({ id: `iam.openLoginConfig.desc.config.task.status.${task.status}` })}</div>
              {task.resultMsg && <div>{task.resultMsg}</div>}
            </Timeline.Item>
          );
        })}
        {!isRunning && instanceIdRef.current && <Timeline.Item color="green">{intl.formatMessage({ id: 'iam.openLoginConfig.desc.check.finished', defaultMessage: '检查完成' })}</Timeline.Item>}
      </Timeline>

    </div>
  );
}

export default inject('AppState')(formatterCollections({ code: 'iam.openLoginConfig' })(injectIntl(observer(ConfigCheckModal))));
