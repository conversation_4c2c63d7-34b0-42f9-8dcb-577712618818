@import '~choerodon-ui/lib/style/themes/default';

.notificationLink {
  flex-shrink: 0;
  margin: 0 10px;
  display: flex;
  align-items: center;
  cursor: pointer;
  color: @primary-color;
}

.optionTag {
  :global {
    .yq-cmp-status-tag {
      line-height: 14px;
      margin-left: 8px;
    }
  }
}

.switchWithLink {
  display: flex;
  align-items: center;

  .link {
    color: @primary-color;
    margin-left: 10px;
    cursor: pointer;
  }
}

.sectionHeader {
  display: inline-flex;
  align-items: center;

  .sectionHeaderIcon {
    margin-left: 2px;
    color: rgba(18, 39, 77, 0.85);
  }
}

.realtimeSync {
  display: flex;
  align-items: center;

  &Alert {
    margin-left: 8px;
  }
}
