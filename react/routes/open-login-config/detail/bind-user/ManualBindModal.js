import React, { useState, useRef, useEffect } from 'react';
import { injectIntl } from 'react-intl';
import { Icon as YQIcon, Button, ExternalComponent, FileUploader, YqAvatar } from '@zknow/components';
import { getAccessToken, getEnv } from '@zknow/utils';
import { axios } from '@yqcloud/apps-master';
import { Timeline } from 'choerodon-ui';
import { DataSet, Form, SelectBox, CheckBox, TextField, message, Output, Lov } from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';
import copy from 'copy-to-clipboard';
import useInterval from '../../../../components/use-interval';

function ConfigCheckModal({ context, tenantId, record, modal }) {
  useEffect(() => {
    modal.handleOk(async () => {
      const res = await axios.post(`/ecos/v1/${tenantId}/userOpenAccountInfo/bind`, {
        infoId: record.get('id'),
        userId: record.get('yqcloudUser.id'),
      });
      record.dataSet.query(record.dataSet.currentPage);
    });
  }, []);
  return (
    <div>
      <Form record={record}>
        <TextField name="openDepartmentName" />
        <TextField name="openName" />
        <TextField name="openId" />
        <Lov name="yqcloudUser" />
      </Form>
    </div>
  );
}

export default injectIntl(observer(ConfigCheckModal));
