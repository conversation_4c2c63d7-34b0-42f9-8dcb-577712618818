import React, { useMemo, useCallback } from 'react';
import { useIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import { Table, Tabs, DataSet, message, Modal, Spin } from 'choerodon-ui/pro';
import { formatterCollections } from '@zknow/utils';
import BindDetailDataSet from '../../../stores/BindDetailDataSet';
import styles from './BindResult.module.less';
// eslint-disable-next-line import/no-cycle
import { ErrorLog } from '../bind-history';

const STATUS_COLOR = { true: '#1ab335', false: '#f34c4b' };

const BindResult = (props) => {
  const { historyId, tenantId, intlPrefix, userAutoBindDataSet, appType } = props;

  const intl = useIntl();

  const dataSet = useMemo(() => new DataSet(BindDetailDataSet({ tenantId, historyId, intlPrefix, intl })), [tenantId, historyId, intlPrefix, intl]);

  const handleOpenError = useCallback((bindLog) => {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.openLoginConfig.model.log', defaultMessage: '日志' }),
      style: { width: '50vw', overflow: 'auto' },
      children: <ErrorLog bindLog={bindLog} />,
      okButton: false,
      cancelButton: false,
      maskClosable: true,
    });
  }, [intl, tenantId]);

  const renderErrorLog = useCallback(({ record, dataSet: _dataSet }) => {
    const bindLog = record.get('bindLog');
    return bindLog ? (
      <span style={{ color: '#2979ff', cursor: 'pointer' }} onClick={() => handleOpenError(bindLog)}>
        {intl.formatMessage({ id: 'zknow.common.button.view', defaultMessage: '查看' })}
      </span>
    ) : '-';
  }, [intl, handleOpenError]);

  const transformCodeToName = (code) => {
    // 从userAutoBindDataSet取后端给的每个code对应的汉语名
    const target = userAutoBindDataSet.find((item) => (item.get('openCode') === code));
    if (target) {
      return target.get('userInfo.fieldName') || '';
    }
    return '';
  };

  const renderFieldsName = (openUser) => {
    return (
      <div style={{ lineHeight: '28px', fontSize: '14px', opacity: '0.7' }}>
        {Object.entries(openUser).map(([key, value]) => (
          <div>
            {intl.formatMessage({ id: appType })}{intl.formatMessage({ id: 'iam.openLoginConfig.desc.bind.show', defaultMessage: '显示' })}{transformCodeToName(key)}({key}){intl.formatMessage({ id: 'iam.openLoginConfig.desc.bind.is', defaultMessage: '为' })}{value}
            {/* ↑ 例如：企业微信显示用户账号(userid)为12345 */}
          </div>
        ))}
      </div>
    );
  };

  const renderFailResult = useCallback(({ record }) => {
    const handleStatus = record.get('handleStatus');
    const bindInfo = record.get('bindInfo');
    return (<div>
      <div style={{ lineHeight: '28px' }}>
        {handleStatus}
      </div>
      {bindInfo?.openUser ? renderFieldsName(bindInfo?.openUser) : null}
    </div>);
  }, []);

  const renderStatus = useCallback(({ record }) => {
    const bindResultFlag = record.get('bindResultFlag');
    const bindResult = record.get('bindResult');
    const color = STATUS_COLOR[bindResultFlag];
    return (
      <div className={styles.statusWrapper}>
        <span className={styles.statusColor} style={{ backgroundColor: color }} />
        <span>{bindResult || '-'}</span>
      </div>
    );
  }, []);

  return (
    <Table
      pristine
      dataSet={dataSet}
      queryBarProps={{ fuzzyQuery: true, simpleMode: true, inlineSearch: false }}
    >
      <Table.Column name="openId" />
      <Table.Column name="openName" />
      <Table.Column name="bindResult" renderer={renderStatus} width={100} />
      <Table.Column name="failResult" className={styles.result} renderer={renderFailResult} width={300} />
      <Table.Column name="bindLog" renderer={renderErrorLog} />
    </Table>
  );
};

export default formatterCollections({ code: ['zknow.common', 'iam.openLoginConfig'] })(observer(BindResult));
