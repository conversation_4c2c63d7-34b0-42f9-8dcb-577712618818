import React, { useContext, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { Select, TextField, Lov, Switch, Tooltip } from 'choerodon-ui/pro';
import { Icon, StatusTag } from '@zknow/components';
import classnames from 'classnames';
import Store from '../../stores';
import '../index.less';
import styles from '../Detail.module.less';

export default observer(({ type = 'user', appType, dataSet, disabled }) => {
  const { prefixCls, intl, intlPrefix, nameMap, keepValueMapping, defaultDataSet } = useContext(Store);
  const curPrefixCls = `${prefixCls}-config-body-user`;

  const infoRef = useRef();

  const handleOpenChange = (value, record) => {
    if (value === 'CUSTOM' || value === 'custom') {
      record.set('openCode', '');
    } else {
      record.set('openCode', value);
    }
  };

  const handleLovChange = (value, record) => {
    const { code: fieldCode, name: fieldName } = value || {};
    record.set(`${type}Info.fieldCode`, fieldCode);
    record.set(`${type}Info.fieldName`, fieldName);
    record.set('constantWidgetType', value);
    if (!fieldCode) {
      record.set('openCode', '');
      record.set('openName', '');
    }
  };

  const handleCreate = () => {
    if (disabled) return;
    const record = dataSet.create();
    dataSet.current = record;
    infoRef.current?.openModal();
  };

  const handleClose = () => {
    dataSet.remove(dataSet.current);
  };

  function renderWxWorkOptions({ text, record }) {
    const privateFieldCodes = ['email', 'phone', 'mobile', 'address', 'gender', 'biz_mail'];
    const basicFieldCodes = ['userid', 'name', 'main_department', 'position', 'telephone', 'external_position', 'custom', 'alias'];
    const code = record.get('openCode') || record.get('code');
    const isPrivateCode = privateFieldCodes.includes(code);
    const isBasicCode = !privateFieldCodes.includes(code);
    const isWxWork = appType === 'wechat_enterprise';
    return (
      <div className={styles.optionTag}>
        {text}
        {isWxWork && type === 'user' && (isPrivateCode || isBasicCode) && <StatusTag
          name="flag"
          color={isPrivateCode ? 'rgb(127, 131, 247)' : 'rgb(204, 247, 131)'}
        >
          {intl.formatMessage({ id: isPrivateCode ? 'private info' : 'basic' })}
        </StatusTag>}

      </div>
    );
  }
  
  function renderConstWidget(record) {
    return (
      <TextField className={`${curPrefixCls}-content-item`} name="constantValue" record={record} />
    );
  }
  const renderMappingFields = (record, index) => {
    const required = keepValueMapping[type]?.includes(record.get(`${type}Info.fieldCode`));
    return (
      <div className={`${curPrefixCls}-content-fields`}>
        {/* 字段名称 */}
        <Lov
          className={`${curPrefixCls}-content-item`}
          name={`${type}Info`}
          record={record}
          disabled={required || disabled}
          onChange={(value) => handleLovChange(value, record)}
          tableProps={{ filter: (r) => !dataSet.some(f => f.get(`${type}Info.fieldCode`) === r.get('code')) }}
        />
        {/* 关系 */}
        <div style={{ width: '50px' }} className={`${curPrefixCls}-content-item`}>
          {intl.formatMessage({ id: 'zknow.common.condition.is', defaultMessage: '等于' })}
        </div>
        {/* 字段名称 */}
        <Select
          className={`${curPrefixCls}-content-item`}
          name="openName"
          record={record}
          optionRenderer={renderWxWorkOptions}
          renderer={renderWxWorkOptions}
          disabled={disabled}
          // disabled={required && appType === 'wechat_enterprise'}
          onChange={(value) => handleOpenChange(value, record)}
          // optionsFilter={handleAppOptionsFilter}
        />
        {/* 飞书字段 */}
        {(['CUSTOM', 'custom', 'CONSTANT', 'constant'].includes(record.get('openCode')) || ['custom', 'CUSTOM'].includes(record.get('openName'))) ? (renderConstWidget(record)) : (
          <TextField className={`${curPrefixCls}-content-item`} name="openCode" record={record} disabled />
        )}
        {!disabled && (
          <div className={`${curPrefixCls}-content-delete`} onClick={() => dataSet.remove(record)}>
            <Icon type="delete" theme="outline" size="14" fill="#f8353f" />
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={curPrefixCls}>
      <div className={`${curPrefixCls}-header`}>
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.auto.binding.rules.for.unbound.personnel', defaultMessage: '人员自动绑定规则' })}
      </div>
      <div className={`${curPrefixCls}-content`}>
        <div className={`${curPrefixCls}-content-fieldsname`}>
          <div className={`${curPrefixCls}-content-item`}>
            {intl.formatMessage({ id: 'iam.openLoginConfig.desc.yqcloud.bind.rule', defaultMessage: '燕千云匹配规则' })}
          </div>
          <div style={{ width: '50px' }} className={`${curPrefixCls}-content-item`}>
            {intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.relation', defaultMessage: '关系' })}
          </div>
          <div className={`${curPrefixCls}-content-item`}>
            {`${nameMap[appType]}${intl.formatMessage({ id: 'iam.openLoginConfig.desc.match.rule', defaultMessage: '匹配规则' })}`}
          </div>
          <div className={`${curPrefixCls}-content-item`}>
            {`${nameMap[appType]}${intl.formatMessage({ id: 'iam.openLoginConfig.desc.field.code', defaultMessage: '字段编码' })}code`}
          </div>
        </div>
        {dataSet.status === 'ready' && dataSet.length > 0 && dataSet.map(renderMappingFields)}
        <div className={classnames({ [`${curPrefixCls}-content-addfields`]: true, disabled })} onClick={handleCreate}>
          <Icon type="file-addition" theme="outline" size="14" fill="currentColor" />
          <span style={{ marginLeft: '4px' }}>
            {intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.add.field', defaultMessage: '添加字段' })}
          </span>
          <Lov
            ref={infoRef}
            type="button"
            style={{ display: 'none' }}
            record={defaultDataSet.current}
            name={`${type}Info`}
            onChange={(value) => handleLovChange(value, dataSet.current)}
            modalProps={{ onCancel: handleClose }}
            tableProps={{ filter: (r) => !dataSet.some(f => f.get(`${type}Info.fieldCode`) === r.get('code')) }}
          />
        </div>
      </div>
    </div>
  );
});
