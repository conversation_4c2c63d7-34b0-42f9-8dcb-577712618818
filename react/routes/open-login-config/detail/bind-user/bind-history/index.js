import React, { useCallback, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import { Table, Modal } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { formatterCollections } from '@zknow/utils';
import { Alert } from 'choerodon-ui';
import { TableHoverAction } from '@zknow/components';
import styles from './BindHistory.module.less';
// eslint-disable-next-line import/no-cycle
import BindResult from '../bind-result';

const { Column } = Table;

const STATUS_COLOR = { 0: '#fd7d23', 1: '#1ab335' };

export const ErrorLog = ({ bindLog }) => {
  return (
    <div className={styles.errorLogWrapper}>
      {bindLog || ''}
    </div>
  );
};

const BindHistory = (props) => {
  const { intlPrefix, dataSet, tenantId } = props;

  const intl = useIntl();

  useEffect(() => {
    dataSet.query();
  }, []);

  const renderResult = useCallback(({ record }) => {
    const status = record.get('bindStatusFlag')?.toString(); // 1 绑定完成 0 绑定中
    if (status === '1') {
      return (
        <div>
          {intl.formatMessage({ id: 'iam.openLoginConfig.desc.bind.result.tips', defaultMessage: '共{all}人，未绑定{unbind}人' }).replace('{all}', record?.get('openUserCount') || '-').replace('{unbind}', record?.get('noBindUserCount') || '-')}
        </div>
      );
    } else {
      return (<div> - </div>);
    }
  }, []);

  const renderStatus = useCallback(({ record }) => {
    const status = record.get('bindStatusFlag')?.toString(); // 1 绑定完成 0 绑定中
    const value = record.get('bindStatus')?.toString();
    const color = STATUS_COLOR[status] || '#2979ff';
    return status ? (
      <div className={styles.statusWrapper}>
        <span className={styles.statusColor} style={{ backgroundColor: color }} />
        <span>{value}</span>
      </div>
    ) : null;
  }, []);

  const handleOpenError = useCallback((bindLog) => {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.openLoginConfig.model.log', defaultMessage: '日志' }),
      style: { width: '50vw', overflow: 'auto' },
      children: <ErrorLog bindLog={bindLog} />,
      okButton: false,
      cancelButton: false,
      maskClosable: true,
    });
  }, [intl, tenantId]);

  const renderErrorLog = useCallback(({ record }) => {
    const bindLog = record.get('bindLog');
    return bindLog ? (
      <span style={{ color: '#2979ff', cursor: 'pointer' }} onClick={() => handleOpenError(bindLog)}>
        {intl.formatMessage({ id: 'zknow.common.button.view', defaultMessage: '查看' })}
      </span>
    ) : '-';
  }, [intl, handleOpenError]);

  const handleOpenResult = useCallback((id) => {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.openLoginConfig.model.bind.result', defaultMessage: '绑定结果' }),
      drawer: true,
      maskClosable: true,
      style: { width: 900, maxWidth: '75vw' },
      bodyStyle: { padding: 0 },
      children: <BindResult historyId={id} tenantId={tenantId} intlPrefix={intlPrefix} {...props} />,
      cancelButton: false,
    });
  }, [tenantId, intl]);

  const renderTableAction = ({ record }) => {
    const actions = [{
      name: intl.formatMessage({ id: 'iam.openLoginConfig.model.bind.result', defaultMessage: '绑定结果' }),
      icon: 'list-success',
      onClick: () => handleOpenResult(record.get('id')),
    }];

    return (
      <TableHoverAction record={record} actions={actions} />
    );
  };

  return (
    <div className={styles.syncHistroyWrapper}>
      <Alert
        className={styles.alert}
        showIcon
        iconType="info"
        type="info"
        message={intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.sync.history.tips', defaultMessage: '仅保留3个月内的同步历史记录' })}
      />
      <Table
        pristine
        className={styles.table}
        dataSet={dataSet}
        queryBarProps={{ fuzzyQuery: true, simpleMode: true, inlineSearch: false }}
      >
        <Column name="bindType" width={100} />
        <Column name="bindStatus" width={150} renderer={renderStatus} />
        <Column
          className={styles.result}
          width={200}
          name="bindResult"
          renderer={renderResult}
        />
        <Column name="executor" width={100} />
        <Column name="bindLog" width={100} renderer={renderErrorLog} />
        <Column name="creationDate" width={100} />
        <Column
          tooltip="none"
          width={1}
          renderer={renderTableAction}
        />
      </Table>
    </div>
  );
};

export default inject('AppState')(formatterCollections({ code: 'iam.openLoginConfig' })(observer(BindHistory)));
