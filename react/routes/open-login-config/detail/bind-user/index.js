import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { observer } from 'mobx-react-lite';
import copy from 'copy-to-clipboard';
import { axios, Content, Header, TabPage } from '@yqcloud/apps-master';
import { getAccessToken, getEnv } from '@zknow/utils';
import {
  CodeArea,
  DateTimePicker,
  Form,
  Lov,
  message,
  DateTime,
  Modal,
  Output,
  Password,
  Select,
  SelectBox,
  Switch,
  TextField,
  Table,
  DataSet,
} from 'choerodon-ui/pro';
import { Button, ExternalComponent, FileUploader, Icon as YQIcon, YqAvatar } from '@zknow/components';
import { Alert } from 'choerodon-ui';
import BindFieldsConfig from './BindFieldsConfig';
import Store from '../../stores';
import defaultAvatar from '../../assets/wechat_avatar.png';
import '../index.less';
import ManualBindModal from './ManualBindModal';

const { Column } = Table;
function ListView(props) {
  const {
    match: { params: { id } },
    edit,
    setEdit,
    handleSave,
    handleCancel,
    toggleEdit,
    usualOpenAppFlag,
    handleEnabled,
    handleOpenBindHistory,
  } = props;
  const context = useContext(Store);
  const {
    intl, prefixCls, intlPrefix, organizationId, history, userDataSet,
    departmentDataSet, infoDetailDataSet, type, tenantId,
    userBindInfoDataSet, userAutoBindDataSet,
  } = context;

  useEffect(() => {
    userBindInfoDataSet.query();
  }, []);

  async function handleAutoBind() {
    try {
      const res = await axios.post(`/ecos/v1/${tenantId}/openApps/bind/${infoDetailDataSet.current.get('id')}`);
      await userBindInfoDataSet.query();
      message.success(intl.formatMessage({ id: 'iam.openLoginConfig.model.bind.success', defaultMessage: '绑定成功' }));
    } catch (e) {
      message.error(e.message);
    }
  }
  const actionButtons = () => {
    if (edit) {
      return (
        <div className={`${prefixCls}-form-buttons`}>
          <Button funcType="raised" onClick={handleSave} color="primary">{intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}</Button>
          <Button funcType="raised" onClick={handleCancel}>{intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}</Button>
        </div>
      );
    } else {
      return (
        <div className={`${prefixCls}-form-buttons`}>
          <Button funcType="raised" onClick={handleAutoBind} color="primary">{intl.formatMessage({ id: 'iam.openLoginConfig.model.auto.bind', defaultMessage: '自动绑定' })}</Button>
          <Button funcType="raised" onClick={toggleEdit} color="primary" icon="write">{intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}</Button>
        </div>
      );
    }
  };

  const renderFormContent = useCallback(() => {
    if (infoDetailDataSet?.current) {
      const appType = infoDetailDataSet?.current?.get('type');
      if (!infoDetailDataSet.current?.get('config.passwordFlag')) {
        infoDetailDataSet.current?.set('config.passwordFlag', false);
      }

      return (
        <div className={`${prefixCls}-config-boda'sy`}>
          <Form header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.scheduled.binding', defaultMessage: '定时绑定' })} />
          {appType === 'wechat_enterprise' && <Alert
            // className={styles.alert}
            showIcon
            iconType="info"
            type="info"
            message={intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.wxwork.tips', defaultMessage: '根据企微隐私政策，暂不支持查询用户的敏感信息，如手机号与邮箱等信息。' })}
          />}

          <BindFieldsConfig appType={appType} dataSet={userAutoBindDataSet} disabled={!edit} />
          <Form dataSet={infoDetailDataSet} columns={2} className="yq-mt-16" disabled={!edit} labelWidth={100} labelLayout="horizontal">

            <Switch name="bindConfig.timingFlag" label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.enable.scheduled.binding', defaultMessage: '开启定时绑定' })} />
            {infoDetailDataSet.current?.get('bindConfig.timingFlag') && <Select name="bindConfig.frequency" label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.bind.frequency', defaultMessage: '绑定频率' })} />}
            {infoDetailDataSet.current?.get('bindConfig.timingFlag') && <DateTimePicker
              label={intl.formatMessage({ id: 'zknow.common.model.startTime', defaultMessage: '开始时间' })}
              mode="dateTime"
              newLine
              name="bindConfig.startBindTime"
              id="yq-test-iam-ldap-sync_start1"
            />}
            {infoDetailDataSet.current?.get('bindConfig.timingFlag') && <DateTimePicker
              label={intl.formatMessage({ id: 'zknow.common.model.endTime', defaultMessage: '结束时间' })}
              mode="dateTime"
              name="bindConfig.endBindTime"
              id="yq-test-iam-ldap-sync_end1"
            />}
          </Form>
        </div>
      );
    }
  }, [edit, usualOpenAppFlag]);

  function handleBind({ record }) {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.openLoginConfig.model.manual.bind', defaultMessage: '手动绑定' }),
      // drawer: true,
      // maskClosable: true,
      style: { width: 1000, maxWidth: '75vw' },
      children: (
        <ManualBindModal context={context} record={record} tenantId={tenantId} />
      ),
      okText: intl.formatMessage({ id: 'iam.common.model.bind', defaultMessage: '绑定' }),
      okButton: intl.formatMessage({ id: 'iam.common.model.bind', defaultMessage: '绑定' }),
      cancelButton: false,
    });
  }

  const actionsList = useMemo(() => {
    const action = [
      {
        name: infoDetailDataSet?.current?.get('enabledFlag') ? intl.formatMessage({ id: 'zknow.common.button.deactivated', defaultMessage: '停用' }) : intl.formatMessage({ id: 'zknow.common.button.enable', defaultMessage: '启用' }),
        icon: infoDetailDataSet?.current?.get('enabledFlag') ? 'reduce-one' : 'check-one',
        onClick: () => handleEnabled(),
      },
    ];
    if (usualOpenAppFlag) {
      action.push(
        {
          name: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.choerodon.bind.history', defaultMessage: '绑定历史' }),
          icon: 'history',
          onClick: handleOpenBindHistory,
        },
      );
    }
    return action;
  }, [infoDetailDataSet?.current, usualOpenAppFlag]);
  async function handleUnbind({ record }) {
    try {
      const res = await axios.delete(`/ecos/v1/${tenantId}/userOpenAccountInfo/unbind?id=${record.get('id')}`);
      userBindInfoDataSet.query(userBindInfoDataSet.currentPage);
    } catch (e) {
      message.error(e.message);
    }
  }
  function renderTableAction({ record }) {
    if (record.get('bindFlag') === true) {
      return (
        <Button funcType="raised" color="red" onClick={() => handleUnbind({ record })}>
          {intl.formatMessage({ id: 'iam.openLoginConfig.desc.unbind', defaultMessage: '解绑' })}
        </Button>
      );
    } else {
      return (
        <Button funcType="raised" color="primary" onClick={() => handleBind({ record })}>
          {intl.formatMessage({ id: 'iam.openLoginConfig.model.manual.bind', defaultMessage: '手动绑定' })}
        </Button>
      );
    }
  }

  function renderManualBindTable() {
    return (
      <div className="yq-mt-16">
        <Form className="yq-mt-16" header={intl.formatMessage({ id: 'iam.openLoginConfig.model.manual.bind', defaultMessage: '手动绑定' })} />
        <Table
          labelLayout="float"
          dataSet={userBindInfoDataSet}
          className={`${prefixCls}-table`}
          queryFieldsLimit={100}
          queryBarProps={{
            title: intl.formatMessage({ id: 'zknow.common.model.menu', defaultMessage: '菜单' }),
            fuzzyQuery: true,
          }}
        >
          <Column name="openDepartmentName" width={200} lock />
          <Column name="openName" minWidth={350} />
          <Column name="openId" minWidth={350} />
          <Column name="yqInfo" header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.yqcloud.username.loginname', defaultMessage: '燕千云账号-登录名' })} minWidth={350} />

          <Column header={intl.formatMessage({ id: 'zknow.common.button.action', defaultMessage: '操作' })} width={100} renderer={renderTableAction} />
        </Table>
      </div>
    );
  }

  return (
    <TabPage className={`${prefixCls}-config`}>
      <Header
        actionsList={actionsList}
        dataSet={infoDetailDataSet}
      >
        <h1>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.menu.name.bind.user', defaultMessage: '人员绑定' })}</h1>
        <div>{actionButtons()}</div>
      </Header>
      <Content>
        {renderFormContent()}
        {renderManualBindTable()}
      </Content>
    </TabPage>
  );
}

export default observer(ListView);
