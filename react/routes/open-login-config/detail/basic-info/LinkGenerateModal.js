import React, { useMemo, useState } from 'react';
import { injectIntl } from 'react-intl';
import { inject } from 'mobx-react';
import { Icon as YQIcon, Button, ExternalComponent, FileUploader, YqAvatar } from '@zknow/components';
import { getAccessToken, getEnv, formatterCollections } from '@zknow/utils';

import { DataSet, Form, SelectBox, CheckBox, TextField, message, Output, Lov, NumberField } from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';
import copy from 'copy-to-clipboard';
import getMobileLink from './getMobileLink';

function LinkGenerateModal({ intl, infoDetailDataSet, domain, tenantId }) {
  const ds = useMemo(() => new DataSet({
    data: [{ path: '/' }],
    fields: [
      { name: 'bot', type: 'object', lovCode: 'SELECT_INTELLIGENT_IN_SERVICE' },
      { name: 'itemId', type: 'object', lovCode: 'SC_ITEM' },
      {
        name: 'view',
        type: 'object',
        lovCode: 'VIEW',
        lovPara: {
          deviceType: 'H5',
          viewType: 'TABLE',
        },
      },
    ],
  }), []);
  const type = infoDetailDataSet?.current?.get('type');
  const optionData = [
    { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.desc.home', defaultMessage: '首页' }), value: '/' },
    { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.desc.service.catalog', defaultMessage: '服务目录' }), value: '/pages/service-catalog/index' },
    { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.desc.service.item', defaultMessage: '服务项' }), value: '/pages/create-order/index?noBreadcrumb=true' },
    // https://m.preprod.yqcloud.com/pages/create-order/index?noBreadcrumb=true&itemId=412033973345472512&tenant=yqcloud
    { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.desc.knowledge.center', defaultMessage: '知识中心' }), value: '/pages/knowledge-center/index' },
    { meaning: intl.formatMessage({ id: 'iam.openLoginConfig.desc.bot', defaultMessage: '智能助理' }), value: '/pages/botpress/index' },
    { meaning: intl.formatMessage({ id: 'zknow.common.model.view', defaultMessage: '视图' }), value: '/pages/task-detail/index' },
  ];
  const optionDs = new DataSet({
    data: optionData,
    selection: 'single',
  });
  const [viewWidth, setWidth] = useState();
  const homePageUrl = getMobileLink({
    type,
    appId: infoDetailDataSet?.current?.get('appId'),
    domain,
    corpId: infoDetailDataSet?.current?.get('dingTalkCorpId'),
    path: ds.current?.get('path'),
    displayType: ds.current?.get('displayType'),
    botId: ds.current?.get('bot')?.id,
    itemId: ds.current?.get('itemId')?.id,
    openOutLink: ds.current?.get('openOutLink'),
    view: ds.current?.get('view'),
  }, viewWidth);
  function copyText(text) {
    copy(text);
    message.success(intl.formatMessage({ id: 'zknow.common.success.copy', defaultMessage: '复制成功' }));
  }
  function getTips() {
    if (type === 'ding_talk') {
      return intl.formatMessage({ id: 'iam.openLoginConfig.desc.please.copy.to.ding.talk.develop.config', defaultMessage: '请复制链接，通过钉钉开放平台-开发管理中配置使用。' });
    }
  }
  const callbackUrl = `${getEnv('API_HOST')}/oauth/oauth/open/lark/callback?organization_id=${tenantId}&client_id=${domain}&corpid=&redirect_uri=${encodeURIComponent(homePageUrl)}&appid=${infoDetailDataSet.current?.get('appId')}`;

  return (
    <div>
      <Form labelWidth="auto" dataSet={ds}>
        <SelectBox options={optionDs} label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.select.home.page', defaultMessage: '选择访问主页' })} name="path" />
        {ds.current?.get('path') === '/pages/botpress/index' && (
          <>
            <Lov name="bot" label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.select.bot', defaultMessage: '选择智能助理' })} />
            <NumberField defaultValue={500} label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.notify.bot.width', defaultMessage: '智能助理宽度' })} step="1" onChange={setWidth} />
          </>
        )}
        {ds.current?.get('path') === '/pages/create-order/index?noBreadcrumb=true' && <Lov name="itemId" label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.select.service.item', defaultMessage: '选择服务项' })} />}
        {ds.current?.get('path') === '/pages/task-detail/index' && <Lov name="view" label={intl.formatMessage({ id: 'zknow.common.model.view', defaultMessage: '视图' })} />}
        <CheckBox label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.force.show.h5', defaultMessage: '始终显示成移动端样式' })} value="h5" name="displayType" />
        <TextField
          disabled
          label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.home.page.url', defaultMessage: '主页URL' })}
          value={homePageUrl}
          suffix={
            <YQIcon
              className="iam-open-login-config-link-copy"
              type="CopyOne"
              onClick={() => copyText(homePageUrl)}
            />
          }
        />
        {type === 'lark' && (
          <TextField
            disabled
            label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.redirect.url', defaultMessage: '重定向URL' })}
            value={callbackUrl}
            suffix={
              <YQIcon
                className="iam-open-login-config-link-copy"
                type="CopyOne"
                onClick={() => copyText(callbackUrl)}
              />
            }
          />
        )}
        {type === 'lark' && (
          <CheckBox label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.out.link', defaultMessage: '从外部浏览器打开' })} value name="openOutLink" />
        )}

        {type === 'ding_talk' && <Output value={getTips()} />}
      </Form>
    </div>
  );
}

export default inject('AppState')(formatterCollections({ code: 'iam.openLoginConfig' })(injectIntl(observer(LinkGenerateModal))));
