import { getAccessToken, getEnv } from '@zknow/utils';
import queryString from 'query-string';

function getMobileLink({ type, appId, domain, corpId, path, displayType, botId, agentid, itemId, openOutLink, view }, viewWidth) {
  const params = {
    tenant: domain,
    type,
    corpid: corpId,
    appid: appId,
    displayType,
    botId,
    openOutLink: openOutLink === true ? true : undefined,
    agentid,
    itemId,
    viewCode: view?.code,
    title: view?.name,
  };
  const paramsSplitter = path?.includes('?') ? '&' : '?';
  if (type === 'ding_talk') {
    return `${getEnv('MB_HOST')}${path || ''}${paramsSplitter}${queryString.stringify(params)}${viewWidth ? `&viewWidth=${viewWidth}` : ''}`;
  }
  if (type === 'wechat_enterprise') {
    return `${getEnv('MB_HOST')}${path || ''}${paramsSplitter}${queryString.stringify(params)}&oauthScope=snsapi_privateinfo${viewWidth ? `&viewWidth=${viewWidth}` : ''}`;
  }
  if (type === 'lark') {
    return `${getEnv('MB_HOST')}${path || ''}${paramsSplitter}${queryString.stringify(params)}${viewWidth ? `&viewWidth=${viewWidth}` : ''}`;
  }
  if (type === 'microsoft') {
    return `${getEnv('MB_HOST')}${path || ''}${paramsSplitter}${queryString.stringify(params)}${viewWidth ? `&viewWidth=${viewWidth}` : ''}`;
  }
  return '';
}

export default getMobileLink;
