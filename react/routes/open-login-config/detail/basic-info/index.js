import React, { use<PERSON><PERSON>back, useContext, useEffect, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import copy from 'copy-to-clipboard';
import { axios, Content, Header, TabPage } from '@yqcloud/apps-master';
import { getEnv } from '@zknow/utils';
import {
  Form,
  Lov,
  message,
  Modal,
  Output,
  Password,
  Select,
  SelectBox,
  Switch,
  TextArea,
  NumberField,
  TextField,
  Tooltip,
} from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { Alert } from 'choerodon-ui';
import { Button, FileUploader, Icon as YQIcon, YqAvatar } from '@zknow/components';
import queryString from 'query-string';
import Guidelines from '../../guide/Guidelines';
import HzeroGuide from '../../guide/HzeroGuide';
import SapGuide from '../../guide/SapGuide';
import EbsGuide from '../../guide/EbsGuide';
import Store from '../../stores';
import defaultAvatar from '../../assets/wechat_avatar.png';
import AppBot from './components/app-bot';
import styles from '../Detail.module.less';
import '../index.less';
import getMobileLink from './getMobileLink';
import LinkGenerateModal from './LinkGenerateModal';
import contactRedirectFlagOnCn from '@/assets/images/contactRedirectFlagOnCn.png';
import contactRedirectFlagOffCn from '@/assets/images/contactRedirectFlagOffCn.png';
import contactRedirectFlagOnEn from '@/assets/images/contactRedirectFlagOnEn.png';
import contactRedirectFlagOffEn from '@/assets/images/contactRedirectFlagOffEn.png';
import SdkHzeroFormContent from './components/SdkHzeroFormContent';
import SapEbsFormContent from './components/SapEbsFormContent';
import ChoerodonContent from './components/ChoerodonContent';
import WechatOpenAccountFormContent from './components/WechatOpenAccountFormContent';
import TodoDescription from './components/todo-description';
import NetSuiteGuide from '../../guide/NetSuiteGuide';
import Approval from './components/approval';

const modalKey = Modal.key();

function ListView(props) {
  const {
    match: { params: { id } },
    edit,
    setEdit,
    AppState: { currentLanguage: language, userInfo, menuType },
    handleSave,
    handleCancel,
    toggleEdit,
    usualOpenAppFlag,
    handleEnabled,
    handleSync,
    handleOpenSyncHistory,
    handleConfigCheck,
    handleOpenBindHistory,
    openAppStore,
    approveDataset,
  } = props;
  const context = useContext(Store);
  const {
    intl, prefixCls, intlPrefix, history,
    infoDetailDataSet, type, tenantId,
    choerodonUserDataSet, HeaderStore: { tenantConfig },
  } = context;
  const domain = tenantConfig?.domain?.toLowerCase() || userInfo.tenantNum?.toLowerCase() || menuType.tenantNum?.toLowerCase();

  const modalStyle = useMemo(() => ({ width: 800 }), []);
  const search = history.location?.search;

  function refresh() {
    if (id === '0') return;
    infoDetailDataSet.setQueryParameter('id', id);
    infoDetailDataSet.query().then(() => {
      infoDetailDataSet.current = infoDetailDataSet.get(0);
    });
  }

  useEffect(() => {
    const appItemId = infoDetailDataSet?.current?.get('id');
    const appItemType = infoDetailDataSet?.current?.get('type');
    if (appItemId && appItemType === 'choerodon') {
      choerodonUserDataSet.query();
    }
  }, [infoDetailDataSet?.current?.get('id')]);

  function getGuidelines() {
    const quickType = infoDetailDataSet?.current?.get('quickType');
    let children;
    let realType;
    // 是否展示下载按钮
    let okButton = true;

    if (quickType === 'CLIENT') { // SAP
      realType = 'SAP';
    }
    if (quickType === 'EBS') { // EBS
      realType = 'EBS';
    }
    if (quickType?.includes('SDK')) { // SDK
      realType = 'SDK';
    }
    if (quickType === 'HZERO') {
      realType = 'HZERO';
    }
    let okText = intl.formatMessage({ id: 'iam.openLoginConfig.desc.download.sap', defaultMessage: '下载{TYPE}请求包' }).replace('{TYPE}', realType);
    if (quickType === 'NETSUITE') {
      okText = null;
      okButton = false;
    }
    switch (quickType) {
      case 'CLIENT':
        children = <SapGuide />;
        break;
      case 'HZERO':
        children = <HzeroGuide intl={intl} prefixCls={prefixCls} dataSet={infoDetailDataSet} />;
        break;
      case 'EBS':
        children = <EbsGuide />;
        break;
      case 'NETSUITE':
        children = <NetSuiteGuide />;
        break;
      default:
        children = <Guidelines intl={intl} prefixCls={prefixCls} dataSet={infoDetailDataSet} />;
    }

    Modal.open({
      title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.guidelines', defaultMessage: '配置指引' }),
      closeable: true,
      drawer: true,
      key: modalKey,
      children,
      style: quickType === 'CLIENT' ? { width: 1000 } : modalStyle,
      bodyStyle: quickType === 'CLIENT' ? { padding: 0, overflow: 'hidden' } : {},
      okText,
      okButton,
      cancelText: intl.formatMessage({ id: 'zknow.common.button.close', defaultMessage: '关闭' }),
      onOk: () => {
        if (realType) {
          axios({
            url: `/ecos/pub/sdk?type=${realType}`,
            method: 'GET',
            responseType: 'blob', // 设置响应类型为 blob
          }).then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${realType}.zip`; // TODO 文件名应该从后端获取
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
          });
          return false;
        }
      },
    });
  }

  const actionButtons = () => {
    if (edit) { // 可编辑状态
      return (
        <div className={`${prefixCls}-form-buttons`}>
          <Button funcType="raised" onClick={handleSave} color="primary">{intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}</Button>
          <Button funcType="raised" onClick={handleCancel}>{intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}</Button>
        </div>
      );
    } else { // 不可编辑状态
      return (
        <div className={`${prefixCls}-form-buttons`}>
          <Button funcType="raised" onClick={toggleEdit} color="primary" icon="write">{intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}</Button>
          {infoDetailDataSet?.current?.get('type') === 'quick_authentication'
            ? (
              <Button
                funcType="raised"
                onClick={getGuidelines}
                color="secondary"
                icon="FileSettings"
              >
                {intl.formatMessage({ id: 'iam.openLoginConfig.desc.guidelines', defaultMessage: '配置指引' })}
              </Button>
            ) : null}
        </div>
      );
    }
  };

  const actionsList = useMemo(() => {
    const action = [
      {
        name: infoDetailDataSet?.current?.get('enabledFlag') ? intl.formatMessage({ id: 'zknow.common.button.deactivated', defaultMessage: '停用' }) : intl.formatMessage({ id: 'zknow.common.button.enable', defaultMessage: '启用' }),
        icon: infoDetailDataSet?.current?.get('enabledFlag') ? 'reduce-one' : 'check-one',
        onClick: () => handleEnabled(),
      },
    ];
    if (usualOpenAppFlag) {
      action.push(
        {
          name: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.sync.hand', defaultMessage: '手动同步' }),
          icon: 'update-rotation',
          onClick: handleSync,
        },
        {
          name: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.sync.history', defaultMessage: '同步历史' }),
          icon: 'history',
          onClick: handleOpenSyncHistory,
        },
        {
          name: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.config.check', defaultMessage: '配置检查' }),
          icon: 'check-one',
          onClick: handleConfigCheck,
        },
        {
          name: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.choerodon.bind.history', defaultMessage: '绑定历史' }),
          icon: 'history',
          onClick: handleOpenBindHistory,
        },
      );
    }
    if (infoDetailDataSet?.current?.get('type') === 'choerodon') {
      action.push({
        name: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.config.check', defaultMessage: '配置检查' }),
        icon: 'check-one',
        onClick: () => handleConfigCheck('choerodon'),
      });
    }
    return action;
  }, [infoDetailDataSet?.current, usualOpenAppFlag]);

  const weChatAgentAvatar = ({ record }) => {
    return (
      <FileUploader disabled={!edit} name="wechatAgentConfig.agentAvatar" record={infoDetailDataSet.current}>
        <div className="iam-open-login-config-wechatAgentConfig-agentAvatar" style={{ position: 'relative' }}>
          <YqAvatar
            style={{ cursor: !edit ? 'default' : 'pointer' }}
            size={58}
            src={infoDetailDataSet.current?.get('wechatAgentConfig.agentAvatar') || defaultAvatar}
          />
          {edit && <div
            className="editable"
          ><YQIcon type="camera" size={30} fill="#fff" /></div>}
        </div>

      </FileUploader>
    );
  };

  function openLinkGenerateModal() {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.generate.home.page.url', defaultMessage: '生成主页URL' }),
      style: modalStyle,
      children: <LinkGenerateModal tenantId={tenantId} domain={domain} infoDetailDataSet={infoDetailDataSet} />,
    });
  }
  function renderDingtalkCallbackUrl() {
    const callbackUrl = getEnv('API_HOST');

    return (
      <TextField
        disabled
        label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.callback.url', defaultMessage: '回调URL' })}
        value={callbackUrl}
        suffix={
          <YQIcon
            className={`${prefixCls}-config-suffix`}
            type="CopyOne"
            onClick={() => copyText(callbackUrl)}
          />
        }
      />
    );
  }

  function renderDingtalkUrl() {
    const dingTalkHomePageUrl = getMobileLink({ type: 'ding_talk', domain, corpId: infoDetailDataSet.current?.get('dingTalkCorpId'), appId: infoDetailDataSet.current?.get('appId') });
    const createChatUrl = `${getEnv('MB_HOST')}/packageOther/pages/auto-create-ding-chat?&type=ding_talk&tenant=${domain}&name=${infoDetailDataSet.current.getState('dingDeptName') || ''}&displayType=h5&appid=${infoDetailDataSet.current?.get('appId')}&corpid=${infoDetailDataSet.current?.get('dingTalkCorpId')}`;
    const dingTalkCreateChatUrl = `dingtalk://dingtalkclient/page/link?pc_slide=true&url=${encodeURIComponent(createChatUrl)}`;
    return [
      <TextField
        colSpan={2}
        disabled
        label={intl.formatMessage({
          id: 'iam.openLoginConfig.desc.home.page.url',
          defaultMessage: '主页URL',
        })}
        help={`${intl.formatMessage({
          id: 'iam.openLoginConfig.desc.generate.url.tip',
          defaultMessage: '当前链接为访问移动端首页、PC首页的通用链接，如需生成其他链接，请点击 ',
        })}"${intl.formatMessage({
          id: 'iam.openLoginConfig.desc.generate.other.link',
          defaultMessage: '生成其他链接',
        })}"`}
        value={dingTalkHomePageUrl}
        suffix={
          <YQIcon
            className={`${prefixCls}-config-suffix`}
            type="CopyOne"
            onClick={() => copyText(dingTalkHomePageUrl)}
          />
        }
      />,
      <div colSpan={2} className="iam-open-login-config-imitative-info">
        <YQIcon
          className={`${prefixCls}-config-suffix`}
          type="info"
          theme="filled"
          size={18}
          style={{ marginRight: '7px' }}
        />
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.generate.url.tip', defaultMessage: '当前链接为访问移动端首页、PC首页的通用链接，如需生成其他链接，请点击 ' })}
        <span onClick={openLinkGenerateModal} className="iam-open-login-config-url">{intl.formatMessage({ id: 'iam.openLoginConfig.desc.generate.other.link', defaultMessage: '生成其他链接' })}</span>
      </div>,
      <TextField
        colSpan={1}
        disabled={false}
        value={infoDetailDataSet.current.getState('dingDeptName')}
        onChange={value => infoDetailDataSet.current.setState('dingDeptName', value)}
        label={intl.formatMessage({
          id: 'iam.openLoginConfig.desc.home.ding.dept.name',
          defaultMessage: '创建钉钉群部门',
        })}
      />,
      <TextField
        colSpan={1}
        disabled
        label={intl.formatMessage({
          id: 'iam.openLoginConfig.desc.home.page.create.chat.url',
          defaultMessage: '创建群链接',
        })}
        value={dingTalkCreateChatUrl}
        suffix={
          <YQIcon
            className={`${prefixCls}-config-suffix`}
            type="CopyOne"
            onClick={() => copyText(dingTalkCreateChatUrl)}
          />
        }
      />,
    ];
  }

  function renderTeamsRedirectUrl() {
    const homePageUrl = getMobileLink({ type: 'microsoft', domain });
    const callbackUrl = `${getEnv('API_HOST')}/oauth/oauth/open/microsoft/callback?organization_id=${tenantId}&client_id=${domain}&redirect_uri=${encodeURIComponent(homePageUrl)}&appid=${infoDetailDataSet.current?.get('appId')}`;
    return (
      <>
        <TextField
          disabled
          label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.redirect.url', defaultMessage: '重定向URL' })}
          value={callbackUrl}
          suffix={
            <YQIcon
              className={`${prefixCls}-config-suffix`}
              type="CopyOne"
              onClick={() => copyText(callbackUrl)}
            />
          }
        />
        <TextField name="corpId" />
      </>
    );
  }

  function renderTeamsUrl() {
    const homePageUrl = getMobileLink({ type: 'microsoft', domain, appId: infoDetailDataSet.current?.get('appId') });
    return [
      <TextField
        colSpan={2}
        disabled
        label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.home.page.url', defaultMessage: '主页URL' })}
        help={intl.formatMessage({ id: 'iam.openLoginConfig.desc.generate.url.tip', defaultMessage: '当前链接为访问移动端首页、PC首页的通用链接，如需生成其他链接，请点击 ' })}
        value={homePageUrl}
        suffix={
          <YQIcon
            className={`${prefixCls}-config-suffix`}
            type="CopyOne"
            onClick={() => copyText(homePageUrl)}
          />
        }
      />,
      <div colSpan={2} className="iam-open-login-config-imitative-info">
        <YQIcon
          className={`${prefixCls}-config-suffix`}
          type="info"
          theme="filled"
          size={18}
          style={{ marginRight: '7px' }}
        />
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.generate.url.tip', defaultMessage: '当前链接为访问移动端首页、PC首页的通用链接，如需生成其他链接，请点击 ' })}
        <span onClick={openLinkGenerateModal} className="iam-open-login-config-url">{intl.formatMessage({ id: 'iam.openLoginConfig.desc.generate.other.link', defaultMessage: '生成其他链接' })}</span>
      </div>,
    ];
  }

  function renderWechatEnterpriseTrustDomain() {
    const callbackUrl = getEnv('API_HOST');
    return (
      <TextField
        disabled
        label={intl.formatMessage({
          id: 'iam.openLoginConfig.desc.trust.domain',
          defaultMessage: '可信域名',
        })}
        value={callbackUrl}
        suffix={
          <YQIcon
            className={`${prefixCls}-config-suffix`}
            type="CopyOne"
            onClick={() => copyText(callbackUrl)}
          />
        }
      />
    );
  }

  function renderWechatEnterpriseUrl() {
    const homePageUrl = getMobileLink({ type: 'wechat_enterprise', domain, agentid: infoDetailDataSet.current?.get('agentId'), appId: infoDetailDataSet.current?.get('appId') });
    return [
      <TextField
        colSpan={2}
        disabled
        label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.home.page.url', defaultMessage: '主页URL' })}
        help={`${intl.formatMessage({ id: 'iam.openLoginConfig.desc.generate.url.tip', defaultMessage: '当前链接为访问移动端首页、PC首页的通用链接，如需生成其他链接，请点击 ' })}"${intl.formatMessage({ id: 'iam.openLoginConfig.desc.generate.other.link', defaultMessage: '生成其他链接' })}"`}
        value={homePageUrl}
        suffix={
          <YQIcon
            className={`${prefixCls}-config-suffix`}
            type="CopyOne"
            onClick={() => copyText(homePageUrl)}
          />
        }
      />,
      <TextField
        colSpan={2}
        disabled
        label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.home.page.url.out', defaultMessage: '外部打开URL' })}
        value={`${homePageUrl}&openWxWorkOutLink=true`}
        suffix={
          <YQIcon
            className={`${prefixCls}-config-suffix`}
            type="CopyOne"
            onClick={() => copyText(homePageUrl)}
          />
        }
      />,
      <div colSpan={2} className="iam-open-login-config-imitative-info">
        <YQIcon
          className={`${prefixCls}-config-suffix`}
          type="info"
          theme="filled"
          size={18}
          style={{ marginRight: '7px' }}
        />
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.generate.url.tip', defaultMessage: '当前链接为访问移动端首页、PC首页的通用链接，如需生成其他链接，请点击 ' })}
        <span onClick={openLinkGenerateModal} className="iam-open-login-config-url">
          {intl.formatMessage({ id: 'iam.openLoginConfig.desc.generate.other.link', defaultMessage: '生成其他链接' })}
        </span>
      </div>,
    ];
  }

  function renderLarkRedirectUrl() {
    const homePageUrl = getMobileLink({ type: 'lark', domain, appId: infoDetailDataSet.current?.get('appId') });
    const callbackUrl = `${getEnv('API_HOST')}/oauth/oauth/open/lark/callback?organization_id=${tenantId}&client_id=${domain}&corpid=&redirect_uri=${encodeURIComponent(homePageUrl)}&appid=${infoDetailDataSet.current?.get('appId')}`;
    return (
      <TextField
        disabled
        label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.redirect.url', defaultMessage: '重定向URL' })}
        value={callbackUrl}
        suffix={
          <YQIcon
            className={`${prefixCls}-config-suffix`}
            type="CopyOne"
            onClick={() => copyText(callbackUrl)}
          />
        }
      />
    );
  }

  function renderLarkUrl() {
    const homePageUrl = getMobileLink({ type: 'lark', domain, appId: infoDetailDataSet.current?.get('appId') });
    return [
      <TextField
        colSpan={2}
        disabled
        label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.home.page.url', defaultMessage: '主页URL' })}
        help={intl.formatMessage({ id: 'iam.openLoginConfig.desc.generate.url.tip', defaultMessage: '当前链接为访问移动端首页、PC首页的通用链接，如需生成其他链接，请点击 ' })}
        value={homePageUrl}
        suffix={
          <YQIcon
            className={`${prefixCls}-config-suffix`}
            type="CopyOne"
            onClick={() => copyText(homePageUrl)}
          />
        }
      />,
      <div colSpan={2} className="iam-open-login-config-imitative-info">
        <YQIcon
          className={`${prefixCls}-config-suffix`}
          type="info"
          theme="filled"
          size={18}
          style={{ marginRight: '7px' }}
        />
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.generate.url.tip', defaultMessage: '当前链接为访问移动端首页、PC首页的通用链接，如需生成其他链接，请点击 ' })}
        <span onClick={openLinkGenerateModal} className="iam-open-login-config-url">{intl.formatMessage({ id: 'iam.openLoginConfig.desc.generate.other.link', defaultMessage: '生成其他链接' })}</span>
      </div>,
    ];
  }

  useEffect(() => {
    const appType = infoDetailDataSet?.current?.get('type');
    if (appType === 'ding_talk') {
      if (infoDetailDataSet.children['jsonConfig.dingTalkChatTransfers']?.length === 0 && infoDetailDataSet.current) {
        infoDetailDataSet.children['jsonConfig.dingTalkChatTransfers'].create({});
      }
    }
    if (appType === 'lark') {
      if (infoDetailDataSet.children['jsonConfig.feishuTalkChatTransfers']?.length === 0 && infoDetailDataSet.current) {
        infoDetailDataSet.children['jsonConfig.feishuTalkChatTransfers'].create({});
      }
    }
  }, []);

  const handleFeishuTalkChatItemRange = (value, record) => {
    if (value === 'PART') {
      if (record.getCascadeRecords('feishuChatTalkTransferItem')?.length === 0) {
        infoDetailDataSet.children['jsonConfig.feishuTalkChatTransfers']?.children['feishuChatTalkTransferItem'].create({});
      }
    }
  }

  function renderTalkRecordTransferOrder() {
    const appType = infoDetailDataSet?.current?.get('type');
    const record = infoDetailDataSet.current;
    const header = (
      <Form
        className="yq-mt-16"
        // disabled={!edit}
        header={
          <>
            <span style={{ marginRight: 16 }}>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.ding.talk.record.transfer.order', defaultMessage: '聊天记录转工单' })}</span>
          </>
        }
      />
    );

    switch (appType) {
      // case 'ding_talk':
      //   return (
      //     <>
      //       {header}
      //       {infoDetailDataSet?.current?.getCascadeRecords?.('jsonConfig.dingTalkChatTransfers')?.map(dingChatRecord => {
      //         const dingTalkParams = {
      //           noBreadcrumb: true,
      //           promptTemplateId: dingChatRecord.get('dingTalkChatTransferTicketPrompt.id'),
      //           transferFieldCode: dingChatRecord.get('dingTalkChatTransferTicketServiceItemField.code'),
      //           itemId: dingChatRecord.get('dingTalkChatTransferTicketServiceItem.id'),
      //           tenant: domain,
      //           type: 'ding_talk',
      //           corpid: record.get('dingTalkCorpId'),
      //           appid: record.get('appId'),
      //         };
      //         const dingdingUrl = `${getEnv('MB_HOST')}/pages/create-order/index?${queryString.stringify(dingTalkParams)}`;
      //         const pcUrl = `dingtalk://dingtalkclient/page/link?url=${encodeURIComponent(dingdingUrl)}&pc_slide=true`;
      //         const mobileUrl = `dingtalk://dingtalkclient/action/im_open_hybrid_panel?panelHeight=percent80&hybridType=online&pageUrl=${encodeURIComponent(dingdingUrl)}`;
      //         return (
      //           <div className="iam-open-login-config-config-body-user-content yq-mt-16">
      //             <Button onClick={() => dingChatRecord.dataSet.remove(dingChatRecord)} style={{ position: 'absolute', right: 16, top: 20 }} funcType="raised" color="red">{intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' })}</Button>
      //             <Form
      //               className="yq-mt-16"
      //               disabled={!edit}
      //               // header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.ding.talk.record.transfer.order', defaultMessage: '聊天记录转工单' })}
      //               labelWidth={100}
      //               columns={2}
      //               labelLayout="horizontal"
      //               record={dingChatRecord}
      //             >
      //               <Lov name="dingTalkChatTransferTicketServiceItem" />
      //               <Lov name="dingTalkChatTransferTicketServiceItemField" />
      //               <Lov name="dingTalkChatTransferTicketPrompt" />
      //               <div colSpan={2} />
      //               <TextField
      //                 colSpan={2}
      //                 disabled
      //                 label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.ding.talk.transfer.pc.url', defaultMessage: '桌面端访问地址' })}
      //                 value={pcUrl}
      //                 suffix={
      //                   <YQIcon
      //                     className={`${prefixCls}-config-suffix`}
      //                     type="CopyOne"
      //                     onClick={() => copyText(pcUrl)}
      //                   />
      //                 }
      //               />
      //               <TextField
      //                 colSpan={2}
      //                 disabled
      //                 label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.ding.talk.transfer.mobile.url', defaultMessage: '移动端访问地址' })}
      //                 value={mobileUrl}
      //                 suffix={
      //                   <YQIcon
      //                     className={`${prefixCls}-config-suffix`}
      //                     type="CopyOne"
      //                     onClick={() => copyText(mobileUrl)}
      //                   />
      //                 }
      //               />
      //             </Form>
      //           </div>
      //         );
      //       })}
      //       <Button
      //         funcType="raised"
      //         style={{ marginTop: 16 }}
      //         icon="plus"
      //         onClick={() => {
      //           infoDetailDataSet.children['jsonConfig.dingTalkChatTransfers'].create({});
      //         }}
      //       >{intl.formatMessage({ id: 'zknow.common.button.add', defaultMessage: '新增' })}</Button>
      //     </>
      //   );
      case 'lark':
        return (
          <>
            {header}
            {infoDetailDataSet?.current?.getCascadeRecords?.('jsonConfig.feishuTalkChatTransfers')?.map(feishuChatRecord => {
              const feishuTalkChatItemRange = feishuChatRecord.get('feishuTalkChatItemRange');
              let feishuTalkParams = {
                noBreadcrumb: true,
                hiddenFooter: true,
                tenant: domain,
                type: 'feishu_talk',
                appid: record.get('appId'),
                rang: feishuTalkChatItemRange,
              };
              if (feishuTalkChatItemRange === 'ALL') {
                feishuTalkParams = {
                  ...feishuTalkParams,
                  transferFieldCode: feishuChatRecord.get('feishuTalkChatTransferTicketServiceItemField.code'),
                  promptFlag: feishuChatRecord.get('feishuTalkChatTransferTicketPromptFlag'),
                  promptTemplateCode: feishuChatRecord.get('feishuTalkChatTransferTicketPrompt.code'),
                }
              }
              const feishuUrl = `${getEnv('MB_HOST')}/packageOther/pages/talk-catalog/index?${queryString.stringify(feishuTalkParams)}`;
              const pcUrl = `https://applink.feishu.cn/client/web_app/open?appId=${record.get('appId')}&mode=sidebar&reload=true&lk_target_url=${encodeURIComponent(feishuUrl)}`;
              const mobileUrl = `https://applink.feishu.cn/client/web_app/open?appId=${record.get('appId')}&mode=appCenter&reload=true&lk_target_url=${encodeURIComponent(feishuUrl)}`;
              return (
                <div className="iam-open-login-config-config-body-user-content yq-mt-16">
                  <Form
                    className="yq-mt-16"
                    disabled={!edit}
                    labelWidth={100}
                    columns={2}
                    labelLayout="horizontal"
                    record={feishuChatRecord}
                  >
                    <SelectBox name="feishuTalkChatItemRange" onChange={(value) => handleFeishuTalkChatItemRange(value, feishuChatRecord)} />
                    {feishuChatRecord?.get('feishuTalkChatItemRange') === 'ALL' && [
                      <Lov name="feishuTalkChatTransferTicketServiceItemField" />,
                      <Switch name="feishuTalkChatTransferTicketPromptFlag" />,
                      <Lov name="feishuTalkChatTransferTicketPrompt" />,
                    ]}
                  </Form>
                  {feishuChatRecord?.get('feishuTalkChatItemRange') === 'PART' && (
                    feishuChatRecord?.getCascadeRecords('feishuChatTalkTransferItem')?.map((serviceItemRecord) => {
                      return (
                        <Form
                          disabled={!edit}
                          labelWidth={100}
                          columns={2}
                          labelLayout="horizontal"
                          record={serviceItemRecord}
                        >
                          <Lov name="feishuTalkChatTransferTicketServiceItem" />
                          <Lov name="feishuTalkChatTransferTicketServiceItemField" />
                          <Switch name="feishuTalkChatTransferTicketPromptFlag" />
                          <Lov name="feishuTalkChatTransferTicketPrompt" />
                        </Form>
                      );
                    })
                  )}
                  <Form columns={2} disabled={!edit}>
                    <Output
                      newLine
                      label=''
                      hidden={feishuChatRecord?.get('feishuTalkChatItemRange') === 'ALL'}
                      renderer={
                        () => {
                          return (
                            <Button
                              // disabled={!edit}
                              funcType="flat"
                              style={{ marginTop: 16 }}
                              icon="plus"
                              onClick={() => {
                                infoDetailDataSet.children['jsonConfig.feishuTalkChatTransfers']?.children['feishuChatTalkTransferItem'].create({});
                              }}
                            >
                              {intl.formatMessage({ id: 'zknow.common.button.add', defaultMessage: '新增' })}
                            </Button>
                          )
                        }
                      }
                    />
                  </Form>
                  <Alert
                    showIcon
                    iconType="info"
                    type="info"
                    message={intl.formatMessage({ id: 'iam.openLoginConfig.desc.lark.talk.transfer.tips', defaultMessage: '上方配置变更后，需重新复制下方的访问地址，配置到飞书三方应用中' })}
                  />
                  <Form>
                    <TextField
                      colSpan={2}
                      disabled
                      label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.lark.talk.transfer.pc.url', defaultMessage: '桌面端访问地址' })}
                      value={pcUrl}
                      suffix={
                        <YQIcon
                          className={`${prefixCls}-config-suffix`}
                          type="CopyOne"
                          onClick={() => copyText(pcUrl)}
                        />
                      }
                    />
                    <TextField
                      colSpan={2}
                      disabled
                      label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.lark.talk.transfer.mobile.url', defaultMessage: '移动端访问地址' })}
                      value={mobileUrl}
                      suffix={
                        <YQIcon
                          className={`${prefixCls}-config-suffix`}
                          type="CopyOne"
                          onClick={() => copyText(mobileUrl)}
                        />
                      }
                    />
                  </Form>
                </div>
              );
            })}
            {/* {infoDetailDataSet?.current?.getCascadeRecords?.('jsonConfig.feishuTalkChatTransfers')?.map(feishuChatRecord => {
              const feishuTalkParams = {
                noBreadcrumb: true,
                promptTemplateCode: feishuChatRecord.get('feishuTalkChatTransferTicketPrompt.code'),
                transferFieldCode: feishuChatRecord.get('feishuTalkChatTransferTicketServiceItemField.code'),
                itemId: feishuChatRecord.get('feishuTalkChatTransferTicketServiceItem.id'),
                tenant: domain,
                type: 'feishu_talk',
                appid: record.get('appId'),
              };
              const feishuUrl = `${getEnv('MB_HOST')}/pages/create-order/index?${queryString.stringify(feishuTalkParams)}`;
              const pcUrl = `https://applink.feishu.cn/client/web_app/open?appId=${record.get('appId')}&mode=sidebar&reload=true&lk_target_url=${encodeURIComponent(feishuUrl)}`;
              const mobileUrl = `https://applink.feishu.cn/client/web_app/open?appId=${record.get('appId')}&mode=appCenter&reload=true&lk_target_url=${encodeURIComponent(feishuUrl)}`;
              return (
                <div className="iam-open-login-config-config-body-user-content yq-mt-16">
                  <Form
                    className="yq-mt-16"
                    disabled={!edit}
                    // header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.ding.talk.record.transfer.order', defaultMessage: '聊天记录转工单' })}
                    labelWidth={100}
                    columns={2}
                    labelLayout="horizontal"
                    record={feishuChatRecord}
                  >
                    <Lov name="feishuTalkChatTransferTicketServiceItem" />
                    <Lov name="feishuTalkChatTransferTicketServiceItemField" />
                    <Lov name="feishuTalkChatTransferTicketPrompt" />
                    <div colSpan={2} />
                    <TextField
                      colSpan={2}
                      disabled
                      label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.ding.talk.transfer.pc.url', defaultMessage: '桌面端访问地址' })}
                      value={pcUrl}
                      suffix={
                        <YQIcon
                          className={`${prefixCls}-config-suffix`}
                          type="CopyOne"
                          onClick={() => copyText(pcUrl)}
                        />
                      }
                    />
                    <TextField
                      colSpan={2}
                      disabled
                      label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.ding.talk.transfer.mobile.url', defaultMessage: '移动端访问地址' })}
                      value={mobileUrl}
                      suffix={
                        <YQIcon
                          className={`${prefixCls}-config-suffix`}
                          type="CopyOne"
                          onClick={() => copyText(mobileUrl)}
                        />
                      }
                    />
                  </Form>
                </div>
              );
            })} */}
          </>
        );
      default:
        return null;
    }
  }

  useEffect(() => {
    if (infoDetailDataSet.current && !infoDetailDataSet?.children?.todoConfig?.current) {
      // todoConfig的dataset自动创建
      infoDetailDataSet?.children?.todoConfig.create({});
    }
  }, []);
  const renderFormContent = useCallback(() => {
    if (infoDetailDataSet?.current) {
      const appType = infoDetailDataSet?.current?.get('type');
      if (!infoDetailDataSet.current?.get('config.passwordFlag')) {
        infoDetailDataSet.current?.set('config.passwordFlag', false);
      }
      let callbackUrl;
      if (appType === 'ding_talk') {
        callbackUrl = `${getEnv('API_HOST')}/ecos/v1/${tenantId}/ding/callback/${infoDetailDataSet.current?.get('appId')}/event`;
      } else if (appType === 'lark') {
        callbackUrl = `${getEnv('API_HOST')}/ecos/v1/${tenantId}/lark/callback/${infoDetailDataSet.current?.get('appId')}/event`;
      }
      const todoConfigDs = infoDetailDataSet?.children?.todoConfig;
      return (
        <div className={`${prefixCls}-config-body`}>
          {/* 基本信息 */}
          <Form disabled={!edit} header={intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' })} labelWidth={100} columns={2} labelLayout="horizontal" dataSet={infoDetailDataSet}>
            <TextField name="name" autoFocus label={intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' })} />
            <Select name="type" disabled />
            {appType === 'other' && <TextField name="code" disabled={id !== '0'} />}
            <TextField name="appId" />
            <TextField name="appSecret" />
            {appType === 'wechat_agent' && (
              <>
                <TextField name="wechatAgentConfig.description" colSpan={2} />
                <TextField name="wechatAgentConfig.agentCheckToken" />
                <TextField name="wechatAgentConfig.agentCheckKey" />
                <TextField disabled name="wechatAgentConfig.agentCallbackUrl" />
              </>
            )}
            {appType === 'wechat_enterprise' && (
              <>
                <TextField name="agentId" />
                {renderWechatEnterpriseTrustDomain()}
                {/* 通知窗口 */}
                <Switch name="noticeFlag" />

                {/* 私有部署地址 */}
                <TextField name="authUrl" label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.private.deployment.address', defaultMessage: '私有化部署地址' })} />
              </>
            )}
            {appType === 'ding_talk' && (
              <>
                <TextField name="agentId" />
                <TextField name="dingTalkCorpId" />
                {renderDingtalkCallbackUrl()}
                {infoDetailDataSet?.current?.get('dingTalkRobotFlag') && <TextField name="dingTalkRobotCode" />}
                {infoDetailDataSet?.current?.get('dingTalkRobotFlag') && <TextField name="dingTalkCardId" />}
                {/* 启用酷应用 */}
                <SelectBox name="dingTalkRobotFlag" renderer={!edit ? ({ value }) => intl.formatMessage({ id: value ? 'yes' : 'no' }) : undefined} />
              </>
            )}
            {appType === 'lark' && renderLarkRedirectUrl()}
            {appType === 'microsoft' && renderTeamsRedirectUrl()}
            {appType === 'lark' && (
              <TextField
                name="authUrl"
                label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.private.deployment.address', defaultMessage: '私有化部署地址' })}
                help={intl.formatMessage({ id: 'iam.openLoginConfig.desc.auth.url.help', defaultMessage: '用于私有化部署飞书应用的情况，但需注意私有化飞书应用的接口参数及逻辑需与公有版飞书保持一致。' })}
              />
            )}
            {/* 作为登陆方式 */}
            {(appType !== 'wechat_agent' && appType !== 'wechat_mini_program') && <SelectBox name="enabledLogin" renderer={!edit ? ({ value }) => intl.formatMessage({ id: value ? 'yes' : 'no' }) : undefined} />};
            {/* 是否启动初始化密码 */}
            {usualOpenAppFlag && <SelectBox name="config.passwordFlag" renderer={!edit ? ({ value }) => intl.formatMessage({ id: value ? 'yes' : 'no' }) : undefined} />}
            {/* 初始化密码 */}
            {usualOpenAppFlag && infoDetailDataSet?.current?.get('config.passwordFlag') && <Password name="config.passwordField" autoComplete="new-password" restrict={/[^\x00-\xff ]/gi} />}
            {appType === 'other' && <TextField name="authUrl" />}
            {appType === 'other' && <TextField name="tokenUrl" />}
            {appType === 'other' && <TextField name="userInfoUrl" />}
            {appType === 'other' && <TextField name="scope" />}
            {appType === 'other' && <TextField name="uuidField" />}
          </Form>
          {/* 回调信息，钉钉和飞书】 */}
          {['ding_talk', 'lark'].includes(appType) && (
            <Form
              style={{ marginTop: '20px' }}
              disabled={!edit}
              header={(
                <div className={styles.sectionHeader}>
                  <span>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.callback', defaultMessage: '回调信息' })}</span>
                  <Tooltip title={intl.formatMessage({ id: 'iam.openLoginConfig.desc.callback.tip', defaultMessage: '当要通过三方应用进行人员信息的实时同步、实现审批集成时，需要填写回调信息' })}>
                    <span className={styles.sectionHeaderIcon}>
                      <YQIcon type="help" size={12} />
                    </span>
                  </Tooltip>
                </div>
              )}
              labelWidth={100}
              columns={2}
              labelLayout="horizontal"
              dataSet={infoDetailDataSet}
            >
              <TextField name="eventKey" />
              <TextField name="eventToken" />
              <TextField
                disabled
                label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.callback.url', defaultMessage: '回调URL' })}
                value={callbackUrl}
                suffix={
                  <YQIcon
                    className={`${prefixCls}-config-suffix`}
                    type="CopyOne"
                    onClick={() => copyText(callbackUrl)}
                  />
                }
              />
            </Form>
          )}
          {/* 链接生成器 */}
          {['wechat_enterprise', 'lark', 'ding_talk', 'microsoft'].includes(appType) && (
            <Form style={{ marginTop: '20px' }} disabled={!edit} header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.url.generator.iam', defaultMessage: '链接生成器' })} labelWidth={100} columns={2} labelLayout="horizontal" dataSet={infoDetailDataSet}>
              {appType === 'lark' && renderLarkUrl()}
              {appType === 'wechat_enterprise' && renderWechatEnterpriseUrl()}
              {appType === 'ding_talk' && renderDingtalkUrl()}
              {appType === 'microsoft' && renderTeamsUrl()}
            </Form>
          )}

          {appType === 'wechat_agent' && <Form className="yq-mt-16" disabled={!edit} header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.wechat.agent.account.setting', defaultMessage: '客服账号设置' })} labelWidth={100} columns={2} labelLayout="horizontal" dataSet={infoDetailDataSet}>
            <Output name="wechatAgentConfig.agentAvatar" renderer={weChatAgentAvatar} />
            <TextField name="wechatAgentConfig.agentName" />
          </Form>}

          {appType === 'wechat_agent' && <Form className="yq-mt-16" disabled={!edit} header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.wechat.agent.conversation.setting', defaultMessage: '会话设置' })} labelWidth={100} columns={2} labelLayout="horizontal" dataSet={infoDetailDataSet}>
            <TextArea name="wechatAgentConfig.agentWelcomeMsg" />
            <TextArea name="wechatAgentConfig.agentMenuBootMsg" />
            <TextArea name="wechatAgentConfig.agentOfflineMsg" />
            <TextArea name="wechatAgentConfig.agentResolveMsg" />
          </Form>}

          {/* 智能助理 */}
          {(appType === 'wechat_enterprise' || appType === 'ding_talk') && (
            <AppBot
              appType={appType}
              record={infoDetailDataSet.current}
              intl={intl}
              disabledEdit={!edit}
              search={search}
              intlPrefix={intlPrefix}
              tenantId={tenantId}
            />
          )}

          {/* 聊天记录转工单 */}
          {renderTalkRecordTransferOrder()}

          {/* 更多设置 */}
          {['wechat_enterprise'].includes(appType) && (
            <Form style={{ marginTop: '20px' }} disabled={!edit} header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.frame.name.more.setting', defaultMessage: '更多设置' })} columns={2} labelWidth={100} labelLayout="horizontal" dataSet={infoDetailDataSet}>
              {/* 快捷联系方式 */}
              <Switch colSpan={2} name="socialContactRedirectFlag" />
              <Alert
                // className={styles.alert}
                showIcon
                iconType="info"
                type="info"
                message={intl.formatMessage({ id: 'iam.openLoginConfig.desc.frame.setting.result.show', defaultMessage: '效果展示' })}
              />
              <div />
              <div>
                {language.includes('en') ? (
                  <img alt="" style={{ border: '1px rgba(0,0,0,0.15) solid', width: '400px', borderRadius: '5px' }} src={infoDetailDataSet?.current?.get('socialContactRedirectFlag') ? contactRedirectFlagOnEn : contactRedirectFlagOffEn} />
                ) : (
                  <img alt="" style={{ border: '1px rgba(0,0,0,0.15) solid', width: '400px', borderRadius: '5px' }} src={infoDetailDataSet?.current?.get('socialContactRedirectFlag') ? contactRedirectFlagOnCn : contactRedirectFlagOffCn} />
                )}
              </div>

            </Form>
          )}
          {/* 钉钉代办 */}
          {appType === 'ding_talk' && (
            <Form style={{ marginTop: '30px' }} disabled={!edit} header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.ding.talk.todo', defaultMessage: '钉钉待办' })} columns={2} labelWidth={100} labelLayout="horizontal" dataSet={todoConfigDs}>
              <Switch colSpan={1} name="todoFlag" />
              <Lov colSpan={1} name="todoCreatorCode" />
              <SelectBox name="taskChangeStatus">
                <SelectBox.Option value={1}>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.ding.talk.todo.complete', defaultMessage: '完成' })}</SelectBox.Option>
                <SelectBox.Option value={0}>{intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' })}</SelectBox.Option>
              </SelectBox>
              <SelectBox name="taskFinalStatus">
                <SelectBox.Option value={1}>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.ding.talk.todo.complete', defaultMessage: '完成' })}</SelectBox.Option>
                <SelectBox.Option value={0}>{intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' })}</SelectBox.Option>
              </SelectBox>
              <NumberField name="deadline" step={1} addonAfter={intl.formatMessage({ id: 'zknow.common.model.day', defaultMessage: '天' })} />
              <Switch colSpan={1} name="notifiedFlag" />
              <Lov
                name="businessType"
                colSpan={2}
                multiple
                onChange={(arr) => {
                  arr.forEach(item => {
                    if (todoConfigDs.children.configTemplate.toData().every(config => config.code !== item.code)) {
                      todoConfigDs.children.configTemplate.create({
                        businessTypeName: item.name,
                        businessObjectId: item.businessObjectId,
                        code: item.code,
                        content: {
                          zh_CN: '',
                          en_US: '',
                        },
                      }, 0);
                    }
                  });
                }}
              />
              {todoConfigDs?.current?.get('businessType') && todoConfigDs?.current?.get('businessType').length > 0 && <TodoDescription label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.ding.talk.todo.config.template', defaultMessage: '配置模板' })} intl={intl} edit={edit} colSpan={2} tenantId={tenantId} todoConfigDataSet={todoConfigDs} />}
            </Form>
          )}
          {/* 审批设置，目前只飞书有此功能, 新建时不可配置 */}
          {appType === 'lark' && id !== '0' && (
            <Approval
              openId={id}
              edit={edit}
              openAppStore={openAppStore}
              approveDataset={approveDataset}
            />)}
          {/* {appType === 'teams' && (
            <Form style={{ marginTop: '30px' }} disabled={!edit} header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.teams.notic', defaultMessage: '通知设置' })} columns={2} labelWidth={100} labelLayout="horizontal" dataSet={todoConfigDs}>
              <SelectBox name='noticEntry' />
            </Form>
          )} */}
        </div>
      );
    }
  }, [infoDetailDataSet?.current, !edit, usualOpenAppFlag]);

  function copyText(text) {
    copy(text);
    message.success(intl.formatMessage({ id: 'zknow.common.success.copy', defaultMessage: '复制成功' }));
  }

  // 渲染正文内容
  function renderContent() {
    const appType = infoDetailDataSet?.current?.get('type');
    const quickType = infoDetailDataSet?.current?.get('quickType');
    if (appType === 'quick_authentication' && (quickType === 'CLIENT' || quickType === 'EBS')) {
      return <SapEbsFormContent
        prefixCls={prefixCls}
        disabledEdit={!edit}
        intl={intl}
        intlPrefix={intlPrefix}
        infoDetailDataSet={infoDetailDataSet}
        copyText={copyText}
        tenantId={tenantId}
        refresh={refresh}
      />;
    } else if (appType === 'quick_authentication') {
      return <SdkHzeroFormContent
        prefixCls={prefixCls}
        disabledEdit={!edit}
        intl={intl}
        intlPrefix={intlPrefix}
        infoDetailDataSet={infoDetailDataSet}
        copyText={copyText}
        tenantId={tenantId}
        refresh={refresh}
      />;
    } else if (appType === 'choerodon') {
      return <ChoerodonContent
        prefixCls={prefixCls}
        disabledEdit={!edit}
        intl={intl}
        intlPrefix={intlPrefix}
        infoDetailDataSet={infoDetailDataSet}
        copyText={copyText}
        tenantId={tenantId}
        refresh={refresh}
        choerodonUserDataSet={choerodonUserDataSet}
        history={history}
      />;
    } else if (appType === 'wechat_open_account') {
      return <WechatOpenAccountFormContent
        prefixCls={prefixCls}
        disabledEdit={!edit}
        intl={intl}
        intlPrefix={intlPrefix}
        infoDetailDataSet={infoDetailDataSet}
        copyText={copyText}
        tenantId={tenantId}
        refresh={refresh}
      />;
    }
    return renderFormContent();
  }

  return (
    <TabPage className={`${prefixCls}-config`}>
      <Header
        // backPath={`/iam${type === 'site' ? '/site' : ''}/open_app_config${search}`}
        actionsList={actionsList}
        dataSet={infoDetailDataSet}
      // onRefresh={refresh}
      >
        <h1>{intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' })}</h1>
        <div>{actionButtons()}</div>
      </Header>
      <Content>
        {renderContent()}
      </Content>
    </TabPage>
  );
}

export default inject('AppState')(observer(ListView));
