import React from 'react';
import { observer } from 'mobx-react-lite';
import {
  Form,
  Lov,
  Select,
  Switch,
  Tabs,
  TextArea,
  TextField,
  SelectBox,
  NumberField,
} from 'choerodon-ui/pro';
import { getAccessToken } from '@zknow/utils';
import { ExternalComponent, Icon as YQIcon, YqAvatar } from '@zknow/components';
import { axios } from '@yqcloud/apps-master';
import OpenFunctionList from './sdk-hzero';
import '../../index.less';

export async function uploadImg(base64, tenantId = 0) {
  const convertBase64UrlToBlob = (urlData, fileType) => {
    const bytes = window.atob(urlData.split(',')[1]);
    const buffer = new ArrayBuffer(bytes.length);
    const unit8Array = new Uint8Array(buffer);
    for (let i = 0; i < bytes.length; i += 1) {
      unit8Array[i] = bytes.charCodeAt(i);
    }
    return new Blob([buffer], { type: fileType });
  };

  try {
    if (base64 && /^data:image\//.test(base64)) {
      const fileType = base64
        .split(',')[0]
        ?.replace('data:', '')
        ?.replace(';base64', '');
      const imgFile = convertBase64UrlToBlob(base64, fileType);
      const formData = new FormData();
      formData.append('file', imgFile, fileType?.replace('/', '.'));
      // 使用axios
      const res = await axios({
        url: `${window._env_.API_HOST}/hfle/yqc/v1/${tenantId}/files/secret-multipart`,
        method: 'post',
        headers: {
          'Access-Control-Allow-Origin': '*',
          Authorization: getAccessToken(),
          'x-tenant-id': tenantId,
        },
        processData: { 'Content-Type': 'multipart/form-data' },
        data: formData,
      });
      const { fileKey } = res;
      return fileKey;
    }
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error(e);
  }
}

const ImageCropper = (props) => (
  <ExternalComponent
    {...props}
    system={{ scope: 'itsm', module: 'ImageCropper' }}
  />
);

const { TabPane } = Tabs;

const SdkHzeroFormContent = (props) => {
  const {
    prefixCls,
    disabledEdit,
    intl,
    intlPrefix,
    infoDetailDataSet,
    copyText,
    tenantId,
    refresh,
  } = props;
  return (
    <div className={`${prefixCls}-config-body`}>
      <Form
        disabled={disabledEdit}
        header={intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' })}
        labelWidth={100}
        columns={2}
        labelLayout="horizontal"
        dataSet={infoDetailDataSet}
      >
        <TextField name="name" autoFocus />
        <Select
          name="quickType"
          disabled
          label={intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.quick.type', defaultMessage: '应用类型' })}
        />
        <Switch label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.auto.create.user.flag', defaultMessage: '是否开启自动创建用户' })} help={intl.formatMessage({ id: 'iam.openLoginConfig.desc.auto.create.user.flag.help', defaultMessage: '开启后如果第三方传入的用户信息在燕千云中无法匹配已存在的用户，会自动创建一个新用户并免登录' })} name="autoCreateUserFlag" />
        <Switch label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.anonymous.creator.flag', defaultMessage: '创建用户是否匿名' })} name="anonymousCreatorFlag" />
        <TextField
          name="appId"
          disabled
          label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.token', defaultMessage: 'Token' })}
          suffix={
            <YQIcon
              className={`${prefixCls}-config-suffix`}
              type="CopyOne"
              onClick={() => copyText(infoDetailDataSet.current?.get('appId'))}
            />
          }
        />
        <Lov name="jsonConfig.userInfoInterface" />
        <TextField
          name="appSecret"
          label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.user.info.secret', defaultMessage: '人员信息密钥' })}
          suffix={
            <YQIcon
              className={`${prefixCls}-config-suffix`}
              type="CopyOne"
              onClick={() => copyText(infoDetailDataSet.current?.get('appSecret'))}
            />
          }
        />
        {['EBS', 'SAP', 'CLIENT'].includes(infoDetailDataSet?.current?.get('quickType')) && <Switch name="freeLoginFlag" />}
        <TextArea
          name="jsonConfig.description"
          newLine
          colSpan={2}
          rows={1}
          autoSize={{ minRows: 1, maxRows: 4 }}
          resize="height"
        />
      </Form>
      <Form
        disabled={disabledEdit}
        header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.ticket.info', defaultMessage: '提单入口信息' })}
        labelWidth={100}
        columns={2}
        labelLayout="horizontal"
        dataSet={infoDetailDataSet}
        className="yq-mt-16"
      >
        <TextField name="jsonConfig.name" disabled />
        <TextField name="jsonConfig.displayName" />
        <div style={{ position: 'relative', height: '58px', width: '58px' }} name="jsonConfig.icon">
          <YqAvatar
            style={{ cursor: disabledEdit ? 'default' : 'pointer', borderRadius: 'unset' }}
            size={58}
            src={infoDetailDataSet.current?.get('jsonConfig.icon')}
          />
          <div className={`${prefixCls}-config-body-image-copper`}>
            {disabledEdit ? null : (
              <ImageCropper
                size="fullFilled"
                onChange={(base64) => {
                  uploadImg(base64, tenantId).then((res) => {
                    infoDetailDataSet.current?.set('jsonConfig.icon', res);
                  });
                }}
                preview={false}
                isCircle={false}
                aspectRatio={1} // 1代表1:1 2代表2:1 undefined代表自由
              />
            )}
          </div>
        </div>
        <SelectBox name="jsonConfig.iconSize" />
        {infoDetailDataSet?.current?.get('jsonConfig.iconSize') === 'SMALL_SIZE'
        && <SelectBox name="jsonConfig.iconCorner" />}
        <Form.Item label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.icon.position', defaultMessage: '图标位置' })}>
          <>
            <NumberField name='jsonConfig.iconX' />
            <span style={{ margin: '0 8px' }}>X</span>
            <NumberField name="jsonConfig.iconY" />
            <span style={{ margin: '0 8px' }}>Y</span>
          </>
        </Form.Item>
        {/* <Output name="jsonConfig.icon" renderer={ticketIcon} /> */}
      </Form>
      <Tabs type="card" className={`${prefixCls}-tabs`}>
        <TabPane tab={intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.function', defaultMessage: '开放功能' })} key="function">
          <OpenFunctionList refresh={refresh} />
        </TabPane>
        {/* <TabPane tab={intl.formatMessage({ id: 'iam.openLoginConfig.desc.page.style', defaultMessage: '页面风格' })} key="style"> */}
        {/* </TabPane> */}
      </Tabs>
    </div>
  );
};

export default observer(SdkHzeroFormContent);
