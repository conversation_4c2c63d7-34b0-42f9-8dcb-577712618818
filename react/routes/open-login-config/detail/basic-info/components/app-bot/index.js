import React, { useMemo, useContext, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import classNames from 'classnames';
import { Output, Switch, Form, Lov, Modal, TextField, message } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import { axios } from '@yqcloud/apps-master';
import Store from '../../../../stores';
import styles from './AppBot.module.less';

const key = Modal.key();

const AppBot = (props) => {
  const { disabledEdit, intl, record, search, intlPrefix, tenantId, appType } = props;

  const { notifyDataSet } = useContext(Store);

  const params = useMemo(() => new URLSearchParams(search), [search]);

  const header = appType === 'ding_talk' ? intl.formatMessage({ id: 'iam.openLoginConfig.detail.dingTalkAssistant', defaultMessage: '钉助理' }) : intl.formatMessage({ id: 'iam.openLoginConfig.detail.wechatEnterpriseAssistant', defaultMessage: '企微助理' });

  const renderLabel = useCallback((intlId, message) => {
    return <span className={styles.botLovLabel}>{intl.formatMessage({ id: intlId, defaultMessage: message })}</span>;
  }, []);

  const handleNewBot = () => {
    const solutionId = params.get('solutionId');
    const url = `${window.location.origin}/#/intelligent/assistant?tenantId=${tenantId}&solutionId=${solutionId}`;
    window.open(url, '_blank');
  };

  const handleOpenNotify = () => {
    let notifyRecord = notifyDataSet.current;
    if (!notifyRecord) {
      notifyRecord = notifyDataSet.create({
        content: appType === 'ding_talk' ? intl.formatMessage({ id: 'iam.openLoginConfig.detail.sendContentDefaultValue.dingTalk', defaultMessage: '钉助理功能上线啦！您可直接在当前会话窗口与我进行问答哦～' }) : intl.formatMessage({ id: 'iam.openLoginConfig.detail.sendContentDefaultValue.wechatEnterprise', defaultMessage: '企微助理功能上线啦！您可直接在当前会话窗口与我进行问答哦～' }),
      });
      notifyDataSet.current = notifyRecord;
    }
    Modal.open({
      key,
      title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.notification.function', defaultMessage: '通知内容' }),
      style: { width: 800 },
      okText: intl.formatMessage({ id: 'zknow.common.button.send', defaultMessage: '发送' }),
      // autoCenter: true,
      children: (
        <Form dataSet={notifyDataSet} labelWidth={90} labelAlign="left">
          <TextField name="content" label={renderLabel('iam.openLoginConfig.detail.sendContentLabel', '发送内容')} />
          <Lov multiple name="range" label={renderLabel('iam.openLoginConfig.detail.sendRangeLabel', '发送范围')} />
        </Form>
      ),
      onOk: async () => {
        const contentValidate = notifyRecord.get('content');
        const rangeValidate = notifyRecord.get('range');
        if (!contentValidate) {
          message.error(intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.send.content.label.validate', defaultMessage: '请填写发送内容' }));
          return false;
        } else if (!rangeValidate) {
          message.error(intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.send.range.label.validate', defaultMessage: '请填写发送范围' }));
          return false;
        } else {
          const closeFlag = { close: false };
          await Modal.warning({
            title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.confirm.to.send', defaultMessage: '确认发送?' }),
            children: <span style={{ color: 'rgba(18,39,77,0.65)' }}>{appType === 'ding_talk' ? intl.formatMessage({ id: 'iam.openLoginConfig.detail.sendConfirmTips.dingTalk', defaultMessage: '点击确认后，发送范围内的用户将在燕千云-钉助理上接收到消息通知' }) : intl.formatMessage({ id: 'iam.openLoginConfig.detail.sendConfirmTips.wechatEnterprise', defaultMessage: '点击确认后，发送范围内的用户将在燕千云-企微助理上接收到消息通知' })}</span>,
            autoCenter: true,
            cancelButton: true,
            onOk: async () => {
              if (appType === 'ding_talk') {
                const res = await axios.post(`/ecos/v1/${tenantId}/ding/send/text`, {
                  text: contentValidate,
                  openAppId: record?.get('config.openAppId'),
                  deptId: rangeValidate.map(r => r?.dept_id),
                });
                if (!res?.failed) {
                  message.success(intl.formatMessage({ id: 'iam.openLoginConfig.desc.sent.successfully', defaultMessage: '发送成功' }));
                  closeFlag.close = true;
                }
              } else {
                const res = await axios.post(`/ecos/v1/${tenantId}/wecom/send/text`, {
                  text: contentValidate,
                  role: 'dept',
                  openAppId: record?.get('config.openAppId'),
                  targetList: rangeValidate.map(r => r?.id),
                });
                if (!res?.failed) {
                  message.success(intl.formatMessage({ id: 'iam.openLoginConfig.desc.sent.successfully', defaultMessage: '发送成功' }));
                  closeFlag.close = true;
                }
              }
            },
            onCancel: () => {
              closeFlag.close = false;
            },
          });
          return closeFlag.close;
        }
      },
    });
  };

  const handleBotChange = (value) => {
    if (value?.id) {
      record.set('botId', value.id);
      record.set('botName', value.name);
    }
  };

  const renderBot = () => (
    <div className={styles.botLovWrapper}>
      <Lov name="notifyBot" className={classNames(styles.botLov, { [styles.botLovDisabled]: disabledEdit })} onChange={handleBotChange} />
      {!disabledEdit && (
        <div className={styles.createBotWrapper} onClick={handleNewBot}>
          <Icon type="share" size={18} fill="currentColor" />
          <span>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.create.assistant', defaultMessage: '创建智能助理' })}</span>
        </div>
      )}
    </div>
  );

  const renderNotify = () => {
    return (
      <div className={styles.assistantWrapper}>
        <Switch name="assistantFlag" />
        {record?.get('assistantFlag') && !disabledEdit ? (
          <div className={styles.notify} onClick={handleOpenNotify}>
            <Icon className={styles.notifyIcon} type="send" fill="currentColor" size={18} />
            <span>{appType === 'ding_talk' ? intl.formatMessage({ id: 'iam.openLoginConfig.detail.send.notifications.through.dingTalk.assistant', defaultMessage: '通过钉助理发送通知' }) : intl.formatMessage({ id: 'iam.openLoginConfig.detail.send.notifications.through.wechatEnterprise.assistant', defaultMessage: '通过企微助理发送通知' })}</span>
          </div>
        ) : null}
      </div>
    );
  };

  return (
    <>
      <Form
        className="yq-mt-16"
        disabled={disabledEdit}
        header={header}
        labelWidth={100}
        columns={2}
        labelLayout="horizontal"
        record={record}
      >
        <Output name="assistantFlag" renderer={renderNotify} />
        {record?.get('assistantFlag') && <Output label={renderLabel('iam.openLoginConfig.detail.smartAssistant', '智能助理')} renderer={renderBot} />}
        {record?.get('assistantFlag') && appType === 'ding_talk' && <TextField name="templateId" />}
        {record?.get('assistantFlag') && appType === 'ding_talk' && <TextField name="dingRobotCode" />}
      </Form>
    </>
  );
};

export default observer(AppBot);
