@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.botLovWrapper {
  display: flex;
  align-items: center;
}

.botLov {
  flex-grow: 1;

  &.botLovDisabled {
    margin-right: 0.9rem;
  }
}

.createBotWrapper {
  flex-shrink: 0;
  color: @primary-color;
  display: flex;
  align-items: center;
  margin: 0 24px;
  cursor: pointer;
}

.botLovLabel {
  &::before {
    display: inline-block;
    margin-right: 0.04rem;
    color: #d50000;
    font-size: .14rem;
    line-height: 1;
    content: '*';
  }
}

.assistantWrapper {
  display: flex;
  align-items: center;
}

.notify {
  min-width: 188px;
  height: 32px;
  margin-left: 40px;
  padding: 0 12px;
  background: @primary-x;
  border-radius: 4px;
  border: 1px solid #dbe9fd;
  display: inline-flex;
  align-items: center;
  font-size: 16px;
  cursor: pointer;
  color: @primary-color;
  font-size: 14px;

  .notifyIcon {
    margin-right: 10px;
    flex-shrink: 0;
  }
}
