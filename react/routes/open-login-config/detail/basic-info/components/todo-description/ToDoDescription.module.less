@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.header {
  display: flex;
  margin-top: 20px;
  margin-bottom: 10px;
  justify-content: space-between;
  align-items: center;
}
.previewFrame {
  padding: 0.16rem;
  background: @yq-fill-2;
  border-radius: 0.08rem;
  min-height: 0.55rem;
  margin-bottom: 10px;
  .previewTitle {
    font-size: 16px;
    font-weight: 500;
  }
}
.textArea {
  resize: none;
  outline: none;
  height: 600px;
  border: 0.01rem solid #d7e2ec;
  cursor: text;
  border-radius: 0.04rem;
  padding: 4px 8px 4px 8px;
  &:hover {
    border-color: rgba(18, 39, 77, 0.65);
  }
  &:focus {
    border-color: @primary-color;
  }
  &::selection {
    background: #e7f3ff;
    color: inherit;
  }
}
.tabArea {
  height: 600px;
  overflow: scroll;
}
