import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import {
  Modal,
  SelectBox,
  Select,
  Tabs,
} from 'choerodon-ui/pro';
import { Button } from '@zknow/components';
import EditModal from './EditModal';
import styles from './ToDoDescription.module.less';

const { TabPane } = Tabs;

const TodoDescription = (props) => {
  const { intl, edit, tenantId, todoConfigDataSet: dataSet } = props;

  const editorDs = dataSet?.children?.configTemplate;

  const handleClickEdit = (code, businessObjectId, name) => {
    const configTemplate = editorDs?.toData();
    editorDs.current = configTemplate?.find((item) => item.code === code);

    Modal.open({
      title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.ding.talk.todo.config.template', defaultMessage: '配置模板' }),
      drawer: true,
      style: { width: 1000 },
      children: (
        <EditModal
          language={language}
          intl={intl}
          editorDs={editorDs}
          tenantId={tenantId}
          businessObjectId={businessObjectId}
        />
      ),
    });
  };

  const handleChangeTab = (code) => {
    const targetRecord = editorDs?.find((item) => item.get('code') === code);
    editorDs.current = targetRecord;
  };

  const [language, setLanguage] = useState('zh_CN');
  return (
    <Tabs type="card" flex col onChange={(code) => handleChangeTab(code)}>
      {editorDs.map((record) => {
        const { businessTypeName, code, businessObjectId } = record.toData();
        return (
          <TabPane tab={businessTypeName} key={code}>
            <div className={styles.header}>
              <SelectBox
                mode="button"
                value={language}
                onChange={(v) => setLanguage(v)}
                className="language"
              >
                <SelectBox.Option value="zh_CN">{intl.formatMessage({ id: 'iam.openLoginConfig.desc.zh.cn', defaultMessage: '中文' })}</SelectBox.Option>
                <SelectBox.Option value="en_US">{intl.formatMessage({ id: 'iam.openLoginConfig.desc.en.us', defaultMessage: '英文' })}</SelectBox.Option>
              </SelectBox>
              {edit && (
                <Button
                  funcType="raised"
                  color="primary"
                  icon="icon-edit"
                  className="edit"
                  onClick={() => handleClickEdit(code, businessObjectId, businessTypeName)}
                >
                  {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
                </Button>
              )}
            </div>
            <div className={styles.previewFrame} style={{ whiteSpace: 'pre-wrap' }}>
              {record?.get(`content.${language}`)}
            </div>
          </TabPane>
        );
      })}
    </Tabs>
  );
};

export default observer(TodoDescription);
