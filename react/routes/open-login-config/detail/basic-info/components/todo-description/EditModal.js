import React, { useState, useRef, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import {
  message,
  Form,
  Tabs,
} from 'choerodon-ui/pro';
import { ExternalComponent } from '@zknow/components';
import styles from './ToDoDescription.module.less';

const { TabPane } = Tabs;

const ParamsSelector = (props) => (
  <ExternalComponent
    {...props}
    system={{ scope: 'hmsg', module: 'params-selector' }}
  />
);

const EditModal = (props) => {
  const { language: fatherLang, intl, editorDs, tenantId, businessObjectId } = props;
  const textAreaZHRef = useRef();
  const textAreaENRef = useRef();
  const [language, setLanguage] = useState(fatherLang);
  const tabChange = (v) => {
    setLanguage(v);
  };
  useEffect(() => {
    if (editorDs?.current?.get('content').length === 0) {
      editorDs?.current?.set('content', {
        zh_CN: '',
        en_US: '',
      });
      // debugger;
    }
  });

  const handleChange = (e) => {
    editorDs?.current?.set(`content.${language}`, e.target?.value);
  };

  const renderEditor = (_lang) => (
    <Form labelLayout="none">
      <textarea
        value={editorDs?.current?.get(`content.${language}`)}
        className={styles.textArea}
        onChange={handleChange}
        ref={_lang === 'zh_CN' ? textAreaZHRef : textAreaENRef}
        style={{ height: '500px' }}
      />
    </Form>
  );

  const handleCopyIntoEditor = (text) => {
    let ref;
    if (language === 'zh_CN') {
      ref = textAreaZHRef;
    } else {
      ref = textAreaENRef;
    }
    const start = ref.current.selectionStart;
    ref.current.focus();
    let currentValue = ref.current.value;
    const currentValueArray = currentValue.split('');
    currentValueArray.splice(start, 0, text);
    currentValue = currentValueArray.join('');
    ref.current.selectionStart = start;
    ref.current.selectionEnd = start + text.length;
    editorDs?.current?.set(`content.${language}`, currentValue);
  };

  return (
    <div>
      <Form columns={3} labelLayout="horizontal">
        <Tabs
          colSpan={2}
          defaultActiveKey={fatherLang}
          onChange={tabChange}
          type="card"
        >
          <TabPane tab={intl.formatMessage({ id: 'iam.openLoginConfig.desc.zh.cn', defaultMessage: '中文' })} key="zh_CN">
            {renderEditor('zh_CN')}
          </TabPane>
          <TabPane tab={intl.formatMessage({ id: 'iam.openLoginConfig.desc.en.us', defaultMessage: '英文' })} key="en_US">
            {renderEditor('en_US')}
          </TabPane>
        </Tabs>
        <Tabs
          colSpan={1}
          defaultActiveKey="dynamic"
          style={{ marginTop: '10px' }}
        >
          <TabPane tab={intl.formatMessage({ id: 'iam.openLoginConfig.desc.ding.talk.todo.dynamic.params', defaultMessage: '动态参数' })} key="dynamic">
            <ParamsSelector
              intl={intl}
              onCopyIntoEditor={handleCopyIntoEditor}
              tableId={businessObjectId}
              tenantId={tenantId}
              isStatic={false}
            />
          </TabPane>
          <TabPane tab={intl.formatMessage({ id: 'iam.openLoginConfig.desc.ding.talk.todo.static.params', defaultMessage: '静态参数' })} key="static">
            <ParamsSelector
              intl={intl}
              onCopyIntoEditor={handleCopyIntoEditor}
              tableId={businessObjectId}
              tenantId={tenantId}
              isStatic
            />
          </TabPane>
        </Tabs>
      </Form>
    </div>
  );
};

export default observer(EditModal);
