import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, Lov, Select, SelectBox } from 'choerodon-ui/pro';
import FieldsDefault from '../FieldsDefault';
import MapFields from '../MapFields';

// 用于SAP EBS
export const OpenModalForm = observer(({ record, intl, drawer = false }) => {
  const header = drawer ? intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' }) : null;
  const columns = drawer ? 2 : 1;

  return (
    <Form
      header={header}
      record={record}
      labelLayout="horizontal"
      columns={columns}
      labelWidth="98"
    >
      <TextField name="name" />
      <SelectBox name="channel" disabled={drawer} />
      <Select name="feature" disabled={drawer} />
      {record.get('feature') === 'create' && <Lov name="serviceItem" />}
      {record.get('feature') === 'view' && <Lov name="view" />}
      {record.get('feature') === 'bot' && (
        <React.Fragment>
          <Lov name="bot" />
          <SelectBox name="method" />
        </React.Fragment>
      )}
      {record.get('feature') === 'knowledge' && <Lov name="knowledge" />}
      {record.get('feature') === 'link' && <TextField name="link" />}
      {/* <TextField name="link" /> */}
    </Form>
  );
});

export default observer((props) => {
  const { record, intl, prefixCls, intlPrefix, fieldListDataSet, fieldsDataSet } = props;

  const serviceItem = record?.get('serviceItem');

  useEffect(() => {
    const { businessObjectId, variableViewId, variableFlag, ticketTypeId } = serviceItem || {};

    if (businessObjectId && ticketTypeId) {
      fieldListDataSet.setQueryParameter('boId', businessObjectId);
    }
    if (variableViewId && !ticketTypeId) {
      fieldListDataSet.setQueryParameter('viewId', variableViewId);
      fieldListDataSet.setQueryParameter('variableFlag', variableFlag);
    }
    fieldListDataSet.query();
  }, [serviceItem]);

  return (
    <React.Fragment>
      <OpenModalForm record={record} intl={intl} drawer />
      {(!record?.get?.('channel') || record?.get?.('channel') === 'pc') && record?.get('feature') === 'create' && (
        <Form
          labelLayout="horizontal"
          record={record}
          labelWidth="98"
          columns={2}
        >
          <Select name="feedbackLinkType" />
        </Form>
      )}
      {record?.get('feature') === 'create' && (
        <Form
          header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.field.default.value', defaultMessage: '字段默认值' })}
          columns={1}
          labelLayout="horizontal"
        >
          <FieldsDefault
            prefixCls={prefixCls}
            intl={intl}
            intlPrefix={intlPrefix}
            dataSet={fieldsDataSet}
            variableViewId={serviceItem?.ticketTypeId ? undefined : serviceItem?.variableViewId}
            businessObjectId={serviceItem?.ticketTypeId ? serviceItem?.businessObjectId : undefined}
          />
        </Form>
      )}

      {record?.get('feature') === 'create' && (
        <Form
          header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.map.fields', defaultMessage: '映射字段' })}
          columns={1}
          labelLayout="horizontal"
        >
          <MapFields
            prefixCls={prefixCls}
            intl={intl}
            intlPrefix={intlPrefix}
            dataSet={record.dataSet.children.mapFields}
          />
        </Form>
      )}
    </React.Fragment>
  );
});
