import React, { useContext, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import { Table, Modal, message, Tooltip } from 'choerodon-ui/pro';
import { TableHoverAction, ClickText, Button, TableStatus, Icon } from '@zknow/components';
import { v4 as uuidv4 } from 'uuid';
import copy from 'copy-to-clipboard';
import ModalDetail, { OpenModalForm } from './ModalDetail';
import Store from '../../../../stores';
import styles from './ModalDetail.module.less';

const { Column } = Table;
const modalKey = Modal.key();

// SAP和EBS的开放功能列表
export default observer(({ refresh = () => {} }) => {
  const {
    intl, openClientFunctionDataSet: dataSet, infoDetailDataSet,
    prefixCls, intlPrefix, fieldListDataSet, fieldsDataSet,
  } = useContext(Store);

  const handleSave = async () => {
    const validate = await dataSet.validate();
    if (!validate) {
      return false;
    }
    infoDetailDataSet.current?.set('jsonConfig.openClientFunctions', dataSet.toData());
    const res = await infoDetailDataSet.submit();
    if (res && !res.failed) {
      refresh();
      return true;
    }
    return false;
  };

  const handleCreate = () => {
    const record = dataSet.create({ id: uuidv4() });
    Modal.open({
      key: modalKey,
      title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.function.new', defaultMessage: '新建功能' }),
      okText: intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' }),
      style: { width: 520 },
      onClose: () => {
        dataSet.reset();
      },
      onOk: handleSave,
      children: <OpenModalForm record={record} intl={intl} />,
    });
  };

  const handleOpenDetail = (record) => {
    Modal.open({
      key: uuidv4(),
      title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.function.detail', defaultMessage: '开放功能详情' }),
      okText: intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' }),
      style: { width: 800 },
      onClose: () => {
        dataSet.reset();
      },
      onOk: handleSave,
      drawer: true,
      children: (
        <ModalDetail
          record={record}
          intl={intl}
          prefixCls={prefixCls}
          intlPrefix={intlPrefix}
          fieldListDataSet={fieldListDataSet}
          fieldsDataSet={fieldsDataSet}
        />
      ),
    });
  };

  const handleFilter = useCallback((record, _, _dataSet) => {
    const queryRecord = record.dataSet.queryDataSet?.current;
    if (queryRecord?.get('name')) {
      return record?.get('name')?.includes(queryRecord?.get('name'));
    }
    return true;
  }, []);

  const copyText = (text) => {
    copy(text);
    message.success(intl.formatMessage({ id: 'zknow.common.success.copy', defaultMessage: '复制成功' }));
  };

  const renderCreateBtn = () => (
    <Button icon="plus" funcType="raised" color="primary" onClick={handleCreate}>
      {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
    </Button>
  );

  const renderName = ({ record, name }) => (
    <ClickText record={record} onClick={() => handleOpenDetail(record)} valueField={name} />
  );

  const renderLink = ({ record }) => {
    return record?.get('link') ? (
      <Tooltip title={record?.get('link')} popupCls={styles.linkWrapper}>
        <span className={`${prefixCls}-link`}>
          <span className={`${prefixCls}-link-url`}>{record?.get('link')}</span>
          <Icon className={`${prefixCls}-link-copy`} type="CopyOne" onClick={() => copyText(record?.get('link'))} />
        </span>
      </Tooltip>
    ) : null;
  };

  const renderStatus = ({ value }) => (
    <TableStatus
      status={value}
      enabledText={intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' })}
      disabledText={intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' })}
    />
  );

  const renderTableAction = ({ dataSet: _dataSet, record }) => {
    const enabledFlag = record.get('enabledFlag');
    const enabledAction = {
      name: enabledFlag ? intl.formatMessage({ id: 'zknow.common.button.disable' }) : intl.formatMessage({ id: 'zknow.common.button.enable' }),
      icon: enabledFlag ? 'ReduceOne' : 'CheckOne',
      onClick: async () => {
        try {
          record.set('enabledFlag', !enabledFlag);
          infoDetailDataSet.current?.set('jsonConfig.openClientFunctions', _dataSet.toData());
          const res = await infoDetailDataSet.submit();
          if (res && !res?.failed) {
            refresh();
            return true;
          }
          return false;
        } catch (e) {
          return false;
        }
      },
    };

    const deleteAction = {
      name: intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' }),
      icon: 'delete',
      onClick: async () => {
        const oldData = _dataSet.toData();
        const currentRecord = record.toData();
        const newData = oldData.filter(_record => (_record.id !== currentRecord.id));
        infoDetailDataSet.current?.set('jsonConfig.openClientFunctions', newData);
        const res = await infoDetailDataSet.submit();
        if (res && !res?.failed) {
          refresh();
          return true;
        }
        return false;
      },
    };
    return <TableHoverAction actions={enabledFlag ? [enabledAction] : [enabledAction, deleteAction]} record={record} intlBtnIndex={0} />;
  };

  return (
    <Table
      title={intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.function', defaultMessage: '开放功能' })}
      labelLayout="float"
      dataSet={dataSet}
      pristine
      filter={handleFilter}
      buttons={[renderCreateBtn()]}
      queryBarProps={{
        fuzzyQuery: false,
        inlineSearch: false,
        simpleMode: true,
        queryFieldsStyle: { name: { width: 140 } },
      }}
    >
      <Column name="name" width={200} renderer={renderName} />
      <Column name="link" renderer={renderLink} tooltip="none" />
      <Column name="enabledFlag" width={150} renderer={renderStatus} />
      <Column width={150} renderer={renderTableAction} />
    </Table>
  );
});
