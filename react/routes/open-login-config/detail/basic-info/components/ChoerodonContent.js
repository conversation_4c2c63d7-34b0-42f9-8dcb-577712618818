import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import {
  CodeArea,
  Form,
  Select,
  SelectBox,
  Table,
  Tabs,
  TextField,
  Switch,
  ClickText,
} from 'choerodon-ui/pro';
import '../../index.less';
import styles from '../../Detail.module.less';

const { TabPane } = Tabs;
const { Column } = Table;

const ChoerodonContent = (props) => {
  const {
    prefixCls,
    disabledEdit,
    intl,
    intlPrefix,
    infoDetailDataSet,
    choerodonUserDataSet,
    history,
  } = props;

  const handleClickToAppMap = () => {
    history.push({
      pathname: '/iam/open_app_field_mapping/',
      search: undefined,
    });
  };

  const [showJump, setShowJump] = useState(false);
  useEffect(() => {
    const version = infoDetailDataSet?.current?.get('jsonConfig.version');
    try {
      if (version) {
        if (version && parseFloat(version) >= 4) {
          setShowJump(true); // 版本大于等于4才展示
        } else {
          setShowJump(false); // 版本小于4不展示
        }
      }
    } catch {
      //
    }
  }, [infoDetailDataSet?.current?.get('jsonConfig.fieldMappingConfig')]);

  if (infoDetailDataSet?.current) {
    return (
      <div className={`${prefixCls}-config-body`}>
        <Form
          disabled={disabledEdit}
          header={intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' })}
          labelWidth={100}
          columns={2}
          labelLayout="horizontal"
          dataSet={infoDetailDataSet}
        >
          <TextField name="name" autoFocus label={intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' })} />
          <Select name="type" disabled />
          <TextField name="jsonConfig.orgCode" />
          <SelectBox name="enabledFlag" renderer={disabledEdit ? ({ value }) => intl.formatMessage({ id: value ? 'yes' : 'no' }) : undefined} />
          <TextField name="appId" />
          <TextField name="appSecret" />
          <TextField name="jsonConfig.envUrl" />
          <TextField name="jsonConfig.domainUrl" />
          <CodeArea name="jsonConfig.fieldMappingConfig" />
          <SelectBox name="jsonConfig.syncC7nJournalFlag" renderer={disabledEdit ? ({ value }) => intl.formatMessage({ id: value ? 'yes' : 'no' }) : undefined} />
          {showJump && <div name="jsonConfig.openFieldMappingFlag" className={styles.switchWithLink}>
            <Switch name="jsonConfig.openFieldMappingFlag" />
            {infoDetailDataSet?.current?.get('jsonConfig.openFieldMappingFlag') && <div className={styles.link} onClick={handleClickToAppMap}>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.c7n.mapping.jump.to.open.field.mapping', defaultMessage: '跳转到可视化映射配置' })}→</div>}
          </div>}
        </Form>
        <Tabs type="card" className={`${prefixCls}-tabs`}>
          <TabPane tab={intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.choerodon.bind.history', defaultMessage: '绑定历史' })} key="fields">
            <Table
              pristine
              dataSet={choerodonUserDataSet}
              labelLayout="float"
              queryBarProps={{
                title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.choerodon.bind.history', defaultMessage: '绑定历史' }),
              }}
            >
              <Column name="realName" />
              <Column name="email" />
              <Column name="loginName" />
              <Column name="creationDate" />
              <Column name="choerodonUserAccount" />
            </Table>
          </TabPane>
        </Tabs>
      </div>
    );
  }
  return null;
};

export default observer(ChoerodonContent);
