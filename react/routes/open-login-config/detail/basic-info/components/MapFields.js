import React from 'react';
import { observer } from 'mobx-react-lite';
import { TextField, Lov } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import '../../index.less';

export default observer((props) => {
  const { prefixCls, intl, intlPrefix, dataSet, parent, variableViewId, businessObjectId } = props;

  const curPrefixCls = `${prefixCls}-config-body-user`;

  const handleCreate = () => {
    dataSet.current = dataSet.create({

    });
  };

  const renderMappingFields = (record) => {
    return (
      <div className={`${curPrefixCls}-content-fields`}>
        <TextField
          className={`${curPrefixCls}-content-item`}
          name="originFieldCode"
          record={record}
        />
        <TextField
          className={`${curPrefixCls}-content-item`}
          name="targetFieldCode"
          record={record}
        />
        <div className={`${curPrefixCls}-content-delete field-default`} onClick={() => dataSet.remove(record)}>
          <Icon type="delete" theme="outline" size="14" fill="#f8353f" />
        </div>
      </div>
    );
  };

  return (
    <div className={curPrefixCls}>
      <div className={`${curPrefixCls}-content-fieldsname field-default`}>
        <div className={`${curPrefixCls}-content-item`}>
          {intl.formatMessage({ id: 'iam.openLoginConfig.desc.origin.field.code', defaultMessage: '原始字段code' })}
        </div>
        <div className={`${curPrefixCls}-content-item`}>
          {intl.formatMessage({ id: 'iam.openLoginConfig.desc.target.field.code', defaultMessage: '映射字段code' })}
        </div>
      </div>
      {dataSet.status === 'ready' && dataSet.length > 0 && dataSet.map(renderMappingFields)}
      <div className={`${curPrefixCls}-content-addfields`} onClick={handleCreate}>
        <Icon type="file-addition" theme="outline" size="14" />
        <span style={{ marginLeft: '4px' }}>
          {intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.add.field', defaultMessage: '添加字段' })}
        </span>
      </div>
    </div>
  );
});
