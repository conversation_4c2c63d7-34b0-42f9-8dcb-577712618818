import React from 'react';
import { observer } from 'mobx-react-lite';
import {
  Form,
  Select,
  Switch,
  Tabs,
  TextArea,
  TextField,
} from 'choerodon-ui/pro';
import { Icon as YQIcon } from '@zknow/components';
import OpenClientFunctionList from './sap-ebs';
import '../../index.less';

const { TabPane } = Tabs;
const SapEbsFormContent = (props) => {
  const {
    prefixCls,
    disabledEdit,
    intl,
    intlPrefix,
    infoDetailDataSet,
    copyText,
    tenantId,
    refresh,
  } = props;
  return (
    <div className={`${prefixCls}-config-body`}>
      <Form
        disabled={disabledEdit}
        header={intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' })}
        labelWidth={100}
        columns={2}
        labelLayout="horizontal"
        dataSet={infoDetailDataSet}
      >
        <TextField name="name" autoFocus />
        <Select
          name="quickType"
          disabled
          label={intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.quick.type', defaultMessage: '应用类型' })}
        />
        <TextField
          name="gateway"
          disabled
          suffix={
            <YQIcon
              className={`${prefixCls}-config-suffix`}
              type="CopyOne"
              onClick={() => copyText(infoDetailDataSet.current?.get('gateway'))}
            />
          }
        />
        <TextField
          name="appSecret"
          label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.user.info.secret', defaultMessage: '人员信息密钥' })}
          suffix={
            <YQIcon
              className={`${prefixCls}-config-suffix`}
              type="CopyOne"
              onClick={() => copyText(infoDetailDataSet.current?.get('appSecret'))}
            />
          }
        />
        <TextArea
          name="jsonConfig.description"
          rows={1}
          autoSize={{ minRows: 1, maxRows: 4 }}
          resize="height"
        />
        <Switch name="freeLoginFlag" />
      </Form>
      <Tabs type="card" className={`${prefixCls}-tabs`}>
        <TabPane tab={intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.function', defaultMessage: '开放功能' })} key="function">
          <OpenClientFunctionList refresh={refresh} />
        </TabPane>
      </Tabs>
    </div>
  );
};

export default observer(SapEbsFormContent);
