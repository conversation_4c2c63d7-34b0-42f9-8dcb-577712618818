import React from 'react';
import { observer } from 'mobx-react-lite';
import { TextField, Lov } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import DynamicComponent from './DynamicComponent';
import '../../index.less';

export default observer((props) => {
  const { prefixCls, intl, intlPrefix, dataSet, parent, variableViewId, businessObjectId } = props;

  const curPrefixCls = `${prefixCls}-config-body-user`;

  const handleCreate = () => {
    dataSet.current = dataSet.create({
      isVariableField: parent?.get('serviceItem.variableFlag'),
      variableViewId: parent?.get('serviceItem.id') || parent?.get('variableViewId') || variableViewId,
      businessObjectId: parent?.get('businessObjectId') || businessObjectId,
    });
  };

  const renderMappingFields = (record) => {
    return (
      <div className={`${curPrefixCls}-content-fields`}>
        <Lov
          className={`${curPrefixCls}-content-item`}
          name="fieldId"
          record={record}
        />
        <TextField
          className={`${curPrefixCls}-content-item`}
          name="fieldCode"
          record={record}
          disabled
        />
        <span className={`${curPrefixCls}-content-item`}>
          <DynamicComponent
            clearButton={false}
            record={record}
            dataSet={record.dataSet}
            name="defaultValue"
            placeholder={intl.formatMessage({ id: 'iam.openLoginConfig.model.default.value', defaultMessage: '默认值' })}
            readOnly={false}
            disabled={false}
            tableId={parent?.get('businessObjectId') || businessObjectId}
            isSider={false}
          />
        </span>
        <div className={`${curPrefixCls}-content-delete field-default`} onClick={() => dataSet.remove(record)}>
          <Icon type="delete" theme="outline" size="14" fill="#f8353f" />
        </div>
      </div>
    );
  };

  return (
    <div className={curPrefixCls}>
      <div className={`${curPrefixCls}-content-fieldsname field-default`}>
        <div className={`${curPrefixCls}-content-item`}>
          {intl.formatMessage({ id: 'zknow.common.model.fieldName', defaultMessage: '字段名称' })}
        </div>
        <div className={`${curPrefixCls}-content-item`}>
          {intl.formatMessage({ id: 'iam.openLoginConfig.model.field.code', defaultMessage: '字段编码' })}
        </div>
        <div className={`${curPrefixCls}-content-item`}>
          {intl.formatMessage({ id: 'iam.openLoginConfig.model.default.value', defaultMessage: '默认值' })}
        </div>
      </div>
      {dataSet.status === 'ready' && dataSet.length > 0 && dataSet.map(renderMappingFields)}
      <div className={`${curPrefixCls}-content-addfields`} onClick={handleCreate}>
        <Icon type="file-addition" theme="outline" size="14" />
        <span style={{ marginLeft: '4px' }}>
          {intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.add.field', defaultMessage: '添加字段' })}
        </span>
      </div>
    </div>
  );
});
