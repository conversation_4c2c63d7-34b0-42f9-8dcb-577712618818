import React, { useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import {
  TextField,
  Select,
  EmailField,
  NumberField,
  Currency,
  TextArea,
  Password,
  DatePicker,
  DateTimePicker,
  TimePicker,
  UrlField,
  Lov,
  DataSet,
  CheckBox,
  Button,
  Modal,
  RichText,
} from 'choerodon-ui/pro';
import classnames from 'classnames';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import moment from 'moment';
import { YqCodeMirror } from '@zknow/components';
import { getQueryParams } from '@zknow/utils';
import { transformResponse, transformField } from '../../../lovConfig';
import {
  notNeedRenderComponentsArray, LovComponentsArray, LovMultipleArray,
  timeFormat, dateFormat, dateTimeFormat,
} from '../../../lovConfig/utils';

const richTextModalKey = Modal.key();

const notRequiredFilters = ['is empty', 'is not empty', 'is current user'];
function DynamicComponent({
  clearButton,
  className,
  placeholder,
  disabled,
  readOnly,
  tableId,
  AppState: { currentMenuType: { organizationId: tenantId, type: envType } },
  record,
  name, intl,
  required = true,
  isSider,
}) {
  // 注意 record与name存在时，优先级比组件上直接写value高
  const lovParentProps = {
    clearButton,
    className,
    placeholder,
    readOnly,
    required,
    disabled,
    key: `DynamicComponent-${record.get('filterUuid')}`,
  };
  const parentProps = {
    clearButton,
    className,
    placeholder,
    readOnly,
    record,
    name,
    disabled,
    required,
    key: `DynamicComponent-${record.get('filterUuid')}`,
  };

  const SelectBoxOptions = [
    {
      meaning: intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' }),
      value: true,
    },
    {
      meaning: intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' }),
      value: false,
    },
  ];

  const getMaxLengthProps = useMemo(() => {
    if (isSider) {
      return {
        maxTagCount: 1,
        maxTagTextLength: 4,
        maxTagPlaceholder: (restValues) => `+${restValues.length}...`,
      };
    } else {
      return {
        maxTagCount: 5,
        maxTagTextLength: 4,
        maxTagPlaceholder: (restValues) => `+${restValues.length}...`,
      };
    }
  }, [isSider]);

  const validateArray = () => {

  };

  const isNeedRenderComponent = () => !notNeedRenderComponentsArray.includes(record?.get('filter'));

  const setLovPara = () => {
    if (record?.get('field') === 'state_id') {
      return { search_business_object_id: tableId };
    }
  };

  const ds = useMemo(() => {
    if (LovComponentsArray.includes(record?.get('widgetType'))) {
      let lovValue;
      try {
        lovValue = record?.get('fieldLovValue') && JSON.parse(record?.get('fieldLovValue'));
      } catch (err) {
        lovValue = record?.get('fieldLovValue');
      }
      return new DataSet({
        fields: [
          {
            name: 'lovFieldName',
            required: !notRequiredFilters.includes(record?.get('filter')),
            lovCode: record?.get('fieldData.relationLovId'),
            type: 'object',
            multiple: LovMultipleArray.includes(record?.get('filter')),
            readOnly,
            lovPara: setLovPara(record?.get('widgetType')), // 状态的时候要tableObjectId
            lovDefineAxiosConfig: lovCode => ({
              url: `/lc/v1${envType === 'site' ? '' : `/${tenantId}`}/object_options/id/${lovCode}`,
              method: 'GET',
              transformResponse: data => transformResponse(data, data?.name, (map, f) => transformField(map, f), intl),
            }),
            lovQueryAxiosConfig: (lovCode, lovConfig = {}, { data, params }) => {
              const { parentIdField, idField, treeFlag } = lovConfig;
              let searchFlag = false;
              const queryParams = getQueryParams(data, [parentIdField, idField]);
              Object.keys(queryParams).forEach((v) => {
                if (v.indexOf('search_') !== -1) {
                  searchFlag = true;
                }
              });
              if (treeFlag === 'Y' && parentIdField) {
                if (data[parentIdField]) {
                  params.size = 999;
                } else if (!searchFlag) {
                  queryParams[parentIdField] = '0';
                }
              }
              return {
                url: `/lc/v1/engine/${tenantId}/options/${lovCode}/query`,
                method: 'POST',
                data: queryParams,
                params,
                transformResponse: (res) => {
                  const allData = JSON.parse(res);
                  allData.content = (allData.content || []).map((item) => {
                    return {
                      ...item,
                      primaryKey: item.id || '',
                    };
                  });
                  return allData;
                },
              };
            },
          },
        ],
        data: [
          {
            lovFieldName: lovValue,
            id: lovValue?.id,
            name: lovValue?.name,
          },
        ],
      });
    }
    return undefined;
  }, [record?.get('filterUuid'), record?.get('widgetType'), record?.get('filter'), readOnly]);

  const handleOnSelectBoxChange = (value) => {
    record?.set('defaultValue', value);
  };
  const renderSelectBox = () => {
    let component = '';
    if (SelectBoxRC) {
      component = <Select
        record={SelectBoxRC?.current}
        name="lookupFieldName"
        {...lovParentProps}
        onChange={(e) => handleOnSelectBoxChange(e)}
      />;
    } else {
      component = <Select
        {...parentProps}
        {...getMaxLengthProps}
      />;
    }
    return component;
  };
  const lookUpDs = useMemo(() => {
    if (record?.get('fieldData')) {
      let defaultSelectValue = '';
      const multipleFlag = ['SelectBox', 'MultipleSelect'].includes(record?.get('widgetType')) ? true : LovMultipleArray.includes(record?.get('filter'));
      if (multipleFlag) {
        defaultSelectValue = Array.isArray(record.get('defaultValue')) ? record.get('defaultValue') : record?.get('defaultValue')?.split(',') || [];
      } else {
        defaultSelectValue = record.get('defaultValue');
      }
      let lookUpObject;
      try {
        lookUpObject = (record?.get('fieldData') && JSON.parse(record?.get('fieldData')))?.widgetConfig || {};
      } catch (err) {
        lookUpObject = record?.get('fieldData').widgetConfig || {};
      }
      if (lookUpObject?.dataSource === 'lookup') {
        return new DataSet({
          paging: false,
          fields: [
            {
              name: 'lookupFieldName',
              multiple: multipleFlag,
              readOnly,
              lookupUrl: `/hpfm/v1/${tenantId}/lookup/queryByCode?lookupTypeCode=${lookUpObject.lookupCode}`,
              defaultValue: defaultSelectValue,
            },
          ],
          selection: 'multiple',
          data: [
            {
              lookupFieldName: defaultSelectValue,
            },
          ],
        });
      }
      if (lookUpObject?.dataSource === 'optionSet') {
        return new DataSet({
          paging: false,
          fields: [
            {
              name: 'lookupFieldName',
              readOnly,
              defaultValue: defaultSelectValue,
              multiple: multipleFlag,
              options: new DataSet({
                paging: false,
                data: lookUpObject?.options || [],
              }),
            },
          ],
          data: [
            {
              lookupFieldName: defaultSelectValue,
            },
          ],
        });
      }
    }
    return undefined;
  }, [record?.get('filterUuid'), record?.get('widgetType'), record?.get('filter'), readOnly]);

  const renderLookUpSelect = (isMultiple) => {
    let component = '';
    if (lookUpDs) {
      component = <Select
        record={lookUpDs?.current}
        name="lookupFieldName"
        {...lovParentProps}
        {...getMaxLengthProps}
        onChange={(value, oldValue) => handleLookUpCodeComponentChange(value)}
      />;
    } else {
      component = <Select
        {...parentProps}
        {...getMaxLengthProps}
        multiple={isMultiple}
      />;
    }
    return component;
  };

  const SelectBoxRC = useMemo(() => {
    if (record?.get('widgetType') === 'Switch' || record?.get('widgetType') === 'CheckBox') {
      // eslint-disable-next-line no-eval
      const fieldValueFlag = eval(record.get('defaultValue'));
      return new DataSet({
        fields: [
          {
            name: 'lookupFieldName',
            readOnly,
            defaultValue: fieldValueFlag,
            options: new DataSet({
              data: SelectBoxOptions || [],
              selection: 'single',
            }),
          },
        ],
        data: [
          {
            lookupFieldName: fieldValueFlag,
          },
        ],
      });
    }
  }, [record?.get('filterUuid'), record?.get('widgetType'), record?.get('filter'), readOnly]);

  // 渲染时间类型的组件的值
  const renderDateComponentValue = (original, format) => {
    if (record?.get('filter') === 'between') {
      // 条件为介于的话，就会出现range属性
      const arr = original && original.split(',');
      return {
        start: arr && arr[0] ? moment(arr[0], format) : undefined,
        end: arr && arr[1] ? moment(arr[1], format) : undefined,
      };
    }
    return record.get('defaultValue') ? moment(record.get('defaultValue'), format) : undefined;
  };

  // 时间类型的组件的onChange方法
  const handleDateComponentChange = (timeValue, format) => {
    if (record?.get('filter') === 'between') {
      const start = timeValue?.start ? timeValue?.start?.format(format) : undefined;
      const end = timeValue?.end ? timeValue?.end?.format(format) : undefined;
      record.set(name, `${start},${end}`);
    } else {
      record.set(name, timeValue.format(format));
    }
  };

  // LOV组件的onchange 方法
  const handleLovComponentChange = (lovValue) => {
    const valueField = ds.getField('lovFieldName').getProp('valueField');
    const nameField = ds.getField('lovFieldName').getProp('textField');
    if (Array.isArray(lovValue)) {
      // 多选
      const ids = lovValue.map((i) => i[valueField]);
      record.set(name, ids.join(','));
      record.set('fieldLovValue', JSON.stringify(lovValue.map(v => ({ [valueField]: v[valueField], [nameField]: v[nameField] }))));
    } else if (lovValue && lovValue[valueField]) {
      // 单选
      record.set(name, lovValue[valueField]);
      record.set('fieldLovValue', JSON.stringify({ [valueField]: lovValue[valueField], [nameField]: lovValue[nameField] }));
    } else {
      record.set(name, '');
      record.set('fieldLovValue', '');
    }
  };

  // 快码 组件的onchange 方法
  const handleLookUpCodeComponentChange = (currentValue) => {
    if (Array.isArray(currentValue)) {
      // 多选
      record.set(name, currentValue.join(','));
    } else {
      // 单选
      record.set(name, currentValue);
    }
  };

  const renderComponent = () => {
    let component = '';
    // fieldValueType expression 即JS表达式类型
    if (record?.get('fieldValueType') === 'EXPRESSION') {
      return <YqCodeMirror mode="button" businessObjectId={tableId} record={record} name="expression" disabled={disabled} />;
    }
    switch (record?.get('widgetType')) {
      case 'MasterDetail':
        component = <Lov
          dataSet={ds}
          record={ds.current}
          name="lovFieldName"
          {...lovParentProps}
          {...getMaxLengthProps}
          onChange={(value, oldValue) => handleLovComponentChange(value)}
          tableProps={{
            onRow: ({ record: current }) => ({
              isLeaf: current.get('isLeaf'),
            }),
          }}
        />;
        break;
      case 'Select': case 'SelectBox': case 'Radio':
        component = renderLookUpSelect();
        break;
      case 'Input':
        component = <TextField {...parentProps} />;
        break;
      case 'TextArea':
        component = <TextField {...parentProps} />;
        break;
      case 'RichText':
        component = <TextField {...parentProps} />;
        break;
      case 'Switch':
        component = renderSelectBox();
        break;
      case 'Password':
        component = <Password {...parentProps} />;
        break;
      case 'EmailField':
        component = <EmailField {...parentProps} />;
        break;
      case 'Single':
        component = <Select {...parentProps} />;
        break;
      case 'MultipleSelect':
        component = renderLookUpSelect(true);
        break;
      case 'DateTime':
        component = <DateTimePicker
          {...lovParentProps}
          mode="dateTime"
          format={dateTimeFormat}
          value={renderDateComponentValue(record.get('defaultValue'), dateTimeFormat)}
          range={record?.get('filter') === 'between' ? ['start', 'end'] : false}
          onChange={(timeValue, oldValue) => handleDateComponentChange(timeValue, dateTimeFormat)}
        />;
        break;
      case 'Date':
        component = <DatePicker
          {...lovParentProps}
          mode="date"
          format={dateFormat}
          type="date"
          range={record?.get('filter') === 'between' ? ['start', 'end'] : false}
          value={renderDateComponentValue(record.get('defaultValue'), dateFormat)}
          onChange={(timeValue, oldValue) => handleDateComponentChange(timeValue, dateFormat)}
        />;
        break;
      case 'Time':
        component = <TimePicker
          {...lovParentProps}
          format={timeFormat}
          type="time"
          range={false}
          value={renderDateComponentValue(record.get('defaultValue'), timeFormat)}
          onChange={(timeValue, oldValue) => handleDateComponentChange(timeValue, timeFormat)}
        />;
        break;
      case 'NumberField':
        component = <NumberField {...parentProps} />;
        break;
      case 'FloatField':
        component = <NumberField {...parentProps} />;
        break;
      case 'Currency':
        component = <Currency {...parentProps} />;
        break;
      case 'Url':
        component = <UrlField {...parentProps} />;
        break;
      case 'Comment':
        component = <TextField {...parentProps} />;
        break;
      case 'CheckBox':
        component = renderSelectBox();
        break;
      default:
        component = <TextField {...parentProps} />;
        break;
    }
    return component;
  };

  const itemClassNames = classnames({
    'content-select-filter-checkbox-content': record?.get('widgetType') === 'CheckBox',
  });

  return (
    <span className="yqcloud-fieldvalue-item">
      {isNeedRenderComponent() && renderComponent()}
    </span>
  );
}

export default injectIntl(inject('AppState')(observer(DynamicComponent)));
