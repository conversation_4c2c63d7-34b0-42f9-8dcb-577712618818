import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, Switch } from 'choerodon-ui/pro';
import { Alert } from 'choerodon-ui';
import stores from './stores';
import Fields from './Fields';

const Approval = () => {
  const {
    intl,
    edit,
    openAppStore,
  } = useContext(stores);

  const handleChange = (value) => {
    openAppStore.setApprovalEventFlag(value);
  };

  return (
    <>
      <Form
        style={{ marginTop: '20px' }}
        disabled={!edit}
        header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.approval', defaultMessage: '审批设置' })}
        labelWidth="auto"
        columns={2}
        labelLayout="horizontal"
        labelAlign="left"
      >
        <Switch
          label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.approval', defaultMessage: '审批设置' })}
          onChange={handleChange}
          checked={openAppStore.getApprovalEventFlag}
        />
      </Form>
      {openAppStore.getApprovalEventFlag && (
        <>
          <Alert
            style={{ margin: '0 16px' }}
            showIcon
            iconType="info"
            type="info"
            message={intl.formatMessage({ id: 'iam.openLoginConfig.desc.approval.sync', defaultMessage: '若要和飞书的审批进行集成配置，需要填写上方的回调信息' })}
          />
          <Fields />
        </>
      )}
    </>
  );
};

export default observer(Approval);
