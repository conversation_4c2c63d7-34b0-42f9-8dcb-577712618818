import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Button, Icon } from '@zknow/components';
import { Switch, TextField, Tooltip, Modal, message } from 'choerodon-ui/pro';
import uuidv4 from 'uuid/v4';
import { axios } from '@yqcloud/apps-master';
import stores from './stores';
import styles from './ApprovalFields.module.less';

const FieldItem = observer(({ record }) => {
  const {
    intl,
    edit,
    openId,
    AppState: { currentMenuType: { tenantId } },
    openAppStore,
  } = useContext(stores);

  const handleRemove = () => {
    record.dataSet.delete(record, false);
  };

  const handleBeforeChange = async (value) => {
    if (record.get('approvalCode')) {
      if (!value) {
        const result = await Modal.confirm({
          title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.approval.close.confirm', defaultMessage: '确认关闭' }),
          children: (
            <div>
              <p>{intl.formatMessage({
                id: 'iam.openLoginConfig.desc.approval.close.confirm.text',
                defaultMessage: '关闭审批流后，相关配置也会失效和丢失',
              })}</p>
            </div>
          ),
        });
        if (result === 'cancel') {
          return false;
        } else {
          const res = await axios.put(`/ecos/v1/${tenantId}/openApps/${openId}/callback/config/approval/enabled?approvalCode=${record.get('approvalCode')}&enabled=false`);
          if (res?.failed) {
            message.error(intl.formatMessage({ id: 'iam.openLoginConfig.desc.approval.close_failed', defaultMessage: '关闭失败，请稍候重试' }));
          } else {
            // 数据回写，有 objectVersionNumber
            openAppStore.setAllConfig(res);
            message.success(intl.formatMessage({ id: 'iam.openLoginConfig.desc.approval.close_success', defaultMessage: '关闭成功' }));
          }
        }
      } else {
        const res = await axios.put(`/ecos/v1/${tenantId}/openApps/${openId}/callback/config/approval/enabled?approvalCode=${record.get('approvalCode')}&enabled=true`);
        if (res?.failed) {
          message.error(intl.formatMessage({ id: 'iam.openLoginConfig.desc.approval.change_failed', defaultMessage: '开启失败，请稍候重试' }));
        } else {
          openAppStore.setAllConfig(res);
          message.success(intl.formatMessage({ id: 'iam.openLoginConfig.desc.approval.change_success', defaultMessage: '开启成功' }));
        }
      }
    }

    return true;
  };

  return (
    <>
      <div className={styles.item}>
        <TextField
          record={record}
          name="approvalCode"
          className={styles.item}
          // disabled={!edit || ((record.get('enabledFlag') || record.getState('loading')) && record.get('approvalCode'))}
          disabled={!edit}
        />
      </div>
      <div className={styles.item}>
        <TextField
          record={record}
          name="name"
          className={styles.item}
          disabled
        />
      </div>
      <div className={styles.item}>
        <Switch
          record={record}
          name="enabledFlag"
          // disabled={!edit || (!record.get('approvalCode') && !record.get('enabledFlag')) || record.getState('loading')}
          disabled={!edit}
          onBeforeChange={handleBeforeChange}
        />
      </div>
      {(!record.get('approvalCode') || (!record.get('enabledFlag') && !record.getState('loading'))) && edit ? (
        <div
          className={styles.closeBtn}
          onClick={handleRemove}
        ><Icon type="close" /></div>
      ) : <span />}
    </>
  );
});

const Fields = () => {
  const {
    intl,
    approveDataset,
    edit,
  } = useContext(stores);

  const handleAdd = () => {
    approveDataset.create({
      uid: uuidv4(),
      enabledFlag: true,
    });
  };

  return (
    <>
      <div className={styles.list}>
        {approveDataset.length ? (
          <>
            <div className={styles.cellHeader}>
              <span>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.approval.flow.code', defaultMessage: '飞书审批流编码' })}</span>
              <span style={{ marginLeft: 4 }}><Tooltip
                title={(<span>
                  {intl.formatMessage({
                    id: 'iam.openLoginConfig.desc.approval.flow.code.desc',
                    defaultMessage: '审批定义对应的唯一编码，详情查看：',
                  })}
                  <a target="_blank" href="https://open.feishu.cn/document/server-docs/approval-v4/approval/overview-of-approval-resources#8151e0ae" rel="noreferrer">
                    {intl.formatMessage({
                      id: 'iam.openLoginConfig.desc.approval.flow.code.link',
                      defaultMessage: '什么是Approval Code',
                    })}
                  </a>
                </span>)}
              ><Icon type="help" /></Tooltip></span>
            </div>
            <div className={styles.cellHeader}>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.approval.flow.name', defaultMessage: '飞书审批名称' })}</div>
            <div className={styles.cellHeader}>
              {intl.formatMessage({ id: 'iam.openLoginConfig.desc.approval.flow.status', defaultMessage: '是否开启' })}
            </div>
            <span />
          </>
        ) : null}
        {approveDataset.map(record => (
          <FieldItem
            key={record.get('uid')}
            record={record}
          />))}
      </div>
      {edit && (
        <div className={styles.bottom}>
          <Button
            funcType="flat"
            icon="FileAddition"
            onClick={handleAdd}
          >{intl.formatMessage({ id: 'iam.openLoginConfig.desc.approval.flow', defaultMessage: '添加审批流' })}</Button>
        </div>
      )}
    </>
  );
};

export default observer(Fields);
