.list {
  display: grid;
  grid-template-columns: 300px 300px 120px 32px;
  grid-gap: 12px;
  margin-top: 12px;
  padding-left: 16px;

  .cellHeader {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: rgba(42, 45, 56, 0.85);
    font-size: 14px;
    font-weight: 500;

    span {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
  }

  .item {
    width: 100%;
  }

  .closeBtn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: rgba(42, 45, 56, 0.65);
    cursor: pointer;

    &:hover {
      color: #000;
    }
  }
}

.bottom {
  margin-top: 12px;
  padding-left: 12px;
}
