import axios from 'axios';
import uuidv4 from 'uuid/v4';

export default ({ tenantId, openId, openAppStore }) => {
  const url = openId ? `/ecos/v1/${tenantId}/openApps/${openId}/callback/config` : '';

  return {
    autoQuery: true,
    selection: false,
    paging: false,
    primaryKey: null,
    transport: {
      read: () => ({
        url,
        method: 'get',
        transformResponse(res) {
          try {
            const response = JSON.parse(res);
            if (!response || response.failed) {
              return res;
            }
            let config = [];
            try {
              config = JSON.parse(response?.approvalConfig);
            } catch (e) {
              config = [];
            }
            openAppStore.setAllConfig(response);
            openAppStore.setApprovalConfig(config);
            openAppStore.setApprovalEventFlag(response?.approvalEventFlag || false);
            config.forEach(item => {
              item.uid = uuidv4();
            });
            return config;
          } catch (e) {
            return res;
          }
        },
      }),
    },
    fields: [
      { name: 'approvalCode', type: 'string', required: true },
      { name: 'name', type: 'string' },
      { name: 'enabledFlag', type: 'boolean' },
    ],
    events: {
      update: ({ record, value, name }) => {
        if (name === 'approvalCode' && value) {
          record.setState('loading', true);
          axios.get(`/ecos/v1/${tenantId}/lark/${openId}/approval/${value}`)
            .then(res => {
              record.set('name', res && !res.failed ? (res.approval_name || '') : '');
              record.setState('loading', false);
              if (res?.failed) {
                // eslint-disable-next-line no-console
                console.error(res.message);
              }
            })
            .catch(e => {
              // eslint-disable-next-line no-console
              console.error(e);
              record.setState('loading', false);
            });
        }
      },
      create: ({ dataSet }) => {
        openAppStore.setApprovalConfig(dataSet.toData());
      },
      remove: ({ dataSet }) => {
        openAppStore.setApprovalConfig(dataSet.toData());
      },
    },
  };
};
