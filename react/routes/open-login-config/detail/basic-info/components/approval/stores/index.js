import React, { createContext } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: 'iam.openLoginConfig' })(injectIntl(
  (props) => {
    const {
      children,
    } = props;

    return (
      <Store.Provider value={props}>
        {children}
      </Store.Provider>
    );
  },
)));
