import React, { useContext, useMemo, useEffect, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { observer } from 'mobx-react-lite';
import copy from 'copy-to-clipboard';
import { Table, Modal, message } from 'choerodon-ui/pro';
import { TableHoverAction, ClickText, Button, YqAvatar, TableStatus, Icon } from '@zknow/components';
import OpenFunctionForm from './ModelDetail';
import Store from '../../../../stores';
import '../../../index.less';

const modalKey = Modal.key();
const { Column } = Table;

// 用于SDK HZERO的开放功能列表
export default observer((props) => {
  const { refresh } = props;
  const {
    intl, openFunctionDataSet, infoDetailDataSet, fieldsSDKDataSet,
    prefixCls, intlPrefix, fieldListDataSet,
  } = useContext(Store);
  const openFunctionNew = intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.function.new', defaultMessage: '新建功能' });
  const openFunctionDetail = intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.function.detail', defaultMessage: '开放功能详情' });
  const mdModalStyle = useMemo(() => ({ width: 800 }), []);
  const modalStyle = useMemo(() => ({ width: 520 }), []);
  let modal = null;
  const queryRecord = openFunctionDataSet?.queryDataSet?.current;

  function openModal(mode, dataSet) {
    // 设置当前服务项是上下游服务项
    fieldsSDKDataSet.setState('udmTenantId', dataSet.current?.get('serviceItem.udmProviderId'));
    if (modal) {
      return;
    }
    if (mode === 'create') {
      dataSet.create({ id: uuidv4() });
    }
    modal = Modal.open({
      title: mode === 'modify' ? openFunctionDetail : openFunctionNew,
      children: (
        <OpenFunctionForm
          type={mode}
          intl={intl}
          prefixCls={prefixCls}
          intlPrefix={intlPrefix}
          dataSet={dataSet}
          fieldsDataSet={fieldsSDKDataSet}
          fieldListDataSet={fieldListDataSet}
        />
      ),
      key: modalKey,
      drawer: mode === 'modify',
      style: mode === 'modify' ? mdModalStyle : modalStyle,
      destroyOnClose: true,
      onClose: () => {
        dataSet.reset();
        modal = false;
      },
      onOk: async () => {
        const validate = await dataSet.validate();
        if (!validate) {
          return false;
        }
        infoDetailDataSet.current?.set('jsonConfig.openFunctions', dataSet.toData());
        const res = await infoDetailDataSet.submit();
        if (res && !res.failed) {
          refresh();
          return true;
        }
        return false;
      },
      okText: intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' }),
    });
  }

  function handleModify(record) {
    record.dataSet.current = record;
    openModal('modify', record.dataSet);
  }

  function handleCreate() {
    openModal('create', openFunctionDataSet);
  }

  function renderCreateBtn() {
    return (
      <Button
        icon="plus"
        funcType="raised"
        color="primary"
        onClick={handleCreate}
      >
        {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
      </Button>
    );
  }

  function renderTableAction({ dataSet, record }) {
    const enabledFlag = record.get('enabledFlag');
    const enabledAction = {
      name: enabledFlag ? intl.formatMessage({ id: 'zknow.common.action.make.invalid', defaultMessage: '失效' }) : intl.formatMessage({ id: 'zknow.common.action.make.valid', defaultMessage: '生效' }),
      icon: enabledFlag ? 'ReduceOne' : 'CheckOne',
      onClick: async () => {
        try {
          record.set('enabledFlag', !enabledFlag);
          infoDetailDataSet.current?.set('jsonConfig.openFunctions', dataSet.toData());
          const res = await infoDetailDataSet.submit();
          if (res && !res?.failed) {
            refresh();
            return true;
          }
          return false;
        } catch (e) {
          return false;
        }
      },
    };
    const deleteAction = {
      name: intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' }),
      icon: 'delete',
      onClick: async () => {
        const oldData = dataSet.toData();
        const currentRecord = record.toData();
        const newData = oldData.filter(_record => (_record.id !== currentRecord.id));
        infoDetailDataSet.current?.set('jsonConfig.openFunctions', newData);
        const res = await infoDetailDataSet.submit();
        if (res && !res?.failed) {
          refresh();
          return true;
        }
        return false;
      },
    };
    return (
      <TableHoverAction
        actions={enabledFlag ? [enabledAction] : [enabledAction, deleteAction]}
        record={record}
        intlBtnIndex={0}
      />
    );
  }

  function renderName({ record, name }) {
    return (
      <ClickText
        record={record}
        onClick={() => handleModify(record)}
        valueField={name}
      />
    );
  }

  function copyText(text) {
    copy(text);
    message.success(intl.formatMessage({ id: 'zknow.common.success.copy', defaultMessage: '复制成功' }));
  }

  function renderLink({ record }) {
    if (!record?.get('link')) {
      return null;
    }
    return (
      <span className={`${prefixCls}-link`}>
        <span className={`${prefixCls}-link-url`}>{record?.get('link')}</span>
        <Icon
          className={`${prefixCls}-link-copy`}
          type="CopyOne"
          onClick={() => copyText(record?.get('link'))}
        />
      </span>
    );
  }

  function renderIcon({ record }) {
    return (
      <YqAvatar
        style={{
          cursor: 'default',
          borderRadius: 'unset',
        }}
        size={20}
        src={record?.get('icon')}
      />
    );
  }

  function renderEnabledFlag({ value }) {
    return (
      <TableStatus
        status={value}
        enabledText={intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' })}
        disabledText={intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' })}
      />
    );
  }

  function renderDragRow(dragProps) {
    delete dragProps.dragColumnAlign;
    return <Table.TableRow {...dragProps} />;
  }

  async function handleDragEnd(dataSet) {
    try {
      infoDetailDataSet.current?.set('jsonConfig.openFunctions', dataSet.toData());
      const res = await infoDetailDataSet.submit();
      if (res && !res?.failed) {
        refresh();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  const tableFilter = useCallback((current) => {
    if (queryRecord?.get('name')) {
      return current?.get('name')?.includes(queryRecord?.get('name'));
    }
    return true;
  }, [queryRecord?.get('name')]);

  return (
    <Table
      title={intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.function', defaultMessage: '开放功能' })}
      labelLayout="float"
      dataSet={openFunctionDataSet}
      pristine
      onDragEnd={handleDragEnd}
      rowDragRender={{ renderClone: renderDragRow }}
      dragRow
      filter={tableFilter}
      buttons={[renderCreateBtn()]}
      queryBarProps={{
        fuzzyQuery: false,
        inlineSearch: false,
        simpleMode: true,
        queryFieldsStyle: {
          name: {
            width: 140,
          },
        },
      }}
    >
      <Column name="name" width={200} renderer={renderName} />
      <Column name="icon" width={100} renderer={renderIcon} />
      <Column name="link" renderer={renderLink} />
      <Column name="enabledFlag" width={150} renderer={renderEnabledFlag} />
      <Column width={150} renderer={renderTableAction} />
    </Table>
  );
});
