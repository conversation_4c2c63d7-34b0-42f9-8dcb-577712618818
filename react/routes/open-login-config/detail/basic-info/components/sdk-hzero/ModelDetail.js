import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, Output, Lov, Select, CheckBox, SelectBox, UrlField } from 'choerodon-ui/pro';
import { FileUploader, YqAvatar, Icon } from '@zknow/components';
import FieldsDefault from '../FieldsDefault';
import styles from './OpenFunctionForm.module.less';
import '../../../index.less';
import MapFields from '../MapFields';

// 用于SDK HZERO的新建窗口
export default observer((props) => {
  const { type, dataSet, intl, fieldsDataSet, prefixCls, intlPrefix, fieldListDataSet } = props;
  const record = dataSet.current;
  const channel = record?.get?.('channel') || 'pc';

  useEffect(() => {
    if (record?.get('businessObjectId')) {
      fieldListDataSet.setQueryParameter('boId', record?.get('businessObjectId'));
    }
    if (record?.get('variableViewId')) {
      fieldListDataSet.setQueryParameter('viewId', record?.get('variableViewId'));
      fieldListDataSet.setQueryParameter('variableFlag', record?.get('variableFlag'));
    }
    if (record?.get('serviceItem.udmItemFlag') === 1) {
      fieldListDataSet.setQueryParameter('_udm', 'a0b923820dcc509a');
      fieldListDataSet.setQueryParameter('udmTenantId', record?.get('serviceItem.udmProviderId'));
    } else {
      fieldListDataSet.setQueryParameter('_udm', undefined);
      fieldListDataSet.setQueryParameter('udmTenantId', undefined);
    }
    fieldListDataSet.query();
  }, [record?.get('variableViewId'), record?.get('businessObjectId')]);

  const ticketIcon = () => {
    return (
      <FileUploader
        name="icon"
        record={record}
      >
        {record?.get('icon') ? (
          <YqAvatar style={{ cursor: 'pointer', borderRadius: '5px' }} size={60} src={record?.get('icon')} />
        ) : (
          <div className={styles.picture}>
            <Icon type="upload-picture" fill="#8c8c8c" size={28} />
          </div>
        )}
      </FileUploader>
    );
  };

  const renderIconLabel = () => (
    <span className={styles.iconLabel}>{intl.formatMessage({ id: 'zknow.common.model.icon', defaultMessage: '图标' })}</span>
  );

  if (!record) {
    return null;
  }
  const channelField = (
    <SelectBox name="channel">
      <SelectBox.Option value="pc">{intl.formatMessage({ id: 'iam.openLoginConfig.desc.channel.pc', defaultMessage: 'PC' })}</SelectBox.Option>
      <SelectBox.Option value="mobile">{intl.formatMessage({ id: 'iam.openLoginConfig.model.channel.mobile', defaultMessage: '移动端' })}</SelectBox.Option>
    </SelectBox>
  );
  if (type !== 'modify') {
    return (
      <Form
        labelLayout="horizontal"
        record={record}
        labelWidth="auto"
      >
        <TextField autoFocus name="name" />
        <Output label={renderIconLabel()} name="icon" renderer={ticketIcon} />
        <SelectBox name="iconSize" />
        {record?.get('iconSize') === 'SMALL_SIZE'
          && <SelectBox name="iconCorner" />}
        {channelField}
        <Select name="feature" />
        {record.get('feature') === 'link' && <UrlField name="link" />}
        {record.get('feature') === 'serviceItem' && <Lov name="serviceItem" clearButton />}
        {record.get('feature') === 'workbench' && <Lov name="workbench" clearButton />}
        {record.get('feature') === 'view' && <Lov name="view" clearButton />}
        {record.get('feature') === 'bot' && [<Lov name="bot" clearButton />, <SelectBox name="method" />]}
        {record.get('feature') === 'knowledge' && <Lov name="knowledge" clearButton />}
      </Form>
    );
  }

  return (
    <>
      <Form
        labelLayout="horizontal"
        record={record}
        labelWidth="auto"
        columns={2}
        header={intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' })}
      >
        <TextField name="name" disabled />
        <TextField name="displayName" autoFocus />
        <Output label={renderIconLabel()} name="icon" renderer={ticketIcon} />
        <SelectBox name="iconSize" />
        {record?.get('iconSize') === 'SMALL_SIZE'
          && <SelectBox name="iconCorner" />}
        <Output name="channel" renderer={({ value }) => intl.formatMessage({ id: `iam.openLoginConfig.desc.channel.${value}`, defaultMessage: value })} />
        {record.get('feature') === 'bot' && <>
          <Select name="feature" disabled />,
          <Lov name="bot" clearButton />,
          <SelectBox name="method" />,
          {channel === 'pc' && <Select name="openType" />}
          {channel === 'pc' && record.get('openType') !== 'NEW' ? <Select name="botViewSize" /> : null}
        </>}
        {record.get('feature') !== 'bot' && (
          <>

            {<Select name="feature" disabled />}
            {record.get('feature') === 'serviceItem' && <Lov name="serviceItem" clearButton />}
            {record.get('feature') === 'workbench' && <Lov name="workbench" clearButton />}
            {record.get('feature') === 'view' && <Lov name="view" clearButton />}
            {record.get('feature') === 'link' && <UrlField name="link" clearButton />}
            {record.get('feature') === 'knowledge' && <Lov name="knowledge" clearButton />}
            {channel === 'pc' && <Select name="openType" />}
            {channel === 'pc' && record.get('openType') !== 'NEW' ? <Select name="viewSize" /> : null}
            <Select name="feedbackLinkType" />

          </>
        )}
      </Form>

      {channel === 'pc' && ['view', 'serviceItem'].includes(record.get('feature')) && (
        <Form
          labelLayout="horizontal"
          record={record}
          labelWidth="auto"
          columns={2}
          header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.screen.shot.setting', defaultMessage: '截屏设置' })}
        >
          <CheckBox name="screenShotFlag" />
          {record.get('screenShotFlag')
            ? (
              <Lov name="screenShotFieldCode" />
            ) : null}
        </Form>
      )}
      {['view', 'serviceItem'].includes(record.get('feature')) && <Form
        header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.field.default.value', defaultMessage: '字段默认值' })}
        columns={1}
        labelLayout="horizontal"
      >
        <FieldsDefault
          parent={record}
          prefixCls={prefixCls}
          intl={intl}
          intlPrefix={intlPrefix}
          dataSet={fieldsDataSet}
        />
      </Form>}
      {['serviceItem'].includes(record.get('feature')) && <Form
        header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.map.fields', defaultMessage: '映射字段' })}
        columns={1}
        labelLayout="horizontal"
      >
        <MapFields
          parent={record}
          prefixCls={prefixCls}
          intl={intl}
          intlPrefix={intlPrefix}
          dataSet={record.dataSet.children.mapFields}
        />
      </Form>}
    </>
  );
});
