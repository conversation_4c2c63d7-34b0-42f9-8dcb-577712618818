import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { observer } from 'mobx-react-lite';
import {
  Form,
  Select,
  TextField,
} from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import '../../index.less';

const WechatOpenAccountFormContent = inject('AppState')(observer((props) => {
  const {
    prefixCls,
    disabledEdit,
    intl,
    intlPrefix,
    infoDetailDataSet,
    AppState: { getUserInfo },
  } = props;

  const [homePageUrl, setUrl] = useState('');
  const record = infoDetailDataSet?.current;
  useEffect(() => {
    const name = record.get('name');
    const appId = record.get('appId');
    const appSecret = record.get('appSecret');
    const href = window.location.href;
    const tenant = getUserInfo.tenantNum || getUserInfo.tenantName || href.split('//')?.[1].split('.')?.[0];
    const organizationId = href.match(/detail\/(\d+)\?/)?.[1];
    if (name && appId && appSecret) {
      setUrl(`${window._env_?.MB_HOST || 'm.yqcloud.com'}?appid=${appId}&tenant=${tenant.toLowerCase()}&type=wechat_open_account&inside=true&isPlatformWechat=${organizationId === '0' ? 'true' : 'false'}`);
    } else {
      setUrl('');
    }
  }, [record.get('name'), record.get('appId'), record.get('appSecret')]);

  if (record) {
    return (
      <div className={`${prefixCls}-config-body`}>
        <Form disabled={disabledEdit} header={intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' })} labelWidth={100} columns={2} labelLayout="horizontal" dataSet={infoDetailDataSet}>
          <TextField name="name" autoFocus label={intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' })} />
          <Select name="type" disabled />
          <TextField name="appId" />
          <TextField name="appSecret" />
        </Form>
        <Form header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.url.generator.iam', defaultMessage: '链接生成器' })}>
          <TextField label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.home.page.url', defaultMessage: '主页URL' })} value={homePageUrl} placeholder={intl.formatMessage({ id: 'iam.openLoginConfig.desc.validate.message.required', defaultMessage: '请填写必填项' })} />
          <TextField label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.home.page.url', defaultMessage: '主页URL' })} value={homePageUrl} placeholder={intl.formatMessage({ id: 'iam.openLoginConfig.desc.validate.message.required', defaultMessage: '请填写必填项' })} />
        </Form>
      </div>
    );
  }
  return null;
}));

export default WechatOpenAccountFormContent;
