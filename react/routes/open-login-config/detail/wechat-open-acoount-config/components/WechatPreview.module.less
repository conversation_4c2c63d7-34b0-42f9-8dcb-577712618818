.frame {
  //width: 480px;
  background-color: #fff;
  border: #ededed 2px solid;
  box-shadow: 0 2px 5px rgba(244, 245, 247, 0.5);
  border-radius: 10px;
  padding: 38 * 0.777px 26 * 0.777px 0 26 * 0.777px;
  position: relative;
  box-sizing: border-box;
  .editButton {
    position: absolute;
    top: 0;
    right: 0;
  }
  .title {
    font-size: 22 * 0.777px;
    margin-bottom: 24 * 0.777px;
  }
  .item {
    font-size: 18 * 0.777px;
    display: flex;
    margin-bottom: 13.2 * 0.777px;
    .itemHeader {
      color: #8c9299;
      width: 110 * 0.777px;
      flex-shrink: 0;
    }
  }
  .divide {
    width: 100%;
    height: 1px;
    background-color: #e7e7e8;
    margin-top: 25 * 0.777px;
    margin-bottom: 15 * 0.777px;
  }
  .footer {
    font-size: 18 * 0.777px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 10 * 0.777px;
  }
}
