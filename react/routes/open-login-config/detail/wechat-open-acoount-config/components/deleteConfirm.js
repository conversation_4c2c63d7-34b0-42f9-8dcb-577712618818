import React, { useState } from 'react';
import { CheckBox } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import { inject } from 'mobx-react';
import { axios } from '@yqcloud/apps-master';
// eslint-disable-next-line import/no-cycle
import WechatOpenAccountPreview from './wechatOpenAccountPreview';

const DeleteConfirm = (props) => {
  const {
    modal,
    templateId,
    openAppId,
    value,
    title,
    intl,
    tenantId,
    wechatOpenAccountListDs,
    HeaderStore: {
      getTenantConfig: { themeColor },
    },
  } = props;

  const [deleteRemote, setDeleteRemote] = useState(false);

  const handleOk = async () => {
    const url = `/ecos/v1/${tenantId}/weChat/message/template?openAppId=${openAppId}&templateId=${templateId}&delRemote=${deleteRemote || 'false'}`;
    await axios.delete(url);
    await wechatOpenAccountListDs.query();
  };

  modal.handleOk(async () => {
    await handleOk();
  });
  return (
    <div>
      <div
        style={{ display: 'flex', alignItems: 'flex', marginBottom: '20px' }}
      >
        <Icon type="info" theme="filled" size="24" fill={themeColor} />
        <span style={{ fontSize: '18px', marginLeft: '10px' }}>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.wechat.open.account.delete.confirm', defaultMessage: '删除确认' })}</span>
      </div>
      <div style={{ marginBottom: '20px' }}>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.wechat.open.account.do.you.realy.delete', defaultMessage: '您要删除以下模板吗' })}</div>
      <WechatOpenAccountPreview
        value={value}
        title={title}
        scale={0.7}
        style={{ marginBottom: '20px' }}
      />
      <CheckBox checked={deleteRemote} onChange={(v) => setDeleteRemote(v)}>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.wechat.open.account.with.remote', defaultMessage: '连同微信远端的模板一同删除' })}</CheckBox>
    </div>
  );
};

export default inject('HeaderStore')(DeleteConfirm);
