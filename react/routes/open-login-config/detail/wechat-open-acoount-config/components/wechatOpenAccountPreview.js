import React, { useEffect, useRef, useState } from 'react';
import { Icon, Button } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import styles from './WechatPreview.module.less';
// eslint-disable-next-line import/no-cycle
import { exampleTransformer } from '../index';

const mockData = [
  {
    name: 'Field1',
    key: 'thing1',
    value: 'Value',
  },
  {
    name: 'Field2',
    key: 'thing2',
    value: 'Value',
  },
  {
    name: 'Field3',
    key: 'thing3',
    value: 'Value',
  },
  {
    name: 'Field4',
    key: 'thing4',
    value: 'Value',
  },
  {
    name: 'Field5',
    key: 'thing5',
    value: 'Value',
  },
  {
    name: 'Field6',
    key: 'thing6',
    value: 'Value',
  },
];

const mockTitle = 'Title';

const WechatOpenAccountPreview = injectIntl((props) => {
  const {
    value,
    title,
    contentDate,
    needToTransformFlag,
    exampleData,
    scale,
    intl,
    style,
    wider = false, // 更宽
  } = props;
  const previewCardRef = useRef();
  const [showValue, setShowValue] = useState(mockData);
  const [showTitle, setShowTitle] = useState(mockTitle);

  useEffect(() => {
    if (needToTransformFlag) {
      const { value: _value } = exampleTransformer(exampleData, contentDate);
      setShowValue(_value);
    }
  }, [exampleData, contentDate]);

  useEffect(() => {
    if (value && scale && !wider) {
      // wider目前不能支持缩放（因为缩放的情况比较复杂，不一定适配）
      // 这段代码的意义是估算框的高度。因为缩放的时候，如果不估算高度，就会出现底部留白过大的问题。
      const lineNum = value.length;
      previewCardRef.current.style.height = `${
        (41 * lineNum + 180) * scale * 0.777
      }px`; // 这些数字是根据css的高度线性拟合的。如果改了css这里也得改。css是仿照微信的样式临摹的。
      previewCardRef.current.style.width = `${
        480 * 0.777 * scale // 也是基于CSS拟合的
      }px`;
    }
  }, [value, scale]);

  useEffect(() => {
    if (value) {
      if (typeof value === 'string') {
        const _value = JSON.parse(value);
        setShowValue(_value);
      } else {
        setShowValue(value);
      }
    }
  }, [value]);

  useEffect(() => {
    if (title) {
      setShowTitle(title);
    }
  }, [title]);

  return (
    <div ref={previewCardRef} style={style}>
      <div
        className={styles.frame}
        style={{
          transform: scale ? `scale(${scale})` : undefined,
          transformOrigin: '0% 0%',
          width: wider ? '505.05px' : '372.96px',
        }}
      >
        <div className={styles.title}>{showTitle}</div>
        {showValue?.map((item) => (
          <div className={styles.item}>
            <div className={styles.itemHeader} title={item.key}>
              {item.name || item.key}
            </div>
            <div title={item.key}>{item.value}</div>
          </div>
        ))}
        <div className={styles.divide} />
        <div className={styles.footer}>
          <div>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.wechat.open.account.click.view.more', defaultMessage: '点击查看详情' })}</div>
          <div>
            <Icon
              theme="outline"
              type="right"
              strokeWidth={3}
              fill="#bbbabd"
              size={28}
            />
          </div>
        </div>
      </div>
    </div>
  );
});

export default inject('AppState')(observer((props) => {
  return (
    <WechatOpenAccountPreview {...props} />
  );
}));

/* externalize: WechatOpenAccountPreview */
