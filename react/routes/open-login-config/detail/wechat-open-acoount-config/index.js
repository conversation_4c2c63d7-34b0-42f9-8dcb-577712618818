import React, { useContext, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { axios, Content, Header, TabPage } from '@yqcloud/apps-master';
import { Button, TableHoverAction } from '@zknow/components';
import { Table, Modal, message } from 'choerodon-ui/pro';
import Store from '../../stores';
import styles from './WechatOpenAccountConfig.module.less';
// eslint-disable-next-line import/no-cycle
import WechatOpenAccountPreview from './components/wechatOpenAccountPreview';
import DeleteConfirm from './components/deleteConfirm';

const { Column } = Table;

export const exampleTransformer = (
  example = 'Field1：Value\\r\\nField2：Value\\r\\nField3：Value\\r\\n',
  content = '{{first.DATA}}\\nField1：{{keyword1.DATA}}\\nField2：{{keyword2.DATA}}\\nField3：{{keyword3.DATA}}\\n{{remark.DATA}}',
) => {
  const pretreat = (text) => {
    // 预处理：换行有\r\n 也有\n ，冒号有中文：也有英文: 。所以先统一成\n和:，然后按\n分割成数组
    let res = text.replace(/\r\n/g, '\n').replace(/\\r\\n/g, '\n').replace(/\\n/g, '\n').replaceAll('：', ':')
      .split('\n');
    if (res.length === 1) {
      res = res[0]?.split('\\n');
    }
    return res;
  };

  const getRealKey = (key) => {
    key = key.replace(/{/g, '');
    if (key?.includes('.DATA')) {
      return key?.split('.')[0];
    }
    return key;
  };

  // 对Content的的逻辑：
  // 先对content进行处理pretreat
  // 然后排除掉{{first.DATA}}和{{remark.DATA}}
  // 然后排除掉没有: 冒号的
  // 最后提取出:前面的字段名称作为name，后面的字段作为key
  const transformedContent = pretreat(content)
    ?.filter((item) => { return !item.includes('{{first.DATA}}') && !item.includes('{{remark.DATA}}'); })
    ?.filter((line) => { return line.includes(':'); })
    ?.map((item) => {
      const res = item.split(':');
      return {
        key: getRealKey(res[1]),
        name: res[0],
      };
    });
  // 对Example的处理逻辑：
  // 先对example进行预处理pretreat
  // 然后排除掉没有:的项
  // 然后按:将项分成两部分，:前面的字段名称作为name，后面的字段作为value
  const transformedExample = pretreat(example)
    ?.filter((line) => { return line.includes(':'); })
    ?.map((line) => {
      const res = line.split(':');
      return {
        value: line.match(/:(.*)/)?.[1],
        name: line.split(':')?.[0],
      };
    });
  // 最后合并成一个对象
  transformedContent.forEach((item) => {
    const res = transformedExample.find((contentItem) => contentItem.name === item.name);
    if (res) {
      item.value = res?.value;
    }
  });

  const valueArray = [];
  const fieldArray = [];
  transformedContent.forEach((item) => {
    valueArray.push(item.value);
    fieldArray.push(item.name);
  });
  return { value: transformedContent, valueArray, fieldArray };
};

const WechatOpenAccountConfig = (props) => {
  const { match: { params: { id } }, edit, setEdit } = props;
  const context = useContext(Store);
  const { intl, prefixCls, wechatOpenAccountListDs, tenantId, infoDetailDataSet } = context;

  const handlePreview = (record) => {
    const example = record.get('example');
    const content = record.get('content');
    const title = record.get('title');
    const { value } = exampleTransformer(example, content);
    Modal.open({
      drawer: true,
      title: intl.formatMessage({ id: 'zknow.common.button.preview', defaultMessage: '预览' }),
      children: <WechatOpenAccountPreview value={value} title={title} />,
      okText: intl.formatMessage({ id: 'zknow.common.button.ok', defaultMessage: '确定' }),
      destroyOnClose: true,
      cancelButton: false,
    });
  };

  const handleDelete = (record) => {
    const templateId = record?.get('id');
    const openAppId = record?.get('openAppId');
    const example = record.get('example');
    const content = record.get('content');
    const title = record.get('title');
    const { value } = exampleTransformer(example, content);
    Modal.open({
      children: <DeleteConfirm templateId={templateId} openAppId={openAppId} value={value} title={title} tenantId={tenantId} wechatOpenAccountListDs={wechatOpenAccountListDs} intl={intl} />,
      okText: intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' }),
    });
  };

  const renderField = ({ record }) => {
    const example = record.get('example');
    const content = record.get('content');
    const { fieldArray } = exampleTransformer(example, content);
    return (<div>{fieldArray.join(', ')}</div>);
  };

  const renderTableAction = ({ dataSet, record }) => {
    const actions = [
      {
        name: intl.formatMessage({ id: 'zknow.common.button.preview', defaultMessage: '预览' }),
        icon: 'preview-open',
        onClick: () => handlePreview(record),
      },
      {
        name: intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' }),
        icon: 'delete',
        onClick: () => handleDelete(record),
      },
    ];

    return <TableHoverAction record={record} actions={actions} />;
  };

  return (
    <TabPage className={`${prefixCls}-config`}>
      <Header>
        <h1>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.wechat.open.account.template.config', defaultMessage: '模板配置' })}</h1>
      </Header>
      <Content style={{ paddingTop: '5px' }}>
        <div className={styles.buttonArea}>
          <Button
            funcType="raised"
            color="primary"
            onClick={() => {
              const appId = infoDetailDataSet.current.get('id');
              axios.put(`/ecos/v1/${tenantId}/weChat/message/sync/template?openAppId=${appId}`).then((res) => {
                if (!res.failed) {
                  message.success(intl.formatMessage({ id: 'zknow.common.success.update', defaultMessage: '更新成功' }));
                  wechatOpenAccountListDs.query();
                }
              });
            }}
          >
            {intl.formatMessage({ id: 'iam.openLoginConfig.desc.wechat.open.account.sync.template', defaultMessage: '同步公众号模板' })}
          </Button>
          <Button
            funcType="raised"
            color="secondary"
            onClick={() => {
              Modal.open({
                title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.wechat.open.account.template.guide', defaultMessage: '模板管理指引' }),
                drawer: true,
                style: { width: '50%' },
                children: (
                  <iframe
                    style={{ width: '100%', height: '100%', border: 'none', overflow: 'hidden' }}
                    title={intl.formatMessage({ id: 'iam.openLoginConfig.desc.wechat.open.account.template.guide', defaultMessage: '模板管理指引' })}
                    src="https://support.yqcloud.com/#/knowledge/share/support/0077313c0f86cff228754ef54eb83560?portal=true&solutionId=358734080981991424&tenantId=228549383619211264&menu=knowledge&knowledgeId=527423469463752704&sourceModule=KNOWLEDGE_CENTER&sourceFunction=OUTSIDE_LINK"
                  />
                ),
              });
            }}
          >
            {intl.formatMessage({ id: 'iam.openLoginConfig.desc.wechat.open.account.template.guide', defaultMessage: '模板管理指引' })}
          </Button>
        </div>
        <Table dataSet={wechatOpenAccountListDs} pristine autoHeight>
          <Column name="template_id" width={250} lock="left" />
          <Column name="title" width={150} lock="left" />
          <Column name="field" renderer={renderField} />
          <Column name="primary_industry" width={150} />
          <Column name="deputy_industry" width={150} />
          <Column width={150} renderer={renderTableAction} tooltip="none" />
        </Table>
      </Content>
    </TabPage>
  );
};

export default observer(WechatOpenAccountConfig);
