import React, { useCallback, useContext, useEffect, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import copy from 'copy-to-clipboard';
import { injectIntl } from 'react-intl';
import { inject } from 'mobx-react';
import { Content, Header, TabPage } from '@yqcloud/apps-master';
import { formatterCollections } from '@zknow/utils';
import {
  DateTimePicker,
  Form,
  Lov,
  message,
  Output,
  Select,
  SelectBox,
  Switch,
} from 'choerodon-ui/pro';
import { Button, ExternalComponent, FileUploader, Icon as YQIcon, YqAvatar } from '@zknow/components';
import { Alert } from 'choerodon-ui';
import FieldsConfig from './FieldsConfig';
import Store from '../../stores';
import styles from '../Detail.module.less';
import '../index.less';

function ListView(props) {
  const {
    match: { params: { id } },
    edit,
    setEdit,
    handleSave,
    handleCancel,
    toggleEdit,
    usualOpenAppFlag,
    handleSync,
    handleOpenSyncHistory,
    handleConfigCheck,
    handleEnabled,
  } = props;
  const context = useContext(Store);
  const {
    intl, prefixCls, intlPrefix, organizationId, history, userDataSet,
    departmentDataSet, infoDetailDataSet, type, tenantId, openFunctionDataSet,
    choerodonUserDataSet, HeaderStore: { tenantConfig: { domain } },
    syncHistoryDataSet,
  } = context;

  const modalStyle = useMemo(() => ({ width: 800 }), []);
  const search = history.location?.search;

  useEffect(() => {
    const appItemId = infoDetailDataSet?.current?.get('id');
    const appItemType = infoDetailDataSet?.current?.get('type');
    if (appItemId && appItemType === 'choerodon') {
      choerodonUserDataSet.query();
    }
  }, [infoDetailDataSet?.current?.get('id')]);

  const renderRole = useCallback(() => {
    if (usualOpenAppFlag) {
      return <Form className="yq-mt-16" disabled={!edit} header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.default.role', defaultMessage: '默认角色' })} labelWidth="auto" columns={1} labelLayout="horizontal" dataSet={infoDetailDataSet}>
        <ExternalComponent
          system={{ scope: 'itsm', module: 'MultiSelect' }}
          disabled={!edit}
          addIcon="plus"
          dataSet={infoDetailDataSet}
          name="config.defaultRoleId"
          queryUrl={`/iam/yqc/v1/${tenantId}/roles/paging`}
          queryMethod="get"
          queryParam="name"
          colSpan={1}
          label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.role.condition', defaultMessage: '角色条件' })}
        />
      </Form>;
    }
    return null;
  }, [infoDetailDataSet?.current, edit, usualOpenAppFlag]);

  const actionButtons = () => {
    if (edit) {
      return (
        <div className={`${prefixCls}-form-buttons`}>
          <Button funcType="raised" onClick={handleSave} color="primary">{intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}</Button>
          <Button funcType="raised" onClick={handleCancel}>{intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}</Button>
        </div>
      );
    } else {
      return (
        <div className={`${prefixCls}-form-buttons`}>
          <Button funcType="raised" onClick={toggleEdit} color="primary" icon="write">{intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}</Button>
        </div>
      );
    }
  };

  const actionsList = useMemo(() => {
    const action = [
      {
        name: infoDetailDataSet?.current?.get('enabledFlag') ? intl.formatMessage({ id: 'zknow.common.button.deactivated', defaultMessage: '停用' }) : intl.formatMessage({ id: 'zknow.common.button.enable', defaultMessage: '启用' }),
        icon: infoDetailDataSet?.current?.get('enabledFlag') ? 'reduce-one' : 'check-one',
        onClick: () => handleEnabled(),
      },
    ];
    if (usualOpenAppFlag) {
      action.push(
        {
          name: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.sync.hand', defaultMessage: '手动同步' }),
          icon: 'update-rotation',
          onClick: handleSync,
        },
        {
          name: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.sync.history', defaultMessage: '同步历史' }),
          icon: 'history',
          onClick: handleOpenSyncHistory,
        },
        {
          name: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.config.check', defaultMessage: '配置检查' }),
          icon: 'check-one',
          onClick: handleConfigCheck,
        },
      );
    }
    return action;
  }, [infoDetailDataSet?.current, usualOpenAppFlag]);

  const renderNotifyTemplate = useCallback(() => (
    <div style={{ display: 'flex', alignItems: 'center', paddingRight: !edit ? '0.9rem' : '0.5rem' }}>
      <Lov
        name="notificationConfig"
        style={{ flexGrow: 1 }}
        onChange={(value) => {
          const record = infoDetailDataSet.current;
          if (value.id) {
            record.set('notificationConfigId', value.id);
            record.set('notificationConfigName', value.name);
          }
        }}
      />
      {edit && (
        <div
          className={styles.notificationLink}
          onClick={() => {
            window.open(`${window.location.origin}/#/hmsg/notification_template${search}`, '_blank');
          }}
        >
          <YQIcon type="share" size={18} fill="currentColor" />
        </div>
      )}
    </div>
  ), [search, infoDetailDataSet?.current, edit]);
  const renderFormContent = useCallback(() => {
    if (infoDetailDataSet?.current) {
      const appType = infoDetailDataSet?.current?.get('type');
      if (!infoDetailDataSet.current?.get('config.passwordFlag')) {
        infoDetailDataSet.current?.set('config.passwordFlag', false);
      }

      return (
        <div className={`${prefixCls}-config-body`}>
          {usualOpenAppFlag && (
            <Form className="yq-mt-16" disabled={!edit} header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.field.match.rule', defaultMessage: '字段匹配原则' })} labelWidth={100} columns={1} labelLayout="horizontal" dataSet={infoDetailDataSet}>
              {appType === 'wechat_enterprise' && <Alert
                // className={styles.alert}
                showIcon
                iconType="info"
                type="info"
                message={intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.wxwork.tips', defaultMessage: '根据企微隐私政策，暂不支持查询用户的敏感信息，如手机号与邮箱等信息。' })}
              />}
              <FieldsConfig type="user" appType={appType} dataSet={userDataSet} disabled={!edit} />
              <FieldsConfig type="department" appType={appType} dataSet={departmentDataSet} disabled={!edit} />
            </Form>
          )}
          {usualOpenAppFlag && (
            <Form className="yq-mt-16" disabled={!edit} header={intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.data.synchronization', defaultMessage: '数据同步' })} labelWidth={100} columns={2} labelLayout="horizontal" dataSet={infoDetailDataSet}>
              <Output name="notificationConfig" renderer={renderNotifyTemplate} />
              <SelectBox name="noticeScope" />
              <Switch className={`${prefixCls}-switch`} renderer={!edit ? ({ value }) => (value ? intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' }) : intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' })) : undefined} name="config.timingFlag" />
              {infoDetailDataSet.current?.get('config.timingFlag') && [
                <DateTimePicker mode="dateTime" newLine name="config.startSyncTime" id="yq-test-iam-ldap-sync_start1" />,
                <DateTimePicker mode="dateTime" name="config.endSyncTime" id="yq-test-iam-ldap-sync_end1" />,
                <Select name="config.frequency" />,
              ]}
              {['ding_talk', 'wechat_enterprise', 'lark'].includes(appType) && (
                <div name="realTimeSyncFlag" className={styles.realtimeSync}>
                  <Switch name="realTimeSyncFlag" />
                  {appType !== 'wechat_enterprise' && (
                    <Alert
                      className={styles.realtimeSyncAlert}
                      showIcon
                      iconType="info"
                      type="info"
                      message={intl.formatMessage({ id: 'iam.openLoginConfig.desc.realtime.tips', defaultMessage: '若要支持实时同步，需要填写基本信息中的回调信息' })}
                    />
                  )}
                </div>
              )}
              {appType === 'wechat_enterprise' && <Switch name="tagSyncFlag" className={`${prefixCls}-switch`} label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.tag.sync.flag', defaultMessage: '启用标签同步' })} />}
            </Form>
          )}
          {renderRole()}
        </div>
      );
    }
  }, [infoDetailDataSet?.current, edit, usualOpenAppFlag, renderNotifyTemplate]);

  function copyText(text) {
    copy(text);
    message.success(intl.formatMessage({ id: 'zknow.common.success.copy', defaultMessage: '复制成功' }));
  }

  return (
    <TabPage className={`${prefixCls}-config`}>
      <Header
        // backPath={`/iam${type === 'site' ? '/site' : ''}/open_app_config${search}`}
        actionsList={actionsList}
        dataSet={infoDetailDataSet}
      // onRefresh={refresh}
      >
        <h1>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.menu.name.sync.user', defaultMessage: '人员同步' })}</h1>
        <div>{actionButtons()}</div>
      </Header>
      <Content>
        {renderFormContent()}
      </Content>
    </TabPage>
  );
}

export default inject('AppState')(formatterCollections({ code: 'iam.openLoginConfig' })(injectIntl(observer(ListView))));
