.syncHistroy<PERSON>rapper {
  height: 100%;

  .alert {
    margin-bottom: 16px;
  }

  .table {
    border-top: 1px solid rgba(203, 210, 220, 0.5);
  }
}

.statusWrapper {
  display: flex;
  align-items: center;

  .statusColor {
    width: 6px;
    height: 6px;
    flex-shrink: 0;
    border-radius: 50%;
    margin-right: 8px;
  }
}

.result {
  :global {
    .c7n-pro-table-cell-inner {
      height: unset !important;
    }
  }

  .resultRow {
    height: 22px;
    font-size: 14px;
    line-height: 22px;
    color: rgba(18, 39, 77, 0.85);
  }

  .user {
    margin-top: 11px;
  }

  .dept {
    margin-bottom: 11px;
    color: rgba(18, 39, 77, 0.65);
  }
}
.errorLogWrapper {
  overflow: auto;
}
