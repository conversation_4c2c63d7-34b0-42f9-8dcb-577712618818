import React, { useCallback, useMemo, useEffect } from 'react';
import classNames from 'classnames';
import axios from 'axios';
import moment from 'moment';
import { useRequest } from 'ahooks';
import { observer } from 'mobx-react-lite';
import styles from './SyncHistory.module.less';

const CompleteStatus = [-2, -1, 1];

const Result = observer(({ record, intl, tenantId }) => {
  const historyId = record.get('id');
  const syncStatusFlag = record.get('syncStatusFlag');
  const isToday = moment(record.get('syncBeginTime'), 'YYYY-MM-DD HH:mm:ss').isSame(moment(), 'day');

  const autoPollingFlag = !CompleteStatus.includes(syncStatusFlag) && isToday; // 只有今天且状态不是【同步失败、部分失败和同步完成】的

  const { data, cancel } = useRequest(
    () => axios.get(`/ecos/v1/${tenantId}/openApps/sync/histories/${historyId}/realTime`),
    { manual: !autoPollingFlag, pollingInterval: 2000 }
  );

  useEffect(() => {
    if (data) {
      record.init('totalUserCount', data.totalUserCount);
      record.init('totalDeptCount', data.totalDeptCount);
      record.init('successUserCount', data.successUserCount);
      record.init('successDeptCount', data.successDeptCount);
      record.init('errorUserCount', data.errorUserCount);
      record.init('errorDepartmentCount', data.errorDepartmentCount);
      record.init('syncStatusFlag', data.syncStatusFlag);
      record.init('syncStatusMeaning', data.syncStatusMeaning);
      if (CompleteStatus.includes(data.syncStatusFlag)) cancel();
    }
  }, [data]);

  const renderResultRow = useCallback((total, success, failed, type, className = '') => (
    <div className={classNames(styles.resultRow, className)}>
      <span>{intl.formatMessage({ id: `iam.openLoginConfig.desc.open.login.config.sync.total.${type}` }).replace('{total}', total)}:&nbsp;</span>
      <span style={{ color: '#1ab335' }}>&nbsp;{success}&nbsp;</span>
      <span>{intl.formatMessage({ id: 'zknow.common.status.success', defaultMessage: '成功' })}</span>
      <span style={{ color: failed !== '0' ? '#f34c4b' : 'unset' }}>&nbsp;{failed}&nbsp;</span>
      <span>{intl.formatMessage({ id: 'zknow.common.status.failure', defaultMessage: '失败' })}</span>
    </div>
  ), [intl]);

  const renderResult = useMemo(() => {
    const status = record.get('syncStatusFlag')?.toString();
    if (status === '-1') {
      return <span>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.sync.error.tips', defaultMessage: '请检查App等配置是否正确, 再尝试重新同步' })}</span>;
    } else {
      const totalUserCount = record.get('totalUserCount');
      const totalDeptCount = record.get('totalDeptCount');
      const successUserCount = record.get('successUserCount');
      const successDeptCount = record.get('successDeptCount');
      const errorUserCount = record.get('errorUserCount');
      const errorDepartmentCount = record.get('errorDepartmentCount');
      return (
        <>
          {renderResultRow(totalUserCount, successUserCount, errorUserCount, 'user', styles.user)}
          {renderResultRow(totalDeptCount, successDeptCount, errorDepartmentCount, 'dept', styles.dept)}
        </>
      );
    }
  }, [intl, JSON.stringify(record.toData?.()), renderResultRow]);

  return renderResult;
});

export default Result;
