import React, { useCallback, useEffect, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import axios from 'axios';
import { useRequest, useLockFn } from 'ahooks';
import { message, Modal } from 'choerodon-ui/pro';
import { TableHoverAction } from '@zknow/components';
import SyncResult from '../sync-result';

const TableAction = observer(({ record, intl, tenantId, intlPrefix }) => {
  const { runAsync } = useRequest(
    (historyId) => axios.put(`/ecos/v1/${tenantId}/openApps/sync/histories/${historyId}/retry`),
    { manual: true }
  );

  const { data, run, cancel } = useRequest(
    (historyId) => axios.get(`/ecos/v1/${tenantId}/openApps/sync/histories/${historyId}/realTime`),
    { manual: true, pollingInterval: 2000 }
  );

  useEffect(() => {
    if (data) {
      record.init('totalUserCount', data.totalUserCount);
      record.init('totalDeptCount', data.totalDeptCount);
      record.init('successUserCount', data.successUserCount);
      record.init('successDeptCount', data.successDeptCount);
      record.init('errorUserCount', data.errorUserCount);
      record.init('errorDepartmentCount', data.errorDepartmentCount);
      if (data.syncStatusFlag !== 0) cancel();
    }
  }, [data]);
  
  const handleRetry = useLockFn(async (id) => {
    if (id) {
      await runAsync(id).then(() => {
        message.success(intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.sync.running', defaultMessage: '同步开始运行' }));
        record.dataSet.query();
        run(id);
      }).catch((err) => {
        message.error(err.message);
      });
    } else {
      message.error('not found history id');
    }
  });

  const handleOpenResult = useCallback((id) => {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.openLoginConfig.model.sync.results', defaultMessage: '同步结果' }),
      drawer: true,
      maskClosable: true,
      style: { width: 900, maxWidth: '75vw' },
      bodyStyle: { padding: 0 },
      children: <SyncResult historyId={id} tenantId={tenantId} intlPrefix={intlPrefix} />,
      okText: intl.formatMessage({ id: 'iam.openLoginConfig.desc.retry', defaultMessage: '重试' }),
      okProps: { icon: 'refresh' },
      cancelButton: false,
      onOk: async () => {
        await handleRetry(id);
        return false;
      },
    });
  }, [tenantId, intl, handleRetry]);

  const actions = useMemo(() => {
    const failed = record.get('syncStatusFlag') < 0;
    const action = [{
      name: intl.formatMessage({ id: 'iam.openLoginConfig.model.sync.results', defaultMessage: '同步结果' }),
      icon: 'list-success',
      onClick: () => handleOpenResult(record.get('id')),
    }];
    if (failed) {
      action.push({
        name: intl.formatMessage({ id: 'iam.openLoginConfig.desc.retry', defaultMessage: '重试' }),
        icon: 'redo',
        onClick: () => handleRetry(record.get('id')),
      });
    }
    return action;
  }, [JSON.stringify(record.toData?.())]);

  return <TableHoverAction record={record} actions={actions} />;
});

export default TableAction;
