import React, { useCallback, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import { Table, Modal, Spin } from 'choerodon-ui/pro';
import { useRequest } from 'ahooks';
import { inject } from 'mobx-react';
import axios from 'axios';
import { Alert } from 'choerodon-ui';
import { formatterCollections } from '@zknow/utils';
import TableAction from './TableAction';
import styles from './SyncHistory.module.less';
import Result from './Result';

const { Column } = Table;

const STATUS_COLOR = { 0: '#2979ff', '-2': '#fd7d23', 1: '#1ab335', '-1': '#f34c4b' };

const ErrorLog = ({ tenantId, id, type }) => {
  const { data, loading } = useRequest(
    () => axios.get(`/ecos/v1/${tenantId}/openApps/sync/histories/${id}/errorLog`),
    { refreshDeps: [tenantId, id, type] }
  );

  return (
    <Spin spinning={loading}>
      <div className={styles.errorLogWrapper}>
        {data || ''}
      </div>
    </Spin>
  );
};

const SyncHistroy = (props) => {
  const { intlPrefix, dataSet, tenantId } = props;

  const intl = useIntl();

  useEffect(() => {
    dataSet.query();
  }, []);

  const renderStatus = useCallback(({ record }) => {
    const status = record.get('syncStatusFlag')?.toString();
    const value = record.get('syncStatusMeaning')?.toString();
    const color = STATUS_COLOR[status] || '#2979ff';
    return status ? (
      <div className={styles.statusWrapper}>
        <span className={styles.statusColor} style={{ backgroundColor: color }} />
        <span>{value}</span>
      </div>
    ) : null;
  }, []);

  const handleOpenError = useCallback((id, name) => {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.openLoginConfig.model.yqcloud.iam.ldap.sync.log', defaultMessage: '错误日志' }),
      style: { width: '50vw', overflow: 'auto' },
      children: <ErrorLog tenantId={tenantId} id={id} type={name} />,
      okButton: false,
      cancelButton: false,
      maskClosable: true,
    });
  }, [intl, tenantId]);

  const renderErrorLog = useCallback(({ record }) => {
    const errorLogFlag = record.get('syncStatusFlag') === -1;
    const name = dataSet.name;
    return errorLogFlag ? (
      <span style={{ color: '#2979ff', cursor: 'pointer' }} onClick={() => handleOpenError(record.get('id'), name)}>
        {intl.formatMessage({ id: 'zknow.common.button.view', defaultMessage: '查看' })}
      </span>
    ) : '-';
  }, [intl, handleOpenError]);

  return (
    <div className={styles.syncHistroyWrapper}>
      <Alert
        className={styles.alert}
        showIcon
        iconType="info"
        type="info"
        message={intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.sync.history.tips', defaultMessage: '仅保留3个月内的同步历史记录' })}
      />
      <Table
        pristine
        className={styles.table}
        dataSet={dataSet}
        queryBarProps={{ fuzzyQuery: true, simpleMode: true, inlineSearch: false }}
      >
        <Column name="syncType" width={100} />
        <Column name="syncBeginTime" width={200} />
        <Column name="syncStatusMeaning" width={100} renderer={renderStatus} />
        <Column
          className={styles.result}
          name="result"
          width={300}
          renderer={({ record }) => <Result record={record} intl={intl} intlPrefix={intlPrefix} tenantId={tenantId} />}
        />
        <Column name="errorLog" renderer={renderErrorLog} width={50} />
        <Column name="executor" width={100} />
        <Column
          tooltip="none"
          width={1}
          renderer={({ record }) => <TableAction record={record} intl={intl} tenantId={tenantId} intlPrefix={intlPrefix} />}
        />
      </Table>
    </div>
  );
};

export default inject('AppState')(formatterCollections({ code: 'iam.openLoginConfig' })(observer(SyncHistroy)));
