import React, { useContext, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { Select, TextField, Lov, Switch, Tooltip } from 'choerodon-ui/pro';
import { Icon, StatusTag } from '@zknow/components';
import classnames from 'classnames';
import Store from '../../stores';
import '../index.less';
import styles from '../Detail.module.less';

export default observer(({ type = 'user', appType, dataSet, disabled }) => {
  const { prefixCls, intl, intlPrefix, nameMap, keepValueMapping, defaultDataSet } = useContext(Store);
  const curPrefixCls = `${prefixCls}-config-body-user`;

  const infoRef = useRef();

  const handleOpenChange = (value, record) => {
    if (value === 'CUSTOM' || value === 'custom') {
      record.set('openCode', '');
    } else {
      record.set('openCode', value);
    }
  };

  const handleLovChange = (value, record) => {
    const { code: fieldCode, name: fieldName } = value || {};
    record.set(`${type}Info.fieldCode`, fieldCode);
    record.set(`${type}Info.fieldName`, fieldName);
    record.set('constantWidgetType', value);
    if (!fieldCode) {
      record.set('openCode', '');
      record.set('openName', '');
    }
  };

  const handleCreate = () => {
    if (disabled) return;
    const record = dataSet.create();
    dataSet.current = record;
    infoRef.current?.openModal();
  };

  const handleClose = () => {
    dataSet.remove(dataSet.current);
  };

  // useEffect(() => {
  //   dataSet.forEach((record, index) => {
  //     if (index < 1 && type === 'user') {
  //       record.set('updateFlag', false);
  //     }
  //   });
  // }, [dataSet]);

  function renderWxWorkOptions({ text, record }) {
    const privateFieldCodes = ['email', 'phone', 'mobile', 'address', 'gender', 'biz_mail'];
    const basicFieldCodes = ['userid', 'name', 'main_department', 'position', 'telephone', 'external_position', 'custom', 'alias'];
    const code = record.get('openCode') || record.get('code');
    const isPrivateCode = privateFieldCodes.includes(code);
    const isBasicCode = !privateFieldCodes.includes(code);
    const isWxWork = appType === 'wechat_enterprise';
    return (
      <div className={styles.optionTag}>
        {text}
        {isWxWork && type === 'user' && (isPrivateCode || isBasicCode) && <StatusTag
          name="flag"
          color={isPrivateCode ? 'rgb(127, 131, 247)' : 'rgb(204, 247, 131)'}
        >
          {isPrivateCode ? intl.formatMessage({ id: 'iam.openLoginConfig.detail.openName.private.info', defaultMessage: '隐私' }) : intl.formatMessage({ id: 'iam.openLoginConfig.detail.openName.basic', defaultMessage: '基本' })}
        </StatusTag>}
      </div>
    );
  }

  function renderConstWidget(record) {
    return (
      // <WidgetField
      //   name="constantValue"
      //   code="constantValue"
      //   record={record}
      //   formDs={dataSet}
      //   {...record.get('constantWidgetType')}
      //   key={record.get('constantWidgetType.id')}
      // />
      <TextField className={`${curPrefixCls}-content-item`} name="constantValue" record={record} />
    );
  }
  const renderMappingFields = (record, index) => {
    const required = keepValueMapping[type]?.includes(record.get(`${type}Info.fieldCode`));
    return (
      <div className={`${curPrefixCls}-content-fields`}>
        {/* 字段名称 */}
        <div style={{ display: 'flex', flexDirection: 'column' }}>
          <Lov
            className={`${curPrefixCls}-content-item`}
            name={`${type}Info`}
            record={record}
            disabled={required}
            onChange={(value) => handleLovChange(value, record)}
            tableProps={{ filter: (r) => !dataSet.some(f => f.get(`${type}Info.fieldCode`) === r.get('code')) }}
          />
          {record.get(`${type}Info.fieldCode`) === 'department_id' && (
            <div>
              <span>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.fieldName.department', defaultMessage: '同步所属部门' })}</span>
              <Switch record={record} name="syncUserDepartments" />
            </div>
          )}
        </div>
        {/* 关系 */}
        <div style={{ width: '50px' }} className={`${curPrefixCls}-content-item`}>
          {intl.formatMessage({ id: 'zknow.common.condition.from', defaultMessage: '来源于' })}
        </div>
        {/* 字段名称 */}
        <Select
          className={`${curPrefixCls}-content-item`}
          name="openName"
          record={record}
          optionRenderer={renderWxWorkOptions}
          renderer={renderWxWorkOptions}
          // disabled={required && appType === 'wechat_enterprise'}
          onChange={(value) => handleOpenChange(value, record)}
        // optionsFilter={handleAppOptionsFilter}
        />
        {/* 飞书字段 */}
        {(['CONSTANT', 'constant'].includes(record.get('openCode')) || ['CONSTANT', 'constant'].includes(record.get('openName'))) ? (renderConstWidget(record)) : (
          <TextField className={`${curPrefixCls}-content-item`} name="openCode" record={record} disabled={(!['CUSTOM', 'custom'].includes(record.get('openCode')) && !['CUSTOM', 'custom'].includes(record.get('openName')))} />
        )}
        {['ding_talk', 'wechat_enterprise', 'lark'].includes(appType) && <Switch disabled={index < 1 && type === 'user'} className={`${curPrefixCls}-content-item`} record={record} name="updateFlag" />}
        {!disabled && !required && (
          <div className={`${curPrefixCls}-content-delete`} onClick={() => dataSet.remove(record)}>
            <Icon type="delete" theme="outline" size="14" fill="#f8353f" />
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={curPrefixCls}>
      <div className={`${curPrefixCls}-header`}>
        {type === 'department' ? intl.formatMessage({ id: 'zknow.common.model.department', defaultMessage: '部门' }) : intl.formatMessage({ id: 'zknow.common.model.person', defaultMessage: '人员' })}
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.data.mapping', defaultMessage: '数据映射关系' })}
      </div>
      <div className={`${curPrefixCls}-content`}>
        <div className={`${curPrefixCls}-content-fieldsname`}>
          <div className={`${curPrefixCls}-content-item`}>
            {intl.formatMessage({ id: 'zknow.common.model.fieldName', defaultMessage: '字段名称' })}
          </div>
          <div style={{ width: '50px' }} className={`${curPrefixCls}-content-item`}>
            {intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.relation', defaultMessage: '关系' })}
          </div>
          <div className={`${curPrefixCls}-content-item`}>
            {`${nameMap[appType]}${intl.formatMessage({ id: 'zknow.common.model.fieldName', defaultMessage: '字段名称' })}`}/{intl.formatMessage({ id: 'iam.openLoginConfig.desc.constant.value', defaultMessage: '固定值' })}
          </div>
          <div className={`${curPrefixCls}-content-item`}>
            {`${nameMap[appType]}${intl.formatMessage({ id: 'zknow.common.model.field', defaultMessage: '字段' })}code`}/{intl.formatMessage({ id: 'iam.openLoginConfig.desc.constant', defaultMessage: '常量' })}
          </div>
          {['ding_talk', 'wechat_enterprise', 'lark'].includes(appType) && <div className={`${curPrefixCls}-content-item`}>
            <span>{`${intl.formatMessage({ id: 'iam.openLoginConfig.desc.real.time.update', defaultMessage: '同步时是否覆盖' })}`}</span>
            <Tooltip placement="top" title={intl.formatMessage({ id: 'iam.openLoginConfig.desc.sync.tips', defaultMessage: '关闭后，数据同步时不会对该字段进行任何更新' })}>
              <Icon className="card-value-help" type="Help" />
            </Tooltip>
          </div>}
        </div>
        {dataSet.status === 'ready' && dataSet.length > 0 && dataSet.map(renderMappingFields)}
        <div className={classnames({ [`${curPrefixCls}-content-addfields`]: true, disabled })} onClick={handleCreate}>
          <Icon type="file-addition" theme="outline" size="14" fill="currentColor" />
          <span style={{ marginLeft: '4px' }}>
            {intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.add.field', defaultMessage: '添加字段' })}
          </span>
          <Lov
            ref={infoRef}
            type="button"
            style={{ display: 'none' }}
            record={defaultDataSet.current}
            name={`${type}Info`}
            onChange={(value) => handleLovChange(value, dataSet.current)}
            modalProps={{ onCancel: handleClose }}
            tableProps={{ filter: (r) => !dataSet.some(f => f.get(`${type}Info.fieldCode`) === r.get('code')) }}
          />
        </div>
      </div>
    </div>
  );
});
