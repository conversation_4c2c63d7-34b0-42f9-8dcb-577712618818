/* eslint-disable react/no-danger */
import React, { useCallback, useMemo, useEffect, useState } from 'react';
import ReactDOMServer from 'react-dom/server';
import axios from 'axios';
import classNames from 'classnames';
import { useIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import { Spin, Modal } from 'choerodon-ui/pro';
import { useRequest } from 'ahooks';
import { inject } from 'mobx-react';
import { Button, Icon } from '@zknow/components';
import { formatterCollections } from '@zknow/utils';
import SyncHistory from '../sync-history';
import SyncResult from '../sync-result';
import styles from './ManualSync.module.less';

const ICON_MAP = {
  '-1': { type: 'close-one', fill: '#f34c4b' },
  1: { type: 'check-one', fill: '#1ab335' },
  '-2': { type: 'attention', fill: '#fd7d23' },
};

const ManualSync = (props) => {
  const { modal, intlPrefix, id, tenantId, syncHistoryDataSet } = props;

  const intl = useIntl();

  const [loading, setLoading] = useState(true);

  const { data } = useRequest(() => axios.post(`/ecos/v1/${tenantId}/openApps/sync/${id}`), {
    refreshDeps: [id, tenantId],
    throttleWait: 300,
  });

  const historyId = data?.id || '';

  const { data: realTimeData, run, cancel } = useRequest(
    (hisId) => axios.get(`/ecos//v1/${tenantId}/openApps/sync/histories/${hisId}/realTime`),
    { manual: true, pollingInterval: 1000 }
  );

  useEffect(() => {
    if (historyId) {
      run(historyId);
    }
  }, [historyId]);

  useEffect(() => {
    if (realTimeData && [-2, -1, 1].includes(realTimeData.syncStatusFlag)) {
      cancel();
      setLoading(false);
      modal?.update({
        header: null,
        style: { width: 448 },
      });
    }
  }, [realTimeData]);

  const handleOpenSyncHistory = (event) => {
    const historyElement = event.target;
    if (historyElement?.className && historyElement.className.includes(styles.syncHistory)) {
      Modal.open({
        title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.sync.history', defaultMessage: '同步历史' }),
        drawer: true,
        maskClosable: true,
        style: { width: 1000, maxWidth: '75vw' },
        children: (
          <SyncHistory intlPrefix={intlPrefix} dataSet={syncHistoryDataSet} tenantId={tenantId} />
        ),
        okButton: false,
        cancelButton: false,
      });
      modal?.close();
    }
  };

  const handleBackendProcess = useCallback(() => {
    // TODO backend process
    modal?.close();
  }, [modal]);

  const handleViewDetail = () => {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.openLoginConfig.model.sync.results', defaultMessage: '同步结果' }),
      drawer: true,
      maskClosable: true,
      style: { width: 900, maxWidth: '75vw' },
      bodyStyle: { padding: 0 },
      children: <SyncResult historyId={historyId} tenantId={tenantId} intlPrefix={intlPrefix} />,
      okButton: false,
      cancelButton: false,
    });
    modal?.close();
  };

  const syncHistory = useMemo(() => (
    <span className={styles.syncHistory}>
      {intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.sync.history', defaultMessage: '同步历史' })}
    </span>
  ), [handleOpenSyncHistory, intl]);

  const renderBrief = useCallback(() => {
    return intl.formatMessage({ id: 'iam.openLoginConfig.desc.manual.sync.tips', defaultMessage: '请耐心等待。您可以选择“后台运行”关闭此弹窗，已隐藏的弹窗可点击应用详情右上角更多操作中的 {sync.history} 查看' }).replace('{sync.history}', ReactDOMServer.renderToString(syncHistory));
  }, [syncHistory, intl]);

  const renderResult = useCallback(() => {
    if (!realTimeData || !realTimeData?.syncStatusFlag) return null;
    const {
      totalDeptCount, totalUserCount, successDeptCount, successUserCount,
      syncStatusFlag, errorDepartmentCount, errorUserCount, syncStatusMeaning,
    } = realTimeData;
    const iconProps = ICON_MAP[syncStatusFlag];
    return (
      <div className={styles.result}>
        <div className={styles.header}>
          <Icon {...iconProps} size={24} theme="filled" />
          <span className={styles.headerText}>{syncStatusMeaning}</span>
        </div>
        <div className={styles.content}>
          <div className={classNames(styles.resultRow)}>
            <span>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.sync.total.user', defaultMessage: '共同步{total}人' }).replace('{total}', totalUserCount)}:&nbsp;</span>
            <span style={{ color: '#1ab335' }}>&nbsp;{successUserCount}&nbsp;</span>
            <span>{intl.formatMessage({ id: 'zknow.common.status.success', defaultMessage: '成功' })}</span>
            <span style={{ color: errorUserCount !== '0' ? '#f34c4b' : 'unset' }}>&nbsp;{errorUserCount}&nbsp;</span>
            <span>{intl.formatMessage({ id: 'zknow.common.status.failure', defaultMessage: '失败' })}</span>
          </div>
          <div className={classNames(styles.resultRow)}>
            <span>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.sync.total.dept', defaultMessage: '共同步{total}部门' }).replace('{total}', totalDeptCount)}:&nbsp;</span>
            <span style={{ color: '#1ab335' }}>&nbsp;{successDeptCount}&nbsp;</span>
            <span>{intl.formatMessage({ id: 'zknow.common.status.success', defaultMessage: '成功' })}</span>
            <span style={{ color: errorDepartmentCount !== '0' ? '#f34c4b' : 'unset' }}>&nbsp;{errorDepartmentCount}&nbsp;</span>
            <span>{intl.formatMessage({ id: 'zknow.common.status.failure', defaultMessage: '失败' })}</span>
          </div>
        </div>
        <div className={styles.buttons}>
          <Button funcType="raised" color="primary" onClick={handleViewDetail}>
            {intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.detail.records', defaultMessage: '查看详细记录' })}
          </Button>
          <Button onClick={() => modal?.close()}>
            {intl.formatMessage({ id: 'zknow.common.button.close', defaultMessage: '关闭' })}
          </Button>
        </div>
      </div>
    );
  }, [intl, modal, realTimeData]);

  return loading ? (
    <div className={styles.manualSyncWrapper}>
      <Spin size="large" />
      <span className={styles.syncTips}>{intl.formatMessage({ id: 'iam.openLoginConfig.desc.synchronizing.people', defaultMessage: '人员同步中' })}</span>
      <span
        onClick={handleOpenSyncHistory}
        className={styles.syncBrief}
        dangerouslySetInnerHTML={{ __html: renderBrief() }}
      />
      <Button funcType="raised" color="primary" onClick={handleBackendProcess}>
        {intl.formatMessage({ id: 'iam.openLoginConfig.desc.background.process', defaultMessage: '后台运行' })}
      </Button>
    </div>
  ) : renderResult();
};

export default inject('AppState')(formatterCollections({ code: ['zknow.common', 'iam.openLoginConfig'] })(observer(ManualSync)));
