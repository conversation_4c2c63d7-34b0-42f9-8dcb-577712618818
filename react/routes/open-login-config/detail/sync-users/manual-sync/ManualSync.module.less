.manualSyncWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;

  .syncTips {
    min-height: 21px;
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #12274d;
    line-height: 21px;
    margin: 15px 0 9px 0;
  }

  .syncBrief {
    min-height: 36px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    text-align: center;
    color: rgba(18, 39, 77, 0.65);
    line-height: 18px;
    margin-bottom: 30px;

    .syncHistory {
      color: #2979ff;
      cursor: pointer;
    }
  }
}

.result {
  .header {
    display: flex;

    .headerText {
      height: 24px;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #12274d;
      line-height: 24px;
      margin-left: 8px;
    }
  }

  .content {
    margin: 12px 0 32px 32px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    color: rgba(18, 39, 77, 0.65);
    line-height: 22px;

    .resultRow {
      font-size: 14px;
      line-height: 22px;
      color: rgba(18, 39, 77, 0.65);
    }
  }

  .buttons {
    display: flex;
    justify-content: flex-end;
  }
}
