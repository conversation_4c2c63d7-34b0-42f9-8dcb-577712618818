import React, { useMemo, useCallback, useEffect } from 'react';
import axios from 'axios';
import { useRequest, useLockFn } from 'ahooks';
import { useIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { inject } from 'mobx-react';
import { Table, Tabs, DataSet, message, Modal, Spin } from 'choerodon-ui/pro';
import { formatterCollections } from '@zknow/utils';
import { TableHoverAction } from '@zknow/components';
import syncDetailDataSet from '../../../stores/SyncDetailDataSet';
import styles from './SyncResult.module.less';

const STATUS_COLOR = { 0: '#2979ff', '-2': '#fd7d23', 1: '#1ab335', '-1': '#f34c4b' };

const ErrorLog = ({ tenantId, id, type }) => {
  const { data, loading } = useRequest(
    () => axios.get(`/ecos/v1/${tenantId}/openApps/sync/histories/${type}/${id}/errorLog`),
    { refreshDeps: [tenantId, id, type] }
  );

  return (
    <Spin spinning={loading}>
      <div className={styles.errorLogWrapper}>
        {typeof data === 'string' ? (data || '') : null}
      </div>
    </Spin>
  );
};

const TableAction = observer(({ record, tenantId, intl, intlPrefix, name }) => {
  const { runAsync } = useRequest(
    (id) => axios.put(`/ecos/v1/${tenantId}/openApps/sync/histories/${name}/${id}/retry`),
    { manual: true }
  );

  const { data, run, cancel } = useRequest(
    (id) => axios.get(`/ecos/v1/${tenantId}/openApps/sync/histories/${name}/${id}`),
    { manual: true, pollingInterval: 2000 }
  );

  useEffect(() => {
    if (data) {
      record.init('syncStatus', data.syncStatus);
      record.init('syncStatusMeaning', data.syncStatusMeaning);
      if (data.syncStatus !== 0) cancel();
    }
  }, [data]);

  const handleRetry = useLockFn(async (id) => {
    if (id) {
      await runAsync(id).then(() => {
        message.success(intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.sync.running', defaultMessage: '同步开始运行' }));
        run(id);
      }).catch((err) => {
        message.error(err.message);
      });
    } else {
      message.error('not found id');
    }
  });

  const actions = useMemo(() => {
    const failed = record.get('syncStatus') < 0;
    if (!failed) return [];
    else {
      return [{
        name: intl.formatMessage({ id: 'iam.openLoginConfig.desc.retry', defaultMessage: '重试' }),
        icon: 'redo',
        onClick: () => handleRetry(record.get('id')),
      }];
    }
  }, []);

  return <TableHoverAction record={record} actions={actions} />;
});

const SyncResult = (props) => {
  const { historyId, tenantId, intlPrefix } = props;

  const intl = useIntl();

  const commonProps = useMemo(() => ({ tenantId, historyId, intlPrefix, intl }), [tenantId, historyId, intlPrefix, intl]);

  const userDataSet = useMemo(() => new DataSet(syncDetailDataSet({ ...commonProps, type: 'users' })), [commonProps]);

  const deptDataSet = useMemo(() => new DataSet(syncDetailDataSet({ ...commonProps, type: 'departments' })), [commonProps]);

  const handleOpenError = useCallback((id, name) => {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.openLoginConfig.model.yqcloud.iam.ldap.sync.log', defaultMessage: '错误日志' }),
      style: { width: '50vw', overflow: 'auto' },
      children: <ErrorLog tenantId={tenantId} id={id} type={name} />,
      okButton: false,
      cancelButton: false,
      maskClosable: true,
    });
  }, [intl, tenantId]);

  const renderErrorLog = useCallback(({ record, dataSet }) => {
    const errorLogFlag = record.get('syncStatus')?.toString() === '-1';
    const name = dataSet.name;
    return errorLogFlag ? (
      <span style={{ color: '#2979ff', cursor: 'pointer' }} onClick={() => handleOpenError(record.get('id'), name)}>
        {intl.formatMessage({ id: 'zknow.common.button.view', defaultMessage: '查看' })}
      </span>
    ) : '-';
  }, [intl, handleOpenError]);

  const renderStatus = useCallback(({ record }) => {
    const status = record.get('syncStatus')?.toString();
    const syncStatusMeaning = record.get('syncStatusMeaning');
    const color = STATUS_COLOR[status];
    return status ? (
      <div className={styles.statusWrapper}>
        <span className={styles.statusColor} style={{ backgroundColor: color }} />
        <span>{syncStatusMeaning || '-'}</span>
      </div>
    ) : null;
  }, []);

  return (
    <Tabs>
      {[userDataSet, deptDataSet].map((dataSet, index) => (
        <Tabs.TabPane key={dataSet.name} title={index ? intl.formatMessage({ id: 'iam.openLoginConfig.detail.sync.dept', defaultMessage: '部门同步' }) : intl.formatMessage({ id: 'iam.openLoginConfig.detail.sync.user', defaultMessage: '人员同步' })}>
          <Table
            pristine
            dataSet={dataSet}
            queryBarProps={{ fuzzyQuery: true, simpleMode: true, inlineSearch: false }}
          >
            <Table.Column name="openId" />
            <Table.Column name="openName" />
            <Table.Column name="handleStatusMeaning" />
            <Table.Column name="syncStatusMeaning" renderer={renderStatus} />
            <Table.Column name="errorLog" renderer={renderErrorLog} />
            <Table.Column
              width={1}
              renderer={({ record }) => (
                <TableAction
                  record={record}
                  tenantId={tenantId}
                  intl={intl}
                  intlPrefix={intlPrefix}
                  name={dataSet.name}
                />
              )}
            />
          </Table>
        </Tabs.TabPane>
      ))}
    </Tabs>
  );
};

export default inject('AppState')(formatterCollections({ code: 'iam.openLoginConfig' })(injectIntl(observer(SyncResult))));
