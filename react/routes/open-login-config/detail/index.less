@import "~choerodon-ui/lib/style/themes/default";
@import "~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables";

.yq-mt-16 {
  margin-top: 16px;
}

.iam-open-login-config {
  &-imitative-info {
    background: #e7f3ff !important;
    padding: 0.1rem 0.16rem 0.08rem 0.17rem;
    box-sizing: border-box;
    border: 0.01rem solid #e7f3ff !important;
    border-radius: 0.04rem;
    color: #12274d;
    display: flex;
    align-items: center;
  }

  &-url {
    color: @primary-color;
    cursor: pointer;
  }

  padding: 0;

  .c7n-pro-form-header {
    margin-top: 24px !important;
  }

  &-type-table-enabledFlag {
    display: flex;
    align-items: center;

    .icon-dot {
      color: #75c940;
    }

    .icon-dot-red {
      color: #f8353f;
    }
  }

  .anticon:first-child:not(:last-child) {
    margin-right: 0.05rem;
  }

  &-switch {
    padding-top: 2px;
  }

  &-config {
    // padding: 0.16rem 0.24rem;
    &-header {
      display: flex;
      margin-bottom: 0.24rem;
      justify-content: space-between;
      align-items: center;
    }

    &-body {
      &-image-copper {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0.1;

        &:hover {
          opacity: 0.65;
        }
      }

      .yq-mt-16 {
        margin-top: 16px;
      }

      .c7n-pro-field-required {
        padding-left: 0.12rem;
      }

      &-user {
        min-height: 184px;
        font-weight: 500;
        font-size: 14px;

        &-header {
          height: 32px;
          line-height: 32px;
          color: #595959;
        }

        &-content {
          position: relative;
          min-height: 154px;
          padding: 0 16px;
          border-radius: 4px;
          border: 1px solid rgba(0, 0, 0, 0.15);

          &-item {
            width: 269px;
            position: relative;
            margin-right: 40px;

            .card-value-help {
              font-size: 0.16rem;
              color: @yq-text-4;
              top: 15px;
              position: absolute;
              margin-left: 5px;
            }

            .c7n-pro-input-wrapper,
            .c7n-pro-select-wrapper {
              width: 1.92rem;
            }
          }

          &-delete {
            position: absolute;
            right: 0;
            width: 32px;
            height: 32px;
            margin-left: -24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #feeaeb;
            border-radius: 4px;
            border: 1px solid #ffb4b0;
            cursor: pointer;

            &.field-default {
              padding: 0 0.07rem;
            }
          }

          &-fieldsname {
            display: flex;
            height: 48px;
            line-height: 48px;
            color: rgba(42, 45, 56, 0.85);

            &.field-default {
              height: 46px;
              border-bottom: 0.02rem solid rgba(0, 0, 0, 0.09);
              margin-bottom: 0.06rem;
            }
          }

          &-fields {
            display: flex;
            position: relative;
            margin-bottom: 8px;
            color: rgba(42, 45, 56, 0.85);
            font-weight: 400;
          }

          &-addfields {
            width: 124px;
            height: 24px;
            margin-bottom: 16px;
            margin-top: 8px;
            display: flex;
            align-items: center;
            color: @primary-color;
            font-size: 14px;
            line-height: 22px;
            cursor: pointer;

            &.disabled {
              cursor: default;
            }
          }
        }
      }
    }

    h4 {
      display: inline-block;
      padding-left: 0.08rem;
      border-left: 2px solid #2196f3;
    }

    &-suffix {
      cursor: pointer;
      color: @primary-color;
    }
  }

  &-guidelines {
    &-title {
      height: 0.2rem;
      font-size: 0.14rem;
      font-weight: 500;
      line-height: 0.2rem;
      color: #262626;
    }

    &-description {
      margin-top: 0.12rem;
      height: auto;
      font-size: 0.13rem;
      font-weight: 400;
      line-height: 0.2rem;
      color: #262626;
    }

    pre {
      position: relative;
      margin-top: 0.06rem;
      background: #f5f6fa;
      border-radius: 0.04rem;
      padding: 0.12rem 0.4rem 0.12rem 0.16rem;
      overflow-x: hidden;
      white-space: pre-wrap;
      word-break: break-all;
      color: #595959;
    }

    &-copy {
      position: absolute;
      right: 10px;
      cursor: pointer;

      &:hover {
        color: @primary-color;
      }
    }
  }

  &-link {
    display: flex;
    align-items: center;

    &-url {
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &-copy {
      margin-left: 0.12rem;
      color: @primary-color;
      cursor: pointer;
    }
  }

  &-wechatAgentConfig-agentAvatar {
    .editable {
      display: none;
    }
  }
}
