import React, { useContext, useEffect, useState, useRef, useMemo } from 'react';
import queryString from 'query-string';
import { axios } from '@yqcloud/apps-master';
import { useLocalStore } from 'mobx-react-lite';
import { getAccessToken } from '@zknow/utils';
import { message, Modal, DataSet } from 'choerodon-ui/pro';
import omit from 'lodash/omit';
import BindUser from './bind-user';
import SyncUser from './sync-users';
import LeftTabMenu from '@/components/left-tab-menu';
import Store from '../stores';
import MainView from './basic-info';
import WechatOpenAccountConfig from './wechat-open-acoount-config';
import defaultAvatar from '../assets/wechat_avatar.png';
import ManualSync from './sync-users/manual-sync';
import ConfigCheckModal from './modal/ConfigCheckModal';
import SyncHistory from './sync-users/sync-history';
import BindHistory from './bind-user/bind-history';
import ApprovalDataSet from './basic-info/components/approval/stores/ApprovalDataSet';

const complexMenu = [
  {
    icon: 'Info',
    code: 'basic_info',
    textCode: 'iam.openLoginConfig.detail.menu.name.basic.info',
    component: MainView,
    defaultMessage: '基本信息',
  },
  {
    icon: 'people-download',
    code: 'sync_user',
    textCode: 'iam.openLoginConfig.detail.menu.name.sync.user',
    component: SyncUser,
    defaultMessage: '人员同步',
  },
  {
    icon: 'branch-one',
    code: 'bind_user',
    textCode: 'iam.openLoginConfig.detail.menu.name.bind.user',
    component: BindUser,
    defaultMessage: '人员绑定',
  },
];

const wechatOpenAccountMenu = [
  {
    icon: 'Info',
    code: 'basic_info',
    textCode: 'iam.openLoginConfig.detail.menu.name.basic.info',
    component: MainView,
    defaultMessage: '基本信息',
  },
  {
    icon: 'mail',
    code: 'wechat_open_account_config',
    textCode: 'iam.openLoginConfig.detail.wechat.openAccount.templateConfig',
    component: WechatOpenAccountConfig,
    defaultMessage: '模板配置',
  },
];

const singleMenu = [
  {
    icon: 'Info',
    code: 'basic_info',
    textCode: 'iam.openLoginConfig.detail.menu.name.basic.info',
    component: MainView,
    defaultMessage: '基本信息',
  },
];

const microsoftMenu = [
  {
    icon: 'Info',
    code: 'basic_info',
    textCode: 'iam.openLoginConfig.detail.menu.name.basic.info',
    component: MainView,
    defaultMessage: '基本信息',
  },
  {
    icon: 'branch-one',
    code: 'bind_user',
    textCode: 'iam.openLoginConfig.detail.menu.name.bind.user',
    component: BindUser,
    defaultMessage: '人员绑定',
  },
];

function urlToBase64(url) {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    img.crossOrigin = 'Anonymous';
    img.onload = () => {
      canvas.height = img.height;
      canvas.width = img.width;
      ctx?.drawImage(img, 0, 0);
      // 获取Base64
      const dataURL = canvas.toDataURL('image/png');
      resolve(dataURL);
    };
    img.src = url;
  });
}

function base64ToFile(dataURL) {
  const arr = dataURL?.split?.(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]); let n = bstr.length; const
    u8arr = new Uint8Array(n);
  // eslint-disable-next-line no-plusplus
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  const filename = `${new Date().getTime()}${Math.ceil(Math.random() * 100)}.${mime.split('/')[1]}`;
  return (new File([u8arr], filename, { type: mime }));
}

export default function Detail(props) {
  const { match: { params: { id: iamId } } } = props;
  const context = useContext(Store);
  const [edit, setEdit] = useState(iamId === '0');
  const {
    intl,
    history,
    intlPrefix,
    infoDetailDataSet,
    HeaderStore: {
      tenantConfig: { domain },
    },
    type,
    tenantId,
    userDataSet,
    departmentDataSet,
    organizationId,
    syncHistoryDataSet,
    userAutoBindDataSet,
    bindHistoryDataSet,
  } = context;
  const search = history.location?.search;

  // TODO: 三方审批设置，后端做在单独的请求里了，等后端改为一个接口后，再合并请求
  const openAppStore = useLocalStore(() => ({
    approvalEventFlag: false,
    approvalConfig: [],
    allConfig: null,
    get getApprovalEventFlag() {
      return openAppStore.approvalEventFlag;
    },
    setApprovalEventFlag(data) {
      openAppStore.approvalEventFlag = data;
    },
    get getApprovalConfig() {
      return openAppStore.approvalConfig.slice();
    },
    setApprovalConfig(config) {
      openAppStore.approvalConfig = config;
    },
    get getAllConfig() {
      return openAppStore.allConfig;
    },
    setAllConfig(config) {
      openAppStore.allConfig = config;
    },
  }));
  // TODO: 详情应该单独有一个数据层，所以暂时先放展现层
  const approveDataset = useMemo(() => new DataSet(ApprovalDataSet({ intl, tenantId, openId: iamId, openAppStore })), [iamId, tenantId, openAppStore]);

  const currentIamId = useRef();

  async function initData() {
    if (iamId === '0') { // 新建
      const { type: _type } = search ? queryString.parse(search) : {};
      if (iamId === '0' && _type) {
        infoDetailDataSet.current = infoDetailDataSet.create({ type: _type });
        setAppType(_type);
      }
    } else if (iamId) { // 打开
      infoDetailDataSet.setQueryParameter('id', iamId);
      await infoDetailDataSet.query();
      infoDetailDataSet.current = infoDetailDataSet.get(0);
      setAppType(infoDetailDataSet?.current?.get('type'));
      currentIamId.current = iamId;
    }
  }

  const [appType, setAppType] = useState();
  currentIamId.current = iamId;
  useEffect(() => {
    initData();
  }, []);

  const usualOpenAppFlag = useMemo(() => {
    const typeList = ['wechat_enterprise', 'lark', 'ding_talk', 'microsoft'];
    // 表示是否为飞书、钉钉或企业微信
    const _appType = infoDetailDataSet?.current?.get('type');
    const flag = typeList.includes(_appType);
    // 若是平台层，则钉钉需要去除
    return type === 'site' ? appType !== 'ding_talk' && flag : flag;
  }, [infoDetailDataSet?.current, type]);

  const validateALl = async (dataSet) => {
    const validateList = [];
    const flagList = await Promise.all(dataSet.map(u => {
      validateList.push(u);
      return u.validate(true);
    }));
    flagList.forEach((flag, index) => {
      if (!flag) {
        dataSet.current = validateList[index];
      }
    });
    return flagList.every(f => f);
  };

  const toggleEdit = () => {
    setEdit(!edit);
  };
  function handleNewApp() {
    const { type: _type } = search ? queryString.parse(search) : {};
    if (iamId === '0' && _type) {
      infoDetailDataSet.current = infoDetailDataSet.create({ type: _type });
    }
  }
  const handleCancel = () => {
    userDataSet.reset();
    departmentDataSet.reset();
    if (iamId === '0') {
      infoDetailDataSet.remove(infoDetailDataSet.current);
      handleNewApp();
      history.goBack();
      return;
    } else {
      infoDetailDataSet.reset();
    }
    toggleEdit();
  };

  const handleSave = async () => {
    const hash = window.location?.hash;
    const pageCode = hash.match(/pageCode=(\w+)/)?.[1];
    const restFlag = usualOpenAppFlag;
    const _appType = infoDetailDataSet?.current?.get('type');
    if (_appType === 'wechat_agent') {
      // 如果当前类型为微信客服且用户没有上传头像，提交时需要提交默认头像的fileKey
      if (!infoDetailDataSet?.current?.get('wechatAgentConfig.agentAvatar')) {
        const base64Img = await urlToBase64(defaultAvatar);
        const fileImg = base64ToFile(base64Img);
        const formData = new FormData();
        formData.append('file', fileImg, 'image.png');
        const uploadRes = await axios({
          url: `${window._env_.API_HOST}/hfle/yqc/v1/${tenantId}/files/secret-multipart`,
          method: 'post',
          headers: {
            'Access-Control-Allow-Origin': '*',
            Authorization: getAccessToken(),
          },
          processData: { 'Content-Type': 'multipart/form-data' },
          data: formData,
        });
        infoDetailDataSet?.current?.set('wechatAgentConfig.agentAvatar', uploadRes?.fileKey);
      }
    }

    if (restFlag && !infoDetailDataSet?.current.get('config.timingFlag')) {
      infoDetailDataSet?.current.set('config.timingFlag', false);
    }
    if (restFlag) {
      const userFlag = await validateALl(userDataSet);
      const departmentFlag = await validateALl(departmentDataSet);
      const userAutoBindFlag = await validateALl(userAutoBindDataSet);
      if (!userFlag || !departmentFlag || !userAutoBindFlag) return;
      const user = userDataSet.toData().map(data => {
        const { userInfo: { fieldCode, fieldName }, syncUserDepartments, openName, openCode, updateFlag, constantValue } = data;
        return { fieldCode, fieldName, syncUserDepartments, openName, openCode, updateFlag, constantValue };
      });
      const department = departmentDataSet.toData().map(data => {
        const { departmentInfo: { fieldCode, fieldName }, openName, openCode, updateFlag, constantValue } = data;
        return { fieldCode, fieldName, openName, openCode, updateFlag, constantValue };
      });
      const userAutoBindData = userAutoBindDataSet.toData().map(data => {
        const { userInfo: { fieldCode, fieldName }, openName, openCode, constantValue } = data;
        return { fieldCode, fieldName, openName, openCode, constantValue };
      });
      const jsonData = { user, department };
      const bindJsonData = userAutoBindData;
      infoDetailDataSet.current?.set('config.jsonData', JSON.stringify(jsonData));
      infoDetailDataSet.current?.set('bindConfig.jsonData', JSON.stringify(bindJsonData));
    }
    const infoDetailFlag = await validateALl(infoDetailDataSet);
    if (!infoDetailFlag) {
      message.error(intl.formatMessage({ id: 'iam.openLoginConfig.desc.need.to.complete', defaultMessage: '尚有必填项未填写' }));
      return;
    }
    let feedbackMessage = false;
    if (_appType === 'lark' && iamId !== '0') {
      const approvalFlag = await approveDataset.validate();
      if (approvalFlag === false) {
        message.error(intl.formatMessage({ id: 'iam.openLoginConfig.desc.approval.required', defaultMessage: '请检查飞书审批流编码' }));
        return;
      }
      const approvalData = {
        ...openAppStore.getAllConfig,
        openAppId: iamId,
        approvalEventFlag: openAppStore.getApprovalEventFlag,
        approvalConfig: JSON.stringify(approveDataset.toData().map(item => omit(item, 'uid'))),
      };
      await axios.post(`ecos/v1/${tenantId}/openApps/${iamId}/callback/config`, approvalData)
        .then(res => {
          if (res?.failed && res?.message) {
            message.error(res.message);
          } else {
            feedbackMessage = true;
            openAppStore.setAllConfig(res);
          }
        })
        .catch(e => {
          message.error(intl.formatMessage({ id: 'iam.openLoginConfig.desc.approval.required', defaultMessage: '请飞书审批设置失败，请联系管理员' }));
        });
    }
    if (infoDetailDataSet.dirty) {
      feedbackMessage = false;
      // 如果路由的id为0就表示新建的应用，此时应该先请求一下创建应用的接口
      if (iamId === '0') {
        // const url = type === 'site' ? 'iam/yqc/open_apps' : `iam/yqc/${organizationId}/open_apps`;
        // const params = infoDetailDataSet.current?.toData();
        //
        // // const res = await infoDetailDataSet.submit()
        // if (params.todoConfig?.[0]) {
        //   debugger
        //   params.todoConfig = params.todoConfig[0];
        // }
        // // 2 configTemplate字符串化
        // if (params.todoConfig?.configTemplate || typeof params.todoConfig?.configTemplate === 'object') {
        //   params.todoConfig.configTemplate = JSON.stringify(params.todoConfig?.configTemplate);
        // }
        // // 3 todoCreatorCode字符串化
        // if (params.todoConfig?.todoCreatorCode) {
        //   params.todoConfig.todoCreatorCode = params.todoConfig.todoCreatorCode?.join(',');
        // }
        // debugger
        // const res = await axios.post(url, omit(params, 'config'));
        const res = await infoDetailDataSet.submit();
        if (!res || res.failed || !res.success) {
          message.error(res?.message || '创建应用失败，请重试');
          return;
        }
        const { id: _id, code } = res?.content?.[0];
        infoDetailDataSet.current?.set('id', _id);
        infoDetailDataSet.current?.set('code', code);
        infoDetailDataSet.current?.set('objectVersionNumber', '1');
      }
      infoDetailDataSet.current.status = 'update';
      await infoDetailDataSet.submit().then(() => {
        setEdit(!edit);
        // message.success(intl.formatMessage({ id: 'zknow.common.success.save', defaultMessage: '保存成功' }));
        // refresh();
        infoDetailDataSet.query();
      }).catch(err => {
        // message.error(err?.message || intl.formatMessage({ id: 'save.error' }));
      }).finally(() => {
        const searchWithoutCode = search?.replace(/[?&]pageCode=[\w]*/g, '');
        history.replace({
          pathname: `/iam/open_app_config/detail/${infoDetailDataSet.current.get('id')}`,
          search: `${searchWithoutCode}&pageCode=${pageCode}`,
        });
      });
    } else {
      if (feedbackMessage) {
        message.success(intl.formatMessage({ id: 'zknow.common.success.save', defaultMessage: '保存成功' }));
      }
      setEdit(false);
    }
  };

  const handleEnabled = async () => {
    if (iamId === '0') {
      message.warning(intl.formatMessage({ id: 'iam.openLoginConfig.desc.save.info.first', defaultMessage: '请先保存应用信息' }));
      return;
    }
    const record = infoDetailDataSet?.current;
    const flag = !record.get('enabledFlag');
    record.set('enabledFlag', flag);
    try {
      const url = `iam/yqc${type === 'site' ? '' : `/${organizationId}`}/open_apps/${flag ? 'enabled' : 'disabled'}`;
      const res = await axios.post(url, record.toData());
      if (res.failed) {
        throw new Error(res.message);
      } else {
        message.success(intl.formatMessage({ id: 'zknow.common.success.save', defaultMessage: '保存成功' }));
      }
    } catch (err) {
      message.error(err.message);
    }
    // await refresh();
  };

  async function handleSync() {
    if (iamId === '0') {
      message.warning(intl.formatMessage({ id: 'iam.openLoginConfig.desc.save.info.first', defaultMessage: '请先保存应用信息' }));
      return;
    }
    Modal.confirm({
      title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.are.you.sure.you.want.to.start.syncing', defaultMessage: '确定要开始同步吗?' }),
    }).then((btn) => {
      if (btn === 'ok') {
        Modal.open({
          title: intl.formatMessage({ id: 'iam.openLoginConfig.model.open.login.config.sync.hand', defaultMessage: '手动同步' }),
          children: (
            <ManualSync
              intlPrefix={intlPrefix}
              tenantId={tenantId}
              syncHistoryDataSet={syncHistoryDataSet}
              id={infoDetailDataSet.current?.get('id')}
            />
          ),
          footer: null,
        });
      } else {
        return null;
      }
    });
  }

  function handleOpenBindHistory() {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.choerodon.bind.history', defaultMessage: '绑定历史' }),
      drawer: true,
      maskClosable: true,
      style: { width: 1020, maxWidth: '75vw' },
      children: (
        <BindHistory intlPrefix={intlPrefix} dataSet={bindHistoryDataSet} tenantId={tenantId} userAutoBindDataSet={userAutoBindDataSet} appType={infoDetailDataSet?.current?.get('type')} />
      ),
      okButton: false,
      cancelButton: false,
    });
  }

  function handleOpenSyncHistory() {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.sync.history', defaultMessage: '同步历史' }),
      drawer: true,
      maskClosable: true,
      style: { width: 1000, maxWidth: '75vw' },
      children: (
        <SyncHistory intlPrefix={intlPrefix} dataSet={syncHistoryDataSet} tenantId={tenantId} />
      ),
      okButton: false,
      cancelButton: false,
    });
  }

  function handleConfigCheck(_type) {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.config.check', defaultMessage: '配置检查' }),
      drawer: true,
      maskClosable: true,
      style: { width: 1000, maxWidth: '75vw' },
      children: (
        <ConfigCheckModal tenantId={tenantId} openAppId={iamId} intl={intl} type={_type} infoDetailDataSet={infoDetailDataSet} />
      ),
      // okButton: false,
      // cancelButton: false,
    });
  }

  const commonProps = {
    edit,
    setEdit,
    handleSave,
    handleCancel,
    toggleEdit,
    usualOpenAppFlag,
    handleEnabled,
    handleSync,
    handleOpenSyncHistory,
    handleConfigCheck,
    handleOpenBindHistory,
    openAppStore,
    approveDataset,
  };

  if (['ding_talk', 'lark', 'wechat_enterprise'].includes(appType)) {
    return <LeftTabMenu {...props} menuMap={complexMenu} {...commonProps} />;
  } else if (appType === 'wechat_open_account') {
    return <LeftTabMenu {...props} menuMap={wechatOpenAccountMenu} {...commonProps} />;
  } else if (appType === 'microsoft') {
    return <LeftTabMenu {...props} menuMap={microsoftMenu} {...commonProps} />;
  } else if (appType) {
    return <LeftTabMenu {...props} menuMap={singleMenu} {...commonProps} />;
  } else {
    return <MainView {...props} {...commonProps} />;
  }
}
