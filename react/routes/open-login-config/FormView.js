import React, { useCallback, useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Content, TabPage } from '@yqcloud/apps-master';
import { Table, message, Form, TextField, Output, Select, SelectBox, Upload } from 'choerodon-ui/pro';
// import { Avatar } from 'choerodon-ui';
import { TableStatus, TableHoverAction, Button, ClickText, Icon, FileUploader, FileShower, YqAvatar } from '@zknow/components';

import Store from './stores';
import './index.less';

const { Column } = Table;

export default observer(({ record, context, modalType, modal }) => {
  const { intlPrefix, intl, tenantId } = context;
  const isEdit = record.getState('isEdit') || modalType === 'create';
  const type = record.get('type');

  modal.handleOk(async () => {
    if (modalType === 'create') {
      const res = await record.dataSet.submit();
      if (res) {
        return;
      } else {
        return false;
      }
    }
    record.setState('isEdit', false);
    const tempData = record.toData();
    const res = await record.dataSet.submit();
    record.init(tempData);
    record.init('objectVersionNumber', tempData.objectVersionNumber + 1);

    // await record.dataSet.query();
    return false;
  });

  modal.handleCancel(() => {
    record?.dataSet?.remove(record);
  });

  function handleCancel() {
    record.setState('isEdit', false);
    record.dataSet.reset();
    return false;
  }

  function renderIcon({ name, value }) {
    return (
      <FileUploader disabled={isEdit} name={name} record={record} tenantId={tenantId}>
        <div style={{ cursor: isEdit ? 'pointer' : undefined }}>
          <FileShower fileKey={value}>
            {({ src, props }) => (
              <YqAvatar className={`iam-open-login-config-avatar${isEdit ? ' editable' : 'editable'}`} size={56} icon="person" src={src} />
            )}
          </FileShower>
        </div>
      </FileUploader>
    );
  }
  return (
    <div>
      <Form disabled={!isEdit} labelWidth={120} labelLayout="horizontal" columns={modalType === 'create' ? 2 : 1} record={record}>
        {/* <Output name="icon" renderer={renderIcon} /> */}
        <TextField name="name" autoFocus label={intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' })} />
        <Select name="type" disabled />
        {type === 'other' && <TextField name="code" disabled={modalType !== 'create'} />}
        <TextField name="appId" />
        <TextField name="appSecret" />
        {(type === 'wechat_enterprise' || type === 'ding_talk') && <TextField name="agentId" />}
        {type === 'ding_talk' && <TextField name="dingTalkCorpId" />}
        {type === 'other' && <TextField name="authUrl" />}
        {type === 'other' && <TextField name="tokenUrl" />}
        {type === 'other' && <TextField name="userInfoUrl" />}
        {type === 'other' && <TextField name="scope" />}
        {type === 'other' && <TextField name="uuidField" />}
        <SelectBox name="enabledLogin" renderer={!isEdit ? ({ value }) => intl.formatMessage({ id: value ? 'yes' : 'no' }) : undefined} />
        <SelectBox name="enabledFlag" renderer={!isEdit ? ({ value }) => intl.formatMessage({ id: value ? 'active' : 'stop' }) : undefined} />
      </Form>
    </div>
  );
});
