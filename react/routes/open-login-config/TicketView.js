import React from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, TextArea, Select } from 'choerodon-ui/pro';
import './index.less';

export default observer(({ record, modal, setOpenKey }) => {
  modal.handleOk(async () => {
    const validate = await record.dataSet.validate();
    if (!validate) {
      return false;
    }
    try {
      const res = await record.dataSet.submit();
      if (!res.failed) {
        setOpenKey('quick_authentication');
        record.dataSet.query();
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  });

  modal.handleCancel(() => {
    record?.dataSet?.remove(record);
  });

  return (
    <div>
      <Form labelWidth="auto" labelLayout="horizontal" record={record}>
        <TextField name="name" autoFocus />
        <Select name="quickType" />
        <TextArea name="jsonConfig.description" />
      </Form>
    </div>
  );
});
