@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-vertical-center {
  display: flex;
  align-items: center;
}

.iam-open-login-config {
  &-wrapper {
    height: 100%;
  }

  &-avatar {
    &.editable {
      cursor: pointer;
      pointer-events: none;
    }
  }

  &-header {
    padding: 0 16px;
    width: 100%;
    height: 48px;
    background-color: #f7f7f7;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-left {
      display: flex;
    }

    .flex-vertical-center();

    .i-icon-hamburger-button {
      display: inline-block;
      background-color: #fff;
      border-radius: 4px;
      border: 1px solid rgba(0, 0, 0, 0.15);
      padding: 8px;
      margin: 0 16px;
      cursor: pointer;
    }

    &-search {
      width: 219px;
      height: 32px;
      margin-right: 16px;
    }

    &-type {
      width: 140px;
      height: 32px;
      .flex-vertical-center();
      background-color: #fff;
      border-radius: 4px;
      border: 1px solid #dfdfdf;

      &-name {
        width: 109px;
        height: 100%;
        .flex-center();
        font-size: 14px;
        font-weight: 400;
      }

      .i-icon-down {
        width: 30px;
        height: 100%;
        .flex-center();
        border-left: 1px solid #dfdfdf;
      }

      .c7n-pro-popup {
        width: 140px;
      }
    }

    &-hamburger {
      display: inline-flex;
      width: 32px;
      height: 32px;
      margin-right: 0.1rem;
      border-radius: 4px;
      border: .01rem solid @primary-2;
      cursor: pointer;
      background-color: @primary-1;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: @primary-color;

        .additional-btn {
          color: #fff;
        }
      }

      & > .additional-btn {
        width: 0.2rem;
        height: 0.2rem;
        color: @primary-color;
        margin: auto;
      }
    }
  }

  &-content {
    height: calc(100% - 0.48rem);
    overflow: scroll;

    &-item {
      min-height: 80px;
      border-bottom: 1px solid #d9d9d9;
      margin: 0 16px;
      display: flex;
      flex-direction: column;

      &:hover {
        .iam-open-login-config-content-item-create {
          display: flex !important;
        }
      }

      &-header {
        width: 100%;
        height: 80px;
        position: relative;

        &:hover {
          background-color: #e5eeff;
          cursor: pointer;
        }
      }

      &-img,
      &-name,
      &-num,
      &-create {
        height: 80px;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
      }

      &-img {
        width: 30px;
        height: 30px;
        left: 16px;
        border-radius: 30px;
      }

      &-name {
        height: 22px;
        width: 1.7rem;
        font-size: 16px;
        font-weight: 500;
        color: #12274d;
        left: 56px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &-num {
        width: 80px;
        height: 24px;
        border-radius: 4px;
        font-size: 12px;
        word-break: keep-all;
        .flex-center();
        color: @primary-color;
        border: 1px solid @primary-color;
        left: 230px;
      }

      &-none {
        background-color: rgba(0, 0, 0, 0.06) !important;
        color: #595959 !important;
        border-color: transparent !important;
      }

      &-create {
        width: 105px;
        height: 32px;
        background-color: #2979ff;
        right: 16px;
        border-radius: 4px;
        cursor: pointer;
        display: none;

        &-add,
        &-showmore {
          .flex-center();
        }

        &-add {
          flex: 1;
          font-size: 14px;
          color: #fff;
        }

        &-showmore {
          width: 30px;
          border-left: 1px solid #fff;
        }
      }

      &-app {
        min-height: 60px;
        padding-left: 230px;

        &-item {
          min-height: 60px;
          padding-right: 16px;
          .flex-vertical-center();
          justify-content: space-between;
          border-top: 1px solid #d9d9d9;
          position: relative;
          &-left {
            min-height: 60px;
            flex: 1;
            .flex-vertical-center();

            .yq-cmp-status-tag {
              margin-left: 20px;
            }
          }
          &-typeTag {
            position: absolute;
            left: 35%;
          }
          &-tag {
            margin-left: 0.1rem;

            .@{c7n-prefix}-tag {
              background: #e9ffeb;
              color: #1ab335;
            }

            &.app-item-disabled {

              .@{c7n-prefix}-tag {
                color: #86909c;
                background: #f2f3f5;
              }
            }
          }
        }
      }
    }
  }
}
