import React, { useContext, useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { message, TextField, Modal, Dropdown, Menu } from 'choerodon-ui/pro';
import { axios } from '@yqcloud/apps-master';
import copy from 'copy-to-clipboard';
import { TableHoverAction, Button, Icon } from '@zknow/components';
import Store from './stores';
import ApplicationInfo from './ApplicationInfo';
import './index.less';

const appListSortMapping = {
  wechat_enterprise: 0,
  lark: 1,
  ding_talk: 2,
  wechat_open_account: 3,
  other: 4,
};

export default observer(() => {
  const context = useContext(Store);
  const { prefixCls, intlPrefix, intl, infoDataSet, organizationId, type } = context;

  const [applist, setApplist] = useState([]);
  const [showApplist, setShowApplist] = useState(false);

  const typeList = [
    { name: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.search.all', defaultMessage: '全部应用' }), value: 'all' },
    { name: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.search.activated', defaultMessage: '已启用' }), value: 'activated' },
    { name: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.search.deactivated', defaultMessage: '已停用' }), value: 'deactivated' },
    { name: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.search.not.added', defaultMessage: '未添加' }), value: 'notAdded' },
  ];

  useEffect(() => {
    infoDataSet.setState('searchType', 'all');
    infoDataSet.query().then(() => {
      const data = Object.entries(infoDataSet.get(0)?.toData() || [])?.filter(item => Array.isArray(item[1]));
      setApplist(data || []);
    });
  }, []);

  useEffect(() => {
    if (infoDataSet.status === 'ready' && applist?.length > 0) {
      const data = Object.entries(infoDataSet.get(0)?.toData() || [])?.filter(item => Array.isArray(item[1]));
      setApplist(data || []);
    }
  }, [infoDataSet.status]);

  function renderTableAction({ record }) {
    return (
      <TableHoverAction
        record={record}
        actions={[
          {
            name: intl.formatMessage({ id: record.get('enabledFlag') ? 'stop' : 'active' }),
            icon: record.get('enabledFlag') ? 'icon-Expires' : 'icon-read',
            onClick: async () => {
              const flag = !record.get('enabledFlag');
              record.set('enabledFlag', flag);
              try {
                const url = `iam/yqc${type === 'site' ? '' : `/${organizationId}`}/open_apps/${flag ? 'enabled' : 'disabled'}`;
                const res = await axios.post(url, record.toData());
                if (res.failed) {
                  throw new Error(res.message);
                }
              } catch (err) {
                message.error(err.message);
              }
              await record.dataSet.query();
            },
          },
        ]}
      />
    );
  }

  function handleCopy() {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.auth.callback', defaultMessage: '授权回调域' }),
      children: (
        <>
          <span style={{ marginRight: 8 }}>{window._env_.API_HOST.split('://')[1]}</span>
          <Button
            onClick={() => {
              copy(window._env_.API_HOST.split('://')[1]);
              message.success(intl.formatMessage({ id: 'zknow.common.success.copy', defaultMessage: '复制成功' }));
              setTimeout(() => {
                Modal.destroyAll();
              }, 500);
            }}
            color="primary"
            icon="copy"
          >{intl.formatMessage({ id: 'zknow.common.button.copy', defaultMessage: '复制' })}</Button>
        </>
      ),
      footer: null,
    });
  }

  function handleChange() {
    const searchType = infoDataSet.getState('searchType') || 'all';
    if (searchType !== 'all' && searchType !== 'notAdded') {
      infoDataSet.setQueryParameter('search_enabledFlag', searchType === 'activated');
      infoDataSet.setQueryParameter('search_notAdd', '');
    }
    if (searchType === 'notAdded') {
      infoDataSet.setQueryParameter('search_notAdd', true);
      infoDataSet.setQueryParameter('search_enabledFlag', '');
    }
    if (searchType === 'all') {
      infoDataSet.setQueryParameter('search_notAdd', '');
      infoDataSet.setQueryParameter('search_enabledFlag', '');
    }
    infoDataSet.query().then(() => {
      const data = Object.entries(infoDataSet.get(0)?.toData() || [])?.filter(item => Array.isArray(item[1]));
      setApplist(data || []);
      setShowApplist(true);
    });
  }

  function handleApplistSort(preApp, curApp) {
    return appListSortMapping[preApp[0]] - appListSortMapping[curApp[0]];
  }

  function handleSearchTypeClick(value) {
    infoDataSet.setState('searchType', value);
    handleChange();
  }

  function renderAutoOverlay() {
    return (
      <Menu>
        <Menu.Item onClick={handleCopy}>
          {intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.auth.callback.watch', defaultMessage: '查看回调域' })}
        </Menu.Item>
      </Menu>
    );
  }

  function renderTypeOverlay() {
    return (
      <Menu>
        {typeList.map(searchType => (
          <Menu.Item
            key={searchType.name}
            onClick={() => handleSearchTypeClick(searchType.value)}
          >{searchType.name}</Menu.Item>
        ))}
      </Menu>
    );
  }

  return (
    <div className={`${prefixCls} ${prefixCls}-wrapper`}>
      <div className={`${prefixCls}-header`}>
        <div className={`${prefixCls}-header-left`}>
          <TextField
            name="search_name"
            record={infoDataSet.queryDataSet?.current}
            className={`${prefixCls}-header-search`}
            labelLayout="placeholder"
            prefix={<Icon type="icon-search" />}
            label={intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.input.appname.search', defaultMessage: '请输入应用名称搜索' })}
            placeholder={intl.formatMessage({ id: 'iam.openLoginConfig.desc.open.login.config.input.appname.search', defaultMessage: '请输入应用名称搜索' })}
            onChange={handleChange}
          />
          <Dropdown
            className={`${prefixCls}-header-copy`}
            overlay={renderTypeOverlay()}
            trigger={['click']}
            getPopupContainer={(that) => that}
          >
            <div className={`${prefixCls}-header-type`}>
              <div className={`${prefixCls}-header-type-name`}>
                {intl.formatMessage({ id: `iam.openLoginConfig.desc.open.login.config.search.${infoDataSet.getState('searchType') || 'all'}`, defaultMessage: '全部应用' })}
              </div>
              <Icon type="down" theme="outline" size="14" fill="#333" />
            </div>
          </Dropdown>
        </div>
        <Dropdown overlay={renderAutoOverlay()} trigger={['click']}>
          <div className={`${prefixCls}-header-hamburger`}>
            <Icon type="more" size="20" className="additional-btn" />
          </div>
        </Dropdown>
      </div>
      <div className={`${prefixCls}-content`}>
        {infoDataSet.status === 'ready' && applist.length > 0 && applist.sort(handleApplistSort).map(record => <ApplicationInfo key={record[0]} records={record[1]} type={record[0]} showApplist={showApplist} />)}
      </div>
    </div>
  );
});
