import { omit } from 'lodash';

export function getQueryFields({ fuzzySearch = [], searchType, advancedSearch = [] }, transform) {
  if (searchType === 'advanced') {
    return advancedSearch
      .sort(({ hidden }, { hidden: hidden2 }) => Number(hidden) - Number(hidden2))
      .map(({ id }) => transform(id));
  } else if (searchType === 'fuzzy') {
    return fuzzySearch.map(({ id }) => transform(id));
  }
  return [];
}

export function getFieldMap(fields, code = 'id') {
  return new Map(fields.map(field => {
    // 改为了所有添加进数据源的字段都是可编辑的字段，添加转换
    field.editable = true;
    return [field[code], field];
  }));
}

export function getQueryFieldMap({ fuzzySearch = [], searchType, advancedSearch = [] }) {
  const fields = searchType === 'advanced' ? advancedSearch : fuzzySearch;
  return getFieldMap(fields);
}

export function transformField(fieldMap, field) {
  const { code, name } = field;
  const presetProps = {
    name: code,
    type: 'auto',
    label: name,
  };
  return presetProps;
}

// 不需要展示动态组件的条件
export const notNeedRenderComponentsArray = [
  'is null',
  'is not null',
  'is empty',
  'is not empty',
  'exists',
  'does not exist',
  'is like',
  'is step',
  'is Nothing',
  'is current user',
  'is update',
];

// 哪些对象是lov
export const LovComponentsArray = [
  'PRIORITY', // 优先级
  'URGENCY', // 紧急度
  'IMPACT', // 影响度
  'BUSINESSUNIT', // 业务部门
  'STATE', // 状态
  'LOCATION', // 位置
  'USER', // 成员
  'CATEGORY', // 类别
  'SUBCATEGORY', // 子类别
  'SERVICE', // 服务
  'SERVICECATALOGUE', // 服务目录
  'GROUP', // 组
  'DEPARTMENT', // 部门
  'MasterDetail', // 多对一的业务对象
];

// LOV为多选的条件
export const LovMultipleArray = [
  'is in list',
  'is not in list',
];

export const timeFormat = 'HH:mm:ss';
export const dateFormat = 'YYYY-MM-DD';
export const dateTimeFormat = 'YYYY-MM-DD HH:mm:ss';

export function simplifyField(field) {
  let filters = [
    'auditFlag',
    'conditionFlag',
    'customFlag',
    'nameFlag',
    'searchFlag',
    'syncedFlag',
    'persistableFlag',
    'tlFlag',
    'presetFlag',
    'queryFlag',

    'createdBy',
    'creationDate',
    'lastUpdateDate',
    'lastUpdatedBy',
    'domainId',
    'tenantId',
    'objectVersionNumber',
    '_token',

    'parentObjectId',
    'relationObject',
    'relationObjectCode',
    'relationObjectName',
    'relationType',
    'relationFields',
    'calculatedFlag',
    'calculatedConfig',
    'defaultLabel',

    // tree
    'widgetConfig.childButtons',
    'widgetConfig.parentButtons',
    'widgetConfig.searchableFlag',
    'widgetConfig.showIconFlag',
    'widgetConfig.syncFlag',
    'widgetConfig.treeNodes',
  ];

  if (field.widgetType !== 'Table') {
    filters = filters.concat([
      'widgetConfig.actions',
      'widgetConfig.buttons',
      'widgetConfig.fields',
      'widgetConfig.lineButtons',
      'widgetConfig.columnViewFlag',
      'widgetConfig.autoQueryFlag',
      'widgetConfig.canExportFlag',
      'widgetConfig.pageSize',
      'widgetConfig.preciseQueryFlag',
      'widgetConfig.selection',
      'widgetConfig.tableFilterFlag',
      'widgetConfig.tableLinkFlag',
      'widgetConfig.tableLinkType',
      'widgetConfig.tableLinks',
    ]);
  }
  if (field.widgetType !== 'Table' && field.widgetType !== 'TicketRelation') {
    filters = filters.concat([
      'widgetConfig.tableLinkViewSize',
      'widgetConfig.tableLinkViewType',
    ]);
  }
  if (field.widgetType !== 'VariableTable') {
    filters = filters.concat(['widgetConfig.variableFields']);
  }
  if (field.widgetType !== 'Custom' && field.widgetType !== 'TreeLov') {
    filters = filters.concat(['widgetConfig.customConfig']);
  }
  if (field.widgetType !== 'IntelligentSearch' && field.widgetType !== 'IntelligentRecommendation') {
    filters = filters.concat([
      'widgetConfig.searchJumpType',
      'widgetConfig.displayLines',
      'widgetConfig.searchInterval',
      'widgetConfig.whenSearching',
      'widgetConfig.whenNotSearched',
      'widgetConfig.intelligentJumpViewJSON',
      'widgetConfig.searchRange',
      'widgetConfig.problemSourceFields',
    ]);
  }
  if (field.widgetType !== 'Dynamic') {
    filters = filters.concat([
      'widgetConfig.dynamicDisplayContent',
      'widgetConfig.dynamicDisplayDataType',
      'widgetConfig.dynamicDisplayFields',
    ]);
  }
  if (field.widgetType !== 'AutoNumber') {
    filters = filters.concat([
      'widgetConfig.autoNumberFormat',
    ]);
  }
  if (field.widgetType !== 'ManyToMany') {
    filters = filters.concat([
      'intermediateObjectId',
      'intermediateField',
      'intermediateRelationField',
      'intermediateFieldName',
      'intermediateObjectName',
      'intermediateRelationFieldName',
    ]);
  }
  if (field.widgetType !== 'MasterDetail') {
    filters = filters.concat([
      'relationLovId',
      'relationLovName',
      'widgetConfig.lovToView',
      'widgetConfig.mappingField',
      'widgetConfig.previewFlag',
    ]);
  }
  if (field.widgetType !== 'Alert') {
    filters = filters.concat([
      'widgetConfig.alertType',
      'widgetConfig.alertMessage',
    ]);
  }
  if (field.widgetType !== 'Chart') {
    filters = filters.concat([
      'widgetConfig.chartType',
      'widgetConfig.chartParams',
    ]);
  }
  if (field.widgetType !== 'Duration') {
    filters = filters.concat([
      'widgetConfig.durationUnit',
      'widgetConfig.durationMode',
    ]);
  }
  if (field.widgetType !== 'Upload' && field.widgetType !== 'Image') {
    filters = filters.concat([
      'widgetConfig.fileDragUpload',
      'widgetConfig.fileFormat',
      'widgetConfig.fileSizeLimit',
    ]);
  }
  if (field.widgetType !== 'TextArea') {
    filters = filters.concat([
      'widgetConfig.htmlFlag',
    ]);
  }
  if (field.widgetType !== 'Comment') {
    filters = filters.concat([
      'widgetConfig.isDefaultExpand',
      'widgetConfig.replyOpenCC',
    ]);
  }
  if (field.widgetType !== 'Rate') {
    filters = filters.concat([
      'widgetConfig.rateCount',
      'widgetConfig.rateCharacter',
    ]);
  }
  if (field.widgetType !== 'TicketSurveyDetail') {
    filters = filters.concat([
      'widgetConfig.respondent',
    ]);
  }
  if (field.widgetType !== 'TicketSurveyDetail') {
    filters = filters.concat([
      'widgetConfig.surveyViewFlag',
    ]);
  }
  if (field.widgetType !== 'Tag') {
    filters = filters.concat([
      'widgetConfig.tagAddMode',
      'widgetConfig.tagMultiFlag',
      'widgetConfig.tagQuickAdd',
    ]);
  }
  if (field.widgetType !== 'TicketHeader') {
    filters = filters.concat([
      'widgetConfig.ticketHeaderEditablePerson',
      'widgetConfig.ticketHeaderEditableRole',
      'widgetConfig.ticketHeaderFields',
    ]);
  }
  console.log(field);
  console.log(omit(field, filters));
  return omit(field, filters);
}
