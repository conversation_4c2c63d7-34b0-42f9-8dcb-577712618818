import { message, DataSet } from 'choerodon-ui/pro';
import { getFieldMap, getQueryFieldMap, getQueryFields } from './utils';

const BOOLEAN_FIELDS = ['Switch', 'CheckBox'];

export const transformResponse = (data, lovTitle, transform, intl) => {
  let originData = {};

  try {
    originData = JSON.parse(data);
    if (originData.failed) {
      message.error(originData.message);
      return {};
    }
  } catch (e) {
    return data;
  }

  const {
    pageSize = 20,
    jsonData,
  } = originData;

  const {
    layout,
    dataSource: {
      parentFieldCode, idField, nameFieldCode, searchType,
      advancedSearch, fuzzySearch, fields = [], lovWidth,
    },
  } = JSON.parse(jsonData);
  const textField = nameFieldCode || 'name';
  const valueField = 'id';
  const title = lovTitle || originData.name;
  const fieldMap = getFieldMap(fields, 'code');
  const _fuzzySearch = fuzzySearch.map(fuzzy => ({ ...fuzzy, id: `${fuzzy.id}${fuzzy.code}` }));
  const queryFieldMap = getQueryFieldMap({ searchType, advancedSearch, fuzzySearch: _fuzzySearch });
  let queryFields = [];
  if (queryFieldMap.size > 0) {
    queryFields = getQueryFields({ fuzzySearch: _fuzzySearch, searchType, advancedSearch }, (id) => transform(queryFieldMap, queryFieldMap.get(id)));
  }
  const lovItems = new Map();
  let queryFieldsStyle = {};
  let tableWidth = 0;
  const fieldOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' }), value: 'true' },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' }), value: 'false' },
    ],
  });
  queryFields.forEach((queryItem, index) => {
    const field = fieldMap.get(queryItem.name);
    const lovItem = {
      display: queryItem.name,
      conditionField: 'Y',
      conditionFieldType: null,
      conditionFieldName: queryItem.id,
      conditionFieldSelectUrl: null,
      conditionFieldSelectVf: null,
      conditionFieldSelectTf: null,
      conditionFieldSelectCode: null,
      conditionFieldLovCode: null,
      conditionFieldSequence: index + 1,
      conditionFieldRequired: queryItem.required,
      gridField: 'N',
      fieldProps: {
        ...queryItem,
        options: BOOLEAN_FIELDS.includes(field?.widgetType) ? fieldOptions : undefined,
        type: BOOLEAN_FIELDS.includes(field?.widgetType) ? 'string' : 'auto',
      },
    };
    lovItems.set(queryItem.name, lovItem);
    queryFieldsStyle = {
      ...queryFieldsStyle,
      [queryItem.name]: {
        width: queryItem.width,
      },
    };
  });
  layout.forEach((tableItem, index) => {
    const { width, minWidth, name: fieldCode, displayFlag } = tableItem.props;
    const field = fieldMap.get(fieldCode);
    const item = lovItems.get(fieldCode);
    const lovItem = {
      gridFieldName: fieldCode || field?.code,
      gridFieldWidth: width,
      gridFieldMinWidth: minWidth,
      gridFieldAlign: tableItem.gridFieldAlign,
      gridField: displayFlag === undefined || displayFlag ? 'Y' : 'N',
      display: tableItem.props.label || tableItem.name,
      gridFieldSequence: index + 1,
      fieldProps: {
        placeholder: tableItem.placeholder || tableItem.name,
        options: BOOLEAN_FIELDS.includes(field?.widgetType) ? fieldOptions : undefined,
        type: BOOLEAN_FIELDS.includes(field?.widgetType) ? 'string' : 'auto',
        transformResponse: (value) => {
          if (field?.widgetType === 'RichText') {
            let fieldValueList = [];
            try {
              if (typeof value === 'string') {
                fieldValueList = JSON.parse(value);
              }
            } catch (e) {
              fieldValueList = [];
            }
            if (fieldValueList?.length) {
              return fieldValueList
                ?.map(v => v?.insert?.replace('\n', '') || '')
                ?.join('');
            }
            return value;
          }
          return value;
        },
      },
    };
    if (item) {
      Object.assign(item, lovItem);
    } else {
      lovItems.set(fieldCode, lovItem);
    }
    tableWidth += tableItem.gridFieldWidth;
  });
  return {
    originData,
    title,
    width: lovWidth || 800,
    customUrl: null,
    lovItems: [...lovItems.values()],
    treeFlag: parentFieldCode ? 'Y' : 'N',
    parentIdField: parentFieldCode,
    idField: idField || 'id',
    textField,
    valueField,
    editableFlag: originData.editFlag ? 'Y' : 'N',
    queryColumns: queryFields && queryFields.length ? 1 : 0,
    delayLoadFlag: true,
    tableProps: {
      queryBarProps: {
        inlineSearch: false,
        queryFieldsStyle,
      },
    },
    dataSetProps: {
      paging: 'server',
      pageSize,
    },
  };
};

export function transformRequest() {
  return [];
}

export { transformField } from './utils';
