import { ExternalComponent } from '@zknow/components';
import { axios } from '@zknow/utils';
import { message } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import React, { useEffect, useState } from 'react';
import './index.less';

export default inject('AppState')((props) => {
  const {
    AppState: {
      currentMenuType: { organizationId: tenantId },
    },
  } = props;
  const [tableViewId, setTableViewId] = useState();

  useEffect(() => {
    getViewId();
  }, []);

  // 获取视图id
  async function getViewId() {
    const res = await axios.get(
      `/lc/v1/${tenantId}/views/form/code/COMPANY_LIST_DEFAULT`
    );
    if (res?.failed) {
      message.error(res.message);
    } else {
      setTableViewId(res.id);
    }
  }

  return tableViewId ? (
    <div className="lc-company-wrapper" style={{ }}>
      <ExternalComponent
        system={{
          scope: 'lcr',
          module: 'PageLoader',
        }}
        viewId={tableViewId}
      />
    </div>
  ) : null;
});
