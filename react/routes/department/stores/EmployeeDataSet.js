import { getQueryParams } from '@zknow/utils';

const EmployeeDataSet = ({ tenantId, intl, intlPrefix }) => {
  const nameLabel = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const emailLabel = intl.formatMessage({ id: 'iam.common.model.email', defaultMessage: '邮箱' });
  const phoneLabel = intl.formatMessage({ id: 'iam.common.model.phone', defaultMessage: '手机号' });
  return {
    autoQuery: false,
    autoQueryAfterSubmit: false,
    selection: false,
    paging: true,
    pageSize: 20,
    autoLocateFirst: false,
    dataKey: 'content',
    totalKey: 'totalElements',
    transport: {
      read: ({ data }) => {
        const { departmentId } = data;
        return {
          url: `/iam/yqc/v1/${tenantId}/users/department/${departmentId}`,
          method: 'get',
          data: getQueryParams(data),
        };
      },
    },
    fields: [{ name: 'realName', type: 'string', label: nameLabel }, { name: 'email', type: 'string', label: emailLabel }, { name: 'phone', type: 'string', label: phoneLabel }],
  };
};
export default EmployeeDataSet;
