import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import ListDataSet from './ListDataSet';
import CommonDataSet from './CommonDataSet';
import MenuDataSet from './MenuDataSet';
import EmployeeDataSet from './EmployeeDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: ['iam.common', 'iam.department'] })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
    } = props;
    const intlPrefix = 'iam.department';
    const prefixCls = 'department';

    const statusMap = {
      PUBLISHED: { name: intl.formatMessage({ id: 'iam.common.status.published', defaultMessage: '已发布' }), color: '#7BC95A' },
      EDITED: { name: intl.formatMessage({ id: 'iam.common.status.edited', defaultMessage: '已编辑' }), color: '#2979FF' },
      DRAFT: { name: intl.formatMessage({ id: 'iam.common.status.draft', defaultMessage: '草稿' }), color: '#6454F4' },
      UPDATING: { name: intl.formatMessage({ id: 'iam.common.status.updating', defaultMessage: '更新中' }), color: '#FF9500' },
    };
    const menuDataSet = useMemo(() => new DataSet(MenuDataSet({ tenantId })));
    const employeeDataSet = useMemo(() => new DataSet(EmployeeDataSet({ tenantId, intlPrefix, intl })), [tenantId]);
    const commonDataSet = useMemo(() => new DataSet(CommonDataSet({ intlPrefix, intl, tenantId, menuDataSet })));
    const listDataSet = useMemo(
      () => new DataSet(ListDataSet({ intlPrefix, intl, tenantId, type: 'site', menuDataSet })),
      [tenantId],
    );
    const orgDataSet = useMemo(
      () => new DataSet(ListDataSet({ intlPrefix, intl, tenantId, type: 'organization', menuDataSet })),
      [tenantId],
    );

    const personalDataSet = useMemo(
      () => new DataSet(ListDataSet({ intlPrefix, intl, tenantId, type: 'user', menuDataSet })),
      [tenantId],
    );

    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      listDataSet,
      orgDataSet,
      personalDataSet,
      statusMap,
      tenantId,
      menuDataSet,
      commonDataSet,
      employeeDataSet,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
