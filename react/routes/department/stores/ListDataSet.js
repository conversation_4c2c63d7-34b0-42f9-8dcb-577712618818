import { DataSet } from 'choerodon-ui/pro';
import { getQueryParams } from '@zknow/utils';

export default ({ intlPrefix, intl, tenantId, statusDataSet, type, menuDataSet }) => {
  const url = `/iam/yqc/${tenantId}/departments/page`;
  const nameLabel = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const codeLabel = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const principalLabel = intl.formatMessage({ id: 'iam.department.model.principal', defaultMessage: '负责人' });
  const companyLabel = intl.formatMessage({ id: 'iam.department.model.company', defaultMessage: '公司' });
  const domainLabel = intl.formatMessage({ id: 'iam.department.model.domain', defaultMessage: '域' });
  const higherOfficeLabel = intl.formatMessage({ id: 'iam.department.model.higherOffice', defaultMessage: '上级部门' });
  const ownedCompanyLabel = intl.formatMessage({ id: 'iam.department.model.ownedCompany', defaultMessage: '所属公司' });
  const domainMgLabel = intl.formatMessage({ id: 'iam.department.model.domainManager', defaultMessage: '域管理' });
  const officePrincipalLabel = intl.formatMessage({ id: 'iam.department.model.officePrincipal', defaultMessage: '部门负责人' });

  const descriptionLabel = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const statusLabel = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const global = intl.formatMessage({ id: 'iam.common.desc.global', defaultMessage: '全局' });

  const enabledFlagDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' }), value: 'true' },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' }), value: 'false' },
    ],
  });

  function codeValidator(value, name, record) {
    if (!/^[A-Z0-9_]+$/.test(value)) {
      return intl.formatMessage({ id: 'iam.commonvalidate.message.code', defaultMessage: '编码只能由大写字母，数字和下划线构成' });
    }
  }
  return {
    autoQuery: false,
    selection: false,
    paging: 'server',
    idField: 'id',
    parentField: 'frontParentId',
    expandField: 'expand',
    autoLocateFirst: false,
    primaryKey: 'id',
    transport: {
      read: ({ data }) => {
        const { frontParentId } = data;
        let searchFlag = false;
        Object.keys(data).forEach((v) => {
          if (v.indexOf('search_') !== -1) {
            searchFlag = true;
          }
        });
        return {
          url: `${url}${searchFlag ? '' : `?parentId=${frontParentId || 0}`}`,
          method: 'get',
          data: getQueryParams(data),
          transformResponse: (resp) => {
            try {
              const respData = JSON.parse(resp);
              respData.content.forEach((v) => {
                if (v.parentId === '0') {
                  v.parentId = null;
                  v.frontParentId = null;
                } else {
                  v.frontParentId = v.parentId;
                }
              });
              return respData;
            } catch {
              return resp;
            }
          },
        };
      },
      create: ({ data: [data] }) => ({
        url: `${url}/create`,
        method: 'post',
        data,
      }),
      update: ({ data }) => {
        const postData = data?.find(item => item.isUpdate);
        return {
          url: `/iam/yqc/${tenantId}/departments`,
          method: 'put',
          data: postData,
        };
      },
      destroy: ({ data: [data] }) => ({
        url: `${url}/version?versionId=${data.id}`,
        method: 'delete',
      }),
    },
    fields: [
      { name: 'name', type: 'string', label: nameLabel, required: true, isIntl: true },
      { name: 'code', type: 'string', label: codeLabel, required: true, format: 'uppercase', validator: codeValidator },
      { name: 'managerName', type: 'string', label: principalLabel },
      { name: 'companyName', type: 'string', label: companyLabel },
      { name: 'domainName', type: 'string', label: domainLabel },
      {
        name: 'companyId',
        label: ownedCompanyLabel,
        textField: 'name',
        valueField: 'id',
        type: 'object',
        lovCode: 'COMPANY',
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            name: data.companyName,
          };
        },
      },
      {
        name: 'parentId',
        label: higherOfficeLabel,
        textField: 'name',
        valueField: 'id',
        type: 'object',
        lovCode: 'DEPARTMENT',
        dynamicProps: {
          lovPara: ({ record }) => ({ excludeIds: record.get('id') }),
        },
        transformRequest: (value) => {
          return value?.id || 0;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            name: data.parentName,
          };
        },
      },
      {
        name: 'parentName',
        label: higherOfficeLabel,
        type: 'string',
      },
      {
        name: 'managerId',
        label: officePrincipalLabel,
        textField: 'realName',
        valueField: 'id',
        type: 'object',
        lovCode: 'USER',
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            realName: data.managerName,
          };
        },
      },
      {
        name: 'domainManageFlag',
        type: 'boolean',
        label: domainMgLabel,
      },
      {
        name: 'domainId',
        type: 'object',
        label: domainLabel,
        textField: 'name',
        valueField: 'id',
        lovCode: 'DOMAIN',
        transformRequest: (value) => {
          return value?.id ? `${value?.id}` : undefined;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            name: (+value) === 0 ? `${global}` : data.domainName,
          };
        },
      },
      { name: 'enabledFlag', type: 'boolean', label: statusLabel },
      { name: 'description', type: 'string', label: descriptionLabel },
      {
        name: 'isLeaf',
        type: 'boolean',
        transformResponse: (value) => {
          return value === null ? true : value;
        },
      },
    ],
    queryFields: [
      { name: 'name', type: 'string', label: nameLabel },
      { name: 'code', type: 'string', label: codeLabel },
      { name: 'managerName', type: 'string', label: principalLabel },
      { name: 'companyName', type: 'string', label: companyLabel },
      { name: 'domainName', type: 'string', label: domainLabel },
      { name: 'enabledFlag', type: 'string', label: statusLabel, options: enabledFlagDs },
    ],
    events: {
      update: ({ dataSet, record, name, value, oldValue }) => {
        if (name.startsWith('_tls')) {
          record.set('isUpdate', true);
        }
      },
    },
  };
};
