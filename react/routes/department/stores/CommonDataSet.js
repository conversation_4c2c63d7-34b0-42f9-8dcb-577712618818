import { DataSet } from 'choerodon-ui/pro';
import { Constants } from '@zknow/utils';

const { CODE_REGEX } = Constants;

export default ({ intlPrefix, intl, tenantId, statusDataSet, type, menuDataSet }) => {
  const url = `/iam/yqc/${tenantId}/departments`;
  const nameLabel = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const codeLabel = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const principalLabel = intl.formatMessage({ id: 'iam.department.model.principal', defaultMessage: '负责人' });
  const companyLabel = intl.formatMessage({ id: 'iam.department.model.company', defaultMessage: '公司' });
  const domainLabel = intl.formatMessage({ id: 'iam.department.model.domain', defaultMessage: '域' });
  const higherOfficeLabel = intl.formatMessage({ id: 'iam.department.model.higherOffice', defaultMessage: '上级部门' });
  const ownedCompanyLabel = intl.formatMessage({ id: 'iam.department.model.ownedCompany', defaultMessage: '所属公司' });
  const domainMgLabel = intl.formatMessage({ id: 'iam.department.model.domainManager', defaultMessage: '域管理' });
  const officePrincipalLabel = intl.formatMessage({ id: 'iam.department.model.officePrincipal', defaultMessage: '部门负责人' });

  const descriptionLabel = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const statusLabel = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });

  function codeValidator(value) {
    if (!CODE_REGEX.test(value)) {
      return intl.formatMessage({ id: 'iam.common.validate.message.code', defaultMessage: '编码只能由大写字母，数字和下划线构成' });
    }
  }
  return {
    autoQuery: false,
    paging: false,
    selection: false,
    parentField: 'parentId',
    idField: 'id',
    autoQueryAfterSubmit: false,
    transport: {
      read: {
        url: `${url}/list/${type}`,
        method: 'get',
      },
      create: ({ data: [data] }) => ({
        url: `${url}`,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => ({
        url: `${url}/update`,
        method: 'put',
        data,
      }),
      destroy: ({ data: [data] }) => ({
        url: `${url}/version?versionId=${data.id}`,
        method: 'delete',
      }),
    },
    fields: [
      { name: 'name', type: 'string', label: nameLabel, required: true, isIntl: true },
      { name: 'code', type: 'string', label: codeLabel, required: true, format: 'uppercase', validator: codeValidator },
      { name: 'managerName', type: 'string', label: principalLabel },
      { name: 'companyName', type: 'string', label: companyLabel },
      { name: 'domainPath', type: 'string', label: domainLabel },
      {
        name: 'parentId',
        label: higherOfficeLabel,
        textField: 'name',
        valueField: 'id',
        type: 'object',
        lovCode: 'DEPARTMENT',
        transformRequest: (value) => {
          return value?.id || 0;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            name: data.parentName,
          };
        },
      },
      {
        name: 'companyId',
        label: ownedCompanyLabel,
        textField: 'name',
        valueField: 'id',
        type: 'object',
        lovCode: 'COMPANY',
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            name: data.companyName,
          };
        },
      },
      {
        name: 'managerId',
        label: officePrincipalLabel,
        textField: 'realName',
        valueField: 'id',
        type: 'object',
        lovCode: 'USER',
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            realName: data.managerName,
          };
        },
      },
      {
        name: 'domainManageFlag',
        type: 'boolean',
        label: domainMgLabel,
      },
      {
        name: 'domainId',
        type: 'object',
        label: domainLabel,
        textField: 'name',
        valueField: 'id',
        lovCode: 'DOMAIN',
        transformRequest: (value) => {
          return value?.id ? `${value?.id}` : undefined;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            name: data.domainName,
          };
        },
      },
      { name: 'enabledFlag', type: 'number', label: statusLabel },
      { name: 'description', type: 'string', label: descriptionLabel, isIntl: true },
    ],
  };
};
