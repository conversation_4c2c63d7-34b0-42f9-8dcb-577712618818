const MenuDataSet = ({ tenantId }) => {
  return {
    autoQuery: false,
    autoQueryAfterSubmit: false,
    selection: false,
    paging: true,
    pageSize: 50,
    dataKey: 'content',
    totalKey: 'totalPages',
    transport: {
      read: ({ data }) => {
        const { level } = data;
        return {
          url: `iam/yqc/v1/common/menus/parents/${level}`,
          method: 'get',
          transformResponse: (resp) => {
            const newData = JSON.parse(resp).filter((v) => (v.type === 'top' || v.type === 'menu'));
            return newData || [];
          },
        };
      },
    },
    field: [{ name: 'id', type: 'string' }, { name: 'name', type: 'string' }, { name: 'type', type: 'string' }],
    queryFields: [{ name: 'param', type: 'string' }],
  };
};
export default MenuDataSet;
