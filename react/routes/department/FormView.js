import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { axios } from '@yqcloud/apps-master';
import { ExternalComponent, Icon, Button } from '@zknow/components';
import { Form, TextField, Select, Output, Table, TextArea, Tooltip, message, CheckBox, Lov } from 'choerodon-ui/pro';
// import { Table } from 'choerodon-ui';
const { Column } = Table;

const defaultCodeStyle = {
  'max-width': '1.8rem',
  overflow: 'hidden',
  'text-overflow': 'ellipsis',
  'white-space': 'nowrap',
};

export default observer((props) => {
  const {
    modal, dataSet, type, updateTag,
    context: { intl, prefixCls, intlPrefix, tenantId, commonDataSet, employeeDataSet, listDataSet },
  } = props;

  const record = dataSet.current;

  modal.handleOk(async () => {
    if (type === 'modify') {
      record.setState('isPreview', true);
    }
    const OriginData = JSON.parse(JSON.stringify(listDataSet.toData()));
    try {
      // 没时间重构，只能在现有代码上做处理了

      if (type !== 'modify') {
        const res = await dataSet.submit();
        if (res?.success && res?.content.length > 0) {
          const addedDs = res.content[0];
          addedDs.frontParentId = addedDs.parentId;
          const parentItem = OriginData.find((v) => (v.id === addedDs.parentId));
          if (parentItem) {
            parentItem.isLeaf = 1;
          }
          listDataSet.loadData([addedDs, ...OriginData]);
        } else {
          message.error(res.message);
          return false;
        }
      } else {
        const data = record.toData();
        const res = await axios.put(`/iam/yqc/${tenantId}/departments`, data);
        if (res.failed) {
          message.error(res.message);
        } else {
          const findItem = listDataSet.find((v) => v.get('id') === res.id);
          if (findItem) {
            // findItem.set('frontParentId', parentId);
            findItem.set('objectVersionNumber', res.objectVersionNumber);
            findItem.set('managerName', res?.managerName);
            findItem.set('companyName', res?.companyName);
            findItem.set('domainName', res?.domainName);
          }
          message.success(intl.formatMessage({ id: 'iam.common.desc.save.success', defaultMessage: '保存成功' }));
        }

        return false;
      }
    } catch (err) {
      if (err.DataSetRequestError) {
        message.error(err.DataSetRequestError);
      }
    }
  });

  function handleCancel() {
    record.setState('isPreview', true);
    record.dataSet.reset();
    return false;
  }

  const Footer = observer(({
    okBtn,
    cancelBtn,
  }) => {
    const myCancelBtn = <Button funcType="raised" onClick={handleCancel}>{intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}</Button>;
    return (record.getState('isPreview') ? (
      <Button
        key="edit"
        funcType="raised"
        icon="icon-edit"
        onClick={() => {
          record.setState('isPreview', false);
        }}
      >
        {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '取消' })}
      </Button>
    ) : [okBtn, myCancelBtn]);
  });

  useEffect(() => {
    if (type === 'modify') {
      record.setState('isPreview', true);
      modal.update({
        footer: (okBtn, cancelBtn) => <Footer okBtn={okBtn} cancelBtn={cancelBtn} />,
      });
    }
    // return () => {
    //   record.dataSet.reset();
    // };
  }, [record, updateTag]);

  const editable = record.getState('isPreview');

  if (type !== 'modify') {
    return (
      <div>
        <Form labelLayout="horizontal" dataSet={dataSet} labelWidth="auto" columns={2}>
          <TextField autoFocus name="name" />
          <TextField name="code" restrict="_|A-Z|a-z|0-9" />
          <Lov name="managerId" />
          <Lov name="companyId" />
          <CheckBox name="domainManageFlag" />
          <Lov disabled={!dataSet?.current?.get('domainManageFlag')} name="domainId" />
          <Lov name="parentId" />
        </Form>
      </div>
    );
  }

  return (
    <div>
      <Form
        header={intl.formatMessage({ id: 'iam.department.desc.info', defaultMessage: '部门信息' })}
        className={`${prefixCls}-form`}
        labelWidth="auto"
        labelLayout="horizontal"
        dataSet={dataSet}
        columns={2}
        disabled={editable}
      >
        <TextField autoFocus name="name" />
        <TextField autoFocus name="code" disabled />
        <Lov name="managerId" />
        <Lov name="companyId" />
        <CheckBox name="domainManageFlag" />
        <Lov name="domainId" disabled={!dataSet?.current?.get('domainManageFlag')} />
        <Lov name="parentId" />
      </Form>
      <Form
        header={intl.formatMessage({ id: 'iam.department.desc.person', defaultMessage: '部门人员' })}
      >
        <Table dataSet={employeeDataSet} labelLayout="float" filter={false} queryBar="none">
          <Column name="realName" />
          <Column name="email" />
          <Column name="phone" />
        </Table>
      </Form>
    </div>
  );
});
