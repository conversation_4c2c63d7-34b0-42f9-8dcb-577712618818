import React, { useContext, useMemo, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Content, TabPage, axios } from '@yqcloud/apps-master';
import { Modal, Table, message } from 'choerodon-ui/pro';
import {
  Button,
  TableHoverAction,
  TableStatus,
  ClickText,
  ModalTitle,
} from '@zknow/components';
import FormView from './FormView';
import Store from './stores';

import './index.less';

const modalKey = Modal.key();
const { Column } = Table;

function MainView() {
  const context = useContext(Store);
  const {
    intl,
    listDataSet,
    prefixCls,
    intlPrefix,
    tenantId,
    menuDataSet,
    commonDataSet,
    employeeDataSet,
    AppState: { currentMenuType: { domainId, domainName } },
  } = context;
  const mdModalStyle = useMemo(() => ({ width: 800 }), []);
  const smModalStyle = useMemo(() => ({ width: 800, maxHeight: 578 }), []);

  useEffect(() => {
    listDataSet.query();
  }, []);

  function openModal(oType, dataSet, curRecord) {
    Modal.open({
      title:
        oType === 'modify'
          ? <ModalTitle title={intl.formatMessage({ id: 'iam.department.title', defaultMessage: '部门' })} dataSet={listDataSet} />
          : intl.formatMessage({ id: 'iam.department.action.create', defaultMessage: '新建部门' }),
      children: (
        <FormView
          type={oType}
          context={context}
          record={curRecord}
          updateTag={new Date()}
          dataSet={oType === 'modify' ? listDataSet : dataSet}
          menuDataSet={menuDataSet}
        />
      ),
      key: modalKey,
      drawer: oType === 'modify',
      style: oType === 'modify' ? mdModalStyle : smModalStyle,
      destroyOnClose: true,
    });
  }

  async function handleModify(record) {
    record.setState('isPreview', true);
    employeeDataSet.setQueryParameter('departmentId', record.get('id'));
    await employeeDataSet.query();
    openModal('modify', false, record);
  }

  function handleCreate({ record }) {
    commonDataSet.deleteAll(false);
    if (record) {
      commonDataSet.create({ parentId: record.get('id'), parentName: record.get('name') });
    } else {
      let newDepartment = {};
      if (domainId) {
        newDepartment = {
          domainManageFlag: true,
          domainId,
          domainName,
        };
      }
      commonDataSet.create(newDepartment);
    }
    openModal('create', commonDataSet, listDataSet);
  }

  function renderName({ record, name }) {
    return (
      <ClickText
        record={record}
        onClick={() => handleModify(record)}
        valueField={name}
      />
    );
  }

  function renderCreateBtn(btnType) {
    return (
      <Button
        funcType="raised"
        color="primary"
        onClick={() => handleCreate(btnType)}
      >
        {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
      </Button>
    );
  }

  function renderEnabledFlag({ value: flag }) {
    return (
      <TableStatus
        status={flag}
        enabledText={intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' })}
        disabledText={intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' })}
      />
    );
  }

  async function handleEnabled(dataSet, record) {
    const url = `/iam/yqc/${tenantId}/departments/invalid/`;
    try {
      const res = await axios.put(
        `${url}/${record.get('id')}?enabledFlag=${!record.get(
          'enabledFlag'
        )}`
      );
      if (!res?.failed) {
        dataSet.query();
        return true;
      } else {
        message.error(res.message);
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  function renderTableAction(dataSet, record) {
    const { id, enabledFlag } = record.toData();
    const actions = enabledFlag ? [
      {
        name: intl.formatMessage({ id: 'iam.department.action.subCreate', defaultMessage: '新建子部门' }),
        icon: 'add-one',
        onClick: async () => handleCreate({ record }),
      },
      {
        name: intl.formatMessage({ id: 'zknow.common.action.make.invalid', defaultMessage: '失效' }),
        icon: 'icon-Expires',
        onClick: () => handleEnabled(dataSet, record),
      },
    ] : [
      {
        name: intl.formatMessage({ id: 'zknow.common.action.make.valid', defaultMessage: '生效' }),
        icon: 'icon-read',
        onClick: () => handleEnabled(dataSet, record),
      },
    ];

    return (
      <TableHoverAction record={record} actions={actions} intlBtnIndex={1} />
    );
  }

  const renderDomainName = ({ record }) => {
    return (+(record.get('domainId')?.id)) === 0 ? intl.formatMessage({ id: 'iam.common.desc.global', defaultMessage: '全局' }) : record.get('domainName');
  };
  return (
    <TabPage>
      <Content className={`${prefixCls}-content`} style={{ padding: 0 }}>
        <Table
          mode="tree"
          dataSet={listDataSet}
          placeholder={intl.formatMessage({ id: 'iam.common.action.search', defaultMessage: '搜索' })}
          className={`${prefixCls}-table`}
          treeAsync
          onRow={({ record }) => ({
            isLeaf: record.get('isLeaf'),
          })}
          autoHeight
          autoLocateFirst={false}
          buttons={[renderCreateBtn('site')]}
          queryBarProps={{
            title: intl.formatMessage({ id: 'iam.department.title', defaultMessage: '部门' }),
          }}
        >
          <Column name="name" renderer={renderName} />
          <Column name="code" minWidth={350} />
          <Column name="managerName" tooltip="overflow" />
          <Column name="companyName" tooltip="overflow" />
          <Column name="domainName" renderer={renderDomainName} tooltip="overflow" />
          <Column name="enabledFlag" width={120} renderer={renderEnabledFlag} />
          <Column
            width={50}
            renderer={({ dataSet, record }) => renderTableAction(dataSet, record)}
          />
        </Table>
      </Content>
    </TabPage>
  );
}

export default observer(MainView);
