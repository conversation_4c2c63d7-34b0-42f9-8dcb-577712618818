import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Table } from 'choerodon-ui/pro';

const { Column } = Table;

const MenuSelectView = (props) => {
  const { dataSet, intl, record, name, modal } = props;

  useEffect(() => {
    dataSet.setState('firstQuery', false);
    dataSet.query().then((data) => {
      dataSet.setState('firstQuery', true);
      dataSet.setState('queryData', data);
      const initSelectedIds = record.get(name).map(o => o.id);
      dataSet.forEach(r => {
        if (initSelectedIds.includes(r.get('id'))) {
          dataSet.select(r);
        } else {
          dataSet.unSelect(r);
        }
      });
    });
  }, []);

  modal.handleOk(() => {
    const field = record.getField(name);
    const multiple = field.get('multiple');
    if (multiple) {
      const value = dataSet.selected.map(r => r.toData());
      record.set(name, value);
    } else {
      const menu = dataSet.current;
      record.set(name, menu.toData());
      return true;
    }
  });

  return (
    <Table
      labelLayout="float"
      dataSet={dataSet}
      mode="tree"
      treeFilter={(r) => {
        if (name === 'parentId') {
          return r.get('id') !== record.get('id');
        } else {
          return true;
        }
      }}
      pristine
      onRow={({ record: menu }) => ({
        onDoubleClick: () => {
          const field = record.getField(name);
          const multiple = field.get('multiple');
          if (multiple) {
            return;
          }
          record.set(name, { id: menu.get('id'), name: menu.get('name') });
          modal.close();
        },
      })}
      queryBarProps={{
        title: intl.formatMessage({ id: 'zknow.common.model.menu', defaultMessage: '菜单' }),
        fuzzyQuery: false,
        queryFieldsStyle: {
          name: {
            width: 140,
          },
          code: {
            width: 140,
          },
        },
      }}
    >
      <Column name="name" />
      <Column name="code" />
    </Table>
  );
};

export default observer(MenuSelectView);
