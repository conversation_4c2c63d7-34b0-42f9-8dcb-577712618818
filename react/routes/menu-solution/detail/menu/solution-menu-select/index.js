import React from 'react';
import { observer } from 'mobx-react-lite';
import { TextField, Icon, Modal } from 'choerodon-ui/pro';
import MenuSelectView from './MenuSelectView';
import styles from './index.module.less';

const parentKey = Modal.key();

const SolutionMenuSelect = (props) => {
  const { name, record, intl, menuDataSet, disabled } = props;

  const handleClear = () => {
    if (name === 'parentId') {
      record.set(name, { id: '0', name: '' });
    } else {
      record.set(name, undefined);
    }
  };

  const openParentModal = () => {
    if (disabled) {
      return;
    }
    const field = record.getField(name);
    const title = field.get('label');
    Modal.open({
      title,
      key: parent<PERSON><PERSON>,
      children: <MenuSelectView name={name} dataSet={menuDataSet} intl={intl} record={record} />,
      style: { width: '8rem' },
      bodyStyle: { padding: 0 },
    });
  };

  return (
    <TextField
      style={{ width: '100%' }}
      disabled={disabled}
      name={name}
      maxTagCount={3}
      className={styles.menuSelect}
      suffix={
        <div className={styles.action}>
          {
            record.get(name)
            && (record.get(name)?.name || record.get(name)[0]?.name)
            && !disabled
            && (
              <Icon type="close" onClick={handleClear} className={styles.clear} />
            )
          }
          <Icon type="search" onClick={openParentModal} style={{ color: 'rgb(18, 39, 77)' }} />
        </div>
      }
      readOnly
    />
  );
};

export default observer(SolutionMenuSelect);
