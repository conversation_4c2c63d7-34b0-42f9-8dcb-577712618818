import { DataSet, message } from 'choerodon-ui/pro';
import { isUndefined, omit } from 'lodash';
import { getFlatTreeChildrenIds } from '@/utils';

const getDeepth = (curDeep, menu, data) => {
  if (menu.parentId === '0' || !menu.parentId) {
    return 1 + curDeep;
  }
  const parent = data.find(o => o.id === menu.parentId);
  if (!parent) {
    menu.parentId = '0';
    return 1 + curDeep;
  }
  return getDeepth(curDeep + 1, parent, data);
};

export default ({ intl, tenantId, type, solutionId, level, displayTypeDs, selection }) => {
  const url = `/iam/yqc/v1/${type === 'site' ? '' : `${tenantId}/`}menuSolutionRelation`;
  const name = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const menu = intl.formatMessage({ id: 'zknow.common.model.menu', defaultMessage: '菜单' });
  const code = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const displayType = intl.formatMessage({ id: 'iam.menu.displayType', defaultMessage: '展现方式' });
  const rankNum = intl.formatMessage({ id: 'iam.common.model.sort', defaultMessage: '排序' });
  const status = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const icon = intl.formatMessage({ id: 'iam.common.model.icon', defaultMessage: '图标' });
  const parentLabel = intl.formatMessage({ id: 'iam.menu.parentLevel', defaultMessage: '父级菜单' });
  const typeLabel = intl.formatMessage({ id: 'zknow.common.model.type', defaultMessage: '类型' });
  const addTypeLabel = intl.formatMessage({ id: 'iam.menu.addType', defaultMessage: '添加方式' });
  const menuSolution = intl.formatMessage({ id: 'iam.common.model.solution', defaultMessage: '解决方案' });
  const descriptionLabel = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const link = intl.formatMessage({ id: 'iam.common.model.link', defaultMessage: '链接' });
  const pageLabel = intl.formatMessage({ id: 'iam.common.model.page', defaultMessage: '页面' });

  const enabledFlagDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.common.model.valid', defaultMessage: '有效' }), value: true },
      { meaning: intl.formatMessage({ id: 'iam.common.model.invalid', defaultMessage: '无效' }), value: false },
    ],
  });

  const addTypeDs = new DataSet({
    data: [
      { text: intl.formatMessage({ id: 'iam.menu.addType.menu', defaultMessage: '基于已有菜单添加' }), value: 'menu' },
      { text: intl.formatMessage({ id: 'iam.menu.addType.solution', defaultMessage: '基于已有解决方案添加' }), value: 'solution' },
    ],
  });

  const typeOptionsDs = new DataSet({
    data: [
      {
        text: menu,
        value: 'menu',
      },
      {
        text: link,
        value: 'link',
      },
    ],
    selection: 'single',
  });

  return {
    autoQuery: false,
    selection,
    paging: false,
    parentField: 'parentId.id',
    idField: 'id',
    autoLocateFirst: false,
    fields: [
      {
        name: 'menuId',
        multiple: true,
        type: 'object',
        label: menu,
        lovCode: type === 'site' ? 'SITE_PARENT_MENU' : 'PARENT_MENU',
        lovPara: {
          level,
        },
        transformRequest: (value) => {
          return value?.id ? `${value?.id}` : value;
        },
        dynamicProps: {
          required: ({ record }) => !record.get('id'),
        },
      },
      {
        name: 'name',
        type: 'string',
        label: name,
      },
      {
        name: 'code',
        type: 'string',
        label: code,
      },
      {
        name: 'icon',
        type: 'string',
        label: icon,
      },
      {
        name: 'parentId',
        type: 'object',
        label: parentLabel,
        help: intl.formatMessage({ id: 'iam.menu.parentLevel.help', defaultMessage: '菜单总层级需在三层以内' }),
        lovCode: type === 'site' ? 'SITE_PARENT_MENU' : 'PARENT_MENU',
        lovPara: {
          level,
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            name: data.parentName,
          };
        },
        transformRequest: (value) => {
          return value?.id ? `${value?.id}` : value;
        },
        textField: 'name',
        valueField: 'id',
      },
      { name: 'solutionId', type: 'string', defaultValue: solutionId },
      // 用于选择菜单
      {
        name: 'menuSolutionId',
        type: 'object',
        ignore: 'always',
        textField: 'name',
        valueField: 'id',
        lovCode: 'SOLUTION',
        label: menuSolution,
        dynamicProps: {
          required: ({ record }) => record?.get('addType') === 'solution',
          lovPara: ({ record }) => ({
            excludeSolutionId: record?.getState('solutionId'),
          }),
        },
      },
      {
        name: 'displayType',
        type: 'string',
        defaultValue: 'NORMAL',
        options: displayTypeDs,
        label: displayType,
      },
      { name: 'type', type: 'string', label: typeLabel },
      {
        name: 'addType',
        type: 'string',
        label: addTypeLabel,
        textField: 'text',
        valueField: 'value',
        options: addTypeDs,
        defaultValue: 'menu',
      },
      { name: 'rankNum', type: 'number', label: rankNum, defaultValue: 0, min: 0 },
      { name: 'enabledFlag', type: 'boolean', label: status, options: enabledFlagDs, defaultValue: true },
      { name: 'description', type: 'string', label: descriptionLabel },
      { name: 'deepth', type: 'number', ignore: 'always' },
      {
        name: 'path',
        type: 'string',
        label: link,
      },
      {
        name: 'type',
        type: 'string',
        label: typeLabel,
        textField: 'text',
        valueField: 'value',
        options: typeOptionsDs,
      },
      {
        name: 'menuPermission',
        type: 'object',
        label: pageLabel,
        textField: 'description',
        valueField: 'id',
      },
      {
        name: 'menuPermissionId',
        bind: 'menuPermission.id',
        type: 'string',
      },
    ],
    transport: {
      read: ({ data }) => ({
        url: `${url}/listBySolution/${data?.solutionId || solutionId}`,
        method: 'get',
        transformResponse: (response) => {
          try {
            if (response && !response.failed) {
              const list = JSON.parse(response);
              list.forEach(o => {
                o.deepth = getDeepth(0, o, list);
                o.isLeaf = !list.some(x => x.parentId === o.id);
                if (o.parentId === '0') {
                  o.parentName = '';
                } else {
                  const parent = list.find(x => x.id === o.parentId);
                  o.parentName = parent?.name;
                }
              });
              return list;
            } else {
              message.error(response.message);
            }
          } catch (e) {
            return response;
          }
        },
      }),
      create: ({ data: [data] }) => {
        const isSolution = data.addType === 'solution';
        const postData = data.menuId.map(menuId => {
          if (isSolution) {
            return {
              ...menuId,
              ...data,
              parentId: menuId.parentId,
              menuId: menuId.menuId[0],
              solutionId: data.solutionId,
              addType: undefined,
            };
          } else {
            return {
              ...data,
              menuId: menuId?.id || menuId,
              addType: undefined,
            };
          }
        });
        delete postData.addType;
        return {
          url: isSolution ? `${url}/createWithSolution?parentId=${data.parentId}` : `${url}/batch`,
          method: 'post',
          data: postData,
        };
      },
      update: ({ data: [data] }) => ({
        url,
        method: 'put',
        data: omit(data, 'menuId'),
      }),
      destroy: ({ data: [data] }) => ({
        url: `${url}/${data.id}`,
        method: 'delete',
      }),
    },
    events: {
      select: ({ dataSet, record }) => {
        const id = record.get('id');
        const childrenIds = getFlatTreeChildrenIds(id, dataSet.toData());
        const children = dataSet.filter(r => childrenIds.includes(r.get('id')));
        dataSet.batchSelect(children);
      },
      query: ({ dataSet, data }) => {
        if (dataSet.getState('firstQuery') === true) {
          const res = dataSet.getState('queryData');
          const searchFuzzyQuery = dataSet.getState('__SEARCHTEXT__');
          const searchName = data.name;
          const searchCode = data.code;
          const searchDisplayType = data.displayType;
          if (isUndefined(searchFuzzyQuery) && isUndefined(searchName) && isUndefined(searchCode) && isUndefined(searchDisplayType)) {
            dataSet.loadData(res);
          } else {
            const newData = res
              .filter(v => (searchFuzzyQuery ? v.name?.toLowerCase?.()?.includes?.(searchFuzzyQuery.toLowerCase()) || v.code?.toLowerCase?.()?.includes?.(searchFuzzyQuery.toLowerCase()) : true))
              .filter(v => (searchName ? v.name?.toLowerCase?.()?.includes?.(searchName?.toLowerCase?.()) : true))
              .filter(v => (searchCode ? v.code?.toLowerCase?.()?.includes?.(searchCode?.toLowerCase?.()) : true))
              .filter(v => (searchDisplayType ? v?.displayType?.includes?.(searchDisplayType) : true));
            dataSet.loadData(newData);
          }
          return false;
        }
        return true;
      },
      update: ({ record, name: fieldName, dataSet }) => {
        if (fieldName === 'addType') {
          record.set('menuSolutionId', undefined);
          record.set('menuId', undefined);
        }
        // const data = dataSet.toData();
        // if (fieldName === 'parentId') {
        //   dataSet.forEach(r => {
        //     r.set('deepth', getDeepth(0, r.toData(), data));
        //     r.set('isLeaf', !data.some(x => x.parentId === r.get('id')));
        //   });
        // }
      },
      reset: ({ dataSet }) => {
        dataSet.loadData(dataSet.getState('queryData'));
      },
    },
    queryFields: [
      { name: 'name', type: 'string', label: name },
      { name: 'code', type: 'string', label: code },
    ],
  };
};
