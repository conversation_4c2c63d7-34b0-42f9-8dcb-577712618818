import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import { formatterCollections } from '@zknow/utils';
import MenuDataSet from './MenuDataSet';
import CreateMenuDataSet from './CreateMenuDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: ['iam.common', 'iam.menu'] })(injectIntl(
  (props) => {
    const {
      children,
      intl,
      solutionId,
      level,
      AppState: { currentMenuType: { organizationId: tenantId, type } },
    } = props;
    const prefixCls = 'iam-solution-menu';
    const intlPrefix = 'iam.menu';

    const displayTypeDs = new DataSet({
      data: [
        { meaning: intl.formatMessage({ id: 'iam.menu.normalMenu', defaultMessage: '普通' }), value: 'NORMAL' },
        // { meaning: intl.formatMessage({ id: 'iam.menuSolution.desc.replicate.menu', defaultMessage: '折叠' }), value: 'REPLICATE' },
        { meaning: intl.formatMessage({ id: 'iam.menu.groupMenu', defaultMessage: '分组' }), value: 'GROUP' },
      ],
    });

    const menuDataSet = useMemo(
      () => new DataSet(MenuDataSet({
        intl,
        tenantId,
        type,
        solutionId,
        level,
        displayTypeDs,
        selection: 'multiple',
      })),
      [tenantId, solutionId, level],
    );
    const createMenuDataSet = useMemo(
      () => new DataSet(CreateMenuDataSet({ intl, tenantId, type, solutionId, level, displayTypeDs, intlPrefix })),
      [tenantId, solutionId, level],
    );

    const parentMenuDataSet = useMemo(
      () => new DataSet(MenuDataSet({
        intl,
        tenantId,
        type,
        solutionId,
        level,
        displayTypeDs,
        selection: false,
      })),
      [tenantId, solutionId, level],
    );

    const solutionMenuDataSet = useMemo(
      () => new DataSet(MenuDataSet({
        intl,
        tenantId,
        type,
        solutionId,
        level,
        displayTypeDs,
        selection: 'multiple',
      })),
      [tenantId, solutionId, level],
    );

    const value = {
      ...props,
      prefixCls,
      intl,
      menuDataSet,
      createMenuDataSet,
      parentMenuDataSet,
      solutionMenuDataSet,
      tenantId,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }
)));
