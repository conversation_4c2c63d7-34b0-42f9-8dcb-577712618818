import { DataSet } from 'choerodon-ui/pro';
import { Constants } from '@zknow/utils';

const { CODE_REGEX } = Constants;
export default ({ intlPrefix, intl, tenantId, type, solutionId, level, displayTypeDs }) => {
  const url = `/iam/yqc/v1/${tenantId}/menuSolutionRelation/createWithMenu`;
  const nameLabel = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const codeLabel = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const descriptionLabel = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const iconLabel = intl.formatMessage({ id: 'iam.common.model.icon', defaultMessage: '图标' });
  const typeLabel = intl.formatMessage({ id: 'zknow.common.model.type', defaultMessage: '类型' });
  const pageLabel = intl.formatMessage({ id: 'iam.common.model.page', defaultMessage: '页面' });
  const parentLabel = intl.formatMessage({ id: 'iam.menu.parentLevel', defaultMessage: '父级菜单' });
  const menu = intl.formatMessage({ id: 'zknow.common.model.menu', defaultMessage: '菜单' });
  const link = intl.formatMessage({ id: 'iam.common.model.link', defaultMessage: '链接' });
  const displayTypeLabel = intl.formatMessage({ id: 'iam.menu.displayType', defaultMessage: '展现方式' });
  const rankNum = intl.formatMessage({ id: 'iam.common.model.sort', defaultMessage: '排序' });

  const typeOptionsDs = new DataSet({
    data: [
      {
        text: menu,
        value: 'menu',
      },
      {
        text: link,
        value: 'link',
      },
    ],
    selection: 'single',
  });

  function codeValidator(value) {
    if (!CODE_REGEX.test(value)) {
      return intl.formatMessage({ id: 'iam.common.validate.message.code', defaultMessage: '编码只能由大写字母，数字和下划线构成' });
    }
  }
  return {
    autoQuery: false,
    paging: false,
    selection: false,
    transport: {
      create: ({ data: [data] }) => ({
        url,
        method: 'post',
        data,
      }),
    },
    fields: [
      { name: 'name', type: 'string', label: nameLabel, required: true },
      {
        name: 'code',
        type: 'string',
        label: codeLabel,
        required: true,
        format: 'uppercase',
        validator: codeValidator,
        unique: true,
      },
      {
        name: 'path',
        type: 'string',
        label: link,
        help: intl.formatMessage({ id: 'iam.menu.path.help', defaultMessage: '若要访问相对路径，请以/开头；若要访问绝对路径，请以http\\https开头' }),
      },
      {
        name: 'menuPermission',
        type: 'object',
        label: pageLabel,
        textField: 'description',
        valueField: 'id',
        lovCode: 'TENANT_PAGE',
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            description: data?.description,
          };
        },
      },
      {
        name: 'menuPermissionId',
        bind: 'menuPermission.id',
        type: 'string',
      },
      {
        name: 'type',
        type: 'string',
        label: typeLabel,
        textField: 'text',
        valueField: 'value',
        options: typeOptionsDs,
        required: true,
      },
      { name: 'level', type: 'string', defaultValue: level },
      { name: 'icon', type: 'string', label: iconLabel, required: true },
      { name: 'description', type: 'string', label: descriptionLabel },
      {
        name: 'parentId',
        type: 'object',
        label: parentLabel,
        help: intl.formatMessage({ id: 'iam.menu.parentLevel.help', defaultMessage: '菜单总层级需在三层以内' }),
        lovCode: type === 'site' ? 'SITE_PARENT_MENU' : 'PARENT_MENU',
        lovPara: {
          level,
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            name: data.parentName,
          };
        },
        transformRequest: (value) => {
          return value?.id ? `${value?.id}` : value;
        },
        textField: 'name',
        valueField: 'id',
      },
      { name: 'solutionId', type: 'string', defaultValue: solutionId },
      {
        name: 'displayType',
        type: 'string',
        defaultValue: 'NORMAL',
        options: displayTypeDs,
        label: displayTypeLabel,
      },
      { name: 'rankNum', type: 'number', label: rankNum, defaultValue: 0, min: 0 },
    ],
  };
};
