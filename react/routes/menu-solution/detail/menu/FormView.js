import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, Lov, SelectBox, TextField, NumberField, TextArea, Select, message } from 'choerodon-ui/pro';
import { Button, IconPicker, Icon } from '@zknow/components';
import { axios } from '@yqcloud/apps-master';
import SolutionMenuSelect from './solution-menu-select';

export default observer((props) => {
  const {
    modal,
    record,
    intl,
    tenantId,
    prefixCls,
    parentMenuDataSet,
    solutionMenuDataSet,
  } = props;
  const isEdit = record.getState('isPreview');

  const handleCancelEdit = () => {
    record.setState('isPreview', true);
    record.reset();
  };

  const Footer = observer(({
    okBtn,
  }) => {
    if (tenantId !== record.get('tenantId')) {
      return [];
    }
    const myCancelBtn = <Button funcType="raised" onClick={handleCancelEdit}>{intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}</Button>;
    return (record.getState('isPreview') ? (
      <Button
        key="edit"
        funcType="raised"
        color="primary"
        icon="icon-edit"
        onClick={() => {
          record.setState('isPreview', false);
        }}
      >
        {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
      </Button>
    ) : [okBtn, myCancelBtn]);
  });

  useEffect(() => {
    const loadDetail = async (id) => {
      if (!id) {
        return;
      }
      const url = tenantId ? `/iam/yqc/v1/${tenantId}/common/menus/detail/${id}`
        : `/iam/yqc/v1/menus/common/menus/detail/${id}`;
      const detail = await axios.get(url);
      if (detail && detail.failed) {
        message.error(detail.message);
      } else {
        record.init({
          path: detail.path,
          menuPermission: { id: detail.menuPermissionId, description: detail.menuPermissionDescription },
        });
        record.setState('loaded', true);
      }
    };

    if (!record.getState('loaded')) {
      let menuId;
      try {
        menuId = record.get('menuId')[0];
      } catch {
        menuId = undefined;
      }
      if (record.get('id') && menuId) {
        loadDetail(menuId);
      }
    }
  }, [record.get('id'), record.get('menuId')]);

  useEffect(() => {
    if (record.status !== 'add') {
      ['name', 'code', 'icon'].forEach(fieldName => {
        const field = record.getField(fieldName);
        field.required = true;
      });
      record.setState('isPreview', true);
      modal.update({
        footer: (okBtn, cancelBtn) => <Footer okBtn={okBtn} cancelBtn={cancelBtn} />,
      });
    }
  }, [record, tenantId]);

  modal.handleOk(async () => {
    const res = await record.dataSet.submit();
    if (res) {
      record.dataSet.getState('firstQuery') && record.dataSet.setState('firstQuery', false);
      const expandIds = record.dataSet.filter(r => r.isExpanded).map(r => r.get('id'));
      const data = await record.dataSet.query();
      record.dataSet.setState('queryData', data);
      record.dataSet.forEach(r => {
        if (expandIds.includes(r.get('id'))) {
          r.isExpanded = true;
        }
      });
      return true;
    }
    return false;
  });

  modal.handleCancel(async () => {
    if (record.status === 'add') {
      await record.dataSet.delete(record, false);
    } else {
      const expandIds = record.dataSet.filter(r => r.isExpanded).map(r => r.get('id'));
      record.reset();
      record.dataSet.query();
      record.dataSet.forEach(r => {
        if (expandIds.includes(r.get('id'))) {
          r.isExpanded = true;
        }
      });
    }
  });

  useEffect(() => {
    const menuSolutionId = record.get('menuSolutionId');
    record.set('menuId', []);
    if (menuSolutionId) {
      solutionMenuDataSet.setQueryParameter('solutionId', menuSolutionId.id);
    }
  }, [record.get('menuSolutionId')]);

  const renderIcon = ({ record: iconRecord }) => (
    <Icon className={`${prefixCls}-icon`} type={iconRecord?.get('icon')} />
  );

  return (
    <div>
      <Form
        labelLayout="horizontal"
        record={record}
        labelWidth="auto"
        disabled={isEdit}
      >
        {record.status !== 'add' && <TextField name="name" disabled required />}
        {record.status !== 'add' && <TextField name="code" restrict="_|A-Z|a-z|0-9" required disabled />}
        {record.status !== 'add' && <Select name="type" disabled />}
        {record.status !== 'add' && record.get('type') === 'menu' && <Lov name="menuPermission" disabled />}
        {record.status !== 'add' && record.get('type') === 'link' && <TextField name="path" disabled />}
        {record.status === 'add' && <SelectBox name="addType" />}
        {record.get('addType') === 'solution' && <Lov name="menuSolutionId" />}
        {record.get('addType') === 'solution' && (
          <SolutionMenuSelect
            name="menuId"
            intl={intl}
            menuDataSet={solutionMenuDataSet}
            record={record}
            disabled={record.get('addType') === 'solution' && !record.get('menuSolutionId')}
          />
        )}
        {record.get('addType') === 'menu' && <Lov name="menuId" />}
        <SolutionMenuSelect name="parentId" intl={intl} menuDataSet={parentMenuDataSet} record={record} disabled={isEdit} />
        {record.status !== 'add' && <IconPicker name="icon" renderer={renderIcon} record={record} placement="bottomLeft" required disabled />}
        <NumberField name="rankNum" />
        {record.status !== 'add' && <TextArea name="description" newLine colSpan={2} rows={1} autoSize={{ minRows: 1, maxRows: 4 }} resize="height" disabled />}
      </Form>
    </div>
  );
});
