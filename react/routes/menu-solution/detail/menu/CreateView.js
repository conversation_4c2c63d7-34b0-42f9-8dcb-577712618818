import React from 'react';
import { observer } from 'mobx-react-lite';
import { Icon, IconPicker } from '@zknow/components';
import { Form, TextField, UrlField, Select, TextArea, Lov, NumberField } from 'choerodon-ui/pro';
import SolutionMenuSelect from './solution-menu-select';

export default observer((props) => {
  const { modal, record, prefixCls, listDataSet, intl, parentMenuDataSet } = props;

  modal.handleOk(async () => {
    if (await record.dataSet.validate()) {
      const res = await record.dataSet.submit();
      if (res) {
        const expandIds = listDataSet.filter(r => r.isExpanded).map(r => r.get('id'));
        record.dataSet.reset();
        listDataSet.getState('firstQuery') && listDataSet.setState('firstQuery', false);
        listDataSet.query().then(newData => {
          listDataSet.setState('firstQuery', true);
          listDataSet.setState('queryData', newData);
          listDataSet.forEach(r => {
            if (expandIds.includes(r.get('id'))) {
              r.isExpanded = true;
            }
          });
        });
        return true;
      }
    }
    return false;
  });

  modal.handleCancel(async () => {
    if (record.status === 'add') {
      await record.dataSet.delete(record, false);
    } else {
      record.reset();
    }
  });

  function renderIcon({ record: iconRecord }) {
    return (
      <Icon className={`${prefixCls}-icon`} type={iconRecord?.get('icon')} />
    );
  }

  return (
    <div>
      <Form labelLayout="horizontal" record={record} labelWidth="auto">
        <TextField autoFocus name="name" />
        <TextField name="code" restrict="_|A-Z|a-z|0-9" />
        <Select name="type" />
        { record.get('type') === 'menu' && <Lov name="menuPermission" searchable />}
        { record.get('type') === 'link' && <UrlField name="path" />}
        <SolutionMenuSelect name="parentId" intl={intl} menuDataSet={parentMenuDataSet} record={record} />
        <IconPicker name="icon" renderer={renderIcon} record={record} placement="bottomLeft" />
        <NumberField name="rankNum" />
        <TextArea name="description" newLine colSpan={2} rows={1} autoSize={{ minRows: 1, maxRows: 4 }} resize="height" />
      </Form>
    </div>
  );
});
