import React, { useContext, useMemo, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Modal, Table, Menu, message } from 'choerodon-ui/pro';
import { Button, TableHoverAction, TableStatus, ClickText, ModalTitle, Icon } from '@zknow/components';
import { batchDisableMenu, batchEnableMenu, batchDelete } from '@/services';
import { getFlatTreeChildrenIds } from '@/utils';
import MenuSort from './menu-sort';
import Store from './stores';
import FormView from './FormView';
import CreateView from './CreateView';

import styles from './index.module.less';

const { Column } = Table;
const { Item } = Menu;
const modalKey = Modal.key();
const modifyModalKey = Modal.key();
const disableConfirmKey = Modal.key();
const removeConfirmKey = Modal.key();
const sortKey = Modal.key();

function ListView() {
  const {
    menuDataSet,
    prefixCls,
    intl,
    tenantId,
    type,
    solutionId,
    // 预置解决方案的 tenantId 与当前 tenantId 不同，可用于判断是否内置
    solutionTenantId,
    createMenuDataSet,
    menuTypeMeaning,
    parentMenuDataSet,
    solutionMenuDataSet,
  } = useContext(Store);

  const modalStyle = useMemo(() => ({ width: 520 }), []);

  useEffect(() => {
    (async () => {
      const res = await menuDataSet.query();
      menuDataSet.setState('firstQuery', true);
      menuDataSet.setState('queryData', res);
    })();
  }, []);

  /**
   * 添加
   * @param record
   */
  function openModal(record) {
    let menuConfig = { parentId: '0', parentName: '' };
    if (record) {
      menuConfig = { parentId: record.get('id'), parentName: record.get('name') };
    }
    const newMenu = menuDataSet.create(menuConfig);
    newMenu.setState('solutionId', solutionId);
    menuDataSet.current = newMenu;
    Modal.open({
      title: record ? intl.formatMessage({ id: 'iam.menu.addSubMenu', defaultMessage: '添加子菜单' }) : intl.formatMessage({ id: 'iam.menu.create', defaultMessage: '新建菜单' }),
      children: (
        <FormView
          record={newMenu}
          intl={intl}
          prefixCls={prefixCls}
          tenantId={tenantId}
          parentMenuDataSet={parentMenuDataSet}
          solutionMenuDataSet={solutionMenuDataSet}
        />
      ),
      key: modalKey,
      style: modalStyle,
      drawer: false,
      destroyOnClose: true,
    });
  }

  function renderAddBtn() {
    if (solutionTenantId === tenantId) {
      return (
        <Button funcType="raised" color="primary" icon="ListAdd" onClick={() => openModal()}>
          {intl.formatMessage({ id: 'zknow.common.button.append', defaultMessage: '添加' })}
        </Button>
      );
    }
    return null;
  }

  /**
   * 新建菜单并加入解决方案
   * @param record
   */
  function openCreateModal(record) {
    let menuConfig = { parentId: '0', parentName: '' };
    if (record) {
      menuConfig = { parentId: record.get('id'), parentName: record.get('name') };
    }
    const newMenu = createMenuDataSet.create(menuConfig);
    createMenuDataSet.current = newMenu;
    Modal.open({
      title: record ? intl.formatMessage({ id: 'iam.menu.addSubMenu', defaultMessage: '添加子菜单' }) : intl.formatMessage({ id: 'iam.menu.create', defaultMessage: '新建菜单' }),
      children: (
        <CreateView
          record={newMenu}
          intl={intl}
          prefixCls={prefixCls}
          tenantId={tenantId}
          listDataSet={menuDataSet}
          parentMenuDataSet={parentMenuDataSet}
        />
      ),
      key: modalKey,
      style: modalStyle,
      drawer: false,
      destroyOnClose: true,
    });
  }

  const openSortModal = () => {
    Modal.open({
      key: sortKey,
      title: intl.formatMessage({ id: 'iam.menu.menuSort', defaultMessage: '菜单排序' }),
      style: { width: 960 },
      bodyStyle: { height: 688, padding: 0 },
      children: <MenuSort dataSet={menuDataSet} type={type} tenantId={tenantId} intl={intl} />,
    });
  };

  function renderCreateBtn() {
    if (solutionTenantId === tenantId) {
      return (
        <Button funcType="raised" color="primary" icon="Plus" onClick={() => openCreateModal()}>
          {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
        </Button>
      );
    }
    return null;
  }

  const renderSortBtn = () => {
    if (solutionTenantId === tenantId) {
      return (
        <Button
          funcType="raised"
          icon="SortTwo"
          color="secondary"
          onClick={openSortModal}
          disabled={menuDataSet.length === 0}
        >
          {intl.formatMessage({ id: 'iam.common.model.sort', defaultMessage: '排序' })}
        </Button>
      );
    }
  };

  const batchToggleMenu = async (enabledFlag) => {
    const handleRequest = async () => {
      const data = menuDataSet.selected.map(r => r.get('id'));
      let res;
      if (enabledFlag) {
        res = await batchEnableMenu({ type, tenantId, data });
      } else {
        res = await batchDisableMenu({ type, tenantId, data });
      }
      if (res && res.failed) {
        message.error(res.message);
      } else {
        message.success(intl.formatMessage({ id: 'iam.common.operation.success', defaultMessage: '操作成功' }));
        const expandIds = menuDataSet.filter(r => r.isExpanded).map(r => r.get('id'));
        menuDataSet.setState('firstQuery', false);
        const newData = await menuDataSet.query();
        menuDataSet.setState('firstQuery', true);
        menuDataSet.setState('queryData', newData);
        menuDataSet.forEach(r => {
          if (expandIds.includes(r.get('id'))) {
            r.isExpanded = true;
          }
        });
      }
    };
    if (!enabledFlag) {
      Modal.warning({
        key: disableConfirmKey,
        title: intl.formatMessage({ id: 'iam.menu.disableConfirm.front', defaultMessage: '确定要失效勾选的这些菜单吗？' }),
        children: (
          <div className={styles.confirmWrapper}>
            <span>{intl.formatMessage({ id: 'iam.menu.disableConfirm.rear', defaultMessage: '失效父级菜单将同时失效其下的子级菜单。' })}</span>
          </div>
        ),
        cancelButton: true,
        onOk: handleRequest,
      });
    } else {
      handleRequest();
    }
  };

  const batchRemove = () => {
    Modal.warning({
      key: removeConfirmKey,
      title: intl.formatMessage({ id: 'iam.menu.removeConfirm.front', defaultMessage: '确定要移除勾选的这些菜单吗？' }),
      children: (
        <div className={styles.confirmWrapper}>
          <span>{intl.formatMessage({ id: 'iam.menu.removeConfirm.rear', defaultMessage: '移除父级菜单将同时移除其下的子级菜单。' })}</span>
        </div>
      ),
      cancelButton: true,
      onOk: async () => {
        const data = menuDataSet.selected.map(r => r.get('id'));
        const res = await batchDelete({ type, tenantId, data });
        if (res && res.failed) {
          message.error(res.message);
        } else {
          message.success(intl.formatMessage({ id: 'iam.common.operation.success', defaultMessage: '操作成功' }));
          const expandIds = menuDataSet.filter(r => r.isExpanded).map(r => r.get('id'));
          menuDataSet.setState('firstQuery', false);
          const newData = await menuDataSet.query();
          menuDataSet.setState('firstQuery', true);
          menuDataSet.setState('queryData', newData);
          menuDataSet.forEach(r => {
            if (expandIds.includes(r.get('id'))) {
              r.isExpanded = true;
            }
          });
        }
      },
    });
  };

  function renderEnabledFlag({ record }) {
    return (
      <TableStatus
        status={record.get('enabledFlag')}
        enabledText={intl.formatMessage({ id: 'iam.common.model.valid', defaultMessage: '有效' })}
        disabledText={intl.formatMessage({ id: 'iam.common.model.invalid', defaultMessage: '无效' })}
      />
    );
  }

  function renderTableAction({ dataSet, record }) {
    const enabledFlag = record.get('enabledFlag');
    const elementId = record.get('id');

    const deleteMenu = async () => {
      const handleDelete = async () => {
        const expandIds = dataSet.filter(r => r.isExpanded).map(r => r.get('id'));
        const childrenIds = getFlatTreeChildrenIds(elementId, dataSet.toData());
        const res = await batchDelete({ type, tenantId, data: [...childrenIds, elementId] });
        if (res && res.failed) {
          message.error(res.message);
        } else {
          dataSet.setState('firstQuery', false);
          const queryRes = await dataSet.query();
          dataSet.setState('queryData', queryRes);
          dataSet.setState('firstQuery', true);
          await dataSet.query();
          dataSet.forEach(r => {
            if (expandIds.includes(r.get('id'))) {
              r.isExpanded = true;
            }
          });
        }
      };
      Modal.warning({
        key: removeConfirmKey,
        title: intl.formatMessage({ id: 'iam.menu.singleRemoveConfirm.front', defaultMessage: '确定要移除该菜单吗？' }),
        children: (
          <div className={styles.confirmWrapper}>
            {record.children && (
              <span>{intl.formatMessage({ id: 'iam.menu.removeConfirm.rear', defaultMessage: '移除父级菜单将同时移除其下的子级菜单。' })}</span>
            )}
          </div>
        ),
        cancelButton: true,
        onOk: handleDelete,
      });
    };

    const activeMenu = async () => {
      const allChildrenIds = getFlatTreeChildrenIds(record.get('id'), dataSet.toData());
      const toggleActive = async () => {
        let res;
        try {
          if (enabledFlag) {
            res = await batchDisableMenu({ type, tenantId, data: [...allChildrenIds, record.get('id')] });
          } else {
            res = await batchEnableMenu({ type, tenantId, data: [...allChildrenIds, record.get('id')] });
          }
          if (!res?.failed) {
            message.success(intl.formatMessage({ id: 'iam.common.operation.success', defaultMessage: '操作成功' }));
            const expandIds = dataSet.filter(r => r.isExpanded).map(r => r.get('id'));
            dataSet.setState('firstQuery', false);
            const queryRes = await dataSet.query();
            dataSet.setState('queryData', queryRes);
            dataSet.setState('firstQuery', true);
            await dataSet.query();
            dataSet.forEach(r => {
              if (expandIds.includes(r.get('id'))) {
                r.isExpanded = true;
              }
            });
            return true;
          }
          return false;
        } catch (e) {
          return false;
        }
      };

      if (record.children && enabledFlag) {
        Modal.warning({
          key: disableConfirmKey,
          title: intl.formatMessage({ id: 'iam.menu.singleDisableConfirm.front', defaultMessage: '确定要失效该菜单吗？' }),
          children: (
            <div className={styles.confirmWrapper}>
              <span>{intl.formatMessage({ id: 'iam.menu.disableConfirm.rear', defaultMessage: '失效父级菜单将同时失效其下的子级菜单。' })}</span>
            </div>
          ),
          cancelButton: true,
          onOk: toggleActive,
        });
      } else {
        await toggleActive();
      }
    };

    let actions = [];

    if (tenantId === solutionTenantId) {
      actions = [
        {
          name: intl.formatMessage({ id: 'iam.menu.addSubMenu', defaultMessage: '添加子菜单' }),
          icon: 'Plus',
          onClick: () => openModal(record),
        },
        {
          name: intl.formatMessage({ id: 'iam.menu.createSubMenu', defaultMessage: '新建子菜单' }),
          icon: 'Plus',
          onClick: () => openCreateModal(record),
        },
        {
          name: enabledFlag ? intl.formatMessage({ id: 'iam.common.make.invalid', defaultMessage: '失效' }) : intl.formatMessage({ id: 'iam.common.make.valid', defaultMessage: '生效' }),
          icon: enabledFlag ? 'ReduceOne' : 'CheckOne',
          onClick: activeMenu,
        },
        {
          name: intl.formatMessage({ id: 'iam.common.action.remove', defaultMessage: '移除' }),
          icon: 'Delete',
          onClick: deleteMenu,
        },
      ];
    } else {
      actions = [
        {
          name: enabledFlag ? intl.formatMessage({ id: 'iam.common.make.invalid', defaultMessage: '失效' }) : intl.formatMessage({ id: 'iam.common.make.valid', defaultMessage: '生效' }),
          icon: enabledFlag ? 'ReduceOne' : 'CheckOne',
          onClick: activeMenu,
        },
      ];
    }

    return (
      <TableHoverAction
        record={record}
        actions={actions}
        intlBtnIndex={0}
        maxButtonCount={2}
      />
    );
  }

  function handleModify(record, dataSet) {
    Modal.open({
      title: <ModalTitle title={intl.formatMessage({ id: 'zknow.common.model.menu', defaultMessage: '菜单' })} dataSet={dataSet} />,
      children: (
        <FormView
          record={record}
          intl={intl}
          prefixCls={prefixCls}
          tenantId={tenantId}
          parentMenuDataSet={parentMenuDataSet}
        />
      ),
      key: modifyModalKey,
      drawer: true,
      style: modalStyle,
      destroyOnClose: true,
    });
  }

  function renderName({ record, name, dataSet }) {
    return (
      <ClickText
        record={record}
        onClick={() => handleModify(record, dataSet)}
        valueField={name}
      />
    );
  }

  function renderIcon({ record }) {
    return (
      <Icon type={record.get('icon')} />
    );
  }

  function renderType({ record }) {
    return menuTypeMeaning[record.get('type')] || record.get('type');
  }

  const renderBarAction = () => {
    const buttons = [
      {
        element: (
          <Item
            key="batchEnabled"
            onClick={() => { batchToggleMenu(true); }}
            disabled={menuDataSet.selected.length === 0 || menuDataSet.selected.every(record => record.get('enabledFlag'))}
          >
            <div className={styles.actionWrapper}>
              <Icon type="CheckOne" style={{ marginRight: '.05rem' }} />
              {intl.formatMessage({ id: 'iam.common.action.batchEnabled', defaultMessage: '批量生效' })}
            </div>
          </Item>
        ),
      },
      {
        element: (
          <Item
            key="batchDisabled"
            onClick={() => { batchToggleMenu(false); }}
            disabled={menuDataSet.selected.length === 0 || menuDataSet.selected.every(record => !record.get('enabledFlag'))}
          >
            <div className={styles.actionWrapper}>
              <Icon type="ReduceOne" style={{ marginRight: '.05rem' }} />
              {intl.formatMessage({ id: 'iam.common.action.batchDisabled', defaultMessage: '批量失效' })}
            </div>
          </Item>
        ),
      },
    ];
    if (solutionTenantId === tenantId) {
      buttons.push({
        element: (
          <Item
            key="batchRemove"
            onClick={batchRemove}
            disabled={menuDataSet.selected.length === 0}
          >
            <div className={styles.actionWrapper}>
              <Icon type="Delete" style={{ marginRight: '.05rem' }} />
              {intl.formatMessage({ id: 'iam.common.action.batchRemove', defaultMessage: '批量移除' })}
            </div>
          </Item>
        ),
      });
    }
    return buttons;
  };

  return (
    <Table
      labelLayout="float"
      dataSet={menuDataSet}
      className={`${prefixCls}-table`}
      mode="tree"
      pristine
      buttons={[
        renderAddBtn(),
        renderCreateBtn(),
        renderSortBtn(),
      ]}
      queryBarProps={{
        title: intl.formatMessage({ id: 'zknow.common.model.menu', defaultMessage: '菜单' }),
        tableActions: renderBarAction(),
      }}
    >
      <Column name="name" renderer={renderName} />
      <Column name="code" />
      <Column name="type" renderer={renderType} />
      <Column name="icon" renderer={renderIcon} />
      <Column name="rankNum" align="left" />
      <Column name="enabledFlag" align="left" renderer={renderEnabledFlag} />
      <Column width={50} renderer={renderTableAction} tooltip="none" />
    </Table>
  );
}

export default observer(ListView);
