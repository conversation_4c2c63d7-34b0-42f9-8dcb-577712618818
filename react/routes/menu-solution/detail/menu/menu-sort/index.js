import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import classNames from 'classnames';
import { message } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import { batchUpdateMenu } from '@/services';
import SortableList from './sortable-list';
import styles from './index.module.less';

const MenuSort = (props) => {
  const { dataSet, type, tenantId, intl, modal } = props;

  const titles = [
    intl.formatMessage({ id: 'iam.menu.levelOne', defaultMessage: '一级菜单' }),
    intl.formatMessage({ id: 'iam.menu.levelTwo', defaultMessage: '二级菜单' }),
    intl.formatMessage({ id: 'iam.menu.levelThree', defaultMessage: '三级菜单' }),
  ];

  modal.handleCancel(() => {
    dataSet.reset();
  });

  modal.handleOk(async () => {
    const dirtyRecords = dataSet.filter(r => r.dirty);
    const data = dirtyRecords.map(record => ({
      id: record.get('id'),
      rankNum: record.get('rankNum'),
      parentId: record.get('parentId.id'),
      objectVersionNumber: record.get('objectVersionNumber'),
      solutionId: record.get('solutionId'),
    }));
    const res = await batchUpdateMenu({ type, tenantId, data });
    if (res && res.failed) {
      message.error(res.message);
      return false;
    } else {
      const expandIds = dataSet.filter(r => r.isExpanded).map(r => r.get('id'));
      dataSet.setState('firstQuery', false);
      dataSet.query().then(newData => {
        dataSet.setState('firstQuery', true);
        dataSet.setState('queryData', newData);
        dataSet.forEach(r => {
          if (expandIds.includes(r.get('id'))) {
            r.isExpanded = true;
          }
        });
        return true;
      });
    }
  });

  const [currentLevel1, setCurrentLevel1] = useState(null);
  const [currentLevel2, setCurrentLevel2] = useState(null);

  useEffect(() => {
    const firstLevel1 = dataSet.filter(r => r.get('parentId.id') === '0').sort((a, b) => a.get('rankNum') - b.get('rankNum'))[0];
    setCurrentLevel1(firstLevel1.get('id'));
  }, []);

  useEffect(() => {
    setCurrentLevel2(null);
  }, [currentLevel1]);

  const renderItem = (record) => {
    const { id, parentId, icon, name, deepth, isLeaf } = record.toData();
    return (
      <div
        key={id}
        className={classNames({
          [styles.menuItem]: true,
          [styles.menuItemCurrent]: deepth === 1 ? id === currentLevel1 : id === currentLevel2,
        })}
        onClick={() => {
          if (deepth === 1) {
            setCurrentLevel1(id);
            return;
          }
          if (deepth === 2) {
            setCurrentLevel2(id);
          }
        }}
        data-id={id}
        data-parent={parentId}
      >
        <Icon type="drag" className={styles.dragHandle} />
        <Icon type={icon} className={styles.menuIcon} />
        <div className={styles.menuName}>{name}</div>
        { !isLeaf && <Icon type="right" className={styles.menuSub} />}
      </div>
    );
  };

  // 添加完排序
  const handleAdd = (evt) => {
    const { item, newIndex, to } = evt;
    const ord = Array.from(to.children).map(div => div.dataset.id);
    ord.splice(newIndex, 0, item.dataset.id);
    ord.forEach((id, index) => {
      const record = dataSet.find(r => r.get('id') === id);
      record.set('rankNum', (index + 1) * 10);
    });
  };

  const handleChange = (ord, sortable, evt) => {
    // add and remove
    const { item, to } = evt;
    if (evt.type === 'add') {
      const id = item.dataset.id;
      const parentId = to.dataset.parent;
      const record = dataSet.find(r => r.get('id') === id);
      record.set('parentId', { id: parentId, name: '' });
    }
    // sort
    if (evt.type === 'update') {
      ord.forEach((id, index) => {
        const record = dataSet.find(r => r.get('id') === id);
        record.set('rankNum', (index + 1) * 10);
      });
    }
  };

  const level = (deep) => {
    const data = {
      1: dataSet.filter(r => r.get('parentId.id') === '0').sort((a, b) => a.get('rankNum') - b.get('rankNum')),
      2: dataSet.filter(r => r.get('parentId.id') === currentLevel1).sort((a, b) => a.get('rankNum') - b.get('rankNum')),
      3: dataSet.filter(r => r.get('parentId.id') === currentLevel2).sort((a, b) => a.get('rankNum') - b.get('rankNum')),
    };
    const sortOptions = {
      group: {
        name: 'level',
        put: (to, _, pullItem) => {
          const noParent = !to.el.dataset.parent;
          const isChild = pullItem.dataset.id === to.el.dataset.parent;
          return !noParent && !isChild;
        },
        pull: true,
      },
      animation: 0,
      onAdd: handleAdd,
      dragClass: styles.menuItemDrag,
    };
    const currentLevel = {
      1: 0,
      2: currentLevel1,
      3: currentLevel2,
    };
    return (
      <div className={styles.column}>
        <span className={styles.title}>{titles[deep - 1]}</span>
        <SortableList
          deep={deep}
          currentLevel={currentLevel[deep]}
          containerClass={styles.list}
          sortOptions={sortOptions}
          renderItem={renderItem}
          dataSource={data[deep]}
          onChange={handleChange}
        />
      </div>
    );
  };

  return (
    <div className={styles.main}>
      { level(1) }
      { level(2) }
      { level(3) }
    </div>
  );
};

export default observer(MenuSort);
