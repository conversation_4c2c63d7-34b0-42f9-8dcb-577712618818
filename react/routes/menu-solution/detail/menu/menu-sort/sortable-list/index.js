import React from 'react';
import { observer } from 'mobx-react-lite';
import ReactSortable from 'react-sortablejs';

const SortableList = (props) => {
  const {
    deep,
    dataSource,
    containerClass,
    renderItem,
    sortOptions,
    empty,
    currentLevel,
    onChange,
  } = props;

  return (
    <ReactSortable
      key={`${currentLevel}-${dataSource.length}`}
      options={sortOptions}
      className={containerClass}
      onChange={onChange}
      data-parent={currentLevel}
      data-deep={deep}
    >
      { dataSource.length === 0 && empty }
      { dataSource.map(o => renderItem(o)) }
    </ReactSortable>
  );
};

export default observer(SortableList);
