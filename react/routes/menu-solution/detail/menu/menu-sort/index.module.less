@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.main {
  display: flex;
  height: 100%;
}

.column {
  height: 100%;
  width: 33.3%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  border-right: 1px solid fade(#cbd2dc, 50%);

  :global {
    .c7n-list {
      width: 100%;
      height: 100%;

      .c7n-list-item-content-single {
        align-items: center;
      }
    }
  }
}

.list {
  overflow: auto;
  height: calc(100% - .21rem);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  padding: 0 .24rem;
}

.menuItem {
  width: 100%;
  min-height: .38rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: @text-color;
  padding-left: .1rem;
  padding-right: .1rem;
  border-radius: .04rem;
  cursor: grab;

  &:hover {
    background: #f1f2f4;
  }
}

.menuItemCurrent {
  background: @yq-primary-color-10 !important;
}

.menuItemDrag {
  background: #f1f2f4 !important;
}

.dragHandle {
  margin-right: .05rem;
}

.menuIcon {
  margin-right: .05rem;
}

.menuName {
  flex: 1;
}

.menuSub {
  margin-left: .05rem;
}

.title {
  font-size: .14rem;
  font-weight: 400;
  color: rgba(18, 39, 77, 0.45);
  margin-bottom: .1rem;
  padding: .16rem 0 0 .34rem;
}
