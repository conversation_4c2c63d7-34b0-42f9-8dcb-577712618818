import { DataSet } from 'choerodon-ui/pro';

export default ({ intl, tenantId, type, solutionId }) => {
  const url = `/iam/yqc/v1/${type === 'site' ? '' : `${tenantId}/`}menuSolution`;
  const name = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const code = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const logo = intl.formatMessage({ id: 'iam.common.model.icon', defaultMessage: '图标' });
  const description = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const rankNum = intl.formatMessage({ id: 'iam.common.model.sort', defaultMessage: '排序' });
  const level = intl.formatMessage({ id: 'iam.common.model.level', defaultMessage: '层级' });
  const status = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const isPreset = intl.formatMessage({ id: 'iam.menu.isPreSet', defaultMessage: '是否预置' });

  const enabledFlagDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.common.model.valid', defaultMessage: '有效' }), value: true },
      { meaning: intl.formatMessage({ id: 'iam.common.model.invalid', defaultMessage: '无效' }), value: false },
    ],
  });

  const levelDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.common.model.user.center', defaultMessage: '个人中心' }), value: 'user' },
      { meaning: type === 'site' ? intl.formatMessage({ id: 'zknow.common.model.platform', defaultMessage: '平台' }) : intl.formatMessage({ id: 'iam.common.model.solution', defaultMessage: '解决方案' }), value: 'site' },
      { meaning: type === 'site' ? intl.formatMessage({ id: 'zknow.common.model.tenant', defaultMessage: '租户' }) : intl.formatMessage({ id: 'iam.common.model.solution', defaultMessage: '解决方案' }), value: 'organization' },
    ],
  });

  return {
    autoQuery: true,
    paging: false,
    autoQueryAfterSubmit: true,
    fields: [
      { name: 'name', type: 'string', label: name, required: true },
      { name: 'code', type: 'string', label: code },
      { name: 'enabledFlag', type: 'boolean', label: status, options: enabledFlagDs },
      { name: 'description', type: 'string', label: description },
      { name: 'logo', type: 'string', label: logo },
      { name: 'rankNum', type: 'number', label: rankNum },
      { name: 'level', type: 'string', label: level, options: levelDs },
      { name: 'tenantId', type: 'string' },
      {
        name: 'isPreset',
        type: 'boolean',
        label: isPreset,
        ignore: 'always',
        disabled: true,
      },
    ],
    transport: {
      read: {
        url: `${url}/${solutionId}`,
        method: 'get',
        transformResponse(response) {
          try {
            const data = JSON.parse(response);
            if (data?.failed) {
              return data;
            } else {
              // 数据中 tenantId 为 '0'， 即为预置数据
              data.isPreset = `${data.tenantId}` === '0';
              return data;
            }
          } catch (e) {
            return response;
          }
        },
      },
      update: ({ data: [data] }) => ({
        url,
        method: 'put',
        data,
      }),
    },
  };
};
