import React, { useState, useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Button } from '@zknow/components';
import { Content, TabPage, Header } from '@yqcloud/apps-master';
import { Tabs } from 'choerodon-ui';
import Store from './stores';
import Basic from './basic';
import MenuTable from './menu';
import DisableTable from './disable';

const { TabPane } = Tabs;

function MainView() {
  const {
    intl,
    prefixCls,
    history,
    detailDataSet,
    solutionId,
    tenantId,
    type,
    menuTypeMeaning,
  } = useContext(Store);
  const [isEdit, setEdit] = useState(false);

  async function handleSave() {
    if (detailDataSet.current?.dirty) {
      await detailDataSet.submit();
      setEdit(false);
      await detailDataSet.query();
    }
  }

  function renderButtons() {
    const record = detailDataSet.current;
    if (!record || tenantId !== record.get('tenantId')) {
      return null;
    }
    const buttons = !isEdit ? (
      <Button
        disabled={detailDataSet.status !== 'ready' || !detailDataSet?.current?.get('enabledFlag')}
        funcType="raised"
        color="primary"
        icon="Write"
        onClick={() => setEdit(true)}
      >
        {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
      </Button>
    ) : [
      <Button
        funcType="raised"
        color="primary"
        key="save"
        onClick={handleSave}
      >
        {intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}
      </Button>,
      <Button
        funcType="raised"
        color="default"
        key="cancel"
        onClick={() => {
          setEdit(false);
          detailDataSet.reset();
        }}
      >
        {intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}
      </Button>,
    ];

    return (
      <div>
        {buttons}
      </div>
    );
  }

  return (
    <TabPage>
      <Header backPath={`/iam/menu_solution${history.location?.search}`} dataSet={detailDataSet}>
        <h1>{intl.formatMessage({ id: 'iam.menu.detail.title', defaultMessage: '解决方案详情' })}</h1>
        {renderButtons()}
      </Header>
      <Content className={prefixCls}>
        <div className={`${prefixCls}-wrapper`}>
          <Basic isEdit={isEdit} />
          <Tabs type="card" className={`${prefixCls}-tabs`}>
            <TabPane tab={intl.formatMessage({ id: 'zknow.common.model.menu', defaultMessage: '菜单' })} key="menu">
              {detailDataSet.current?.get('level')
                ? (
                  <MenuTable
                    menuTypeMeaning={menuTypeMeaning}
                    solutionTenantId={detailDataSet?.current?.get('tenantId')}
                    solutionId={solutionId}
                    level={detailDataSet.current?.get('level')}
                  />
                ) : null}
            </TabPane>
            {type === 'site'
              ? (
                <TabPane tab={intl.formatMessage({ id: 'iam.menu.disableList', defaultMessage: '禁用列表' })} key="disable">
                  <DisableTable solutionId={solutionId} />
                </TabPane>
              ) : null}
          </Tabs>
        </div>
      </Content>
    </TabPage>
  );
}

export default observer(MainView);
