import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import { formatterCollections } from '@zknow/utils';
import DisableDataSet from './DisableDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: ['iam.common', 'iam.menu'] })(injectIntl(
  (props) => {
    const {
      children,
      intl,
      solutionId,
    } = props;
    const prefixCls = 'iam-solution-disable';
    const disableDataSet = useMemo(() => new DataSet(DisableDataSet({ intl, solutionId })), [solutionId]);

    const value = {
      ...props,
      prefixCls,
      intl,
      disableDataSet,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }
)));
