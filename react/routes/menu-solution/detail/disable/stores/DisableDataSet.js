import { getQueryParams } from '@zknow/utils';

export default ({ intl, solutionId }) => {
  const url = `/iam/yqc/v1/menuSolutionDisable?solutionId=${solutionId}`;
  const name = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const code = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });

  return {
    autoQuery: true,
    selection: false,
    paging: true,
    autoLocateFirst: false,
    fields: [
      {
        name: 'tenantName',
        type: 'string',
        label: name,
      },
      {
        name: 'tenantNum',
        type: 'string',
        label: code,
      },
    ],
    transport: {
      read: ({ data }) => ({
        url,
        method: 'get',
        data: getQueryParams(data),
      }),
    },
    queryFields: [
      { name: 'tenantName', type: 'string', label: name },
      { name: 'tenantNum', type: 'string', label: code },
    ],
  };
};
