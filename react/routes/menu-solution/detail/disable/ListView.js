import React, { useContext, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Modal, Table } from 'choerodon-ui/pro';
import Store from './stores';

const { Column } = Table;

function ListView() {
  const {
    disableDataSet,
    prefixCls,
    intl,
  } = useContext(Store);

  return (
    <Table
      labelLayout="float"
      dataSet={disableDataSet}
      className={`${prefixCls}-table`}
      pristine
      canFold
      queryBarProps={{
        title: intl.formatMessage({ id: 'iam.menu.disableList', defaultMessage: '禁用列表' }),
      }}
    >
      <Column name="tenantName" />
      <Column name="tenantNum" />
    </Table>
  );
}

export default observer(ListView);
