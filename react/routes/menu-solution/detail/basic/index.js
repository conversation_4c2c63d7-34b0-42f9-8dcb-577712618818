import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { ExternalComponent } from '@zknow/components';
import { Form, TextField, TextArea, CheckBox, NumberField, Select } from 'choerodon-ui/pro';
import Store from '../stores';

export default observer((props) => {
  const { isEdit } = props;
  const { detailDataSet, prefixCls } = useContext(Store);

  return (
    <div>
      <Form columns={2} labelLayout="horizontal" dataSet={detailDataSet} disabled={!isEdit}>
        <TextField autoFocus name="name" />
        <TextField name="code" disabled />
        <Select name="level" disabled />
        <NumberField name="rankNum" />
        <ExternalComponent
          system={{
            scope: 'itsm',
            module: 'ImageSetter',
          }}
          disabled={!isEdit}
          name="logo"
          record={detailDataSet.current}
        />
        <CheckBox name="isPreset" />
        <TextArea name="description" newLine colSpan={2} rows={1} autoSize={{ minRows: 1, maxRows: 4 }} resize="height" />
      </Form>
    </div>
  );
});
