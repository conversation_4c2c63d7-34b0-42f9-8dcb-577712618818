import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { TabPage, Content, axios } from '@yqcloud/apps-master';
import { Table, Modal } from 'choerodon-ui/pro';
import { TableStatus, ClickText, useFilter, Button, TableHoverAction } from '@zknow/components';
import Store from './stores';
import FormView from './FormView';

const { Column } = Table;
const modalKey = Modal.key();

function ListView() {
  const {
    listDataset,
    intl,
    history,
    type,
    tenantId,
  } = useContext(Store);

  useFilter(listDataset);

  function openModal(baseId) {
    listDataset.create({ baseId });
    Modal.open({
      title: intl.formatMessage({ id: baseId ? 'iam.menu.baseCreate' : 'iam.menu.solution.create', defaultMessage: baseId ? '基于新建' : '新建解决方案' }),
      children: (
        <FormView
          dataSet={listDataset}
          intl={intl}
          type={type}
        />
      ),
      key: modalKey,
      drawer: false,
      destroyOnClose: true,
      afterClose: () => {
        if (listDataset?.current?.getState() !== 'sync') {
          listDataset.remove(listDataset.current);
        }
      },
    });
  }

  function renderName({ record, name, value }) {
    return (
      <ClickText
        record={record}
        valueField={name}
        history={history}
        path={`/iam/menu_solution/detail/${record.get('id')}`}
      >
        {value}
      </ClickText>
    );
  }

  function renderTenant({ record }) {
    return record.get('tenantId') === '0' ? intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' }) : intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' });
  }

  function renderEnabledFlag({ value }) {
    return (
      <TableStatus
        status={value}
        enabledText={intl.formatMessage({ id: 'iam.common.model.valid', defaultMessage: '有效' })}
        disabledText={intl.formatMessage({ id: 'iam.common.model.invalid', defaultMessage: '无效' })}
      />
    );
  }

  function renderAction({ record }) {
    const url = `/iam/yqc/v1/${type === 'site' ? '' : `${tenantId}/`}menuSolution`;
    const enabledFlag = record.get('enabledFlag');

    const activeSolution = async () => {
      try {
        const res = await axios.put(`${url}/${record.get('id')}/${enabledFlag ? 'disable' : 'enable'}`);
        if (!res?.failed) {
          listDataset.query();
          return true;
        }
        return false;
      } catch (e) {
        return false;
      }
    };

    const deleteSolution = async () => {
      await listDataset.delete(record);
      listDataset.query();
    };

    const actions = [];
    if (!(record.get('level') === 'user' && type === 'organization')) {
      actions.push(
        {
          name: intl.formatMessage({ id: 'iam.menu.baseCreate', defaultMessage: '基于新建' }),
          icon: 'Plus',
          onClick: () => {
            openModal(record.get('id'));
          },
        },
      );
    }

    actions.push(
      {
        name: !enabledFlag ? intl.formatMessage({ id: 'iam.common.make.valid', defaultMessage: '生效' }) : intl.formatMessage({ id: 'iam.common.make.invalid', defaultMessage: '失效' }),
        icon: !enabledFlag ? 'icon-read' : 'icon-Expires',
        onClick: activeSolution,
      },
    );

    if (tenantId === record.get('tenantId')) {
      actions.push(
        {
          name: intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' }),
          icon: 'Delete',
          onClick: deleteSolution,
        },
      );
    }

    return (
      <TableHoverAction
        record={record}
        actions={actions}
        intlBtnIndex={2}
        maxButtonCount={2}
      />
    );
  }

  return (
    <TabPage>
      <Content style={{ padding: 0 }}>
        <Table
          pristine
          dataSet={listDataset}
          autoHeight
          buttons={[
            <Button icon="plus" funcType="raised" color="primary" onClick={() => openModal(false)}>
              {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
            </Button>,
          ]}
          queryFieldsLimit={100}
          queryBarProps={{
            title: intl.formatMessage({ id: 'iam.common.model.solution', defaultMessage: '解决方案' }),
          }}
        >
          <Column name="name" renderer={renderName} />
          <Column name="code" />
          <Column name="description" />
          <Column name="level" />
          <Column name="rankNum" align="left" />
          {tenantId !== '0'
            ? <Column name="tenantId" renderer={renderTenant} />
            : null}
          <Column name="enabledFlag" align="left" renderer={renderEnabledFlag} />
          <Column width={100} renderer={renderAction} tooltip="none" />
        </Table>
      </Content>
    </TabPage>
  );
}

export default observer(ListView);
