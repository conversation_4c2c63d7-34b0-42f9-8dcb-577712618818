import React, { useContext, useMemo, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { ClickText, TableHoverAction, Button as YQButton, YqTable } from '@zknow/components';
import { Content, TabPage, axios } from '@yqcloud/apps-master';
import { Table, Modal, message } from 'choerodon-ui/pro';
import uniq from 'lodash/uniq';
import EnabledFlag from '@/components/enabled-flag';
import BatchButton from '@/components/batch-button';
import LevelChange from './level-change';
import Store from './stores';

const { Column } = Table;

const ListView = () => {
  const { tenantId, companyDataSet, prefixCls, intlPrefix, intl, history, match } = useContext(Store);

  useEffect(() => {
    companyDataSet.query();
  }, []);

  function openModal() {
    Modal.open({
      children: <LevelChange intl={intl} intlPrefix={intlPrefix} dataSet={companyDataSet} prefixCls={prefixCls} />,
      key: Modal.key(),
      destroyOnClose: true,
      movable: true,
      title: intl.formatMessage({ id: 'iam.company.desc.change.level', defaultMessage: '调整级别' }),
      drawer: false,
      style: { width: 520, minHeight: 186, maxHeight: 688 },
    });
  }

  function handleModify(record) {
    history.push({
      pathname: `${match.path}/detail/${record.get('id')}`,
      search: history.location?.search,
    });
  }

  function handleCreate({ record }) {
    history.push({
      pathname: `${match.path}/detail/new`,
      search: history.location?.search,
      state: { parentId: record?.get('id'), parentName: record?.get('companyName') },
    });
  }

  function renderCreate() {
    return (
      <YQButton funcType="raised" color="primary" onClick={handleCreate}>
        {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
      </YQButton>
    );
  }

  function handleChangeLevel() {
    openModal();
  }

  function handleChangeEnabledFlag({ batch, enabledFlag, disabled }) {
    if (disabled) {
      return false;
    }
    const requestData = batch ? companyDataSet.selected.map(r => r.get('id')) : [companyDataSet.current?.get('id')];
    axios.put(`/iam/yqc/${tenantId}/companies/toggle?enabled_flag=${enabledFlag}`, requestData).then(res => {
      if (!res.success) {
        message.error(res.message);
      } else {
        companyDataSet.query();
      }
    }).catch(e => {
      message.error(e.message);
    });
  }

  function renderBatchAction() {
    const items = [
      {
        icon: 'change',
        text: intl.formatMessage({ id: 'iam.company.desc.change.level', defaultMessage: '调整级别' }),
        handleClick: handleChangeLevel,
      },
      {
        icon: 'icon-Expires',
        text: intl.formatMessage({ id: 'iam.common.desc.batch.disabled', defaultMessage: '批量失效' }),
        disabled: uniq(companyDataSet.selected.map(r => r.get('enabledFlag'))).includes(false),
        handleClick: () => handleChangeEnabledFlag({ batch: true, enabledFlag: false, disabled: uniq(companyDataSet.selected.map(r => r.get('enabledFlag'))).includes(false) }),
      },
      {
        icon: 'icon-read',
        text: intl.formatMessage({ id: 'iam.common.desc.batch.enabled', defaultMessage: '批量生效' }),
        disabled: uniq(companyDataSet.selected.map(r => r.get('enabledFlag'))).includes(true),
        handleClick: () => handleChangeEnabledFlag({ batch: true, enabledFlag: true, disabled: uniq(companyDataSet.selected.map(r => r.get('enabledFlag'))).includes(true) }),
      },
    ];
    return <BatchButton text={intl.formatMessage({ id: 'zknow.common.button.batch.action', defaultMessage: '批量操作' })} dataSet={companyDataSet} items={items} />;
  }

  const buttons = useMemo(() => [
    renderCreate(),
    // renderBatchAction(),
  ], [companyDataSet.selected.length]);

  function renderName({ record }) {
    return <ClickText record={record} onClick={() => handleModify(record)} valueField="companyName" />;
  }

  function renderAction({ record }) {
    const enabledFlag = record?.get('enabledFlag');
    const actions = enabledFlag ? [
      {
        name: intl.formatMessage({ id: 'iam.company.desc.create.sub', defaultMessage: '新建子公司' }),
        icon: 'add-one',
        onClick: async () => handleCreate({ record }),
      },
      {
        name: intl.formatMessage({ id: 'zknow.common.action.make.invalid', defaultMessage: '失效' }),
        icon: 'icon-Expires',
        onClick: () => handleChangeEnabledFlag({ batch: false, enabledFlag: false }),
      },
    ] : [
      {
        name: intl.formatMessage({ id: 'zknow.common.action.make.valid', defaultMessage: '生效' }),
        icon: 'icon-read',
        onClick: () => handleChangeEnabledFlag({ batch: false, enabledFlag: true }),
      },
    ];
    return <TableHoverAction record={record} actions={actions} intlBtnIndex={2} />;
  }

  return (
    <TabPage>
      <Content style={{ padding: 0 }}>
        <Table
          dataSet={companyDataSet}
          placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
          mode="tree"
          treeAsync
          onRow={({ dataSet, record, index, expandedRow }) => ({
            isLeaf: !record.get('leaf'),
          })}
          autoHeight
          autoLocateFirst={false}
          buttons={buttons}
          queryBarProps={{
            title: intl.formatMessage({ id: 'zknow.common.model.company', defaultMessage: '公司' }),
          }}
        >
          <Column name="companyName" width={280} renderer={renderName} tooltip="overflow" />
          <Column name="companyCode" />
          <Column name="abbreviation" tooltip="overflow" />
          <Column name="managerId" />
          <Column name="domainName" tooltip="overflow" />
          <Column name="enabledFlag" width={150} renderer={({ value }) => <EnabledFlag enabledFlag={value} />} />
          <Column renderer={({ record }) => renderAction({ record })} width={10} />
        </Table>
      </Content>
    </TabPage>
  );
};

export default observer(ListView);
