.company {
  &-form {
    &-body {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      flex-direction: column;
      margin-bottom: 0.1rem;

      .c7n-pro-field-wrapper {
        margin-right: 24px;
      }
      .title {
        display: flex;
        .avatar {
          position: relative;
          .camera {
            z-index: 100;
            width: 56px;
            height: 56px;
            background: #000;
            opacity: 0;
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            border-radius: 0.04rem;
            cursor: pointer;
          }
          .camera:hover {
            opacity: 0.618;
            transition: all 0.3s;
          }
        }
        .title-right {
          margin-left: 0.12rem;
          .name {
            font-size: 0.16rem;
            font-weight: 500;
            color: #020e26;
            line-height: 24px;
            margin-bottom: 0.04rem;
          }
          .code {
            font-size: 0.12rem;
            font-weight: 400;
            color: #8c8c8c;
            line-height: 20px;
          }
        }
      }
    }
    &-header {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
    }

    &-avator {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 40%;
    }

    &-buttons {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;
      margin-right: 0.3rem;
    }

    &-container {
      width: 40%;
    }
  }

  &-wrapper {
    .lc-form-page-loader {
      min-height: unset;
    }
  }
}
