import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { FileUploader, FileShower, Icon as YQIcon } from '@zknow/components';
import defaultUnitIcon from '@/assets/images/favicon.png';
import Store from '../stores';

import './index.less';

const BasicInfo = (props) => {
  const { editable } = props;
  const { companyDetailDataSet, intl, intlPrefix, tenantId } = useContext(Store);

  return (
    <div className="title">
      <FileUploader
        disabled={!editable}
        name="imageUrl"
        record={companyDetailDataSet?.current}
        colSpan={2}
        tenantId={tenantId}
      >
        <div className="avatar" style={{ cursor: editable ? 'pointer' : 'auto' }}>
          <FileShower
            fileKey={companyDetailDataSet?.current?.get('imageUrl')}
            colSpan={2}
          >
            {({ src }) => [
            companyDetailDataSet?.current
            && <img
              style={{
                width: '0.56rem',
                height: '0.56rem',
                border: '1px solid rgba(0, 0, 0, 0.15)',
                borderRadius: '0.04rem',
              }}
              src={src || defaultUnitIcon}
              alt={intl.formatMessage({ id: 'iam.company.desc.info', defaultMessage: '公司信息' })}
            />,
            ]}
          </FileShower>
          {editable && <div
            className="camera"
          ><YQIcon type="camera" size={30} fill="#fff" /></div>}
        </div>
      </FileUploader>
      <div className="title-right">
        <div className="name">{companyDetailDataSet?.current?.get('companyName')}</div>
        <div className="code">{companyDetailDataSet?.current?.get('companyCode')}</div>
      </div>
    </div>
  );
};
export default observer(BasicInfo);
