import React, { useEffect, useState, useContext, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { Content, TabPage, Header, axios } from '@yqcloud/apps-master';
import { Button } from '@zknow/components';
import { message } from 'choerodon-ui/pro';
import { ExternalComponent } from '@zknow/components';
import BasicInfo from './BasicInfo';
import Store from '../stores';

import './index.less';

const Detail = (props) => {
  const { match: { params: { id } }, location } = props;
  const {
    companyDetailDataSet, intl, intlPrefix, prefixCls, history, tenantId,
    AppState: { currentMenuType: { domainId, domainName } }, pageRef,
  } = useContext(Store);
  const [editable, setEditable] = useState(id === 'new');

  const { search } = history.location;

  useEffect(() => {
    if (id !== 'new') {
      companyDetailDataSet.setQueryParameter('id', id);
      companyDetailDataSet.query().then(() => {
        companyDetailDataSet.current = companyDetailDataSet.get(0);
      });
    } else {
      companyDetailDataSet.create({});
    }
  }, [id]);

  function handleBack() {
    history.push({
      pathname: '/iam/company',
      search: history.location?.search,
    });
  }

  async function handleSave() {
    const formDataSet = pageRef.current?.formDataSet;
    const validate = await formDataSet?.validate();
    const imageUrl = companyDetailDataSet.current?.get('imageUrl');
    if (validate && formDataSet.current) {
      formDataSet.current?.set('image_url', imageUrl);
      formDataSet.submit().then(() => {
        if (id === 'new') {
          handleBack();
        } else {
          setEditable(false);
          formDataSet.query();
        }
      });
    } else {
      return false;
    }
  }

  function handleCancel() {
    const formDataSet = pageRef.current?.formDataSet;
    setEditable(!editable);
    formDataSet?.current?.reset();
    if (id === 'new') {
      formDataSet.remove(formDataSet.current);
      handleBack();
    }
  }

  function toggleEdit() {
    setEditable(!editable);
  }

  function handleChangeEnabledFlag(enabledFlag) {
    const requestData = [companyDetailDataSet.current?.get('id')];
    axios.put(`/iam/yqc/${tenantId}/companies/toggle?enabled_flag=${enabledFlag}`, requestData).then(res => {
      if (!res.success) {
        message.error(res.message);
      } else {
        message.success(intl.formatMessage({ id: 'zknow.common.success.submit', defaultMessage: '提交成功' }));
        companyDetailDataSet.query();
      }
    }).catch(e => {
      message.error(e.message);
    });
  }

  const actionButtons = () => {
    if (id === 'new') {
      return (
        <div className={`${prefixCls}-form-buttons`}>
          <Button funcType="raised" onClick={handleSave} color="primary">{intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}</Button>
          <Button funcType="raised" onClick={handleCancel}>{intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}</Button>
        </div>
      );
    } else if (companyDetailDataSet.current?.get('enabledFlag')) {
      return editable ? (
        <div className={`${prefixCls}-form-buttons`}>
          <Button funcType="raised" onClick={handleSave} color="primary">{intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}</Button>
          <Button funcType="raised" onClick={handleCancel}>{intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}</Button>
        </div>
      ) : (
        <div className={`${prefixCls}-form-buttons`}>
          <Button funcType="raised" onClick={toggleEdit} color="default" icon="write">{intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}</Button>
          <Button funcType="raised" onClick={() => handleChangeEnabledFlag(false)} icon="icon-Expires">{intl.formatMessage({ id: 'zknow.common.action.make.invalid', defaultMessage: '失效' })}</Button>
        </div>
      );
    } else {
      return (
        <div className={`${prefixCls}-form-buttons`}>
          <Button funcType="raised" onClick={() => handleChangeEnabledFlag(true)} icon="icon-read">{intl.formatMessage({ id: 'zknow.common.action.make.valid', defaultMessage: '生效' })}</Button>
        </div>
      );
    }
  };

  return (
    <TabPage>
      <Header backPath={`/iam/company${search}`} dataSet={companyDetailDataSet}>
        <h1>{intl.formatMessage({ id: id === 'new' ? `${intlPrefix}.create` : `${intlPrefix}.detail` })}</h1>
        <div>
          {actionButtons()}
        </div>
      </Header>
      <Content>
        <div className={`${prefixCls}-wrapper`} style={{ height: '100%' }}>
          <div className={`${prefixCls}-form-body`}>
            <BasicInfo editable={editable} />
          </div>
          <ExternalComponent
            system={{
              scope: 'lcr',
              module: 'PageLoader',
            }}
            viewCode="COMPANY_DETAIL_DEFAULT"
            instanceId={id}
            pageRef={pageRef}
            mode={editable ? 'MODIFY' : 'READONLY'}
            ticketFlag
            events={{
              indexChange: ({ dataSet, record }) => {
                if (record && !record?.get('id') && id === 'new') {
                  if (location?.state?.parentId && location?.state?.parentName) {
                    record.set('parent_id', {
                      id: location?.state?.parentId,
                      company_name: location?.state?.parentName,
                    });
                  }
                  if (domainId && domainName) {
                    record.set('domain_manage_flag', true);
                    record.set('domain_id', {
                      id: domainId,
                      name: domainName,
                    });
                  }
                }
                if (record && record?.get('id') && id !== 'new') {
                  dataSet.getField('parent_id')?.set('lovPara', { excludeIds: id });
                }
              },
              update: ({ record, name, value }) => {
                if (companyDetailDataSet?.current) {
                  if (name === 'company_code') {
                    companyDetailDataSet.current.set('companyCode', value);
                  }
                  if (name === 'company_name') {
                    companyDetailDataSet.current.set('companyName', value);
                  }
                }
              },
            }}
          />
        </div>
      </Content>
    </TabPage>
  );
};

export default observer(Detail);
