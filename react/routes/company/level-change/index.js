import React from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TreeSelect } from 'choerodon-ui/pro';
import { CloseSmall } from '@icon-park/react';

import './index.less';

const LevelChange = (props) => {
  const { dataSet, modal, prefixCls, intl, intlPrefix } = props;

  const classPrefix = `${prefixCls}-level-change`;

  async function handleOk() {
    await dataSet.submit();
    dataSet.query();
  }

  function handleCancel() {
    dataSet.reset();
  }

  modal.handleOk(handleOk);
  modal.handleCancel(handleCancel);

  function handleUnSelect(record) {
    dataSet.unSelect(record);
  }

  function handleChange(value) {
    dataSet.selected.forEach(r => {
      r.set('parentId', value || '0');
    });
  }

  return (
    <div style={{ width: '100%' }}>
      <div className={`${classPrefix}-selected-container`}>
        <span className={`${classPrefix}-selected-container-title`}>
          {intl.formatMessage({ id: `${intlPrefix}.selected` }, { num: dataSet.selected.length })}
        </span>
        <div className={`${classPrefix}-selected-container-company`}>
          {dataSet.selected.slice(0, 4).map(r => (
            <div className={`${classPrefix}-selected-container-name`}>
              <span className={`${classPrefix}-selected-container-name-text`}>{r.get('companyName')}</span>
              <CloseSmall
                theme="outline"
                size="24"
                fill="#8c8c8c"
                style={{ marginTop: '0.05rem', cursor: 'pointer' }}
                onClick={() => handleUnSelect(r)}
              />
            </div>
          ))}
          {dataSet.selected.length > 5 && <span>...</span>}
        </div>
      </div>
      <div className={`${classPrefix}-tree-select`}>
        <span className={`${classPrefix}-selected-container-title`}>{intl.formatMessage({ id: 'iam.company.model.parent', defaultMessage: '父级公司' })}</span>
        <Form>
          <TreeSelect
            dataSet={dataSet}
            name="parentId"
            onChange={handleChange}
          />
        </Form>
      </div>
    </div>
  );
};

export default observer(LevelChange);
