@import '~choerodon-ui/lib/style/themes/default';

.company-level-change {
  &-selected-container {
    width: 100%;

    &-title {
      font-size: 0.14rem;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      display: block;
      margin-bottom: 0.1rem;
    }

    &-company {
      margin-top: 0.1rem;
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      align-items: center;
      justify-content: flex-start;
    }

    &-name {
      display: flex;
      align-items: center;
      justify-content: space-between;
      max-width: 1.6rem;
      height: 0.22rem;
      background: rgba(0, 0, 0, 0.04);
      border-radius: 2px;
      border: 1px solid rgba(0, 0, 0, 0.15);
      padding: 0 0.05rem 0 0.05rem;
      margin-right: 0.1rem;
      margin-top: 0.1rem;

      &-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 1.2rem;
        font-size: 0.12rem;
        font-weight: 400;
      }
    }
  }

  &-tree-select {
    margin-top: 0.1rem;
  }
}
