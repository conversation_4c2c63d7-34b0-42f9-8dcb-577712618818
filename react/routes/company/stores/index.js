import React, { createContext, useMemo, useRef } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import {
  PersonDataSet as PersonSelectDataSet,
  EnabledFlagDataSet,
} from '@/components/common-stores';
import CompanyDataSet from './CompanyDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: 'iam.company' })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
    } = props;
    const pageRef = useRef();
    const intlPrefix = 'company';
    const prefixCls = 'company';

    const personSelectDataSet = useMemo(() => new DataSet(PersonSelectDataSet({ intlPrefix, intl, tenantId })), []);

    const enabledFlagDataSet = useMemo(() => new DataSet(EnabledFlagDataSet({ tenantId, intl })), []);

    const companyDataSet = useMemo(() => new DataSet(CompanyDataSet({ intlPrefix, intl, tenantId, personSelectDataSet, enabledFlagDataSet, autoLocateFirst: false })), []);

    const companyDetailDataSet = useMemo(() => new DataSet(CompanyDataSet({ pageRef, intlPrefix, intl, tenantId, personSelectDataSet, enabledFlagDataSet, autoLocateFirst: true })), [personSelectDataSet]);

    const value = {
      ...props,
      tenantId,
      intlPrefix,
      prefixCls,
      personSelectDataSet,
      companyDataSet,
      companyDetailDataSet,
      pageRef,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
