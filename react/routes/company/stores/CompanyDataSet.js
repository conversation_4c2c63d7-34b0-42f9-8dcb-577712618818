import { Constants, getQueryParams } from '@zknow/utils';

const { CODE_REGEX } = Constants;

const CompanyDataSet = ({ pageRef, tenantId, intlPrefix, intl, enabledFlagDataSet, autoLocateFirst }) => {
  const urlPrefix = '/iam/yqc';

  const companyName = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const code = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const abbr = intl.formatMessage({ id: 'iam.company.model.abbr', defaultMessage: '简称' });
  const parent = intl.formatMessage({ id: 'iam.company.model.parent', defaultMessage: '父级公司' });
  const telephone = intl.formatMessage({ id: 'iam.company.model.telephone', defaultMessage: '电话' });
  const fax = intl.formatMessage({ id: 'iam.company.model.fax', defaultMessage: '传真' });
  const country = intl.formatMessage({ id: 'iam.common.model.company.country', defaultMessage: '国家或地区' });
  const city = intl.formatMessage({ id: 'iam.company.model.city', defaultMessage: '城市' });
  const address = intl.formatMessage({ id: 'zknow.common.model.address', defaultMessage: '地址' });
  const tz = intl.formatMessage({ id: 'zknow.common.model.timeZone', defaultMessage: '时区' });
  const postalCode = intl.formatMessage({ id: 'iam.company.model.postal.code', defaultMessage: '邮编' });
  const email = intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' });
  const website = intl.formatMessage({ id: 'iam.company.model.website', defaultMessage: '公司网站' });
  const status = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const customer = intl.formatMessage({ id: 'iam.company.model.customer', defaultMessage: '客户' });
  const manufacturer = intl.formatMessage({ id: 'iam.company.model.manufacturer', defaultMessage: '厂商' });
  const vendor = intl.formatMessage({ id: 'iam.company.model.vendor', defaultMessage: '供应商' });
  const vendorManager = intl.formatMessage({ id: 'iam.company.model.vendor.manager', defaultMessage: '供应商管理员' });
  const vendorType = intl.formatMessage({ id: 'iam.company.model.vendor.type', defaultMessage: '供应商类型' });
  const contact = intl.formatMessage({ id: 'iam.company.model.contact', defaultMessage: '联系人' });
  const contactTelephone = intl.formatMessage({ id: 'iam.company.model.contact.telephone', defaultMessage: '联系人电话' });
  const contactMobile = intl.formatMessage({ id: 'iam.company.model.contact.mobile', defaultMessage: '联系人手机' });
  const contactFax = intl.formatMessage({ id: 'iam.company.model.contact.fax', defaultMessage: '联系人传真' });
  const domain = intl.formatMessage({ id: 'iam.renderer.model.domain', defaultMessage: '域' });
  const domainManageFlag = intl.formatMessage({ id: 'iam.common.model.domain.manager', defaultMessage: '域管理' });
  const global = intl.formatMessage({ id: 'iam.common.model.global', defaultMessage: '全局' });
  const logo = intl.formatMessage({ id: 'iam.company.model.logo', defaultMessage: '公司LOGO' });
  function codeValidator(value) {
    if (!CODE_REGEX.test(value)) {
      return intl.formatMessage({ id: 'iam.common.model.validate.message.code', defaultMessage: '编码只能由大写字母，数字和下划线构成' });
    }
  }

  return {
    autoQuery: false,
    autoLocateFirst,
    dataKey: 'content',
    totalKey: 'totalElements',
    idField: 'id',
    parentField: 'frontParentId',
    paging: 'server',
    primaryKey: 'id',
    selection: false,
    transport: {
      read: ({ data, params }) => {
        const { id, frontParentId } = data;
        let searchFlag = false;
        Object.keys(data).forEach((v) => {
          if (v.indexOf('search_') !== -1) {
            searchFlag = true;
          }
        });
        return ({
          url: id ? `${urlPrefix}/${tenantId}/companies/query/${id}` : `${urlPrefix}/${tenantId}/companies/page?${searchFlag ? '' : `parentId=${frontParentId || 0}`}`,
          method: 'get',
          params: id ? {} : params,
          data: getQueryParams(data),
          transformResponse(response) {
            try {
              const originRes = JSON.parse(response);
              if (originRes?.parentId === '0') {
                originRes.parentId = null;
                originRes.frontParentId = null;
              }
              if (Array.isArray(originRes.content)) {
                originRes.content.forEach((v) => {
                  if (v.parentId === '0') {
                    v.parentId = null;
                    v.frontParentId = null;
                  } else {
                    v.frontParentId = v.parentId;
                  }
                });
              }
              return originRes;
            } catch (e) {
              return response;
            }
          },
        });
      },
      create: ({ data: [data] }) => ({
        url: `${urlPrefix}/${tenantId}/companies`,
        method: 'post',
        data: {
          ...data,
          ...pageRef?.current?.formDataSet?.toData() || {},
        },
      }),
      update: ({ data: [data] }) => ({
        url: `${urlPrefix}/${tenantId}/companies`,
        method: 'put',
        data: {
          ...data,
          ...pageRef?.current?.formDataSet?.toData()[0] || {},
        },
      }),
      destroy: ({ data }) => ({
        url: `${urlPrefix}/${tenantId}/companies`,
        method: 'put',
        data: data.map(o => o.id),
      }),
    },
    fields: [
      { name: 'id', type: 'string', label: 'ID' },
      { name: 'imageUrl', type: 'string', label: logo },
      { name: 'companyName', type: 'string', label: companyName, required: true, isIntl: true },
      { name: 'companyCode', type: 'string', label: code, required: true, format: 'uppercase', validator: codeValidator },
      { name: 'abbreviation', type: 'string', label: abbr, required: true, isIntl: true },
      { name: 'parentName', type: 'string', label: parent },
      { name: 'telephone', type: 'string', label: telephone, pattern: /^(\+\d{2}-)?(\d{3,4}-)\d{7,8}$/ },
      { name: 'faxNumber', type: 'string', label: fax, pattern: /^(?:\d{3,4}-)?\d{7,8}(?:-\d{1,6})?$/ },
      { name: 'countryId', type: 'string', label: country, textField: 'countryName', valueField: 'countryId' },
      { name: 'city', type: 'string', label: city },
      { name: 'address', type: 'string', label: address },
      { name: 'timeZone', type: 'string', label: tz, lookupCode: 'TIME_ZONE' },
      { name: 'postalCode', type: 'string', label: postalCode, pattern: /^[\s\S]{1,20}/ },
      { name: 'email', type: 'email', label: email },
      { name: 'website', type: 'url', label: website },
      { name: 'enabledFlag', type: 'boolean', label: status, options: enabledFlagDataSet, textField: 'text', valueField: 'value' },
      { name: 'customerFlag', type: 'boolean', label: customer },
      { name: 'manufacturerFlag', type: 'boolean', label: manufacturer },
      { name: 'vendorFlag', type: 'boolean', label: vendor },
      {
        name: 'vendorManager',
        label: vendorManager,
        textField: 'realName',
        valueField: 'id',
        type: 'object',
        lovCode: 'USER',
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            realName: data.vendorManagerName,
          };
        },
      },
      { name: 'vendorManagerName', type: 'string', label: vendorManager },
      { name: 'vendorType', type: 'string', label: vendorType, lookupCode: 'VENDOR_TYPE' },
      { name: 'contact', type: 'string', label: contact },
      { name: 'contactTelephone', type: 'string', label: contactTelephone },
      { name: 'contactMobilePhone', type: 'string', label: contactMobile },
      { name: 'contactFaxNumber', type: 'string', label: contactFax },
      { name: 'domainName', type: 'string', label: domain },
      {
        name: 'domainId',
        type: 'object',
        label: domain,
        textField: 'name',
        valueField: 'id',
        lovCode: 'DOMAIN',
        transformRequest: (value) => {
          return value?.id ? `${value?.id}` : undefined;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            name: (+value) === 0 ? `${global}` : data.domainName,
          };
        },
      },
      {
        name: 'domainManageFlag',
        type: 'boolean',
        label: domainManageFlag,
      },
      {
        name: 'parentId',
        label: parent,
        textField: 'name',
        valueField: 'id',
        type: 'object',
        lovCode: 'COMPANY',
        transformRequest: (value) => {
          return value?.id || 0;
        },
        transformResponse: (value, data) => {
          if (value) {
            return {
              id: value,
              name: data.parentName,
            };
          }
          return undefined;
        },
      },
      {
        name: 'managerId',
        label: contact,
        textField: 'realName',
        valueField: 'id',
        type: 'object',
        lovCode: 'USER',
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            realName: data.contact,
          };
        },
      },
    ],
    events: {
      update: ({ value, record, name }) => {
        if (name === 'managerId') {
          record.set('contactTelephone', value.phone);
        }
      },
    },
    queryFields: [
      { name: 'companyName', type: 'string', label: companyName },
      { name: 'companyCode', type: 'string', label: code },
      { name: 'abbreviation', type: 'string', label: abbr },
      { name: 'domainName', type: 'string', label: domain },
      { name: 'enabledFlag', type: 'boolean', label: status, options: enabledFlagDataSet, textField: 'text', valueField: 'value' },
    ],

  };
};

export default CompanyDataSet;
