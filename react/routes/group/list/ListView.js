import React, { useContext, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { TabPage, axios, Content } from '@yqcloud/apps-master';
import { Modal, Table, message } from 'choerodon-ui/pro';
import { ClickText, TableHoverAction, Button, YqTable, useFilter } from '@zknow/components';
import EnabledFlag from '@/components/enabled-flag';
import Store from './stores';
import FormView from './FormView';

const { Column } = Table;
const modalKey = Modal.key();

function Group() {
  const {
    intl, intlPrefix, prefixCls, tenantId, groupDataSet, history,
    AppState: { currentMenuType: { domainId, domainName } },
    admin, personId,
  } = useContext(Store);

  useFilter(groupDataSet);

  const modalStyle = useMemo(() => ({ width: 800, maxHeight: 578 }), []);

  // 失效/生效组
  async function handleChangeGroupStatus(record, status) {
    try {
      const res = await axios.put(`/iam/yqc/${tenantId}/userGroups/${record.get('id')}/${status ? 'disabled' : 'enabled'}`);
      if (!res?.failed) {
        groupDataSet.query();
        return true;
      } else {
        message.error(res?.message);
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  const openModal = (type) => {
    Modal.open({
      key: modalKey,
      style: modalStyle,
      title: type === 'create' && intl.formatMessage({ id: `${intlPrefix}.${type}` }),
      drawer: type === 'edit',
      className: `${prefixCls}-modal`,
      children: (
        <FormView
          intl={intl}
          intlPrefix={intlPrefix}
          openType={type}
          groupDataSet={groupDataSet}
          record={groupDataSet.current}
          tenantId={tenantId}
        />
      ),
      destroyOnClose: true,
    });
  };

  function handleCreate({ type, parentId, parentName }) {
    if (type === 'create') {
      let newGroup = {
        parentId: {
          id: parentId,
          name: parentName,
        },
        parentName,
      };
      if (domainId) {
        newGroup = {
          ...newGroup,
          domainEnabledFlag: true,
          domainId,
          domainName,
        };
      }
      groupDataSet.create(newGroup);
    }
    openModal('create');
  }

  // 头部按钮组
  const buttons = useMemo(() => (
    [
      <Button funcType="raised" color="primary" onClick={() => handleCreate({ type: 'create' })}>
        {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
      </Button>,
    ]
  ), []);

  // 操作栏
  function renderAction({ record }) {
    const enabledFlag = record?.get('enabledFlag');
    const isOwner = admin || record?.get('ownerId') === personId || record?.get('createdBy') === personId;
    const isVirtual = record?.get('type') === 'VIRTUAL';

    const actionsList = [
      {
        name: !enabledFlag ? intl.formatMessage({ id: 'zknow.common.action.make.valid', defaultMessage: '生效' }) : intl.formatMessage({ id: 'zknow.common.action.make.invalid', defaultMessage: '失效' }),
        icon: !enabledFlag ? 'icon-read' : 'icon-Expires',
        onClick: () => handleChangeGroupStatus(record, enabledFlag),
      }];

    if (isOwner && !isVirtual) {
      actionsList.unshift({
        name: intl.formatMessage({ id: 'iam.group.desc.create.sub', defaultMessage: '新增子组' }),
        icon: 'plus',
        onClick: () => handleCreate({ type: 'create', parentId: record.get('id'), parentName: record.get('name') }),
      },);
    }
    return (
      <TableHoverAction
        intlBtnIndex={2}
        record={record}
        actions={actionsList}
      />
    );
  }

  function renderName({ record }) {
    return (
      <ClickText
        record={record}
        history={history}
        path={`/iam/group/detail/${record.get('id')}`}
        valueField="name"
      />
    );
  }

  function renderStatus({ value }) {
    return <EnabledFlag enabledFlag={value} />;
  }

  return (
    <TabPage>
      <Content className={`${prefixCls}-content`} style={{ padding: 0 }}>
        <Table
          mode="tree"
          pristine
          labelLayout="float"
          placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
          dataSet={groupDataSet}
          condition="params"
          treeAsync
          onRow={({ dataSet, record, index, expandedRow }) => ({
            isLeaf: record.get('isLeaf'),
          })}
          autoHeight
          buttons={buttons}
          queryBarProps={{
            title: intl.formatMessage({ id: 'iam.group.desc.menu.title.group', defaultMessage: '人员组' }),
          }}
        >
          <Column name="name" width={230} tooltip="overflow" renderer={renderName} />
          <Column name="description" />
          <Column name="ownerName" />
          <Column name="email" />
          <Column name="domainName" />
          <Column name="enabledFlag" width={100} renderer={renderStatus} />
          <Column width={10} renderer={renderAction} />
        </Table>
      </Content>
    </TabPage>
  );
}

export default observer(Group);
