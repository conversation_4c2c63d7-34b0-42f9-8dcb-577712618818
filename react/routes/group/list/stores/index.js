import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import GroupDataSet from './GroupDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: 'iam.group' })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId }, userInfo: { admin, personId } },
    } = props;
    const intlPrefix = 'group';
    const prefixCls = 'group';
    const groupDataSet = useMemo(() => new DataSet(GroupDataSet({ intlPrefix, intl, tenantId })), []);
    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      tenantId,
      groupDataSet,
      admin,
      personId,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
