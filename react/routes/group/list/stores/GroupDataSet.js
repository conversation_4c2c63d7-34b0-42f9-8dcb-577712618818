import { DataSet } from 'choerodon-ui/pro';
import { getQueryParams } from '@zknow/utils';

export default ({ intl, tenantId, intlPrefix }) => {
  const urlPrefix = `/iam/yqc/${tenantId}/userGroups`;
  const name = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const description = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const ownerName = intl.formatMessage({ id: 'iam.renderer.model.group.owner.name', defaultMessage: '管理员' });
  const email = intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' });
  const emailError = intl.formatMessage({ id: 'iam.renderer.model.group.email.error', defaultMessage: 'Email format error, please re-enter' });
  const domain = intl.formatMessage({ id: 'iam.renderer.model.domain', defaultMessage: '域' });
  const domainFlag = intl.formatMessage({ id: 'iam.renderer.model.group.domain.flag', defaultMessage: '域管理' });
  const enabledFlag = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const parentGroup = intl.formatMessage({ id: 'iam.renderer.model.group.parent.group', defaultMessage: '所属组' });
  const type = intl.formatMessage({ id: 'iam.renderer.model.group.type', defaultMessage: '组类型' });
  const recursiveLabel = intl.formatMessage({ id: 'iam.renderer.model.group.is.recursive', defaultMessage: '是否递归' });

  const enabledFlagDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' }), value: 'true' },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' }), value: 'false' },
    ],
  });
  const recursiveFlag = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' }), value: 'NORMAL' },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' }), value: 'VIRTUAL' },
    ],
  });
  return {
    autoQuery: false,
    primaryKey: 'id',
    selection: false,
    paging: 'server',
    idField: 'id',
    parentField: 'parentId',
    expandField: 'expand',
    dataKey: 'content',
    totalKey: 'totalElements',
    autoLocateFirst: false,
    transport: {
      read: ({ data }) => {
        const { parentId } = data;
        let searchFlag = false;
        Object.keys(data).forEach((v) => {
          if (v.indexOf('search_') !== -1) {
            searchFlag = true;
          }
        });
        return ({
          url: `${urlPrefix}${parentId ? '?page=-1&size=-1' : `?${searchFlag ? '' : 'parentId=0'}`}`,
          method: 'get',
          data: getQueryParams(data),
          transformResponse(resp) {
            const jsonData = JSON.parse(resp);
            if (searchFlag) {
              return {
                ...jsonData,
                content: jsonData.content?.map(row => ({ ...row, parentId: null, isLeaf: true })),
              };
            } else {
              return {
                ...jsonData,
                content: jsonData.content?.map(row => ({ ...row, parentId: row.parentId === '0' ? null : row.parentId })),
              };
            }
          },
        });
      },
      create: ({ data: [data] }) => ({
        url: urlPrefix,
        method: 'post',
        data: {
          ...data,
          parentId: data.parentId || '0',
        },
      }),
      update: ({ data: [data] }) => ({
        url: urlPrefix,
        method: 'put',
        data,
      }),
      destroy: ({ data: [data] }) => ({
        url: `${urlPrefix}/${data.id}`,
        method: 'delete',
      }),
    },
    fields: [
      {
        name: 'expand',
        type: 'boolean',
        ignore: 'always',
      },
      {
        name: 'name',
        type: 'string',
        label: name,
        required: true,
        isIntl: true,
      },
      { name: 'description', label: description, type: 'string', isIntl: true },
      {
        name: 'ownerId',
        type: 'object',
        required: true,
        label: ownerName,
        lovCode: 'USER',
        textField: 'realName',
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          if (value) {
            return {
              id: value,
              name: data?.ownerName,
            };
          }
          return undefined;
        },
      },
      { name: 'ownerName', label: ownerName, type: 'string' },
      {
        name: 'email',
        type: 'string',
        label: email,
        validator: (value) => {
          const reg = /^([a-zA-Z0-9_.-])+@([a-zA-Z0-9-])+((\.[a-zA-Z0-9-]{2,}){1,4})$/;

          if (value && !reg.test(value)) {
            return emailError;
          }
          return true;
        },
      },
      { name: 'domainEnabledFlag', label: domainFlag, type: 'boolean' },
      {
        name: 'domainId',
        type: 'object',
        label: domain,
        lovCode: 'DOMAIN',
        textField: 'name',
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          if (value) {
            return {
              id: value,
              name: data?.domainName,
            };
          }
          return undefined;
        },
      },
      { name: 'domainName', label: domain, type: 'string' },
      { name: 'enabledFlag', type: 'boolean', label: enabledFlag },
      {
        name: 'parentId',
        type: 'object',
        label: parentGroup,
        lovCode: 'USER_GROUP',
        textField: 'name',
        transformRequest: (value) => {
          return value?.id || 0;
        },
        dynamicProps: {
          lovPara: ({ record }) => ({ self: record.get('id') }),
        },
      },
      { name: 'parentName', label: parentGroup },
      { name: 'type', type: 'string', label: type, defaultValue: 'NORMAL' },
      { name: 'recursiveFlag', type: 'boolean', label: recursiveLabel },
    ],
    queryFields: [
      { name: 'name', type: 'string', label: name },
      { name: 'description', type: 'string', label: description },
      { name: 'ownerName', type: 'string', label: ownerName },
      { name: 'email', type: 'string', label: email },
      { name: 'domainName', type: 'string', label: domain },
      { name: 'enabledFlag', type: 'string', label: enabledFlag, options: enabledFlagDs },
    ],
  };
};
