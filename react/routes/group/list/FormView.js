import React from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, EmailField, Lov, TextArea, CheckBox, Radio } from 'choerodon-ui/pro';

export default observer((props) => {
  const { modal, groupDataSet, record, intl } = props;
  async function handleOk() {
    try {
      const res = await groupDataSet.validate();
      if (res && await groupDataSet.submit()) {
        groupDataSet.query();
        return true;
      } else {
        return false;
      }
    } catch (err) {
      return false;
    }
  }

  modal.handleOk(() => handleOk());
  modal.handleCancel(() => {
    groupDataSet.reset();
  });

  return (
    <div>
      <Form
        record={record}
        labelLayout="horizontal"
        labelWidth="auto"
        columns={2}
      >
        <TextField name="name" autoFocus />
        <Lov name="ownerId" />
        <Lov name="parentId" renderer={({ value }) => (value?.id ? value : '')} />
    
        <TextField name="email" />
        <CheckBox name="domainEnabledFlag" />
        <Lov name="domainId" disabled={!record.get('domainEnabledFlag')} />
        <TextArea name="description" newLine colSpan={2} rows={1} autoSize={{ minRows: 1, maxRows: 4 }} resize="height" />
        {record?.get('parentId').id && <div name="type">
          <Radio name="type" value="NORMAL">{intl.formatMessage({ id: 'iam.group.desc.normal.group', defaultMessage: '普通组' })}</Radio>
          <Radio name="type" value="VIRTUAL">{intl.formatMessage({ id: 'iam.group.desc.virtual.group', defaultMessage: '虚拟组' })}</Radio>
        </div>}
        {record?.get('parentId').id && <CheckBox name="recursiveFlag" />}
      </Form>
    </div>
  );
});
