import React, { useContext, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { axios } from '@yqcloud/apps-master';
import { Modal, Table } from 'choerodon-ui/pro';
import { TableHoverAction, Button } from '@zknow/components';
import Store from './stores';

const { Column } = Table;
const modalKey = Modal.key();

function RoleList(props) {
  const { intl, intlPrefix, prefixCls, roleDataSet, roleListDataSet, tenantId, groupId, mainStore } = useContext(Store);
  const modalStyle = useMemo(() => ({ width: 800, maxHeight: 578 }), []);
  const { isOwner } = props;

  const openModal = () => {
    roleListDataSet.unSelectAll();
    mainStore.setSelectValue([]);
    roleListDataSet.query();
    Modal.open({
      key: modalKey,
      style: modalStyle,
      title: intl.formatMessage({ id: 'iam.group.desc.add.role', defaultMessage: '添加角色' }),
      drawer: false,
      className: `${prefixCls}-modal`,
      children: (
        <Table
          pristine
          dataSet={roleListDataSet}
          queryBarProps={{
            title: intl.formatMessage({ id: 'iam.group.desc.add.role', defaultMessage: '添加角色' }),
            queryFieldsStyle: {
              name: { width: 100 },
              description: { width: 200 },
            },
          }}
        >
          <Column name="name" />
          <Column name="description" />
        </Table>
      ),
      destroyOnClose: true,
      onOk: async () => {
        const roleIds = mainStore.getSelectValue;
        if (roleIds?.length) {
          try {
            const res = await axios.post(`/iam/yqc/v1/${tenantId}/memberRoles/assignRolesForGroup?groupId=${groupId}`, roleIds);
            if (!res?.failed) {
              roleDataSet.query();
              return true;
            }
            return false;
          } catch (e) {
            return false;
          }
        }
        return true;
      },
    });
  };

  async function handleDelete(record) {
    await roleDataSet.delete(record);
  }

  // 头部按钮组
  const buttons = useMemo(() => {
    if (isOwner) {
      return (
        [
          <Button funcType="raised" color="primary" onClick={openModal}>
            {intl.formatMessage({ id: 'zknow.common.button.append', defaultMessage: '添加' })}
          </Button>,
        ]
      );
    } else {
      return [];
    }
  }, [isOwner]);

  // 操作栏
  function renderAction({ record }) {
    if (isOwner) {
      return (
        <TableHoverAction
          record={record}
          actions={[
            {
              name: intl.formatMessage({ id: 'zknow.common.button.remove', defaultMessage: '移除' }),
              icon: 'delete',
              onClick: () => handleDelete(record),
            },
          ]}
        />
      );
    } else {
      return null;
    }
  }

  return (
    <Table
      pristine
      dataSet={roleDataSet}
      canFold
      buttons={buttons}
      queryBarProps={{
        title: intl.formatMessage({ id: 'zknow.common.model.role', defaultMessage: '角色' }),
      }}
    >
      <Column name="name" />
      <Column name="description" />
      <Column width={10} renderer={renderAction} />
    </Table>
  );
}

export default observer(RoleList);
