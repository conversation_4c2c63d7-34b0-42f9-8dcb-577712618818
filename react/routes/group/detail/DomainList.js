import React, { useContext, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { axios } from '@yqcloud/apps-master';
import { Modal, Table } from 'choerodon-ui/pro';
import { TableHoverAction, Button } from '@zknow/components';
import Store from './stores';

const { Column } = Table;
const modalKey = Modal.key();

function DomainList(props) {
  const { intl, intlPrefix, prefixCls, domainDataSet, domainListDataSet, tenantId, groupId, mainStore } = useContext(Store);
  const modalStyle = useMemo(() => ({ width: 800, maxHeight: 578 }), []);
  const { isOwner } = props;
  const openModal = () => {
    domainListDataSet.unSelectAll();
    mainStore.setSelectValue([]);
    domainListDataSet.query();
    Modal.open({
      key: modalKey,
      style: modalStyle,
      title: intl.formatMessage({ id: 'iam.group.desc.add.domain', defaultMessage: '添加域' }),
      drawer: false,
      className: `${prefixCls}-modal`,
      children: (
        <Table
          pristine
          dataSet={domainListDataSet}
          queryBarProps={{
            title: intl.formatMessage({ id: 'iam.group.desc.add.domain', defaultMessage: '添加域' }),
            queryFieldsStyle: {
              name: { width: 100 },
              code: { width: 140 },
            },
          }}
        >
          <Column name="name" />
          <Column name="code" />
        </Table>
      ),
      destroyOnClose: true,
      onOk: async () => {
        const domainIds = mainStore.getSelectValue;
        if (domainIds?.length) {
          try {
            const res = await axios.post(`/iam/yqc/${tenantId}/domains/assignDomainsForUserGroup?userGroupId=${groupId}`, domainIds);
            if (!res?.failed) {
              domainDataSet.query();
              return true;
            }
            return false;
          } catch (e) {
            return false;
          }
        }
        return true;
      },
    });
  };

  async function handleDelete(record) {
    await domainDataSet.delete(record);
  }

  // 头部按钮组
  const buttons = useMemo(() => {
    if (isOwner) {
      return (
        [
          <Button funcType="raised" color="primary" onClick={openModal}>
            {intl.formatMessage({ id: 'zknow.common.button.append', defaultMessage: '添加' })}
          </Button>,
        ]
      );
    } else {
      return [];
    }
  }, [isOwner]);

  // 操作栏
  function renderAction({ record }) {
    if (isOwner) {
      return (
        <TableHoverAction
          record={record}
          actions={[
            {
              name: intl.formatMessage({ id: 'zknow.common.button.remove', defaultMessage: '移除' }),
              icon: 'delete',
              onClick: () => handleDelete(record),
            },
          ]}
        />
      );
    } else {
      return null;
    }
  }

  return (
    <Table
      pristine
      dataSet={domainDataSet}
      canFold
      buttons={buttons}
      queryBarProps={{
        title: intl.formatMessage({ id: 'iam.group.desc.domain', defaultMessage: '可见域' }),
      }}
    >
      <Column name="name" />
      <Column name="code" />
      <Column width={10} renderer={renderAction} />
    </Table>
  );
}

export default observer(DomainList);
