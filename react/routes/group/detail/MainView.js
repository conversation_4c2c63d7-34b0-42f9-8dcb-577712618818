import React, { useState, useCallback, useContext, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Tabs } from 'choerodon-ui';
import { Button } from '@zknow/components';
import { Content, TabPage, Header } from '@yqcloud/apps-master';
import { CheckBox, EmailField, Form, Lov, Output, TextArea, TextField, Radio } from 'choerodon-ui/pro';
import DomainList from './DomainList';
import RoleList from './RoleList';
import UserList from './UserList';
import Store from './stores';

import './index.less';

const { TabPane } = Tabs;

function MainView() {
  const {
    intl,
    intlPrefix,
    prefixCls,
    history,
    groupDataSet,
    tenantAdmin,
    personId,
  } = useContext(Store);
  const [isEdit, setEdit] = useState(false);
  const [isOwner, setOwner] = useState(false);
  const [isVirtual, setVirtual] = useState(false);

  useEffect(() => {
    const ownerFlag = tenantAdmin || groupDataSet?.current?.get('ownerId')?.id === personId || groupDataSet?.current?.get('createdBy') === personId;
    groupDataSet?.current?.get('type') === 'VIRTUAL' ? setVirtual(true) : setVirtual(false);
    setOwner(ownerFlag);
  }, [tenantAdmin, personId, groupDataSet.status]);

  function renderTabs() {
    return (
      <Tabs type="card" className={`${prefixCls}-tabs`}>
        <TabPane tab={intl.formatMessage({ id: 'zknow.common.model.person', defaultMessage: '人员' })} key="user">
          <UserList isOwner={isOwner} />
        </TabPane>
        <TabPane tab={intl.formatMessage({ id: 'zknow.common.model.role', defaultMessage: '角色' })} key="role">
          <RoleList isOwner={isOwner} />
        </TabPane>
        <TabPane tab={intl.formatMessage({ id: 'iam.group.desc.domain', defaultMessage: '可见域' })} key="domain">
          <DomainList isOwner={isOwner} />
        </TabPane>
      </Tabs>
    );
  }

  async function handleSave() {
    if (await groupDataSet.validate()) {
      await groupDataSet.submit();
      await groupDataSet.query();
      setEdit(false);
    }
  }

  const renderButtons = useCallback(() => {
    if (isOwner) {
      return !isEdit ? (
        <Button
          disabled={groupDataSet.status !== 'ready' || !groupDataSet?.current?.get('enabledFlag')}
          funcType="raised"
          color="default"
          icon="Write"
          onClick={() => setEdit(true)}
        >
          {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
        </Button>
      ) : [
        <Button
          funcType="raised"
          color="primary"
          key="save"
          onClick={handleSave}
        >
          {intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}
        </Button>,
        <Button
          funcType="raised"
          color="default"
          key="cancel"
          onClick={() => {
            setEdit(false);
            groupDataSet.reset();
          }}
        >
          {intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}
        </Button>,
      ];
    } else {
      return [];
    }
  }, [isEdit, groupDataSet, isOwner]);

  return (
    <TabPage>
      <Header backPath={`/iam/group${history.location?.search}`} dataSet={groupDataSet}>
        <h1>{intl.formatMessage({ id: 'iam.group.desc.title', defaultMessage: '人员组详情' })}</h1>
        <div>
          {renderButtons()}
        </div>
      </Header>
      <Content className={prefixCls}>
        <div className={`${prefixCls}-wrapper`}>
          <Form disabled={!isEdit} columns={2} labelLayout="horizontal" dataSet={groupDataSet}>
            <TextField autoFocus name="name" />
            <Lov name="ownerId" />
            <Lov name="parentId" />
            <EmailField name="email" />
            <CheckBox name="domainEnabledFlag" />
            <Lov name="domainId" disabled={!groupDataSet.current?.get('domainEnabledFlag')} />
            <TextArea name="description" newLine colSpan={2} rows={1} autoSize={{ minRows: 1, maxRows: 4 }} resize="height" />
            <div name="type">
              <Radio name="type" value="NORMAL">{intl.formatMessage({ id: 'iam.group.desc.normal.group', defaultMessage: '普通组' })}</Radio>
              <Radio name="type" value="VIRTUAL">{intl.formatMessage({ id: 'iam.group.desc.virtual.group', defaultMessage: '虚拟组' })}</Radio>
            </div>
            <CheckBox name="recursiveFlag" />
          </Form>
          {!isVirtual && renderTabs()}
        </div>
      </Content>
    </TabPage>
  );
}

export default observer(MainView);
