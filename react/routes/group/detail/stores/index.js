import React, { createContext, useMemo, useState } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import { formatterCollections } from '@zknow/utils';
import GroupDataSet from './GroupDataSet';
import DomainDataSet from './DomainDataSet';
import RoleDataSet from './RoleDataSet';
import UserDataSet from './UserDataSet';
import useStore from './useStore';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: 'iam.group' })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId }, userInfo: { tenantAdmin, personId } },
      match,
    } = props;
    const groupId = match?.params?.id;
    const mainStore = useStore();
    const intlPrefix = 'group';
    const prefixCls = 'iam-group-detail';
    const groupDataSet = useMemo(() => new DataSet(GroupDataSet({ intl, intlPrefix, tenantId, groupId })), [groupId]);
    const domainDataSet = useMemo(() => new DataSet(DomainDataSet({ intl, intlPrefix, tenantId, groupId })), [groupId]);
    const domainListDataSet = useMemo(() => new DataSet(DomainDataSet({ intl, intlPrefix, tenantId, groupId, unAssign: true, mainStore })), []);
    const roleDataSet = useMemo(() => new DataSet(RoleDataSet({ intl, intlPrefix, tenantId, groupId })), [groupId]);
    const roleListDataSet = useMemo(() => new DataSet(RoleDataSet({ intl, intlPrefix, tenantId, groupId, unAssign: true, mainStore })), []);
    const userDataSet = useMemo(() => new DataSet(UserDataSet({ intl, intlPrefix, tenantId, groupId })), [groupId]);
    const userInviteDataSet = useMemo(() => new DataSet(UserDataSet({ intl, intlPrefix, tenantId, groupId, isInvite: true })), [groupId]);
    const userListDataSet = useMemo(() => new DataSet(UserDataSet({ intl, intlPrefix, tenantId, groupId, unAssign: true, mainStore })), []);

    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      intl,
      groupDataSet,
      domainDataSet,
      domainListDataSet,
      roleDataSet,
      roleListDataSet,
      userDataSet,
      userListDataSet,
      groupId,
      tenantId,
      mainStore,
      tenantAdmin,
      personId,
      userInviteDataSet,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }
)));
