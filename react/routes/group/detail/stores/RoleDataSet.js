import { getQueryParams } from '@zknow/utils';

export default ({ intl, tenantId, groupId, unAssign = false, mainStore }) => {
  const urlPrefix = `/iam/yqc/v1/${tenantId}/memberRoles`;
  const name = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const description = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  return {
    autoQuery: !unAssign,
    primaryKey: 'id',
    selection: unAssign ? 'multiple' : false,
    paging: true,
    pageSize: unAssign ? 5 : 10,
    autoLocateFirst: false,
    transport: {
      read: ({ data }) => ({
        url: `${urlPrefix}/selectAssignRolesForGroup?groupId=${groupId}&unAssign=${unAssign}`,
        method: 'get',
        data: getQueryParams(data),
      }),
      destroy: ({ data: [data] }) => ({
        url: `${urlPrefix}/removeRolesForGroup?groupId=${groupId}&roleId=${data.id}`,
        method: 'delete',
      }),
    },
    fields: [
      { name: 'name', label: name, type: 'string' },
      { name: 'description', label: description, type: 'string' },
    ],
    queryFields: [
      { name: 'name', label: name, type: 'string' },
      { name: 'description', label: description, type: 'string' },
    ],
    events: {
      load: ({ dataSet }) => {
        if (unAssign) {
          dataSet.map(record => {
            const id = record.get('id');
            if (mainStore.getSelectValue?.includes(id)) {
              record.isSelected = true;
            }
            return record;
          });
        }
      },
      select: ({ dataSet, record, previous }) => {
        if (unAssign) {
          const id = record.get('id');
          mainStore.setSelectValue([...(mainStore.getSelectValue?.filter(itemId => itemId !== id) || []), id]);
          return true;
        }
      },
      unSelect: ({ dataSet, record }) => {
        if (unAssign) {
          const id = record.get('id');
          mainStore.setSelectValue([...(mainStore.getSelectValue?.filter(itemId => itemId !== id) || [])]);
          return true;
        }
      },
      selectAll: ({ dataSet }) => {
        if (unAssign) {
          const ids = dataSet.map(record => record.get('id'));
          mainStore.setSelectValue([...(mainStore.getSelectValue?.filter(itemId => !ids.includes(itemId)) || []), ...ids]);
          return true;
        }
      },
      unSelectAll: ({ dataSet }) => {
        if (unAssign) {
          const ids = dataSet.map(record => record.get('id'));
          mainStore.setSelectValue([...(mainStore.getSelectValue?.filter(itemId => !ids.includes(itemId)) || [])]);
          return true;
        }
      },
    },
  };
};
