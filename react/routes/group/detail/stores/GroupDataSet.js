export default ({ intl, tenantId, intlPrefix, groupId }) => {
  const urlPrefix = `/iam/yqc/${tenantId}/userGroups`;
  const name = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const description = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const ownerName = intl.formatMessage({ id: 'iam.renderer.model.group.owner.name', defaultMessage: '管理员' });
  const email = intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' });
  const domain = intl.formatMessage({ id: 'iam.renderer.model.domain', defaultMessage: '域' });
  const domainFlag = intl.formatMessage({ id: 'iam.renderer.model.group.domain.flag', defaultMessage: '域管理' });
  const enabledFlag = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const parentGroup = intl.formatMessage({ id: 'iam.renderer.model.group.parent.group', defaultMessage: '所属组' });
  const emailError = intl.formatMessage({ id: 'iam.renderer.model.group.email.error', defaultMessage: 'Email format error, please re-enter' });
  const type = intl.formatMessage({ id: 'iam.renderer.model.group.type', defaultMessage: '组类型' });
  const recursiveLabel = intl.formatMessage({ id: 'iam.renderer.model.group.is.recursive', defaultMessage: '是否递归' });
  return {
    autoQuery: true,
    primaryKey: 'id',
    selection: false,
    paging: false,
    transport: {
      read: {
        url: `${urlPrefix}/${groupId}`,
        method: 'get',
        transformResponse(data) {
          const jsonData = JSON.parse(data);
          return {
            ...jsonData,
            parentId: jsonData.parentId === '0' ? undefined : jsonData.parentId,
          };
        },
      },
      update: ({ data: [data] }) => ({
        url: urlPrefix,
        method: 'put',
        data: {
          ...data,
          parentId: data.parentId || '0',
        },
      }),
    },

    fields: [
      {
        name: 'name',
        type: 'string',
        label: name,
        required: true,
      },
      { name: 'description', label: description, type: 'string' },
      {
        name: 'ownerId',
        type: 'object',
        required: true,
        label: ownerName,
        lovCode: 'USER',
        textField: 'realName',
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          if (value) {
            return {
              id: value,
              realName: data?.ownerName,
            };
          }
          return undefined;
        },
      },
      { name: 'ownerName', label: ownerName, type: 'string' },
      {
        name: 'email',
        type: 'string',
        label: email,
        validator: (value) => {
          const reg = /^([a-zA-Z0-9_.-])+@([a-zA-Z0-9-])+((\.[a-zA-Z0-9-]{2,}){1,4})$/;

          if (value && !reg.test(value)) {
            return emailError;
          }
          return true;
        },
      },
      { name: 'domainEnabledFlag', label: domainFlag, type: 'boolean' },
      {
        name: 'domainId',
        type: 'object',
        label: domain,
        lovCode: 'DOMAIN',
        textField: 'name',
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          if (value) {
            return {
              id: value,
              name: data?.domainName,
            };
          }
          return undefined;
        },
      },
      { name: 'domainName', label: domain, type: 'string' },
      { name: 'enabledFlag', type: 'boolean', label: enabledFlag },
      {
        name: 'parentId',
        type: 'object',
        label: parentGroup,
        lovCode: 'USER_GROUP',
        textField: 'name',
        dynamicProps: {
          lovPara: ({ record }) => ({ self: record.get('id') }),
        },
        transformRequest: (value) => {
          return value?.id || 0;
        },
        transformResponse: (value, data) => {
          if (value) {
            return {
              id: value,
              name: data?.parentName,
            };
          }
          return undefined;
        },
      },
      { name: 'parentName', label: parentGroup },
      { name: 'type', type: 'string', label: type, defaultValue: 'NORMAL', disabled: true },
      { name: 'recursiveFlag', type: 'boolean', label: recursiveLabel },
    ],
  };
};
