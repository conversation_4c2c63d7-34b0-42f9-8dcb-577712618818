import React, { useContext, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { axios } from '@yqcloud/apps-master';
import { Modal, Table, Form, TextField, message, Icon } from 'choerodon-ui/pro';
import { TableHoverAction, Button } from '@zknow/components';
import Store from './stores';
import './index.less';

const { Column } = Table;
const modalKey = Modal.key();

function UserList(props) {
  const { intl, intlPrefix, prefixCls, userDataSet, userListDataSet, tenantId, groupId, mainStore, userInviteDataSet } = useContext(Store);
  const modalStyle = useMemo(() => ({ width: 800, maxHeight: 578 }), []);
  const smallStyle = useMemo(() => ({ width: 520 }), []);
  const { isOwner } = props;

  const sourceTag = (userSourceText) => {
    if (userSourceText) {
      return (
        <span className="account-source">
          {userSourceText}
        </span>
      );
    }
    return null;
  };

  const openModal = (type) => {
    userListDataSet.unSelectAll();
    mainStore.setSelectValue([]);
    userListDataSet.query();
    Modal.open({
      key: modalKey,
      style: modalStyle,
      title: intl.formatMessage({ id: 'iam.renderer.desc.group.add.user', defaultMessage: '添加人员' }),
      drawer: false,
      className: `${prefixCls}-modal`,
      children: (
        <Table
          pristine
          placeholder={intl.formatMessage({ id: 'iam.renderer.desc.group.add.user', defaultMessage: '添加人员' })}
          dataSet={userListDataSet}
          queryBarProps={{
            queryFieldsStyle: {
              realName: { width: 100 },
              email: { width: 200 },
              phone: { width: 140 },
            },
          }}
        >
          <Column name="realName" />
          <Column name="email" />
          <Column name="phone" />
        </Table>
      ),
      destroyOnClose: true,
      onOk: async () => {
        const userIds = mainStore.getSelectValue;
        if (userIds?.length) {
          try {
            const res = await axios.post(`/iam/yqc/${tenantId}/userGroups/assignUserForGroup?groupId=${groupId}`, userIds);
            if (!res?.failed) {
              message.success(intl.formatMessage({ id: 'iam.renderer.model.group.invite.success', defaultMessage: '邀请成功' }));
              userDataSet.query();
              return true;
            }
            message.success(res.message);
            return false;
          } catch (e) {
            return false;
          }
        }
        return true;
      },
    });
  };

  const openInviteModal = (type) => {
    userInviteDataSet.create({});
    Modal.open({
      key: modalKey,
      style: smallStyle,
      title: intl.formatMessage({ id: 'iam.renderer.desc.group.invite.user', defaultMessage: '邀请人员' }),
      drawer: false,
      children: (
        <div>
          <div>
            <Icon type="help_outline" style={{ color: '#2979ff', fontSize: '0.18rem', marginTop: '-0.05rem', marginRight: '0.1rem' }} />
            <span style={{ color: '#595959' }}>{intl.formatMessage({ id: 'iam.renderer.desc.group.invite.warning', defaultMessage: '保存成功后将通过邮箱发送邀请' })}</span></div>
          <Form
            // header="保存成功后将通过邮箱发送邀请"
            labelWidth="auto"
            dataSet={userInviteDataSet}
          >
            <TextField name="realName" />
            <TextField name="email" />
            <TextField name="phone" />
          </Form>
        </div>
      ),
      destroyOnClose: true,
      onOk: async () => {
        const userData = userInviteDataSet.current.toData();
        if (userData && Object.keys(userData).length > 1) {
          userData.inviteGroupId = groupId;
          try {
            const res = await axios.post(`iam/yqc/v1/${tenantId}/users?`, userData);
            if (!res?.failed) {
              userDataSet.query();
              return true;
            }
            return false;
          } catch (e) {
            return false;
          }
        }
        return true;
      },
    });
  };

  async function handleDelete(record) {
    await userDataSet.delete(record);
  }

  // 头部按钮组
  const buttons = useMemo(() => {
    if (isOwner) {
      return (
        [
          <Button funcType="raised" color="primary" onClick={openModal}>
            {intl.formatMessage({ id: 'zknow.common.button.append', defaultMessage: '添加' })}
          </Button>,
          <Button funcType="raised" color="primary" onClick={openInviteModal}>
            {intl.formatMessage({ id: 'iam.group.desc.invite', defaultMessage: '邀请' })}
          </Button>,
        ]
      );
    } else {
      return [];
    }
  }, [isOwner]);
  async function handleInvite(record) {
    try {
      const personId = record.get('id');
      const userEmail = record.get('email');

      const data = { personId, userEmail };
      const res = await axios.post(`iam/yqc/v1/${tenantId}/users/invite`, data);

      if (res.failed) {
        throw new Error(res.message);
      } else {
        message.success(intl.formatMessage({ id: 'iam.renderer.model.group.invite.success', defaultMessage: '邀请成功' }));
      }
    } catch (err) {
      message.error(err?.message);
    }
    await userDataSet.query();
  }

  // 操作栏
  function renderAction({ record }) {
    const invitationStatus = record.get('invitationStatus');
    if (isOwner) {
      const actionsList = [{
        name: intl.formatMessage({ id: 'zknow.common.button.remove', defaultMessage: '移除' }),
        icon: 'delete',
        onClick: () => handleDelete(record),
      }];
      if (invitationStatus && invitationStatus !== 'joined') {
        actionsList.unshift({
          name: intl.formatMessage({ id: 'iam.group.desc.invite', defaultMessage: '邀请' }),
          icon: 'share-one',
          onClick: () => handleInvite(record),
        });
      }
      return (
        <TableHoverAction
          record={record}
          actions={actionsList}
        />
      );
    } else {
      return null;
    }
  }

  const renderName = ({ record, value, text }) => {
    const flag = record.get('invitationStatus');
    const userSourceText = intl.formatMessage({ id: 'iam.group.desc.waiting.add', defaultMessage: '待加入' });
    return (
      <div className={`${prefixCls}-table-name-content`}>
        <span className="br-link-text ">{text}</span>
        {flag !== 'inviting' ? <></> : sourceTag(userSourceText)}
      </div>
    );
  };

  return (
    <Table
      pristine
      dataSet={userDataSet}
      canFold
      buttons={buttons}
      queryBarProps={{
        title: intl.formatMessage({ id: 'zknow.common.model.person', defaultMessage: '人员' }),
      }}
    >
      <Column name="realName" renderer={renderName} />
      <Column name="email" />
      <Column name="phone" />
      <Column width={10} renderer={renderAction} />
    </Table>
  );
}

export default observer(UserList);
