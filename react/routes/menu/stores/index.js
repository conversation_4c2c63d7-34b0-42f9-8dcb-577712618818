import React, { createContext, useMemo, useState } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import ListDataSet from './ListDataSet';
import CommonDataSet from './CommonDataSet';
import ChildDataSet from './ChildDataSet';
import DetailDataSet from './DetailDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: ['iam.common', 'iam.menu'] })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId, type } },
    } = props;
    const prefixCls = 'menu';

    const detailDataSet = useMemo(() => new DataSet(DetailDataSet({ tenantId, type })));
    const childDataSet = useMemo(() => new DataSet(ChildDataSet({ intl, tenantId, type, detailDataSet })), []);
    const listDataSet = useMemo(
      () => new DataSet(ListDataSet({ intl, tenantId, type, detailDataSet })),
      [tenantId],
    );
    const commonDataSet = useMemo(() => new DataSet(CommonDataSet({ intl, tenantId, type, detailDataSet, childDataSet })), [childDataSet]);

    const menuTypeMeaning = useMemo(() => ({
      top: intl.formatMessage({ id: 'iam.common.model.top.menu', defaultMessage: '顶层菜单' }),
      filter: intl.formatMessage({ id: 'iam.common.model.filter', defaultMessage: '筛选器' }),
      filter_item: intl.formatMessage({ id: 'iam.common.model.filter.item', defaultMessage: '筛选项' }),
      menu: intl.formatMessage({ id: 'zknow.common.model.menu', defaultMessage: '菜单' }),
      link: intl.formatMessage({ id: 'iam.common.model.link', defaultMessage: '链接' }),
      dir: intl.formatMessage({ id: 'iam.common.menu.dir', defaultMessage: '目录' }),
    }), []);

    const value = {
      ...props,
      prefixCls,
      listDataSet,
      tenantId,
      type,
      detailDataSet,
      commonDataSet,
      childDataSet,
      menuTypeMeaning,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
