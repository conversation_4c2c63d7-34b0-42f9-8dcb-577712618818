import { runInAction } from 'mobx';
import { DataSet } from 'choerodon-ui/pro';
import { getQueryParams } from '@zknow/utils';

export default ({ intlPrefix, intl, tenantId, type, menuDataSet }) => {
  const url = type === 'site' ? 'iam/yqc/v1/menus/common/menus' : `iam/yqc/v1/${tenantId}/common/menus`;
  const nameLabel = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const codeLabel = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const descriptionLabel = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const statusLabel = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const iconLabel = intl.formatMessage({ id: 'iam.common.model.icon', defaultMessage: '图标' });
  const typeLabel = intl.formatMessage({ id: 'zknow.common.model.type', defaultMessage: '类型' });
  const parentLabel = intl.formatMessage({ id: 'iam.menu.parentLevel', defaultMessage: '父级菜单' });
  const pathLabel = intl.formatMessage({ id: 'iam.common.model.path', defaultMessage: '路径' });
  const pageLabel = intl.formatMessage({ id: 'iam.common.model.page', defaultMessage: '页面' });
  const isPreSetLabel = intl.formatMessage({ id: 'iam.menu.isPreSet', defaultMessage: '是否预置' });
  const sortLabel = intl.formatMessage({ id: 'iam.common.model.sort', defaultMessage: '排序' });
  const levelLabel = intl.formatMessage({ id: 'iam.common.model.level', defaultMessage: '层级' });

  const enabledFlagDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.common.model.valid', defaultMessage: '有效' }), value: 'true' },
      { meaning: intl.formatMessage({ id: 'iam.common.model.invalid', defaultMessage: '无效' }), value: 'false' },
    ],
  });

  const levelDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.common.model.user.center', defaultMessage: '个人中心' }), value: 'user' },
      { meaning: intl.formatMessage({ id: 'zknow.common.model.platform', defaultMessage: '平台' }), value: 'site' },
      { meaning: intl.formatMessage({ id: 'zknow.common.model.tenant', defaultMessage: '租户' }), value: 'organization' },
    ],
  });

  const typeOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.common.model.top.menu', defaultMessage: '顶层菜单' }), value: 'top' },
      { meaning: intl.formatMessage({ id: 'iam.common.model.filter', defaultMessage: '筛选器' }), value: 'filter' },
      { meaning: intl.formatMessage({ id: 'iam.common.model.filter.item', defaultMessage: '筛选项' }), value: 'filter_item' },
      { meaning: intl.formatMessage({ id: 'zknow.common.model.menu', defaultMessage: '菜单' }), value: 'menu' },
      { meaning: intl.formatMessage({ id: 'iam.common.model.link', defaultMessage: '链接' }), value: 'link' },
      // { meaning: intl.formatMessage({ id: 'iam.common.model.menu.dir', defaultMessage: '目录' }), value: 'dir' },
    ],
  });

  const checkIsPreset = (record) => {
    return !!record.get('customFlag');
  };

  function getUpdateData(data) {
    return {
      ...data,
      customFlag: data.customFlag ? 0 : 1,
    };
  }

  const commonQuery = [
    { name: 'name', type: 'string', label: nameLabel },
    { name: 'code', type: 'string', label: codeLabel, format: 'uppercase' },
    { name: 'type', type: 'string', label: typeLabel, options: typeOptions },
    { name: 'path', type: 'string', label: pathLabel },
  ];

  const queryFieldList = type !== 'site'
    ? { name: 'isPreset', label: isPreSetLabel, type: 'boolean' }
    : { name: 'level', type: 'string', label: levelLabel, options: levelDs };
  return {
    autoQuery: true,
    paging: true,
    pageSize: 20,
    selection: false,
    idField: 'id',
    primaryKey: 'id',
    autoQueryAfterSubmit: false,
    expandField: 'expand',
    autoLocateFirst: false,
    transport: {
      read: ({ data }) => ({
        url: `${url}/page`,
        method: 'get',
        data: getQueryParams(data),
      }),
      create: ({ data: [data] }) => ({
        url: `${url}/create`,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => {
        return ({
          url: `${url}/update`,
          method: 'put',
          data: getUpdateData(data),
        });
      },
    },
    fields: [
      {
        name: 'name',
        type: 'string',
        label: nameLabel,
        required: true,
        dynamicProps: {
          isIntl: ({ record }) => !checkIsPreset(record),
          disabled: ({ record }) => checkIsPreset(record),
        },
      },
      { name: 'code', type: 'string', label: codeLabel, required: true, format: 'uppercase' },
      {
        name: 'type',
        type: 'string',
        label: typeLabel,
        required: true,
      },
      {
        name: 'menuPermissionId',
        type: 'object',
        label: pageLabel,
        textField: 'description',
        valueField: 'id',
        lovCode: 'TENANT_PAGE',
        dynamicProps: {
          required: ({ record }) => record.get('type') === 'menu',
        },
        transformRequest: (value) => {
          return value?.id || 0;
        },
        transformResponse: (value, data) => {
          if (+value === 0) {
            return undefined;
          }
          return {
            id: value,
            description: data.menuPermissionDescription,
          };
        },
      },
      {
        name: 'permissionName',
        type: 'string',
        label: pageLabel,
      },
      { name: 'path', type: 'string', label: pathLabel },
      {
        name: 'icon',
        type: 'string',
        label: iconLabel,
        required: true,
        dynamicProps: {
          disabled: ({ record }) => checkIsPreset(record),
        },
      },
      { name: 'enabledFlag', type: 'number', label: statusLabel },
      {
        name: 'description',
        type: 'string',
        label: descriptionLabel,
        dynamicProps: {
          isIntl: ({ record }) => !checkIsPreset(record),
          disabled: ({ record }) => checkIsPreset(record),
        },
      },
      { name: 'objectVersionNumber', type: 'string' },
      {
        name: 'customFlag',
        type: 'boolean',
        label: isPreSetLabel,
        transformResponse: ((v, data) => {
          return tenantId === '0' ? !v : (data.tenantId === '0');
        }),
      },
      {
        name: 'sort',
        type: 'number',
        label: sortLabel,
        dynamicProps: {
          disabled: ({ record }) => checkIsPreset(record),
        },
      },
      {
        name: 'level',
        type: 'string',
        label: levelLabel,
        options: levelDs,
      },
      {
        name: 'parentId',
        type: 'object',
        label: parentLabel,
        textField: 'name',
        valueField: 'id',
        lovCode: 'PARENT_MENU',
        // required: true,
        transformRequest: (value) => {
          return value?.id || 0;
        },
        transformResponse: (value, data) => {
          if (value) {
            return {
              id: value,
              name: data.parentName,
            };
          }
          return undefined;
        },
      },
      {
        name: 'isPreset',
        label: isPreSetLabel,
        type: 'boolean',
        ignore: 'always',
      },
    ],
    events: {
      load: ({ dataSet }) => {
        runInAction(() => {
          dataSet.forEach(record => {
            record.init('isPreset', record.get('tenantId') === '0');
          });
        });
      },
      update: ({ record, name, value }) => {
        if (name === 'menuPermissionId') {
          record.set('path', value?.path || '');
        }
      },
    },
    queryFields: [...commonQuery, queryFieldList, { name: 'enabledFlag', type: 'string', label: statusLabel, options: enabledFlagDs }],
  };
};
