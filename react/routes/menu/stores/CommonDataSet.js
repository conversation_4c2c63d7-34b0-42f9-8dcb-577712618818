import { DataSet } from 'choerodon-ui/pro';
import { Constants, getQueryParams } from '@zknow/utils';

const { CODE_REGEX } = Constants;
export default ({ intlPrefix, intl, tenantId, statusDataSet, menuDataSet, orgDataSet }) => {
  const url = `iam/yqc/v1/${tenantId}/common/menus`;
  const nameLabel = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const codeLabel = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const descriptionLabel = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const statusLabel = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const iconLabel = intl.formatMessage({ id: 'iam.common.model.icon', defaultMessage: '图标' });
  const typeLabel = intl.formatMessage({ id: 'zknow.common.model.type', defaultMessage: '类型' });
  const levelLabel = intl.formatMessage({ id: 'iam.menu.parentLevel', defaultMessage: '父级菜单' });
  const pathLabel = intl.formatMessage({ id: 'iam.common.model.path', defaultMessage: '路径' });
  const methodLabel = intl.formatMessage({ id: 'iam.menu.method', defaultMessage: '请求方式' });
  const authorityLabel = intl.formatMessage({ id: 'iam.common.model.authority', defaultMessage: '权限' });
  const pageLabel = intl.formatMessage({ id: 'iam.common.model.page', defaultMessage: '页面' });
  const menu = intl.formatMessage({ id: 'zknow.common.model.menu', defaultMessage: '菜单' });
  const link = intl.formatMessage({ id: 'iam.common.model.link', defaultMessage: '链接' });
  const sortLabel = intl.formatMessage({ id: 'iam.common.model.sort', defaultMessage: '排序' });

  const typeOptionsDs = new DataSet({
    data: [
      { meaning: menu, value: 'menu' },
      { meaning: link, value: 'link' },
    ],
    selection: 'single',
  });

  function codeValidator(value) {
    if (!CODE_REGEX.test(value)) {
      return intl.formatMessage({ id: 'iam.common.validate.message.code', defaultMessage: '编码只能由大写字母，数字和下划线构成' });
    }
  }
  return {
    autoQuery: false,
    paging: false,
    selection: false,
    parentField: 'parentId',
    idField: 'id',
    autoQueryAfterSubmit: false,
    autoLocateFirst: false,
    transport: {
      read: ({ data }) => {
        const { level, detail, id } = data;
        return ({
          url: detail ? `${url}/detail/${id}` : `${url}/list/organization`,
          method: 'get',
          data: getQueryParams(data),
          transformResponse: (res) => {
            if (res && detail) {
              const JSONData = JSON.parse(res);
              if (JSONData.permissions && JSONData.permissions.length > 0) {
                return JSONData.permissions;
              } else {
                return [];
              }
            } else {
              return res;
            }
          },
        });
      },
      create: ({ data: [data] }) => ({
        url: `${url}/create`,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => ({
        url: `${url}/update`,
        method: 'put',
        data,
      }),
      destroy: ({ data: [data] }) => ({
        url: `${url}/version?versionId=${data.id}`,
        method: 'delete',
      }),
    },
    fields: [
      { name: 'name', type: 'string', label: nameLabel, required: true, isIntl: true },
      { name: 'code', type: 'string', label: codeLabel, required: true, format: 'uppercase', validator: codeValidator },
      {
        name: 'parentId',
        type: 'object',
        label: levelLabel,
        textField: 'name',
        valueField: 'id',
        lovCode: 'PARENT_MENU',
        transformRequest: (value) => {
          return value?.id || 0;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            name: data.parentName,
          };
        },
      },
      {
        name: 'menuPermissionId',
        type: 'object',
        label: pageLabel,
        textField: 'description',
        valueField: 'id',
        lovCode: 'TENANT_PAGE',
        dynamicProps: {
          required: ({ record }) => record.get('type') === 'menu',
        },
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          return {
            id: value,
            name: data.pageName,
          };
        },
      },
      {
        name: 'type',
        type: 'string',
        label: typeLabel,
        required: true,
        textField: 'meaning',
        valueField: 'value',
        options: typeOptionsDs,
        defaultValue: 'menu',
      },
      {
        name: 'path',
        type: 'string',
        label: pathLabel,
        computedProps: {
          required: ({ record }) => {
            return record?.get('type') === 'link';
          },
        },
      },
      { name: 'icon', type: 'string', label: iconLabel, required: true },
      { name: 'enabledFlag', type: 'number', label: statusLabel },
      { name: 'description', type: 'string', label: descriptionLabel, isIntl: true },
      { name: 'authority', type: 'string', label: authorityLabel },
      { name: 'method', type: 'string', label: methodLabel },
      { name: 'permissions', type: 'array' },
      { name: 'sort', type: 'number', defaultValue: 0, label: sortLabel },
    ],
    events: {
      update: ({ record, value, name }) => {
        if (name === 'type') {
          record.set({
            path: undefined,
            menuPermissionId: undefined,
          });
        }
      },
    },
  };
};
