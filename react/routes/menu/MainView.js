import React, { useContext, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Content, TabPage, axios } from '@yqcloud/apps-master';
import { Modal, Table } from 'choerodon-ui/pro';
import { TableHoverAction, TableStatus, ClickText, ModalTitle, Button } from '@zknow/components';
import FormView from './FormView';
import Store from './stores';

import './index.less';

const modalKey = Modal.key();
const { Column } = Table;

function MainView() {
  const context = useContext(Store);
  const {
    intl,
    listDataSet,
    prefixCls,
    intlPrefix,
    tenantId,
    type,
    detailDataSet,
    commonDataSet,
    menuTypeMeaning,
  } = context;
  const mdModalStyle = useMemo(() => ({ width: 800 }), []);
  const smModalStyle = useMemo(() => ({ width: 520, maxHeight: 578 }), []);

  function openModal(_type, dataSet, curDataSet) {
    Modal.open({
      title: _type === 'modify'
        ? <ModalTitle title={intl.formatMessage({ id: 'zknow.common.model.menu', defaultMessage: '菜单' })} dataSet={dataSet} />
        : intl.formatMessage({ id: 'iam.menu.create', defaultMessage: '新建菜单' }),
      children: (
        <FormView
          type={_type}
          context={context}
          dataSet={dataSet}
          curDataSet={curDataSet}
          menuDataSet={detailDataSet}
          updateTag={new Date()}
        />
      ),
      key: modalKey,
      drawer: _type === 'modify',
      style: _type === 'modify' ? mdModalStyle : smModalStyle,
      destroyOnClose: true,
    });
  }

  async function handleModify(record) {
    listDataSet.getField('parentId').set('lovPara', { level: 'organization' });
    openModal('modify', listDataSet);
  }

  function handleCreate() {
    commonDataSet.reset();
    commonDataSet.getField('parentId').set('lovPara', { level: 'organization' });
    commonDataSet.create({ level: 'organization', type: null, icon: 'ViewGridList' });
    openModal('create', commonDataSet, listDataSet);
  }

  function renderName({ record, name }) {
    return <ClickText record={record} onClick={() => handleModify(record)} valueField={name} />;
  }

  function renderType({ record }) {
    return menuTypeMeaning[record.get('type')] || record.get('type');
  }

  function renderCreateBtn() {
    return [
      <Button
        icon="plus"
        funcType="raised"
        color="primary"
        onClick={handleCreate}
      >
        {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
      </Button>,
    ];
  }

  function renderEnabledFlag({ record }) {
    const flag = (record.get('enabledFlag') === 1);
    return (
      <TableStatus
        status={flag}
        enabledText={intl.formatMessage({ id: 'iam.common.model.valid', defaultMessage: '有效' })}
        disabledText={intl.formatMessage({ id: 'iam.common.model.invalid', defaultMessage: '无效' })}
      />
    );
  }

  function renderTableAction({ record }) {
    const id = record.get('id');
    const enabledFlag = record.get('enabledFlag');
    const url = `iam/yqc/v1/${tenantId}/menus/invalid/${id}?enabledFlag=${!enabledFlag}`;
    const activeSolution = async () => {
      try {
        const res = await axios.put(`${url}`);
        if (!res?.failed) {
          listDataSet.query();
          return true;
        }
        return false;
      } catch (e) {
        return false;
      }
    };

    const actions = [
      {
        name: intl.formatMessage({ id: enabledFlag ? 'iam.common.make.invalid' : 'iam.common.make.valid', defaultMessage: enabledFlag ? '失效' : '生效' }),
        icon: enabledFlag ? 'ReduceOne' : 'CheckOne',
        onClick: activeSolution,
      },
    ];
    if (record.get('customFlag')) {
      return null;
    }

    return (
      <TableHoverAction
        record={record}
        actions={record.get('customFlag') ? [] : actions}
        intlBtnIndex={0}
        maxButtonCount={2}
      />
    );
  }

  function renderPreset({ value }) {
    return intl.formatMessage({ id: value ? 'zknow.common.status.yes' : 'zknow.common.status.no', defaultMessage: value ? '是' : '否' });
  }

  return (
    <TabPage>
      <Content style={{ padding: 0 }}>
        <Table
          labelLayout="float"
          dataSet={listDataSet}
          className={`${prefixCls}-table`}
          buttons={renderCreateBtn()}
          autoHeight
          queryFieldsLimit={100}
          queryBarProps={{
            title: intl.formatMessage({ id: 'zknow.common.model.menu', defaultMessage: '菜单' }),
          }}
        >
          <Column name="name" renderer={renderName} width={200} lock />
          <Column name="code" minWidth={350} />
          <Column name="type" renderer={renderType} width={200} align="left" />
          <Column name="path" minWidth={300} />
          {type !== 'site' && <Column name="isPreset" width={110} renderer={renderPreset} />}
          {type === 'site' && <Column name="level" width={150} />}
          <Column name="enabledFlag" width={200} renderer={renderEnabledFlag} align="center" />
          <Column width={100} renderer={renderTableAction} />
        </Table>
      </Content>
    </TabPage>
  );
}

export default observer(MainView);
