import React, { useEffect, useMemo, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { axios } from '@yqcloud/apps-master';
import { Icon, Button, IconPicker } from '@zknow/components';
import { Form, TextField, Select, Table, TextArea, message, Lov, CheckBox, NumberField } from 'choerodon-ui/pro';
import './index.less';

const { Column } = Table;

export default observer((props) => {
  const {
    modal, dataSet, type, curDataSet, updateTag,
    context: {
      intl, prefixCls, intlPrefix, commonDataSet, orgDataSet,
      tenantId, menuTypeMeaning, type: menuType,
    },
  } = props;
  const record = dataSet.current;

  useEffect(() => {
    if (type === 'modify') {
      commonDataSet.setQueryParameter('detail', true);
      commonDataSet.setQueryParameter('id', record.get('id'));
      commonDataSet.query();
    }
  }, [updateTag]);

  const isEdit = record.getState('isPreview');

  modal.handleOk(async () => {
    if (!await dataSet.validate()) {
      return false;
    }
    if (type === 'modify') {
      record.setState('isPreview', true);
    }
    try {
      if (type !== 'modify') {
        const res = await dataSet.submit();
        if (res?.content && res.content.length > 0) {
          curDataSet.query();
          return true;
        } else {
          message.error(res.message);
          return false;
        }
      } else if (record?.dataSet.dirty) {
        const data = record.toData();
        const putData = await axios.put(`/iam/yqc/v1/${tenantId}/common/menus/update`, JSON.stringify({
          ...data,
          customFlag: data?.customFlag ? 0 : 1,
        }));
        if (putData && !putData.failed) {
          message.success(intl.formatMessage({ id: 'iam.common.success.put', defaultMessage: '更新成功' }));
          putData.menuPermissionId = { id: putData.menuPermissionId, description: putData.menuPermissionDescription };
          record.init(putData);
          const findItem = (orgDataSet || []).find((v) => v.get('id') === putData.id);
          if (findItem) {
            findItem.set('objectVersionNumber', putData.objectVersionNumber);
            findItem.set('frontParentId', putData.parentId);
            findItem.set('parentId', { id: putData.parentId, name: putData.parentName });
            findItem.set('parentName', putData.parentName);
          }
          commonDataSet.query();
          return false;
        }
      } else {
        return false;
      }
    } catch (err) {
      return false;
    }
  });

  function handleCancel() {
    record.setState('isPreview', true);
    record.dataSet.reset();
    return false;
  }

  const Footer = observer(({
    okBtn,
    cancelBtn,
  }) => {
    const myCancelBtn = (
      <Button
        funcType="raised"
        onClick={handleCancel}
      >{intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}</Button>
    );
    if ((record.get('tenantId') === '0' && menuType !== 'site') || record.get('type') === 'filter_item' || record.get('type') === 'filter' || record.get('type') === 'top') {
      return [];
    } else {
      return (record.getState('isPreview') ? (
        <Button
          key="edit"
          funcType="raised"
          icon="icon-edit"
          onClick={() => {
            record.setState('isPreview', false);
          }}
          color="primary"
        >
          {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
        </Button>
      ) : [okBtn, myCancelBtn]);
    }
  });

  useEffect(() => {
    if (type === 'modify') {
      record.setState('isPreview', true);
      modal.update({
        footer: (okBtn, cancelBtn) => <Footer okBtn={okBtn} cancelBtn={cancelBtn} />,
      });
    }
  }, [updateTag, record]);

  function renderIcon({ record: iconRecord }) {
    return (
      <Icon className={`${prefixCls}-icon`} type={iconRecord?.get('icon')} />
    );
  }

  function renderTarget() {
    const currentRecord = dataSet.current;
    if (!currentRecord) {
      return null;
    }
    return currentRecord.get('type') === 'menu'
      ? <Lov name="menuPermissionId" searchable />
      : <TextField name="path" help={intl.formatMessage({ id: 'iam.menu.path.help', defaultMessage: '若要访问相对路径，请以/开头；若要访问绝对路径，请以http\\https开头' })} />;
  }

  if (type !== 'modify') {
    return (
      <div>
        <Form labelLayout="horizontal" dataSet={dataSet} labelWidth="auto">
          <TextField autoFocus name="name" />
          <TextField name="code" restrict="_|A-Z|a-z|0-9" />
          <Select name="type" />
          {renderTarget()}
          <IconPicker
            name="icon"
            renderer={renderIcon}
            record={dataSet?.current}
            placement="bottomLeft"
          />
          <TextArea
            name="description"
            newLine
            colSpan={2}
            rows={1}
            autoSize={{ minRows: 1, maxRows: 4 }}
            resize="height"
          />
        </Form>
      </div>
    );
  }

  function renderType({ record: cuRecord }) {
    return menuTypeMeaning[cuRecord.get('type')];
  }

  return (
    <div className="menu-modal-container">
      <Form
        className={`${prefixCls}-form`}
        labelWidth="auto"
        header={intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' })}
        labelLayout="horizontal"
        dataSet={dataSet}
        columns={2}
        disabled={isEdit}
      >
        <TextField autoFocus name="name" />
        {record.getState('isPreview') ? <TextField name="code" disabled className="menu-code" />
          : <TextField name="code" disabled />}
        <TextField name="type" disabled renderer={renderType} />
        {dataSet?.current?.get('type') === 'menu' && <Lov name="menuPermissionId" searchable />}
        <TextField name="path" disabled={record.get('type') !== 'link'} />
        <IconPicker name="icon" renderer={renderIcon} record={dataSet?.current} placement="bottomLeft" />
        <CheckBox
          name="customFlag"
          disabled
        />
        <TextArea
          name="description"
          newLine
          colSpan={2}
          rows={1}
          autoSize={{ minRows: 1, maxRows: 4 }}
          resize="height"
        />
      </Form>
      <Form
        className="authority-form"
        header={intl.formatMessage({ id: 'iam.common.menu.authority', defaultMessage: '菜单权限' })}
      >
        <Table dataSet={commonDataSet} labelLayout="float" filter={false} queryBar="none">
          <Column name="description" tooltip="overflow" width={150} />
          <Column name="code" tooltip="overflow" width={200} minWidth={130} />
          <Column name="method" width={100} />
          <Column name="path" tooltip="overflow" />
        </Table>
      </Form>
    </div>
  );
});
