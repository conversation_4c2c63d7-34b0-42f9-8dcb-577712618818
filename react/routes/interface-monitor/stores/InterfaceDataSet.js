import { getQueryParams } from '@zknow/utils';
import { getItfSearch } from '@/utils';

export default ({ intl, tenantId }) => {
  const url = `hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}interface-logs`;

  return {
    autoQuery: true,
    paging: 'server',
    pageSize: 20,
    selection: false,
    autoLocateFirst: false,
    transport: {
      read: ({ data }) => ({
        url,
        method: 'get',
        data: getQueryParams(data),
      }),
      update: ({ data: [data] }) => {
        return ({
          url: `${url}/update`,
          method: 'put',
          data,
        });
      },
    },
    fields: [
      { name: 'interfaceName', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' }) },
      { name: 'interfaceCode', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }), required: true, format: 'uppercase' },
      { name: 'clientId', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.clientId', defaultMessage: '客户端ID' }) },
      { name: 'category', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.category', defaultMessage: '接口分类' }) },
      { name: 'interfaceType', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.interfaceType', defaultMessage: '客户端ID' }) },
      { name: 'interfaceUrl', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.interfaceUrl', defaultMessage: '接口地址' }) },
      { name: 'interfaceResponseTime', type: 'number', label: intl.formatMessage({ id: 'iam.interface.model.interfaceResponseTime', defaultMessage: '请求时间' }) },
      { name: 'asyncFlag', type: 'number', label: intl.formatMessage({ id: 'iam.interface.model.asyncFlag', defaultMessage: '异步调用' }) },
      { name: 'tenantName', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.tenant', defaultMessage: '租户' }) },
    ],
    queryFields: [
      { name: 'interfaceName', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' }) },
      { name: 'interfaceCode', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }) },
    ],
    events: {
      query: ({ dataSet, params, data }) => {
        getItfSearch(params);
      },
    },
  };
};
