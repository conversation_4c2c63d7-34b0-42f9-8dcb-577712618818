import { DataSet } from 'choerodon-ui/pro';

export default ({ intl }) => {
  const cleanLogOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.interface.model.clearType.threeDay', defaultMessage: '清理三天之前日志数据' }), value: 'THREE_DAYS_AGO' },
      { meaning: intl.formatMessage({ id: 'iam.interface.model.clearType.week', defaultMessage: '清理一周之前日志数据' }), value: 'ONE_WEEK_AGO' },
      { meaning: intl.formatMessage({ id: 'iam.interface.model.clearType.month', defaultMessage: '清理一个月之前日志数据' }), value: 'ONE_MONTH_AGO' },
      { meaning: intl.formatMessage({ id: 'iam.interface.model.clearType.threeMonth', defaultMessage: '清理三个月之前日志数据' }), value: 'THREE_MONTHS_AGO' },
      { meaning: intl.formatMessage({ id: 'iam.interface.model.clearType.sixMonth', defaultMessage: '清理六个月以前日志数据' }), value: 'SIX_MONTHS_AGO' },
      { meaning: intl.formatMessage({ id: 'iam.interface.model.clearType.year', defaultMessage: '清理一年以前日志数据' }), value: 'ONE_YEAR_AGO' },
    ],
  });

  return {
    fields: [
      { name: 'clearType', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.type', defaultMessage: '类型' }), options: cleanLogOptions },
      {
        name: 'tenantId',
        type: 'object',
        label: intl.formatMessage({ id: 'zknow.common.model.tenant', defaultMessage: '租户' }),
        textField: 'name',
        valueField: 'id',
        lovCode: 'TENANT',
      },
    ],
  };
};
