import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import InterfaceDataSet from './InterfaceDataSet';
import ClearDataSet from './ClearDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: 'iam.interface' })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
    } = props;
    const prefixCls = 'iam-interface';

    const interfaceDataSet = useMemo(() => new DataSet(InterfaceDataSet({ intl, tenantId })));
    const clearDataSet = useMemo(() => new DataSet(ClearDataSet({ intl, tenantId })));

    const value = {
      ...props,
      prefixCls,
      interfaceDataSet,
      tenantId,
      clearDataSet,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
