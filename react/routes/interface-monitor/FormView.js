import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { axios } from '@yqcloud/apps-master';
import { Form, Select, Lov, message } from 'choerodon-ui/pro';

export default observer((props) => {
  const { modal, dataSet, interfaceDataSet, tenantId } = props;
  const handleOk = async () => {
    const body = {
      tenantId: tenantId === '0' ? dataSet?.current?.get('tenantId')?.id : tenantId,
      clearType: dataSet?.current?.get('clearType'),
    };
    try {
      const res = await axios.delete(`hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}interface-logs/clear-logs`, { data: body });
      if (!res?.failed) {
        interfaceDataSet.query();
      } else {
        message.error(res?.message);
      }
    } catch (e) {
      return false;
    }
  };

  modal.handleOk(() => handleOk());
  modal.handleCancel(() => dataSet.reset());

  return (
    <Form
      dataSet={dataSet}
      labelWidth="auto"
    >
      {tenantId === '0' && <Lov name="tenantId" />}
      <Select name="clearType" />
    </Form>
  );
});
