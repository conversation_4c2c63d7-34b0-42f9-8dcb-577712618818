import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Content, TabPage } from '@yqcloud/apps-master';
import { Modal, Table } from 'choerodon-ui/pro';
import { Button } from '@zknow/components';
import Store from './stores';

import './index.less';
import FormView from './FormView';

const modalKey = Modal.key();
const { Column } = Table;
function MainView() {
  const context = useContext(Store);
  const {
    intl,
    prefixCls,
    interfaceDataSet,
    clearDataSet,
    tenantId,
  } = context;

  const isSite = tenantId === '0';

  const openModal = (type, record) => {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.interface.monitor.clearLog', defaultMessage: '清理日志' }),
      children: (
        <FormView
          dataSet={clearDataSet}
          interfaceDataSet={interfaceDataSet}
          tenantId={tenantId}
        />
      ),
      key: modalKey,
      drawer: type === 'modify',
      destroyOnClose: true,
    });
  };

  const handleView = () => {
    openModal();
  };

  const buttons = [
    <Button funcType="raised" icon="Clear" color="primary" onClick={() => handleView()}>
      {intl.formatMessage({ id: 'iam.interface.monitor.clearLog', defaultMessage: '清理日志' })}
    </Button>,
  ];

  return (
    <TabPage>
      <Content style={{ padding: 0 }}>
        <Table
          labelLayout="float"
          dataSet={interfaceDataSet}
          className={`${prefixCls}-table`}
          autoHeight
          buttons={buttons}
          queryBarProps={{
            fuzzyQuery: false,
          }}
        >
          <Column name="interfaceName" />
          <Column name="interfaceCode" />
          {isSite && <Column name="tenantName" />}
          {isSite && <Column name="clientId" />}
          <Column name="category" />
          <Column name="interfaceType" />
          <Column name="interfaceUrl" />
          <Column name="interfaceResponseTime" align="left" renderer={({ record }) => (`${record?.get('interfaceResponseTime')}ms`)} />
          <Column name="asyncFlag" align="left" renderer={({ record }) => (record?.get('asyncFlag') === 1 ? intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' }) : intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' }))} />
        </Table>
      </Content>
    </TabPage>
  );
}

export default observer(MainView);
