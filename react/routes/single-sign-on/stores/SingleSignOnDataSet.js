import { DataSet } from 'choerodon-ui/pro';
import { getQueryParams } from '@zknow/utils';

const AUTHRequiredMap = ['remark', 'ssoLoginUrl', 'ssoServerUrl', 'ssoClientId', 'ssoClientPwd', 'ssoUserInfo', 'loginNameField'];
const CASRequiredMap = ['remark', 'ssoServerUrl', 'ssoLoginUrl', 'loginNameField'];
const SMALRequiredMap = ['samlIdpPublicKey', 'samlEntityId', 'ssoServerUrl', 'remark'];

export default ({ intlPrefix, intl, tenantId }) => {
  const url = `iam/yqc/${tenantId}/hdomains`;
  const ssoLoginUrl = intl.formatMessage({ id: 'iam.single.model.ssoLoginUrl', defaultMessage: '单点登录地址' });
  const ssoServerUrl = intl.formatMessage({ id: 'iam.single.model.ssoServerUrl', defaultMessage: '认证服务器地址' });
  const casSsoServerUrl = intl.formatMessage({ id: 'iam.single.model.cas.ssoServerUrl', defaultMessage: 'CAS服务器地址' });
  const ssoLogoutUrl = intl.formatMessage({ id: 'iam.single.model.ssoLogoutUrl', defaultMessage: '登出地址' });
  const ssoClientId = intl.formatMessage({ id: 'iam.single.model.ssoClientId', defaultMessage: '认证客户端ID' });
  const ssoClientPwd = intl.formatMessage({ id: 'iam.single.model.ssoClientPwd', defaultMessage: '认证客户端密码' });
  const ssoUserInfo = intl.formatMessage({ id: 'iam.single.model.ssoUserInfo', defaultMessage: '根据token获取用户信息地址' });
  const ssoTypeCode = intl.formatMessage({ id: 'zknow.common.model.type', defaultMessage: '类型' });
  const remark = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const creationDate = intl.formatMessage({ id: 'zknow.common.model.creationDate', defaultMessage: '创建时间' });
  const enabledFlag = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const loginNameField = intl.formatMessage({ id: 'iam.single.model.loginNameField', defaultMessage: '登录名字段' });
  const serverUrl = intl.formatMessage({ id: 'iam.single.model.serverAddress', defaultMessage: '服务器地址' });
  const publicKey = intl.formatMessage({ id: 'iam.common.model.publicKey', defaultMessage: '公钥' });
  const metaDownload = intl.formatMessage({ id: 'iam.single.model.metaDownload', defaultMessage: '元数据下载' });

  const isRequired = (type, field) => {
    switch (type) {
    case 'AUTH':
    case 'WEAVER':
      return AUTHRequiredMap.includes(field);
    case 'CAS':
      return CASRequiredMap.includes(field);
    case 'SAML':
      return SMALRequiredMap.includes(field);
    default:
      return false;
    }
  };

  const ssoLoginLabel = (type) => {
    switch (type) {
    case 'AUTH':
    case 'WEAVER':
      return ssoServerUrl;
    case 'CAS':
      return casSsoServerUrl;
    default:
      return ssoServerUrl;
    }
  };

  return {
    autoQuery: true,
    selection: false,
    paging: true,
    autoLocateFirst: false,
    pageSize: 20,
    transport: {
      read: ({ data }) => ({
        url,
        method: 'get',
        data: getQueryParams(data),
      }),
      create: ({ data: [data] }) => ({
        url,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => ({
        url,
        method: 'put',
        data,
      }),
      destroy: ({ data }) => ({
        url: `${url}/${data.id}`,
        method: 'delete',
      }),
    },
    fields: [
      { name: 'ssoTypeCode', type: 'string', defaultValue: 'AUTH', required: true, label: ssoTypeCode, lookupCode: 'SSO_TYPE_CODE' },
      { name: 'remark', isIntl: true, type: 'string', dynamicProps: { required: ({ record }) => isRequired(record.get('ssoTypeCode'), 'remark') }, label: remark },
      { name: 'ssoLoginUrl', type: 'url', dynamicProps: { required: ({ record }) => isRequired(record.get('ssoTypeCode'), 'ssoLoginUrl') }, label: ssoLoginUrl },
      { name: 'ssoServerUrl', type: 'url', dynamicProps: { required: ({ record }) => isRequired(record.get('ssoTypeCode'), 'ssoServerUrl'), label: ({ record }) => ssoLoginLabel(record.get('ssoTypeCode')) } },
      { name: 'ssoLogoutUrl', type: 'url', dynamicProps: { required: ({ record }) => isRequired(record.get('ssoTypeCode'), 'ssoLogoutUrl') }, label: ssoLogoutUrl },
      { name: 'ssoClientId', type: 'string', dynamicProps: { required: ({ record }) => isRequired(record.get('ssoTypeCode'), 'ssoClientId') }, label: ssoClientId },
      { name: 'ssoClientPwd', type: 'string', dynamicProps: { required: ({ record }) => isRequired(record.get('ssoTypeCode'), 'ssoClientPwd') }, label: ssoClientPwd },
      { name: 'ssoUserInfo', type: 'string', dynamicProps: { required: ({ record }) => isRequired(record.get('ssoTypeCode'), 'ssoUserInfo') }, label: ssoUserInfo },
      { name: 'creationDate', type: 'string', dynamicProps: { required: ({ record }) => isRequired(record.get('ssoTypeCode'), 'creationDate') }, label: creationDate },
      { name: 'enabledFlag', type: 'boolean', dynamicProps: { required: ({ record }) => isRequired(record.get('ssoTypeCode'), 'enabledFlag') }, label: enabledFlag },
      { name: 'loginNameField', type: 'string', dynamicProps: { required: ({ record }) => isRequired(record.get('ssoTypeCode'), 'loginNameField') }, label: loginNameField },
      { name: 'ssoServerUrl', type: 'string', label: serverUrl, dynamicProps: { required: ({ record }) => isRequired(record.get('ssoTypeCode'), 'ssoServerUrl') } },
      { name: 'samlEntityId', type: 'string', label: 'Entity ID', dynamicProps: { required: ({ record }) => isRequired(record.get('ssoTypeCode'), 'samlEntityId') } },
      { name: 'samlIdpPublicKey', type: 'string', label: publicKey, dynamicProps: { required: ({ record }) => isRequired(record.get('ssoTypeCode'), 'samlIdpPublicKey') } },
      { name: 'metaDownload', label: metaDownload },
      { name: 'customCode', type: 'string', label: 'Code' },
    ],
    queryFields: [
      { name: 'remark', type: 'string', label: remark },
      { name: 'ssoTypeCode', type: 'string', label: ssoTypeCode, lookupCode: 'SSO_TYPE_CODE' },
      { name: 'enabledFlag',
        type: 'boolean',
        label: enabledFlag,
        options: new DataSet({
          data: [
            { meaning: intl.formatMessage({ id: 'zknow.common.button.enable', defaultMessage: '启用' }), value: true },
            { meaning: intl.formatMessage({ id: 'iam.common.action.stop', defaultMessage: '停用' }), value: false },
          ],
        }) },
    ],
  };
};
