import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import SingleSignOnDataSet from './SingleSignOnDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: ['iam.common', 'iam.single'] })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { tenantId, type } },
    } = props;
    const intlPrefix = 'iam.single.sign.on';
    const prefixCls = 'iam-single-sign-on';
    const isSite = type === 'site';
    
    const singleSignOnDataSet = useMemo(() => new DataSet(SingleSignOnDataSet({ intlPrefix, intl, tenantId, isSite })), []);
    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      tenantId,
      singleSignOnDataSet,
      isSite,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
