import React from 'react';
import { Route, Switch } from 'react-router-dom';
import { nomatch } from '@yqcloud/apps-master';
import { StoreProvider } from './stores';
import MainView from './MainView';
import Edit from './edit';

export default (props) => (
  <StoreProvider {...props}>
    <Switch>
      <Route path={`${props.match.url}`} component={MainView} />
      <Route path="*" component={nomatch} />
    </Switch>
  </StoreProvider>
);
