import React, { useContext, Fragment, useCallback, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Table, Modal, message } from 'choerodon-ui/pro';
import { ClickText, StatusTag, Button, TableHoverAction, ModalTitle } from '@zknow/components';
import { TabPage, Content } from '@yqcloud/apps-master';
import { color as colorUtils } from '@zknow/utils';
import copy from 'copy-to-clipboard';
import Detail from './Detail';
import CreateView from './CreateView';
import ButtonGroup from './ButtonGroup';
import Store from './stores';
import './index.less';

const { Column } = Table;
const modalKey = Modal.key();

const MainView = () => {
  const { prefixCls, intl, intlPrefix, singleSignOnDataSet, tenantId } = useContext(Store);

  const rendererStatus = ({ record }) => {
    const flag = record.get('enabledFlag');
    const using = intl.formatMessage({ id: 'zknow.common.button.enable', defaultMessage: '启用' });
    const deactivate = intl.formatMessage({ id: 'iam.common.action.stop', defaultMessage: '停用' });

    return (
      <StatusTag name="enabledFlag" color={flag ? colorUtils?.getColorCorrespondingValue('green') : colorUtils?.getColorCorrespondingValue('grey')}>{flag ? using : deactivate}</StatusTag>
    );
  };

  const handleClickCreateBtn = () => {
    singleSignOnDataSet.create({
      tenantId,
      domainUrl: window.location.origin,
    });
    Modal.open({
      title: intl.formatMessage({ id: 'iam.single.action.create', defaultMessage: '新建单点登录' }),
      children: (
        <CreateView
          singleSignOnDataSet={singleSignOnDataSet}
          intl={intl}
          intlPrefix={intlPrefix}
          prefixCls={prefixCls}
        />
      ),
      okText: intl.formatMessage({ id: 'zknow.common.button.ok', defaultMessage: '确定' }),
      style: { width: 520 },
      closable: true,
      key: modalKey,
      destroyOnClose: true,
    });
  };

  const renderTableButton = useMemo(() => (
    [<Button key="create" icon="plus" funcType="raised" color="primary" onClick={handleClickCreateBtn}>
      {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
    </Button>]
  ), [singleSignOnDataSet.current]);

  const handleConfirmOk = async (record, flag) => {
    record.set('enabledFlag', !flag);
    await singleSignOnDataSet.submit();
    singleSignOnDataSet.query();
  };

  const openConfirmModal = (record, flag) => {
    const usingContent = intl.formatMessage({ id: 'iam.single.tips.using.confirm', defaultMessage: '启用当前配置，会停用之前已启用的配置' });
    const deactivateContent = intl.formatMessage({ id: 'iam.single.tips.deactivate.confirm', defaultMessage: '停用后，无法使用当前配置进行单点登录' });
    const usingConfirm = intl.formatMessage({ id: 'iam.common.confirm.using', defaultMessage: '确认启用？' });
    const deactivateConfirm = intl.formatMessage({ id: 'iam.common.confirm.deactivate', defaultMessage: '确认停用？' });
    Modal.confirm({
      title: flag ? deactivateConfirm : usingConfirm,
      children: (
        <div>
          {flag ? deactivateContent : usingContent}
        </div>
      ),
      onOk: () => handleConfirmOk(record, flag),
      key: modalKey,
      destroyOnClose: true,
    });
  };

  const renderTableAction = ({ record }) => {
    const flag = record.getPristineValue('enabledFlag');
    const using = intl.formatMessage({ id: 'zknow.common.button.enable', defaultMessage: '启用' });
    const deactivate = intl.formatMessage({ id: 'iam.common.action.stop', defaultMessage: '停用' });
    return (
      <TableHoverAction
        record={record}
        actions={[{
          name: flag ? deactivate : using,
          icon: flag ? 'icon-Expires' : 'icon-read',
          onClick: () => openConfirmModal(record, flag),
        }]}
      />
    );
  };

  const handleChangeEdit = useCallback((flag) => {
    singleSignOnDataSet?.current.setState('isEdit', flag);
  }, [singleSignOnDataSet?.current]);

  const handleClickViewCallbackUrl = () => {
    const record = singleSignOnDataSet?.current;
    const handleClickCopy = () => {
      copy(record.get('clientHostUrl'));
      message.success(intl.formatMessage({ id: 'zknow.common.success.copy', defaultMessage: '复制成功' }));
    };
    Modal.open({
      title: intl.formatMessage({ id: 'iam.single.action.viewCallbackUrl', defaultMessage: '查看回调地址' }),
      children: (
        <div className={`${prefixCls}-copy`}>
          <div className={`${prefixCls}-copy-text`}>
            {record.get('clientHostUrl')}
          </div>
          <Button style={{ color: '#2979FF' }} icon="copy" key="copy" funcType="flat" color="#2979FF" onClick={handleClickCopy}>
            {intl.formatMessage({ id: 'zknow.common.button.copy', defaultMessage: '复制' })}
          </Button>
        </div>
      ),
      footer: null,
      key: modalKey,
      destroyOnClose: true,
    });
  };

  const actionsList = [
    {
      name: intl.formatMessage({ id: 'iam.single.action.viewCallbackUrl', defaultMessage: '查看回调地址' }),
      onClick: () => handleClickViewCallbackUrl(),
    },
  ];

  const handleClickName = () => {
    Modal.open({
      title: <ModalTitle title={intl.formatMessage({ id: 'iam.single.desc.singleSignOn', defaultMessage: '单点登录' })} dataSet={singleSignOnDataSet} actionsList={actionsList} />,
      children: (
        <Detail
          singleSignOnDataSet={singleSignOnDataSet}
          intl={intl}
          intlPrefix={intlPrefix}
          prefixCls={prefixCls}
        />
      ),
      closable: true,
      drawer: true,
      okText: intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' }),
      footer: (
        <div className="flex-align-center">
          <ButtonGroup
            onChangeEdit={handleChangeEdit}
            dataSet={singleSignOnDataSet}
            prefixCls={prefixCls}
            intl={intl}
            intlPrefix={intlPrefix}
          />
        </div>
      ),
      key: modalKey,
      destroyOnClose: true,
    });
  };

  const renderRemark = ({ record }) => (
    <ClickText
      record={record}
      onClick={handleClickName}
      valueField="remark"
    />
  );

  return (
    <Fragment>
      <TabPage>
        <Content style={{ padding: 0 }}>
          <Table
            dataSet={singleSignOnDataSet}
            labelLayout="float"
            placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
            pristine
            autoHeight
            autoLocateFirst={false}
            buttons={renderTableButton}
            queryBarProps={{
              title: intl.formatMessage({ id: 'iam.single.desc.singleSignOn', defaultMessage: '单点登录' }),
            }}
          >
            <Column name="remark" renderer={renderRemark} />
            <Column width={120} name="ssoTypeCode" />
            <Column width={80} name="enabledFlag" renderer={rendererStatus} />
            <Column width={180} name="creationDate" />
            <Column width={50} renderer={renderTableAction} tooltip="none" />
          </Table>
        </Content>
      </TabPage>
    </Fragment>
  );
};

export default observer(MainView);
