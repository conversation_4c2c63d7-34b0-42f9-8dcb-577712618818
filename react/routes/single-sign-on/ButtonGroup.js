import React, { useState, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import { Button } from '@zknow/components';
import { Modal } from 'choerodon-ui/pro';

const modalKey = Modal.key();

const ButtonGroup = ({
  onChangeEdit,
  intl,
  dataSet,
  intlPrefix,
}) => {
  const [isEdit, setIsEdit] = useState(false);
  const record = dataSet?.current;
  const flag = record?.getPristineValue('enabledFlag');
  const using = intl.formatMessage({ id: 'zknow.common.button.enable', defaultMessage: '启用' });
  const deactivate = intl.formatMessage({ id: 'iam.common.action.stop', defaultMessage: '停用' });

  const handleOk = async (ds) => {
    try {
      if (await ds.submit()) {
        setIsEdit(false);
        onChangeEdit(false);
      } else {
        const res = await dataSet.validate();
        if (res) {
          setIsEdit(false);
          onChangeEdit(false);
        }
        return false;
      }
    } catch (err) {
      return false;
    }
  };

  const handleClickSaveBtn = useCallback(() => {
    handleOk(dataSet);
  }, [dataSet.current]);

  const handleClickCancelBtn = useCallback(() => {
    setIsEdit(false);
    onChangeEdit(false);
    dataSet.reset();
  }, []);

  const handleClickEditBtn = useCallback(() => {
    setIsEdit(true);
    onChangeEdit(true);
  }, [isEdit]);

  const handleConfirmOk = async () => {
    record.set('enabledFlag', !flag);
    const id = record.get('id');
    await dataSet.submit();
    await dataSet.query();
    dataSet.current = dataSet.find(r => r.get('id') === id);
  };

  const handleClickEnabledFlag = () => {
    const usingContent = intl.formatMessage({ id: 'iam.single.tips.using.confirm', defaultMessage: '启用当前配置，会停用之前已启用的配置' });
    const deactivateContent = intl.formatMessage({ id: 'iam.single.tips.deactivate.confirm', defaultMessage: '停用后，无法使用当前配置进行单点登录' });
    const usingConfirm = intl.formatMessage({ id: 'iam.common.confirm.using', defaultMessage: '确认启用？' });
    const deactivateConfirm = intl.formatMessage({ id: 'iam.common.confirm.deactivate', defaultMessage: '确认停用？' });
    Modal.confirm({
      title: flag ? deactivateConfirm : usingConfirm,
      children: (
        <div>
          {flag ? deactivateContent : usingContent}
        </div>
      ),
      onOk: handleConfirmOk,
      key: modalKey,
      destroyOnClose: true,
    });
  };

  if (isEdit) {
    return (
      <div>
        <Button
          key="confirm"
          funcType="raised"
          color="primary"
          onClick={handleClickSaveBtn}
        >
          {intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}
        </Button>
        <Button
          key="cancel"
          funcType="raised"
          color="secondary"
          onClick={handleClickCancelBtn}
        >
          {intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}
        </Button>
      </div>
    );
  }

  return (
    <div>
      <Button
        key="edit"
        icon="icon-edit"
        funcType="raised"
        color="primary"
        onClick={handleClickEditBtn}
      >
        {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
      </Button>
      <Button
        key="flag"
        icon={flag ? 'icon-Expires' : 'icon-read'}
        funcType="raised"
        color="secondary"
        onClick={handleClickEnabledFlag}
      >
        {flag ? deactivate : using}
      </Button>
    </div>
  );
};

export default observer(ButtonGroup);
