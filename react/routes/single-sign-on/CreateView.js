import React, { useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, SelectBox, Password, Select, EmailField, UrlField } from 'choerodon-ui/pro';

export default observer((props) => {
  const { modal, singleSignOnDataSet, intl, prefixCls } = props;

  async function handleOk() {
    try {
      if (await singleSignOnDataSet.submit()) {
        singleSignOnDataSet.query();
      } else {
        return false;
      }
    } catch (err) {
      return false;
    }
  }

  modal.handleOk(() => handleOk());
  modal.handleCancel(() => {
    singleSignOnDataSet.reset();
  });

  const renderSelect = useMemo(() => {
    const type = singleSignOnDataSet?.current?.get('ssoTypeCode');
    if (!type) {
      return null;
    }
    if (['AUTH', 'WEAVER'].includes(type)) {
      const fields = [
        <UrlField name="ssoServerUrl" />,
        <UrlField name="ssoLoginUrl" />,
        <UrlField name="ssoLogoutUrl" />,
        <TextField name="ssoClientId" />,
        <TextField name="ssoClientPwd" />,
        <TextField name="ssoUserInfo" />,
        <TextField name="loginNameField" />,
      ];
      if (type === 'AUTH') {
        fields.push(<TextField name="customCode" />)
      }
      return fields;
    }

    if (['CAS', 'CAS2', 'CAS3'].includes(type)) {
      return [
        <UrlField name="ssoLoginUrl" />,
        <UrlField name="ssoServerUrl" />,
        <UrlField name="ssoLogoutUrl" />,
        <TextField name="loginNameField" />,
      ];
    }

    if (type === 'SAML') {
      return [
        <TextField name="ssoLoginUrl" />,
        <TextField name="ssoLogoutUrl" />,
        <TextField name="ssoServerUrl" />,
        <TextField name="samlEntityId" />,
        <TextField name="samlIdpPublicKey" />,
      ];
    }
  }, [singleSignOnDataSet?.current?.get('ssoTypeCode')]);

  return (
    <div>
      <Form labelWidth={120} labelLayout="horizontal" dataSet={singleSignOnDataSet}>
        <TextField autoFocus name="remark" />
        <Select name="ssoTypeCode" />
        {renderSelect}
      </Form>
    </div>
  );
});
