import React, { useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, message, Select } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import { axios } from '@yqcloud/apps-master';
import { download } from '@/utils';
import './index.less';

export default observer((props) => {
  const { singleSignOnDataSet, prefixCls, intl } = props;
  const record = singleSignOnDataSet?.current;
  const isEdit = record?.getState('isEdit');

  const handleDownload = async () => {
    try {
      const res = await axios.get('oauth/saml/metadata');
      if (!res?.failed) {
        download('spring_saml_metadata.xml', res);
      } else {
        message.error(res?.message);
      }
    } catch (e) {
      return false;
    }
  };

  const renderSelect = useMemo(() => {
    const type = singleSignOnDataSet?.current?.get('ssoTypeCode');
    if (!type) {
      return null;
    }
    if (['AUTH', 'WEAVER'].includes(type)) {
      const fields = [
        <TextField name="ssoServerUrl" />,
        <TextField name="ssoLoginUrl" />,
        <TextField name="ssoLogoutUrl" />,
        <TextField name="ssoClientId" />,
        <TextField name="ssoClientPwd" />,
        <TextField name="ssoUserInfo" />,
        <TextField name="loginNameField" />,
      ];
      if (type === 'AUTH') {
        fields.push(<TextField name="customCode" />);
      }
      return fields;
    }

    if (['CAS', 'CAS2', 'CAS3'].includes(type)) {
      return [
        <TextField name="ssoLoginUrl" />,
        <TextField name="ssoServerUrl" />,
        <TextField name="ssoLogoutUrl" />,
        <TextField name="loginNameField" />,
      ];
    }

    if (type === 'SAML') {
      return [
        <TextField name="ssoLoginUrl" />,
        <TextField name="ssoLogoutUrl" />,
        <TextField name="ssoServerUrl" />,
        <TextField name="samlEntityId" />,
        <TextField name="samlIdpPublicKey" />,
        <div name="metaDownload" className={`${prefixCls}-copy-download-field`} onClick={handleDownload}>
          <Icon type="download" className={`${prefixCls}-copy-download-color`} />
          <div style={{ display: 'inline-block' }}>{intl.formatMessage({ id: 'zknow.common.button.download', defaultMessage: '下载' })}</div>
        </div>,
      ];
    }
  }, [singleSignOnDataSet?.current?.get('ssoTypeCode'), isEdit]);

  return (
    <div>
      <Form disabled={!isEdit} labelWidth={120} labelLayout="horizontal" dataSet={singleSignOnDataSet}>
        <TextField autoFocus name="remark" />
        <Select name="ssoTypeCode" />
        {renderSelect}
      </Form>
    </div>
  );
});
