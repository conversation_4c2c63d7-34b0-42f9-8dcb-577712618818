import { ExternalComponent } from '@zknow/components';
import { axios } from '@zknow/utils';
import { message } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import React, { useEffect, useState } from 'react';
import './index.less';

const viewMapping = {
  '/iam/manufacture': 'MANUFACTURE_TABLE',
};

export default inject('AppState')((props) => {
  const {
    AppState: {
      currentMenuType: { organizationId: tenantId },
    },
    match: { path },
  } = props;
  const [tableViewId, setTableViewId] = useState('');

  useEffect(() => {
    (async () => {
      try {
        const viewCode = viewMapping[path];
        const res = await axios.get(`/lc/v1/${tenantId}/views/form/code/${viewCode}`);
        if (res?.failed) {
          message.error(res.message);
        } else {
          setTableViewId(res.id);
        }
      } catch (e) {
        throw new Error(e);
      }
    })();
  }, [path]);

  return tableViewId ? (
    <div className="manufacture-page">
      <ExternalComponent
        system={{
          scope: 'lcr',
          module: 'PageLoader',
        }}
        viewId={tableViewId}
      />
    </div>
  ) : null;
});
