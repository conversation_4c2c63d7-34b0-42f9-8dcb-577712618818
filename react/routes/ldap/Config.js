import React, { useContext, useState, useEffect, useCallback, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Content, TabPage, axios, Header } from '@yqcloud/apps-master';
import { Output, message, DatePicker, SelectBox, Select, Switch, Tooltip, Form, TextArea, TextField, Password, Modal } from 'choerodon-ui/pro';
import { Icon as YQIcon, ExternalComponent, Button } from '@zknow/components';
import Store from './stores';
import History from './History';
import Sync from './Sync';

import './index.less';

function ListView() {
  const context = useContext(Store);
  const { intl, ldapDataSet, syncLeaveDataSet, syncUserDataSet, syncDepartmentDataSet, prefixCls, intlPrefix, organizationId, publicKeyStore } = context;
  const [disabledEdit, setDisabledEdit] = useState(true);
  const errorDefaultMessageMap = useMemo(() => ({ canConnectServer: '无法连接LDAP服务器', canLogin: '无法使用给定帐号登录 LDAP 服务器', matchAttribute: '无法匹配字段匹配规则中配置的属性' }), []);
  async function handleTestLink(isEnable) {
    try {
      const res = await axios.post(`iam/yqc/${organizationId}/ldaps/${ldapDataSet.current?.get('id')}/test-connect`, {
        account: ldapDataSet.current?.get('account'),
        ldapPassword: publicKeyStore.encryptPwd(ldapDataSet.current?.get('ldapPassword')),
      });
      if (res.failed) {
        message.error(res.message);
      }
      let pass = true;
      ['canConnectServer', 'canLogin', 'matchAttribute'].forEach((field) => {
        if (!res[field]) {
          pass = false;
          throw Error(field);
        }
      });
      if (pass && !isEnable) {
        message.success(intl.formatMessage({ id: 'iam.ldap.tips.test.pass', defaultMessage: '测试通过' }));
      }
      return true;
    } catch (err) {
      if (['canConnectServer', 'canLogin', 'matchAttribute'].includes(err.message)) {
        message.error(intl.formatMessage({ id: `iam.ldap.error.${err}`, defaultMessage: errorDefaultMessageMap[err] }));
      } else {
        message.error(err.message);
      }
      return false;
    }
  }
  async function handleToggleLDAP() {
    if (ldapDataSet?.current?.get('id')) {
      try {
        if (ldapDataSet.current?.get('enabled')) {
          const ok = await Modal.confirm({
            title: intl.formatMessage({ id: 'iam.ldap.title.stopLdap', defaultMessage: '确定停用LDAP' }),
            children: intl.formatMessage({ id: 'iam.ldap.content.stopLdap', defaultMessage: '停用后，之前所同步的用户将无法使用LDAP密码登录平台,且不会再从LDAP服务中同步用户信息。' }),
          });
          if (ok === 'ok') {
            ldapDataSet.current?.set('enabled', false);
            await ldapDataSet.submit();
          }
        } else if (await handleTestLink(true)) {
          ldapDataSet.current?.set('enabled', true);
          await ldapDataSet.submit();
        }
        ldapDataSet.query();
      } catch (err) {
        ldapDataSet.query();
      }
    } else {
      message.error(intl.formatMessage({ id: 'iam.ldap.error.configFailed', defaultMessage: 'LDAP配置信息错误，请检查' }));
    }
  }

  async function handleSync() {
    if (ldapDataSet?.current?.get('id')) {
      try {
        const res = await axios.post(`iam/yqc/${organizationId}/ldaps/${ldapDataSet.current?.get('id')}/sync-users`);
        if (res.failed) {
          message.error(res.message);
          return;
        } else {
          message.success(intl.formatMessage({ id: 'iam.ldap.success.syncRunning', defaultMessage: '同步开始运行' }));
        }
      } catch (err) {
        message.error(err.message || intl.formatMessage({ id: 'iam.ldap.error.syncRunning', defaultMessage: '同步程序运行失败，请重试' }));
      } 
    } else {
      message.error(intl.formatMessage({ id: 'iam.ldap.error.configFailed', defaultMessage: 'LDAP配置信息错误，请检查' }));
    }
  }

  function handleHistory() {
    Modal.open({
      drawer: true,
      title: intl.formatMessage({ id: 'iam.ldap.title.syncHistory', defaultMessage: '同步记录' }),
      style: { width: 800 },
      children: <History context={context} />,
      closable: true,
      footer: null,
    });
  }

  const extraBtn = (
    <ExternalComponent
      moreButtonText={intl.formatMessage({ id: 'iam.common.desc.moreAction', defaultMessage: '更多操作' })}
      system={{
        scope: 'itsm',
        module: 'action-btn-group',
      }}
    >
      <Button
        key="edit"
        funcType="raised"
        color="primary"
        icon="icon-edit"
        id="yq-test-iam-ldap_edit"
        onClick={async () => {
          if (!disabledEdit && ldapDataSet.dirty) {
            const success = await ldapDataSet.submit();
            if (!success) return;
            ldapDataSet.query();
          }
          setDisabledEdit(!disabledEdit);
        }}
      >
        {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
      </Button>
      <Button
        key="connect"
        icon="icon-link"
        funcType="raised"
        color="secondary"
        id="yq-test-iam-ldap_test"
        onClick={() => handleTestLink(false)}
      >
        {intl.formatMessage({ id: 'iam.ldap.action.testConnect', defaultMessage: '测试连接' })}
      </Button>
    </ExternalComponent>
  );

  const handSave = async () => {
    const validateList = [ldapDataSet, syncUserDataSet, syncLeaveDataSet, syncDepartmentDataSet];
    const flag = await Promise.all(validateList.map(ds => ds.validate()));
    if (!flag.every(f => f)) return;
    if (!disabledEdit && ldapDataSet?.dirty) {
      const success = await ldapDataSet.submit();
      if (success) {
        ldapDataSet.query();
      } else {
        // message.error('failed');
      }
    }
    if (!disabledEdit && (syncLeaveDataSet?.dirty || syncUserDataSet?.dirty || syncDepartmentDataSet?.dirty)) {
      const res1 = await syncUserDataSet.submit();
      const res2 = await syncLeaveDataSet.submit();
      const res3 = await syncDepartmentDataSet.submit();
      if (res1) {
        syncUserDataSet.query();
      }
      if (res2) {
        syncLeaveDataSet.query();
      }
      if (res3) {
        syncDepartmentDataSet.query();
      }
    }
    setDisabledEdit(!disabledEdit);
  };
  const extraSaveBtn = (
    <>
      <Button
        key="save"
        funcType="raised"
        id="yq-test-iam-ldap_save"
        color="primary"
        onClick={handSave}
      >
        {intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}
      </Button>
      <Button
        key="cancel"
        id="yq-test-iam-ldap_cancel"
        onClick={() => {
          ldapDataSet.reset();
          syncUserDataSet.reset();
          syncLeaveDataSet.reset();
          syncDepartmentDataSet.reset();
          setDisabledEdit(!disabledEdit);
        }}
      >
        {intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}
      </Button>
    </>
  );

  async function getPublicKey() {
    try {
      const res = await axios.get('hpfm/v1/tool/pass/public-key');
      if (res.failed) {
        message.error(res.message);
      }
      publicKeyStore.setPublicKey(res.publicKey);
    } catch (err) {
      message.error(err.message);
    }
  }
  useEffect(() => {
    getPublicKey();
  }, []);

  const actionsList = useMemo(() => {
    return [
      {
        icon: ldapDataSet.current?.get('enabled') ? 'reduce-one' : 'check-one',
        name: `${intl.formatMessage({ id: ldapDataSet.current?.get('enabled') ? 'iam.common.action.stop' : 'zknow.common.button.enable', defaultMessage: ldapDataSet.current?.get('enabled') ? '停用' : '启用' })}LDAP`,
        onClick: () => handleToggleLDAP(),
      },
      {
        icon: 'reload',
        name: `${intl.formatMessage({ id: 'iam.ldap.action.sync.hand', defaultMessage: '手动同步' })}`,
        onClick: () => handleSync(),
      },
      {
        icon: 'log',
        name: `${intl.formatMessage({ id: 'iam.ldap.action.watchHistory', defaultMessage: '查看同步记录' })}`,
        onClick: () => handleHistory(),
      },
    ];
  }, [ldapDataSet?.current]);

  return (
    <div className={`${prefixCls}-config`}>
      <Header actionsList={actionsList} dataSet={ldapDataSet}>
        <h1>{intl.formatMessage({ id: 'iam.ldap.title.setting', defaultMessage: 'LDAP设置' })}</h1>
        <div>{disabledEdit ? extraBtn : extraSaveBtn}</div>
      </Header>
      <div className={`${prefixCls}-config-body`}>
        <Form disabled={disabledEdit} header={intl.formatMessage({ id: 'iam.ldap.desc.serverInfo', defaultMessage: '服务器信息' })} columns={2} labelLayout="horizontal" dataSet={ldapDataSet}>
          <SelectBox name="directoryType" colSpan={2} />
          <TextField name="serverAddress" newLine />
          <TextField name="port" />
          <TextField name="baseDn" />
          <SelectBox renderer={disabledEdit ? ({ value }) => intl.formatMessage({ id: value ? 'zknow.common.status.yes' : 'zknow.common.status.no', defaultMessage: value ? '是' : '否' }) : undefined} name="useSSL">
            <SelectBox.Option value>{intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' })}</SelectBox.Option>
            <SelectBox.Option value={false}>{intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' })}</SelectBox.Option>
          </SelectBox>
          <TextField name="account" />
          <Password renderer={disabledEdit ? ({ value }) => '******' : undefined} name="ldapPassword" />
        </Form>
        <Form disabled={disabledEdit} header={intl.formatMessage({ id: 'iam.ldap.desc.serverRule', defaultMessage: '字段匹配规则' })} columns={2} labelLayout="horizontal" dataSet={ldapDataSet}>
          <TextField name="objectClass" />
          <TextField name="uuidField" />
          <TextField name="loginNameField" />
          <TextField name="realNameField" />
          <TextField name="emailField" />
          <TextField name="phoneField" />
          <TextArea name="customFilter" />
        </Form>
        <Form disabled={disabledEdit} header={intl.formatMessage({ id: 'iam.ldap.desc.departmentRule', defaultMessage: '部门匹配规则' })} columns={2} labelLayout="horizontal" dataSet={ldapDataSet}>
          <Switch name="departmentSyncEnabled" />
          <Select name="departmentClass" />
          <TextField name="departmentNameField" />
          {ldapDataSet?.current?.get('departmentClass') !== 'organizationalUnit'
            ? [
              <TextField name="departmentNameDelimiter" />,
              <Select name="departmentNameDelimiterOrder" />,
            ] : <TextField name="departmentCodeField" />}
          <TextArea name="departmentFilter" />
        </Form>
        <Sync disabledEdit={disabledEdit} />
      </div>
    </div>
  );
}

export default observer(ListView);
