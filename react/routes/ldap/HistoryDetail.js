import React, { useEffect, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { axios } from '@yqcloud/apps-master';
import { DataSet } from 'choerodon-ui/pro';
import moment from 'moment';
import { TableHoverAction, Icon as YQIcon } from '@zknow/components';
import { Form, Output, message, Table, Button, TextArea, Switch, Select, Date, DatePicker } from 'choerodon-ui/pro';

export default observer(({ context, ldapHistoryDataSet, renderTime, renderStatus }) => {
  const { intl, intlPrefix, prefixCls, organizationId, ldapDataSet } = context;

  return (
    <div>
      <Form labelLayout="horizontal" dataSet={ldapHistoryDataSet}>
        <Output name="syncBeginTime" />
        <Output name="syncEndTime" />
        <Output name="syncType" />
        <Output label={intl.formatMessage({ id: 'iam.ldap.desc.time', defaultMessage: '时长' })} renderer={renderTime} />
        <Output name="syncStatusFlag" renderer={renderStatus} />
        <Output name="log" label={intl.formatMessage({ id: 'iam.ldap.desc.historyLog', defaultMessage: '同步日志' })} />
      </Form>
    </div>
  );
});
