import React, { useEffect, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { axios } from '@yqcloud/apps-master';
import { DataSet, CodeArea } from 'choerodon-ui/pro';
import moment from 'moment';
import { TableHoverAction, Icon as YQIcon, ClickText, withErrorBoundary, StatusTag } from '@zknow/components';
import { Modal, TextField, message, Table, TextArea, Switch, Select, Date, DatePicker } from 'choerodon-ui/pro';
import LdapHistoryDataSet from './stores/LdapHistoryDataSet';
import HistoryDetail from './HistoryDetail';

const { Column } = Table;
export default withErrorBoundary(observer(({ context }) => {
  const { intl, intlPrefix, prefixCls, organizationId, ldapDataSet } = context;
  const ldapHistoryDataSet = useMemo(() => new DataSet(LdapHistoryDataSet({ intlPrefix, intl, organizationId, ldapDataSet })), [ldapDataSet, organizationId]);
  useEffect(() => {
    if (ldapDataSet?.current?.get('id')) {
      ldapHistoryDataSet.query();
    }
  }, [ldapDataSet?.current]);

  function renderQueryBar({ dataSet, querydataSet, buttons: _buttons }) {
    return null;
    // return (
    //   <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
    //     <TextField onEnterDown={() => dataSet.query()} dataSet={dataSet.queryDataSet} name="condition" placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })} />
    //     <div>
    //       {_buttons}
    //     </div>
    //   </div>
    // );
  }
  function renderTime({ record }) {
    if (record.get('syncEndTime')) {
      return `${moment(record.get('syncEndTime')).diff(moment(record.get('syncBeginTime')), 'minute')}${intl.formatMessage({ id: 'iam.ldap.unit.minutes', defaultMessage: '分钟' })}`;
    }
  }
  function renderStatus({ record, value, text, name }) {
    const field = record.getField(name);
    const color = field.options.find(r => r.get('value') === value)?.get('color');
    return (
      <StatusTag color={color}>{text}</StatusTag>
    );
  }

  const openLog = (data) => {
    Modal.open({
      key: 'ldap-syncLog-modal',
      title: intl.formatMessage({ id: 'iam.ldap.model.syncLog', defaultMessage: '错误日志' }),
      style: { width: 800 },
      children: (
        <CodeArea disabled defaultValue={data} style={{ height: '100%' }} />
      ),
      drawer: true,
      footer: null,
    });
  };

  const renderSyncLog = ({ value, record }) => {
    if (!value) {
      return '-';
    } else {
      return (
        <ClickText
          onClick={() => openLog(value)}
        >
          查看
        </ClickText>
      );
    }
  };

  function handleDetail(record) {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.ldap.desc.historyLog', defaultMessage: '同步日志' }),
      children: <HistoryDetail renderStatus={renderStatus} renderTime={renderTime} context={context} ldapHistoryDataSet={ldapHistoryDataSet} />,
      footer: null,
      style: { height: 500 },
      closable: true,
    });
  }
  function renderTableAction({ record }) {
    return (
      <TableHoverAction
        actions={[
          {
            name: intl.formatMessage({ id: 'iam.common.action.watch', defaultMessage: '查看' }),
            // icon: 'icon-Expires',
            onClick: () => handleDetail(record),
          },
        ]}
      />
    );
  }

  return (
    <div>
      <Table
        dataSet={ldapHistoryDataSet}
        queryBar={renderQueryBar}
      >
        <Column width={200} name="syncBeginTime" />
        <Column width={200} name="syncEndTime" />
        <Column header={intl.formatMessage({ id: 'iam.ldap.desc.timeMinite', defaultMessage: '时长/分钟' })} renderer={renderTime} />
        <Column name="status" renderer={renderStatus} />
        <Column name="syncLog" renderer={renderSyncLog} />
        {/* <Column width={0} renderer={renderTableAction} name="status" /> */}
      </Table>
    </div>
  );
}));
