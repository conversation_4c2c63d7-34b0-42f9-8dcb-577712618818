import React, { useContext, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, Button, TextArea, Switch, Select, Date, DateTimePicker as DatePicker, Output } from 'choerodon-ui/pro';
import Store from './stores';

export default observer(({
  disabledEdit,
}) => {
  const context = useContext(Store);
  const { syncLeaveDataSet, syncUserDataSet, syncDepartmentDataSet, intl, intlPrefix, prefixCls, organizationId, ldapDataSet } = context;

  const ISwitch = disabledEdit ? Output : Switch;
  const ISelect = disabledEdit ? Output : Select;
  const ITextArea = disabledEdit ? Output : TextArea;
  const IDatePicker = disabledEdit ? Output : DatePicker;

  return (
    <React.Fragment>
      <Form header={intl.formatMessage({ id: 'iam.ldap.desc.scheduleSync', defaultMessage: '定时同步' })} columns={2} labelLayout="horizontal" dataSet={syncUserDataSet} disabled={disabledEdit}>
        <Switch className={`${prefixCls}-switch`} renderer={disabledEdit ? ({ value }) => intl.formatMessage({ id: value ? 'zknow.common.status.yes' : 'zknow.common.status.no', defaultMessage: value ? '是' : '否' }) : undefined} name="enabledFlag" />
        {syncUserDataSet.current?.get('enabledFlag') && [
          <IDatePicker
            newLine
            name="startDate"
            id="yq-test-iam-ldap-sync_start"
          />,
          <DatePicker
            name="endDate"
            id="yq-test-iam-ldap-sync_end"
          />,
          <Select name="frequency" />,
          <TextArea label={intl.formatMessage({ id: 'iam.ldap.desc.syncCustomFilter', defaultMessage: '同步筛选条件' })} name="customFilter" />,
        ]}
      </Form>
      <Form columns={2} labelLayout="horizontal" dataSet={syncLeaveDataSet} disabled={disabledEdit}>
        <Switch className={`${prefixCls}-switch`} renderer={disabledEdit ? ({ value }) => intl.formatMessage({ id: value ? 'zknow.common.status.yes' : 'zknow.common.status.no', defaultMessage: value ? '是' : '否' }) : undefined} name="enabledFlag" />
        {syncLeaveDataSet.current?.get('enabledFlag') && [
          <DatePicker
            newLine
            name="startDate"
            id="yq-test-iam-ldap-sync_start1"
          />,
          <DatePicker
            name="endDate"
            id="yq-test-iam-ldap-sync_end1"
          />,
          <Select name="frequency" />,
          <TextArea label={intl.formatMessage({ id: 'iam.ldap.desc.leaveCustomFilter', defaultMessage: '离职筛选条件' })} name="customFilter" record={syncLeaveDataSet.current} />,
        ]}
      </Form>
      <Form columns={2} labelLayout="horizontal" dataSet={syncDepartmentDataSet} disabled={disabledEdit}>
        <Switch className={`${prefixCls}-switch`} renderer={disabledEdit ? ({ value }) => intl.formatMessage({ id: value ? 'zknow.common.status.yes' : 'zknow.common.status.no', defaultMessage: value ? '是' : '否' }) : undefined} name="enabledFlag" />
        {syncDepartmentDataSet.current?.get('enabledFlag') && [
          <DatePicker
            newLine
            name="startDate"
            id="yq-test-iam-ldap-sync_start2"
          />,
          <DatePicker
            name="endDate"
            id="yq-test-iam-ldap-sync_end2"
          />,
          <Select name="frequency" />,
          <Switch name="syncPersonDepartmentFlag" record={syncDepartmentDataSet.current} />,
        ]}
      </Form>
    </React.Fragment>

  );
});
