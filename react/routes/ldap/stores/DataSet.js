import { DataSet, message } from 'choerodon-ui/pro';
import JSEncrypt from 'jsencrypt';

function encryptPwd(password, publicKey) {
  if (publicKey) {
    // 初始化加密器
    const encrypt = new JSEncrypt(); // 设置公钥

    encrypt.setPublicKey(publicKey); // 加密

    return encrypt.encrypt(password);
  } else {
    return password;
  }
}

export default ({ publicKeyStore, intlPrefix, intl, organizationId = 1 }) => {
  const url = `/iam/yqc/${organizationId}/ldaps`;
  const account = intl.formatMessage({ id: 'iam.ldap.model.account', defaultMessage: '管理员帐号' });
  const baseDn = intl.formatMessage({ id: 'iam.ldap.model.baseDn', defaultMessage: '基准DN' });
  const description = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const typeOptionDs = new DataSet({
    data: [
      { meaning: 'OpenLDAP', value: 'OpenLDAP' },
      { meaning: 'Microsoft Active Directory', value: 'Microsoft Active Directory' },
    ],
  });

  const orderOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.ldap.map.order.frontToBack', defaultMessage: '从前往后' }), value: 'FRONT_TO_BACK' },
      { meaning: intl.formatMessage({ id: 'iam.ldap.map.order.BackToFront', defaultMessage: '从后往前' }), value: 'BACK_TO_FRONT' },
    ],
  });

  const classOptions = new DataSet({
    data: [
      { meaning: 'Organizational Unit', value: 'organizationalUnit' },
      { meaning: 'User object class', value: null },
    ],
  });

  return {
    autoQuery: true,
    selection: false,
    autoCreate: true,
    transport: {
      read: {
        url,
        method: 'get',
      },
      create: ({ data: [data] }) => ({
        url,
        method: 'post',
        data,
      }),
      update: ({ data: [data], dataSet }) => {
        let _url = url;
        if (dataSet.current?.get('enabled') !== dataSet.current?.getPristineValue('enabled')) {
          if (dataSet.current?.get('enabled')) {
            _url = `iam/yqc/${organizationId}/ldaps/enable`;
          } else {
            _url = `iam/yqc/${organizationId}/ldaps/disable`;
          }
        }
        return {
          url: _url,
          method: 'put',
          data,
        };
      },
      destroy: ({ data: [data] }) => ({
        url: `${url}/${data.id}`,
        method: 'delete',
      }),
    },
    fields: [
      { name: 'directoryType', required: true, type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.type', defaultMessage: '类型' }), options: typeOptionDs, defaultValue: 'OpenLDAP' },
      { name: 'account', type: 'string', label: account, required: true },
      { name: 'baseDn', type: 'string', label: baseDn, required: true },
      { name: 'connectionTimeout', type: 'string', defaultValue: 10 },
      { name: 'customFilter', type: 'string', defaultValue: '', label: intl.formatMessage({ id: 'iam.ldap.model.customFilter', defaultMessage: '过滤条件' }) },
      { name: 'emailField', label: intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' }), type: 'string', required: true },
      { name: 'phoneField', label: intl.formatMessage({ id: 'iam.common.model.phone', defaultMessage: '手机号' }) },
      { name: 'enabled', type: 'boolean', defaultValue: false },
      { name: 'ldapPassword', label: intl.formatMessage({ id: 'iam.ldap.model.ldapPassword', defaultMessage: '管理员密码' }), type: 'string', required: true },
      { name: 'loginNameField', label: intl.formatMessage({ id: 'iam.common.model.loginName', defaultMessage: '登录名' }), type: 'string', required: true },
      { name: 'name', label: intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' }), type: 'string', defaultValue: organizationId },
      { name: 'objectClass', label: intl.formatMessage({ id: 'iam.ldap.model.objectClass', defaultMessage: '用户对象类' }), type: 'string', required: true },
      { name: 'port', label: intl.formatMessage({ id: 'iam.ldap.model.port', defaultMessage: '端口' }), type: 'string', required: true },
      { name: 'realNameField', label: intl.formatMessage({ id: 'iam.common.model.realName', defaultMessage: '姓名' }), type: 'string', required: true },
      { name: 'serverAddress', label: intl.formatMessage({ id: 'iam.ldap.model.serverAddress', defaultMessage: '服务器地址' }), type: 'string', required: true },
      { name: 'useSSL', label: intl.formatMessage({ id: 'iam.ldap.model.useSSL', defaultMessage: 'SSL连接' }), type: 'boolean', defaultValue: false, required: true },
      { name: 'uuidField', label: intl.formatMessage({ id: 'iam.ldap.model.uuidField', defaultMessage: '用户唯一标识' }), type: 'string', required: true },
      { name: 'organizationId', type: 'string', defaultValue: organizationId },
      { name: 'sagaBatchSize', type: 'number', defaultValue: 50 },
      { name: 'description', type: 'string', label: description },
      { name: 'departmentSyncEnabled', type: 'boolean', label: intl.formatMessage({ id: 'iam.ldap.model.departmentSyncEnabled', defaultMessage: '同步组织架构' }), defaultValue: false },
      { name: 'departmentClass', type: 'string', label: intl.formatMessage({ id: 'iam.ldap.model.departmentClass', defaultMessage: '部门对象类' }), defaultValue: 'organizationalUnit', options: classOptions },
      { name: 'departmentNameField', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' }) },
      { name: 'departmentNameDelimiter', type: 'string', label: intl.formatMessage({ id: 'iam.ldap.model.separator', defaultMessage: '名称分隔符' }) },
      { name: 'departmentNameDelimiterOrder', type: 'string', label: intl.formatMessage({ id: 'iam.ldap.model.departmentNameDelimiterOrder', defaultMessage: '遍历顺序' }), options: orderOptions },
      { name: 'departmentCodeField', type: 'boolean', label: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }) },
      { name: 'departmentFilter', type: 'string', defaultValue: '', label: intl.formatMessage({ id: 'iam.ldap.model.customFilter', defaultMessage: '过滤条件' }) },
    ],
    feedback: {
      submitSuccess: (res, test) => {
        if (res?.content[0]?.config?.url?.includes('enable')) {
          message.success(intl.formatMessage({ id: 'iam.common.success.enable', defaultMessage: '生效成功' }));
        } else if (res?.content[0]?.config?.url?.includes('disable')) {
          message.success(intl.formatMessage({ id: 'iam.common.success.disable', defaultMessage: '失效成功' }));
        } else {
          message.success(intl.formatMessage({ id: 'iam.common.success.submit', defaultMessage: '提交成功' }));
        }
      },
    },
    events: {
      load: ({ dataSet }) => {
        if (!dataSet.current?.get('directoryType')) {
          dataSet.create();
        }
      },
      submit: ({ dataSet, data: [data] }) => {
        const encryptPass = publicKeyStore.encryptPwd(data.ldapPassword);
        data.ldapPassword = encryptPass;
        return [data];
      },
    },
  };
};
