import { useLocalStore } from 'mobx-react-lite';
import JSEncrypt from 'jsencrypt';

export default function useStore() {
  return useLocalStore(() => ({
    publicKey: '',
    setPublicKey(data) {
      this.publicKey = data;
    },
    get getPublicKey() {
      return this.v;
    },
    encryptPwd(password) {
      if (this.publicKey) {
        // 初始化加密器
        const encrypt = new JSEncrypt(); // 设置公钥
    
        encrypt.setPublicKey(this.publicKey); // 加密
    
        return encrypt.encrypt(password);
      } else {
        return password;
      }
    },
  }));
}
