import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { axios } from '@yqcloud/apps-master';
import { formatterCollections } from '@zknow/utils';
import LdapDataSet from './DataSet';
import SyncDataSet from './SyncDataSet';
import LdapHistoryDataSet from './LdapHistoryDataSet';
import usePublicKeyStore from './usePublicKeyStore';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: ['iam.common', 'iam.ldap'] })(injectIntl(
  (props) => {
    // FIXME: 现在 organizationId 与 tenantId 同值，但是现在系统中应该是没有 organizationId 概念的
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId } },
    } = props;
    axios.get('hpfm/v1/tool/pass/public-key');
    const intlPrefix = 'yqcloud.iam.ldap';
    const prefixCls = 'yqcloud-iam-ldap';
    const publicKeyStore = usePublicKeyStore();
    const ldapDataSet = useMemo(() => new DataSet(LdapDataSet({ publicKeyStore, intlPrefix, intl, organizationId })), [organizationId]);
    const syncLeaveDataSet = useMemo(() => new DataSet(SyncDataSet({ intlPrefix, intl, organizationId, ldapDataSet, type: 'leave' })), [organizationId]);
    const syncUserDataSet = useMemo(() => new DataSet(SyncDataSet({ intlPrefix, intl, organizationId, ldapDataSet, type: 'user' })), [organizationId]);
    const syncDepartmentDataSet = useMemo(() => new DataSet(SyncDataSet({ intlPrefix, intl, organizationId, ldapDataSet, type: 'department' })), [organizationId]);
    
    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      ldapDataSet,
      syncUserDataSet,
      syncDepartmentDataSet,
      organizationId,
      syncLeaveDataSet,
      publicKeyStore,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
