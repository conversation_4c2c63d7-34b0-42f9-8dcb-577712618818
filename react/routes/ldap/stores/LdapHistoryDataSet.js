import { getQueryParams } from '@zknow/utils';

export default ({ intlPrefix, intl, organizationId = 1, ldapDataSet }) => {
  const url = `/iam/yqc/${organizationId}/ldaps/${ldapDataSet.current?.get('id')}/history`;

  return {
    autoQuery: false,
    selection: false,
    autoCreate: false,
    transport: {
      read: ({ data }) => ({
        url,
        method: 'get',
        data: getQueryParams(data),
      }),
    },
    fields: [
      { name: 'syncBeginTime', required: true, type: 'datetime', label: intl.formatMessage({ id: 'zknow.common.model.startTime', defaultMessage: '开始时间' }) },
      { name: 'syncEndTime', type: 'datetime', label: intl.formatMessage({ id: 'zknow.common.model.endTime', defaultMessage: '结束时间' }) },
      { name: 'syncStatusFlag', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' }) },
      {
        name: 'status',
        type: 'string',
        label: intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' }),
        lookupCode: 'LDAP_STATUS',
      },
      { name: 'syncType', type: 'string', label: intl.formatMessage({ id: 'iam.ldap.model.syncType', defaultMessage: '同步类型' }) },
      { name: 'syncLog', type: 'string', label: intl.formatMessage({ id: 'iam.ldap.model.syncLog', defaultMessage: '错误日志' }) },
    ],
  };
};
