import { message } from 'choerodon-ui/pro';

export default ({ type, intlPrefix, intl, organizationId = 1, ldapDataSet }) => {
  const url = `/iam/yqc/${organizationId}/ldaps/sync-user-config?organizationId=${organizationId}`;

  const asyncDataMap = {
    user: {
      syncType: 'SYNC_USER',
      intl: intl.formatMessage({ id: 'iam.ldap.model.enabledFlag', defaultMessage: '定时同步成员' }),
    },
    leave: {
      syncType: 'SYNC_LEAVE',
      intl: intl.formatMessage({ id: 'iam.ldap.model.enabledFlag.leave', defaultMessage: '定时同步离职成员' }),
    },
    department: {
      syncType: 'SYNC_DEPATRTMENT',
      intl: intl.formatMessage({ id: 'iam.ldap.model.enabledFlag.department', defaultMessage: '定时同步部门' }),
    },
  };

  return {
    autoQuery: true,
    selection: false,
    autoCreate: true,
    dataKey: null,
    transport: {
      read: () => ({
        url,
        method: 'get',
        transformResponse: (data) => {
          const originData = JSON.parse(data);
          return originData.filter(d => d.syncType === asyncDataMap[type].syncType);
        },
      }),
      create: ({ data: [data] }) => ({
        url,
        method: 'post',
        data: [{
          ldapId: ldapDataSet.current?.get('id'),
          syncType: asyncDataMap[type].syncType,
          ...data,
        }],
      }),
      update: ({ data: [data] }) => ({
        url,
        method: 'post',
        data: [{
          ldapId: ldapDataSet.current?.get('id'),
          syncType: asyncDataMap[type].syncType,
          ...data,
        }],
      }),
      destroy: ({ data: [data] }) => ({
        url: `${url}/${data.id}`,
        method: 'delete',
      }),
    },
    fields: [
      { name: 'ldapId', defaultValue: ldapDataSet.current?.get('id') },
      {
        name: 'enabledFlag',
        required: true,
        type: 'boolean',
        label: asyncDataMap[type].intl,
        trueValue: 1,
        falseValue: 0,
      },
      {
        name: 'frequency',
        lookupCode: 'SYNC_JOB_INTERVAL',
        required: true,
        type: 'string',
        label: intl.formatMessage({ id: 'iam.ldap.model.frequency', defaultMessage: '同步间隔' }),
        defaultValue: 'DAY',
      },
      { name: 'startDate', required: true, type: 'datetime', label: intl.formatMessage({ id: 'zknow.common.model.startTime', defaultMessage: '开始时间' }) },
      { name: 'endDate', type: 'datetime', label: intl.formatMessage({ id: 'zknow.common.model.endTime', defaultMessage: '结束时间' }) },
      { name: 'customFilter', type: 'string' },
      { name: 'syncPersonDepartmentFlag', type: 'boolean', label: intl.formatMessage({ id: 'iam.ldap.model.syncPersonDepartmentFlag', defaultMessage: '更新人员部门' }) },
    ],
    events: {
      update: ({ dataSet, record, name, value, oldValue }) => {
        // debugger;
        if (value && name === 'enabledFlag' && !ldapDataSet.current?.get('enabled')) {
          message.info(intl.formatMessage({ id: 'iam.ldap.tips.needEnableLdap', defaultMessage: '开启定时同步，请先启用LDAP' }));
          record.set(name, false);
        }
      },
    },
    feedback: {
      submitSuccess: () => {
        message.success(intl.formatMessage({ id: `iam.ldap.success.saveSync${type ? 'Leave' : ''}`, defaultMessage: type ? '保存定时同步离职成员成功' : '保存定时同步成员成功' }));
      },
    },
  };
};
