import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Tabs } from 'choerodon-ui';
import { Content, TabPage } from '@yqcloud/apps-master';

import Config from './Config';
import Store from './stores';
import './index.less';

const TabPane = Tabs.TabPane;

const MainView = () => {
  const { intl, intlPrefix, prefixCls } = useContext(Store);

  return (
    <TabPage>
      <Content className={prefixCls} style={{ padding: 0 }}>
        <Config />
      </Content>
    </TabPage>

  );
};

export default observer(MainView);
