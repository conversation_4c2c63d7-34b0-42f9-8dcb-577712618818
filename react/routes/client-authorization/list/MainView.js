import React, { useContext, useMemo, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { TabPage, Content, axios } from '@yqcloud/apps-master';
import { Table, Modal, message } from 'choerodon-ui/pro';
import { TableStatus, ClickText, TableHoverAction, Icon, Button } from '@zknow/components';
import FormView from './FormView';
import Store from './store';
import './index.less';

const { Column } = Table;
const modalKey = Modal.key();

export default observer(() => {
  const { clientDataSet, intl, tenantId, history } = useContext(Store);

  // 头部按钮组
  const buttons = useMemo(() => (
    [
      <Button icon="plus" funcType="raised" color="primary" onClick={() => handleCreate({ type: 'create' })}>
        {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
      </Button>,
    ]
  ), []);

  const openModal = (type) => {
    Modal.open({
      key: modalKey,
      title: intl.formatMessage({ id: 'iam.client.list.create.title', defaultMessage: '新建客户端授权' }),
      drawer: false,
      children: (
        <FormView
          intl={intl}
          dataSet={clientDataSet}
          record={clientDataSet?.current}
          tenantId={tenantId}
        />
      ),
      destroyOnClose: true,
    });
  };

  const handleCreate = async ({ type }) => {
    if (type === 'create') {
      try {
        const res = await axios.get('/iam/yqc/client-info/generation');
        if (res.failed) {
          message.error(res?.message);
        } else {
          const newClient = {
            name: res?.name,
            secret: res?.secret,
            accessTokenValidity: 3600,
          };
          clientDataSet.create(newClient);
        }
      } catch (e) {
        message.error(e?.message);
      }
    }
    openModal(type);
  };

  const renderStatus = ({ record }) => {
    const flag = record.get('enabledFlag');
    return (
      <TableStatus
        status={flag === 1}
        enabledText={intl.formatMessage({ id: 'zknow.common.button.enable', defaultMessage: '启用' })}
        disabledText={intl.formatMessage({ id: 'zknow.common.button.deactivated', defaultMessage: '停用' })}
      />
    );
  };

  const renderName = ({ record }) => {
    return (
      <ClickText
        record={record}
        history={history}
        path={`/iam/client/detail/${record.get('id')}`}
        valueField="name"
      />
    );
  };

  const Sercet = ({ record }) => {
    const [isPerview, setPreview] = useState(false);
    return (
      <div>
        <span style={{ marginRight: 8 }}>{isPerview ? record.get('secret') : '********'}</span>
        {
          isPerview
            ? <Icon type="preview-close-one" style={{ verticalAlign: 'sub' }} className="iam-client-icon" onClick={() => setPreview(!isPerview)} />
            : <Icon type="preview-open" className="iam-client-icon" onClick={() => setPreview(!isPerview)} />
        }
      </div>
    );
  };

  async function handleEnabled(record) {
    record.set('enabledFlag', record.get('enabledFlag') === 1 ? 0 : 1);
    try {
      const res = await clientDataSet.submit();
      if (!res?.failed) {
        await clientDataSet.query();
      } else {
        message.error(res?.message);
        return false;
      }
    } catch (err) {
      return false;
    }
  }

  const renderAction = ({ record }) => {
    const actionsList = [
      {
        key: 'stop',
        name: record.get('enabledFlag') === 1 ? intl.formatMessage({ id: 'zknow.common.button.deactivated', defaultMessage: '停用' }) : intl.formatMessage({ id: 'zknow.common.button.enable', defaultMessage: '启用' }),
        icon: record?.get('enabledFlag') === 1 ? 'reduce-one' : 'check-one',
        onClick: () => handleEnabled(record),
      },
    ];
    return (
      <TableHoverAction
        record={record}
        actions={record.get('scope') === 'default' ? [] : actionsList}
      />
    );
  };

  return (
    <TabPage>
      <Content style={{ padding: 0 }}>
        <Table
          dataSet={clientDataSet}
          pristine
          autoHeight
          buttons={buttons}
          queryBarProps={{
            fuzzyQuery: false,
          }}
        >
          <Column name="name" renderer={renderName} />
          <Column name="secret" renderer={({ record }) => <Sercet record={record} />} />
          <Column name="accessTokenValidity" align="left" renderer={({ record }) => (intl.formatMessage({ id: 'iam.client.list.accessTokenValidity.seconds', defaultMessage: '{seconds}秒' }, { seconds: record.get('accessTokenValidity') }))} />
          <Column name="enabledFlag" renderer={renderStatus} align="left" />
          <Column width={10} renderer={renderAction} />
        </Table>
      </Content>
    </TabPage>);
});
