import React from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, NumberField } from 'choerodon-ui/pro';

export default observer(({ dataSet, intl, record, modal }) => {
  const handleOk = async () => {
    try {
      const res = await dataSet.validate();
      if (res && await dataSet.submit()) {
        dataSet.query();
        return true;
      } else {
        return false;
      }
    } catch (err) {
      return false;
    }
  };
  modal.handleOk(() => handleOk());
  modal.handleCancel(() => {
    dataSet.reset();
  });
  return (
    <Form labelLayout="horizontal" dataSet={dataSet} record={record}>
      <TextField name="name" disabled />
      <TextField name="secret" autoFocus />
      <NumberField name="accessTokenValidity" step={1} min={0} addonAfter={intl.formatMessage({ id: 'zknow.common.model.second', defaultMessage: '秒' })} />
    </Form>
  );
});
