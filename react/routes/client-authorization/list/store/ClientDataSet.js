import { getQueryParams } from '@zknow/utils';

export default ({ intl, tenantId }) => {
  const url = `/iam/yqc/${tenantId}/clients`;

  return {
    autoQuery: true,
    autoLocateFirst: false,
    selection: false,
    pageSize: 20,
    paging: 'server',
    idField: 'id',
    parentField: 'parentId',
    primaryKey: 'id',
    transport: {
      read: ({ data }) => ({
        url: `${url}/page`,
        method: 'get',
        data: getQueryParams(data),
      }),
      create: ({ data: [data] }) => ({
        url: `${url}/create`,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => ({
        url: `${url}/update`,
        method: 'put',
        data,
      }),
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'name', type: 'string', label: intl.formatMessage({ id: 'iam.client.model.name', defaultMessage: '客户端ID' }), required: true },
      { name: 'secret', type: 'string', label: intl.formatMessage({ id: 'iam.client.model.secret', defaultMessage: '客户端密钥' }), required: true },
      { name: 'accessTokenValidity', type: 'number', label: intl.formatMessage({ id: 'iam.client.model.accessTokenValidity', defaultMessage: 'Token有效时长' }), required: true },
      { name: 'enabledFlag', type: 'number', label: intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' }) },
    ],
    queryFields: [
      { name: 'name', type: 'string', label: intl.formatMessage({ id: 'iam.client.model.name', defaultMessage: '客户端ID' }) },
    ],
  };
};
