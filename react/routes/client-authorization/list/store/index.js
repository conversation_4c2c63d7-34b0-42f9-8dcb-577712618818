import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import { DataSet } from 'choerodon-ui/pro';
import ClientDataSet from './ClientDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: 'iam.client' })(injectIntl((props) => {
  const {
    intl,
    children,
    AppState: { currentMenuType: { organizationId: tenantId } },
  } = props;
  const prefixCls = 'iam-client';
  const clientDataSet = useMemo(() => new DataSet(ClientDataSet({ intl, tenantId })), [tenantId]);

  const value = {
    ...props,
    prefixCls,
    intl,
    clientDataSet,
  };
  return (
    <Store.Provider value={value}>
      {children}
    </Store.Provider>
  );
})));
