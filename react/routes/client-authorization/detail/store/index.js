import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import { formatterCollections } from '@zknow/utils';
import ClientDataSet from './ClientDataSet';
import RoleDataSet from './RoleDataSet';
import RoleListDataSet from './RoleListDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: 'iam.client' })(injectIntl((props) => {
  const {
    intl,
    children,
    AppState: { currentMenuType: { organizationId: tenantId }, getUserInfo },
    match,
  } = props;
  const id = match?.params?.id;
  const prefixCls = 'iam-client';
  const clientId = getUserInfo.tenantNum;
  const clientDataSet = useMemo(() => new DataSet(ClientDataSet({ intl, tenantId, id })), [tenantId, id]);
  const roleDataSet = useMemo(() => new DataSet(RoleDataSet({ intl, tenantId, id })), [tenantId, id]);
  const roleListDataSet = useMemo(() => new DataSet(RoleListDataSet({ intl, tenantId, id, clientId })), [tenantId, id]);
  const value = {
    ...props,
    prefixCls,
    intl,
    clientDataSet,
    roleDataSet,
    roleListDataSet,
    tenantId,
    clientId,
    id,
  };
  return (
    <Store.Provider value={value}>
      {children}
    </Store.Provider>
  );
})));
