export default ({ intl, tenantId, id }) => {
  const url = `/hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}hitf-client-auths/${id}/client-roles`;

  return {
    autoQuery: !!id,
    autoLocateFirst: false,
    selection: false,
    primaryKey: 'id',
    transport: {
      read: () => ({
        url,
        method: 'get',
      }),
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'name', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' }) },
      { name: 'code', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }) },
      { name: 'description', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' }) },
    ],
    queryFields: [
      { name: 'name', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' }) },
      { name: 'code', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }) },
      { name: 'description', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' }) },
    ],
  };
};
