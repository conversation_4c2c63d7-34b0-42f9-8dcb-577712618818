import { useLocalStore } from 'mobx-react-lite';

export default function useStore() {
  return useLocalStore(() => ({
    selectIds: [],
    selectRecords: [],
    detailData: {},
    setDetailData(data) {
      this.detailData = data;
    },
    get getDetailData() {
      return this.detailData;
    },
    setSelectValue(data) {
      this.setSelectIds = data;
    },
    get getSelectValue() {
      return this.setSelectIds;
    },
    setSelectRecord(record) {
      this.selectRecords = record;
    },
    get getSelectRecords() {
      return this.selectRecords;
    },
  }));
}
