export default ({ intl, tenantId, id }) => {
  const url = `/hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}hitf-client-auths/${id}`;

  return {
    autoQuery: true,
    transport: {
      read: {
        url,
        method: 'get',
      },
      create: ({ data: [data] }) => ({
        url,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => ({
        url: `/iam/yqc/${tenantId}/clients/update`,
        method: 'put',
        data,
      }),
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'name', type: 'string', label: intl.formatMessage({ id: 'iam.client.model.name', defaultMessage: '客户端ID' }), required: true },
      { name: 'secret', type: 'string', label: intl.formatMessage({ id: 'iam.client.model.secret', defaultMessage: '客户端密钥' }), required: true },
      { name: 'accessTokenValidity', type: 'number', label: intl.formatMessage({ id: 'iam.client.model.accessTokenValidity', defaultMessage: 'Token有效时长' }), required: true },
      { name: 'enabledFlag', type: 'number', label: intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' }) },
    ],
    queryFields: [
      { name: 'name', type: 'string', label: intl.formatMessage({ id: 'iam.client.model.name', defaultMessage: '客户端ID' }) },
      { name: 'code', type: 'string' },
      { name: 'description', type: 'string' },
    ],
  };
};
