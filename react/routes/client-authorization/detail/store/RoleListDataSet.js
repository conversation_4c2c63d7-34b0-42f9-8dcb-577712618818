export default ({ intl, tenantId, id }) => {
  const url = `/hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}hitf-client-roles/roles?clientAuthId=${id}`;
  return {
    autoQuery: false,
    selection: 'multiple',
    transport: {
      read: () => ({
        url,
        method: 'get',
      }),
    },
    fields: [
      { name: 'name', label: intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' }), type: 'string' },
      { name: 'code', label: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }), type: 'string' },
      { name: 'description', label: intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' }), type: 'string' },
    ],
    queryFields: [
      { name: 'name', label: intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' }), type: 'string' },
      { name: 'code', label: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }), type: 'string' },
      // { name: 'description', label: description, type: 'string' },
    ],
  };
};
