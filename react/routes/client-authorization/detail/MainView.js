import React, { useContext, useCallback, useState, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { TabPage, Content, Header } from '@yqcloud/apps-master';
import { Table, Form, TextField, Modal, message, NumberField, Password } from 'choerodon-ui/pro';
import { Button, TableHoverAction } from '@zknow/components';
import { axios } from '@zknow/utils';
import Store from './store';
import './index.less';

const { Column } = Table;
const modalKey = Modal.key();

export default observer(() => {
  const {
    clientId, clientDataSet, prefixCls, intl, roleDataSet,
    roleListDataSet, history, id, tenantId,
  } = useContext(Store);
  const [isEdit, setEdit] = useState(false);
  const modalStyle = useMemo(() => ({ width: 800, maxHeight: 578 }), []);

  const handleSave = async () => {
    try {
      const res = await clientDataSet.submit();
      if (!res?.failed) {
        setEdit(false);
        await clientDataSet.query();
      } else {
        message.error(res?.message);
        return false;
      }
    } catch (err) {
      return false;
    }
  };

  async function handleEnabled(record) {
    record.set('enabledFlag', record.get('enabledFlag') === 1 ? 0 : 1);
    try {
      const res = await clientDataSet.submit();
      if (!res?.failed) {
        await clientDataSet.query();
      } else {
        message.error(res?.message);
        return false;
      }
    } catch (err) {
      return false;
    }
  }

  const renderButtons = useCallback(() => {
    const editBtn = [
      <Button
        funcType="raised"
        color="primary"
        icon="Write"
        onClick={() => setEdit(true)}
      >
        {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
      </Button>,
    ];

    if (clientDataSet?.current?.get('scope') !== 'default') {
      editBtn.push(<Button
        funcType="raised"
        color="default"
        icon={clientDataSet?.current?.get('enabledFlag') === 1 ? 'reduce-one' : 'check-one'}
        onClick={() => handleEnabled(clientDataSet?.current)}
      >
        {clientDataSet?.current?.get('enabledFlag') === 1 ? intl.formatMessage({ id: 'zknow.common.button.deactivated', defaultMessage: '停用' }) : intl.formatMessage({ id: 'zknow.common.button.enable', defaultMessage: '启用' })}
      </Button>);
    }

    return !isEdit ? editBtn : [
      <Button
        funcType="raised"
        color="primary"
        key="save"
        onClick={handleSave}
      >
        {intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}
      </Button>,
      <Button
        funcType="raised"
        color="default"
        key="cancel"
        onClick={() => {
          setEdit(false);
          clientDataSet.reset();
        }}
      >
        {intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}
      </Button>,
    ];
  }, [isEdit]);

  function handleDelete(record) {
    const url = `/hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}hitf-client-auths/${id}/role/batch-delete`;
    axios.delete(url, {
      data: [record.toData()],
    }).then(res => {
      if (res && res.failed) {
        message.error(res.message);
      } else {
        roleDataSet.query();
      }
    }).catch(e => {
      message.error(e.message);
    });
  }

  const renderAction = ({ record }) => {
    const actionsList = [
      {
        key: 'delete',
        name: intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' }),
        icon: 'delete',
        onClick: () => handleDelete(record),
      },
    ];
    return (
      <TableHoverAction
        record={record}
        actions={actionsList}
      />
    );
  };

  const buttons = useMemo(() => (
    [
      <Button icon="plus" funcType="raised" color="primary" onClick={() => openModal({ type: 'add' })}>
        {intl.formatMessage({ id: 'zknow.common.button.append', defaultMessage: '添加' })}
      </Button>,
    ]
  ), []);

  function getClientRoleData(role) {
    const data = {
      code: role.code,
      name: role.name,
      roleId: role.id,
      tenantId,
    };
    return data;
  }

  const openModal = () => {
    roleListDataSet.query();
    Modal.open({
      key: modalKey,
      title: intl.formatMessage({ id: 'iam.client.detail.add.role', defaultMessage: '添加角色' }),
      style: modalStyle,
      drawer: false,
      className: `${prefixCls}-modal`,
      children: (
        <div className="modal-table">
          <Table
            labelLayout="float"
            dataSet={roleListDataSet}
            pristine
            autoHeight
            autoLocateFirst={false}
            queryBarProps={{
              fuzzyQuery: false,
              simpleMode: true,
              inlineSearch: false,
              queryFieldsStyle: {
                name: {
                  width: 140,
                },
                code: {
                  width: 140,
                },
              },
            }}
          >
            <Column name="name" />
            <Column name="code" tooltip="overflow" />
            <Column name="description" />
          </Table>
        </div>
      ),
      destroyOnClose: true,
      onOk: async () => {
        const roles = roleListDataSet.selected.map(r => r?.toData());
        if (roles.length > 0) {
          try {
            const res = await axios.post(`/hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}hitf-client-auths?tenantId=${tenantId}&clientName=${clientId}`, {
              ...clientDataSet.current.toData(),
              clientRoleList: roles.map(v => getClientRoleData(v)),
              statisticsLevel: 'CLIENT',
            });
            if (!res?.failed) {
              roleDataSet.query();
              return true;
            } else {
              message.error(res.message);
            }
            return false;
          } catch (error) {
            return false;
          }
        }
        return true;
      },
    });
  };

  return (
    <TabPage>
      <Header backPath={`/iam/client${history.location?.search}`} dataSet={clientDataSet}>
        <h1>{intl.formatMessage({ id: 'iam.client.detail.title', defaultMessage: '客户端授权详情' })}</h1>
        <div>
          {renderButtons()}
        </div>
      </Header>
      <Content style={{ paddingBotton: 0 }}>
        <Form disabled={!isEdit} columns={2} labelLayout="horizontal" dataSet={clientDataSet}>
          <TextField name="name" disabled />
          <Password name="secret" disabled={clientDataSet?.current?.get('scope') === 'default'} />
          <NumberField name="accessTokenValidity" step={1} min={0} addonAfter={intl.formatMessage({ id: 'zknow.common.model.second' })} />
        </Form>
        <div style={{ height: 20 }} />
        <Table
          dataSet={roleDataSet}
          pristine
          autoHeight
          buttons={buttons}
        >
          <Column name="name" />
          <Column name="code" />
          <Column name="description" />
          <Column width={10} renderer={renderAction} tooltip="none" />
        </Table>
      </Content>
    </TabPage>);
});
