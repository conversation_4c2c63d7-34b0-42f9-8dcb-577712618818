import React from 'react';
import { CodeArea } from 'choerodon-ui/pro';
import <PERSON><PERSON>ormatter from 'choerodon-ui/pro/lib/code-area/formatters/JSFormatter';
import './index.less';

export default (props) => {
  const { data: { interfacesDetail }, data, intl } = props;
  const res = interfacesDetail;
  if (!res) {
    return (
      <div>{data.message}</div>
    );
  }
  return (
    <div className="interface-doc">
      <h1>{intl.formatMessage({ id: 'iam.interface.document.api', defaultMessage: '平台API信息' })}</h1>
      <div>
        <h2>{intl.formatMessage({ id: 'iam.interface.document.auth', defaultMessage: '认证方式' })}</h2>
        {/* {interfacesDetail?.requestMethod && <h3>{interfacesDetail?.requestMethod}</h3>} */}
      </div>
      <div>
        <h2>{intl.formatMessage({ id: 'iam.interface.document.requestMethod', defaultMessage: '请求方式' })}</h2>
        {res?.requestMethod && <h3>{res?.requestMethod}</h3>}
      </div>
      <div>
        <h2>{intl.formatMessage({ id: 'iam.interface.document.publishUrl', defaultMessage: '请求地址' })}</h2>
        {res?.publishUrl && <h3>{`${`${window._env_.API_HOST}/hitf${res?.publishUrl}`}`}</h3>}
      </div>
      <h1>{intl.formatMessage({ id: 'iam.interface.document.example', defaultMessage: '示例' })}</h1>
      <h2>{intl.formatMessage({ id: 'iam.interface.document.requestExample', defaultMessage: '请求示例' })}</h2>
      {res?.publishUrl && <CodeArea
        value={`${data?.requestDemoString.replace(res?.publishUrl, `/hitf${res?.publishUrl}`)}`}
        formatter={JSFormatter}
        options={{ theme: 'idea' }}
      />}
      <h2>{intl.formatMessage({ id: 'iam.interface.document.responseExample', defaultMessage: '响应示例' })}</h2>
      <CodeArea
        value={`public Class ApiInfo {
  private String apiVersion; // ${intl.formatMessage({ id: 'iam.interface.document.responseExample.apiVersion', defaultMessage: 'api版本' })}
  private String apiStatus; // ${intl.formatMessage({ id: 'iam.interface.document.responseExample.apiStatus', defaultMessage: 'api状态' })}
  private String apiWarnings; // ${intl.formatMessage({ id: 'iam.interface.document.responseExample.apiWarnings', defaultMessage: 'api警告信息，例如，即将过期' })}
}
public Class ResponseResult {
  private String status;
  private String message;
  private String mediaType; // ${intl.formatMessage({ id: 'iam.interface.document.responseExample.mediaType', defaultMessage: '指定payload解析格式，默认为application/json;charset=UTF-8' })}
  private String payload; // ${intl.formatMessage({ id: 'iam.interface.document.responseExample.payload', defaultMessage: '指定payload实体内容' })}
  private ApiInfo apiInfo; // ${intl.formatMessage({ id: 'iam.interface.document.responseExample.apiInfo', defaultMessage: 'api信息' })}
}`}
        options={{ theme: 'idea' }}
        formatter={JSFormatter}
      />

      <h3>{intl.formatMessage({ id: 'iam.interface.document.successExample', defaultMessage: '成功示例' })}</h3>
      <div>{intl.formatMessage({ id: 'iam.interface.document.noData', defaultMessage: '暂无' })}</div>
      <h3>{intl.formatMessage({ id: 'iam.interface.document.failureExample', defaultMessage: '失败示例' })}</h3>
      <div>{intl.formatMessage({ id: 'iam.interface.document.noData', defaultMessage: '暂无' })}</div>
      <h2>{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode', defaultMessage: 'HTTP状态码参考表' })}</h2>
      <table border="1" className="http-table">
        <tr>
          <th style={{ width: 80 }} className="status-table">{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.status', defaultMessage: '状态码' })}</th>
          <th style={{ width: 380 }} className="status-table">{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.info', defaultMessage: '含义' })}</th>
          <th className="status-table">{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.example', defaultMessage: '举例' })}</th>
        </tr>
        <tr>
          <td>1xx</td>
          <td>{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.1xx', defaultMessage: '(Informational) 信息性状态码，表示正在处理。' })}</td>
        </tr>
        <tr>
          <td>2xx</td>
          <td>{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.2xx', defaultMessage: '(Success) 成功状态码，表示请求正常。' })}</td>
          <td>
            <div>{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.2xx.200', defaultMessage: '200: ok 请求被成功处理。' })}</div>
            <div>{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.2xx.204', defaultMessage: '204: No Content 该状态码表示服务器接收到的请求已经处理完毕，但是服务器不需要返回响应体。' })}</div>
            <div>{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.2xx.206', defaultMessage: '206: Partial Content 该状态码表示客户端进行了范围请求，而服务器成功执行了这部分的GET请求。' })}</div>
          </td>
        </tr>
        <tr>
          <td>3xx</td>
          <td>{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.3xx', defaultMessage: '(Redirection) 重定向状态码，表示客户端需要进行附加操作。' })}</td>
          <td>
            <div>{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.3xx.301', defaultMessage: '301: Moved Permanently 永久性重定向。' })}</div>
            <div>{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.3xx.302', defaultMessage: '302: Found 临时性重定向' })}</div>
          </td>
        </tr>
        <tr>
          <td>4xx</td>
          <td>{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.4xx', defaultMessage: '(Client Error) 客户端错误状态码，表示服务器无法处理请求。' })}</td>
          <td>
            <div>{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.4xx.400', defaultMessage: '400: Bad Request 指出客户端请求中的语法错误。' })}</div>
            <div>{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.4xx.401', defaultMessage: '401: Unauthorized 该状态码表示发送的请求需要有认证。' })}</div>
            <div>{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.4xx.403', defaultMessage: '403: Forbidden 该状态码表明对请求资源的访问被服务器拒绝了。' })}</div>
            <div>{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.4xx.404', defaultMessage: '404: Not Found 该状态码表明服务器上无法找到指定的资源。' })}</div>
          </td>
        </tr>
        <tr>
          <td>5xx</td>
          <td>{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.5xx', defaultMessage: '(Server Error) 服务器错误状态码，表示服务器处理请求出错。' })}</td>
          <td>
            <div>{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.5xx.500', defaultMessage: '500: Internal Server Error 该状态码表明服务器端在执行请求时发生了错误。' })}</div>
            <div>{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.5xx.502', defaultMessage: '502: Bad Gateway 该状态码表明服务器网关错误。' })}</div>
            <div>{intl.formatMessage({ id: 'iam.interface.document.httpStatusCode.5xx.503', defaultMessage: '503: Service Unavailable 该状态码表明服务器暂时处于超负载或正在进行停机维护，现在无法处理请求。' })}</div>
          </td>
        </tr>
      </table>
    </div>
  );
};
