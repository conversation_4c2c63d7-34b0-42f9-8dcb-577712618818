import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Content, TabPage, axios } from '@yqcloud/apps-master';
import { Modal, Table, message } from 'choerodon-ui/pro';
import { getEnv, getCookieToken } from '@zknow/utils';
import { TableHoverAction, Button } from '@zknow/components';
import { download } from '@/utils';
import Document from './Document';
import Store from './stores';

import './index.less';

const modalKey = Modal.key();
const { Column } = Table;
function MainView() {
  const context = useContext(Store);
  const {
    intl,
    prefixCls,
    interfaceDataSet,
    isCopyTenant,
  } = context;

  const renderAction = (dataSet, record) => {
    const actions = [
      {
        name: intl.formatMessage({ id: 'iam.interface.view.document', defaultMessage: '查看文档' }),
        icon: 'ViewList',
        onClick: () => handleView(record),
      },
    ];
    return <TableHoverAction record={record} actions={actions} />;
  };

  const handleDownload = async (tenantId) => {
    try {
      const res = await axios.post(`hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}interfaces/${interfaceDataSet.current.get('interfaceId')}/export`);
      download(`${`${interfaceDataSet.current.get('interfaceName')}.doc`}`, res);
    } catch (e) {
      return false;
    }
  };

  const openModal = async (record) => {
    const interfaceCode = record?.get('interfaceCode');
    const serverCode = record?.get('serverCode');
    const namespace = record?.get('namespace');
    const tenantId = record?.get('tenantId');
    let data;
    try {
      data = await axios.get(`hitf/v1/${tenantId}/interfaces/document?interfaceCode=${interfaceCode}&namespace=${namespace}&serverCode=${serverCode}&tenantId=${tenantId}`);
      if (data?.failed) {
        message.error(data?.message);
      }
    } catch (e) {
      return false;
    }

    Modal.open({
      title: intl.formatMessage({ id: 'iam.interface.view.interfaceDocument', defaultMessage: '查看接口文档' }),
      children: (
        <div>
          <Document data={data} intl={intl} />
        </div>
      ),
      okText: intl.formatMessage({ id: 'zknow.common.button.download', defaultMessage: '下载' }),
      onOk: () => handleDownload(tenantId),
      key: modalKey,
      style: { width: 1200 },
      drawer: false,
      destroyOnClose: true,
    });
  };

  const handleView = (record) => {
    openModal(record);
  };

  const handleGoToJeeStar = () => {
    const accessToken = getCookieToken();
    const url = `${getEnv('HZERO_FRONT')}/hitf/service-guide/list?access_token=${accessToken}`;
    window.open(url, '_blank');
  };

  const goToJeeStar = () => {
    if (!isCopyTenant) {
      return (
        <Button color="secondary" funcType="raised" icon="ArrowCircleRight" onClick={handleGoToJeeStar}>
          {intl.formatMessage({ id: 'iam.interface.goto.jeestar', defaultMessage: '前往接口平台' })}
        </Button>
      );
    }
    return null;
  };

  return (
    <TabPage>
      <Content style={{ padding: 0 }}>
        <Table
          labelLayout="float"
          dataSet={interfaceDataSet}
          className={`${prefixCls}-table`}
          autoHeight
          queryFieldsLimit={20}
          buttons={[goToJeeStar()]}
          queryBarProps={{
            fuzzyQuery: false,
          }}
        >
          <Column name="interfaceName" />
          <Column name="interfaceCode" />
          <Column name="serviceCategory" renderer={({ dataSet, record }) => `${record.get('serviceCategory') === 'INTERNAL' ? intl.formatMessage({ id: 'iam.interface.model.category.inner', defaultMessage: '内部接口' }) : intl.formatMessage({ id: 'iam.interface.model.category.outer', defaultMessage: '外部接口' })}`} />
          <Column name="serverType" />
          <Column name="serverName" />
          <Column name="isPublicFlag" align="left" renderer={({ dataSet, record }) => `${record.get('isPublicFlag') ? intl.formatMessage({ id: 'zknow.common.desc.public', defaultMessage: '公开' }) : intl.formatMessage({ id: 'zknow.common.desc.private', defaultMessage: '私有' })}`} />
          <Column
            width={50}
            renderer={({ dataSet, record }) => renderAction(dataSet, record)}
            tooltip="none"
          />
        </Table>
      </Content>
    </TabPage>
  );
}

export default observer(MainView);
