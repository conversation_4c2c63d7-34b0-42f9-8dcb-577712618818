import { DataSet } from 'choerodon-ui/pro';
import { getItfSearch } from '@/utils';

export default ({ intl, tenantId }) => {
  const url = `hitf/v1/${tenantId === '0' ? '' : `${tenantId}/`}interfaces/self`;

  const interfaceOptions = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.interface.model.interfaceType.restful' }), value: 'REST' },
      // { meaning: intl.formatMessage({ id: 'SOAP' }), value: 'SOAP' },
    ],
  });

  return {
    autoQuery: true,
    paging: 'server',
    pageSize: 20,
    selection: false,
    autoLocateFirst: false,
    transport: {
      read: ({ data }) => ({
        url: `${url}`,
        method: 'get',
        data,
      }),
    },
    fields: [
      { name: 'interfaceName', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.interfaceName', defaultMessage: '接口名称' }) },
      { name: 'interfaceCode', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }), format: 'uppercase' },
      { name: 'serverName', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.serverName', defaultMessage: '服务名称' }) },
      { name: 'serviceCategory', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.category', defaultMessage: '接口分类' }) },
      { name: 'serverType', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.interfaceType', defaultMessage: '接口类型' }), options: interfaceOptions },
      { name: 'isPublicFlag', type: 'number', label: intl.formatMessage({ id: 'iam.interface.model.publicFlag', defaultMessage: '是否公开' }) },
      { name: 'status', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.status', defaultMessage: '发布状态' }) },
    ],
    queryFields: [
      { name: 'interfaceName', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.interfaceName', defaultMessage: '接口名称' }) },
      { name: 'interfaceCode', type: 'string', label: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }), format: 'uppercase' },
      { name: 'serverType', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.interfaceType', defaultMessage: '接口类型' }), options: interfaceOptions },
      { name: 'serverName', type: 'string', label: intl.formatMessage({ id: 'iam.interface.model.serverName', defaultMessage: '服务名称' }) },
    ],
    events: {
      query: ({ params, data }) => {
        getItfSearch(params);
      },
    },
  };
};
