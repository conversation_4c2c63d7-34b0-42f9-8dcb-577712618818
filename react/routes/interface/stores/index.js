import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import InterfaceDataSet from './InterfaceDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState', 'HeaderStore')(formatterCollections({ code: 'iam.interface' })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      HeaderStore: { tenantConfig: { tenantType } },
    } = props;
    const prefixCls = 'iam-interface';
    // 复制租户标识
    const isCopyTenant = tenantType === 'TRIALING';

    const interfaceDataSet = useMemo(() => new DataSet(InterfaceDataSet({ intl, tenantId })));

    const value = {
      ...props,
      prefixCls,
      interfaceDataSet,
      tenantId,
      isCopyTenant,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
