import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, TextArea, CheckBox, Output, Lov } from 'choerodon-ui/pro';
import Store from '../stores';

export default observer((props) => {
  const { isEdit } = props;
  const { formDataset, prefixCls } = useContext(Store);

  return (
    <div>
      <Form columns={2} labelLayout="horizontal" dataSet={formDataset} disabled={!isEdit}>
        <TextField autoFocus name="name" />
        <TextField name="code" disabled />
        {/* <CheckBox name="defaultFlag" /> */}
        <CheckBox name="primaryFlag" />
        <TextField name="parentName" disabled />
        <TextArea name="description" newLine colSpan={2} rows={1} autoSize={{ minRows: 1, maxRows: 4 }} resize="height" />
      </Form>
    </div>
  );
});
