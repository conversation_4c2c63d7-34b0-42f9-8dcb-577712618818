export default ({ intlPrefix, intl, tenantId, domainId }) => {
  const url = `/iam/yqc/${tenantId}/domains`;
  const name = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const code = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const status = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const desc = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const parent = intl.formatMessage({ id: 'iam.domain.model.parent', defaultMessage: '父级域' });
  const defaultFlag = intl.formatMessage({ id: 'iam.domain.model.default.flag', defaultMessage: '默认域' });
  const primaryFlag = intl.formatMessage({ id: 'iam.domain.model.primary.flag', defaultMessage: '主域' });

  return {
    autoQuery: true,
    paging: false,
    fields: [
      { name: 'name', type: 'string', label: name, required: true },
      { name: 'code', type: 'string', label: code, required: true },
      { name: 'enabledFlag', type: 'boolean', label: status },
      {
        name: 'description',
        type: 'string',
        label: desc,
        transformRequest: (value) => {
          return value || '';
        },
      },
      { name: 'defaultFlag', type: 'boolean', label: defaultFlag },
      {
        name: 'primaryFlag',
        type: 'boolean',
        label: primaryFlag,
        dynamicProps: {
          disabled: ({ record }) => record.get('parentId') && record.get('parentId') !== '0',
        },
      },
      {
        name: 'parentId',
        // bind: 'parentIdLov.id',
        type: 'string',
      },
      {
        name: 'parentName',
        label: parent,
        // bind: 'parentIdLov.name',
        type: 'string',
      },
      {
        name: 'parentIdLov',
        label: parent,
        textField: 'name',
        valueField: 'id',
        type: 'object',
        // lovCode: 'DOMAIN',
        ignore: 'always',
      },
    ],
    transport: {
      read: {
        url,
        method: 'get',
        params: {
          id: domainId,
        },
      },
      update: ({ data: [data] }) => ({
        url,
        method: 'put',
        data,
      }),
    },
    // feedback: {
    //   submitSuccess: (resp) => {},
    //   submitFailed: (error) => {},
    // },
  };
};
