import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import { formatterCollections } from '@zknow/utils';
import FormDataSet from './FormDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: 'iam.domain' })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      match,
    } = props;
    const domainId = match?.params?.id;
    const intlPrefix = 'iam.domain';
    const prefixCls = 'iam-domain-detail';
    const formDataset = useMemo(() => new DataSet(FormDataSet({ intl, intlPrefix, tenantId, domainId })), [tenantId, domainId]);

    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      intl,
      domainId,
      formDataset,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }
)));
