import React, { useState, useCallback, useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Tabs } from 'choerodon-ui';
import { message } from 'choerodon-ui/pro';
import { Button } from '@zknow/components';
import { Content, TabPage, Header } from '@yqcloud/apps-master';
import Store from './stores';
import Basic from './basic';
import CompanyList from './panes/company';
import DomainList from './panes/domain';

import './index.less';

const { TabPane } = Tabs;

function MainView() {
  const {
    intl,
    intlPrefix,
    prefixCls,
    history,
    domainId,
    formDataset,
  } = useContext(Store);
  const [isEdit, setEdit] = useState(false);

  function renderTabs() {
    const panelProps = {
      intlPrefix,
      domainId,
      prefixCls,
    };
    return (
      <Tabs type="card" className={`${prefixCls}-tabs`}>
        <TabPane tab={intl.formatMessage({ id: 'zknow.common.model.company', defaultMessage: '公司' })} key="company">
          <CompanyList {...panelProps} />
        </TabPane>
        <TabPane tab={intl.formatMessage({ id: 'iam.domain.desc.contain.domain', defaultMessage: '包含域' })} key="children">
          <DomainList {...panelProps} relation="contain" />
        </TabPane>
        <TabPane tab={intl.formatMessage({ id: 'iam.domain.desc.to.contain', defaultMessage: '被包含' })} key="parents">
          <DomainList {...panelProps} relation="containBy" />
        </TabPane>
      </Tabs>
    );
  }

  async function handleSave() {
    if (formDataset.current?.dirty) {
      await formDataset.submit();
      await formDataset.query();
    }
    setEdit(false);
  }

  const renderButtons = useCallback(() => {
    return !isEdit ? (
      <Button
        disabled={formDataset.status !== 'ready' || !formDataset?.current?.get('enabledFlag')}
        funcType="raised"
        color="primary"
        icon="Write"
        onClick={() => setEdit(true)}
      >
        {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
      </Button>
    ) : [
      <Button
        funcType="raised"
        color="primary"
        key="save"
        onClick={handleSave}
      >
        {intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}
      </Button>,
      <Button
        funcType="raised"
        color="default"
        key="cancel"
        onClick={() => {
          setEdit(false);
          formDataset.reset();
        }}
      >
        {intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}
      </Button>,
    ];
  }, [isEdit, formDataset]);

  return (
    <TabPage>
      <Header backPath={`/iam/domain${history.location?.search}`} dataSet={formDataset}>
        <h1>{intl.formatMessage({ id: 'iam.domain.desc.detail.title', defaultMessage: '域详情' })}</h1>
        <div>
          {renderButtons()}
        </div>
      </Header>
      <Content className={prefixCls}>
        <div className={`${prefixCls}-wrapper`}>
          <Basic isEdit={isEdit} />
          {renderTabs()}
        </div>
      </Content>
    </TabPage>
  );
}

export default observer(MainView);
