import { getQueryParams } from '@zknow/utils';

export default ({ intlPrefix, intl, tenantId, domainId, relation }) => {
  const name = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const code = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const url = `/iam/yqc/${tenantId}/domains`;

  return {
    autoQuery: true,
    paging: 'server',
    pageSize: 20,
    autoLocateFirst: false,
    fields: [
      { name: 'id', type: 'string' },
      { name: 'name', type: 'string', label: name, required: true },
      { name: 'code', type: 'string', label: code, required: true },
    ],
    transport: {
      read: ({ data }) => ({
        url: `${url}/${relation}?id=${domainId}`,
        method: 'get',
        data: getQueryParams(data),
      }),
      destroy: ({ data: [data] }) => ({
        url: `${url}/contain?containId=${data.containId}`,
        method: 'delete',
        data: null,
      }),
    },
    queryFields: [
      { name: 'name', type: 'string', label: name },
      { name: 'code', type: 'string', label: code },
    ],
  };
};
