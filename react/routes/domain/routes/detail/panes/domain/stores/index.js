import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import ListDataSet from './ListDataSet';
import RelationListDataSet from './RelationListDataSet';

const Store = createContext();

export default Store;

const addKeyMapper = {
  contain: 'addContain',
  containBy: 'addContainBy',
};

export const StoreProvider = injectIntl(inject('AppState')((props) => {
  const {
    children,
    intl,
    intlPrefix,
    domainId,
    relation,
    AppState: { currentMenuType: { organizationId: tenantId } },
  } = props;
  const listDataset = useMemo(() => new DataSet(ListDataSet({ intl, intlPrefix, tenantId, domainId, relation })), [tenantId, domainId, relation]);
  const relationListDataset = useMemo(() => new DataSet(RelationListDataSet({ intl, intlPrefix, tenantId, domainId })), [tenantId, domainId]);

  const value = {
    ...props,
    intlPrefix,
    intl,
    domainId,
    listDataset,
    relationListDataset,
    addKey: addKeyMapper[relation],
  };
  return (
    <Store.Provider value={value}>
      {children}
    </Store.Provider>
  );
}));
