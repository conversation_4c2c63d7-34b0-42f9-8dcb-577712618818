export default ({ intlPrefix, intl, tenantId, domainId }) => {
  const name = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const code = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });

  return {
    autoQuery: true,
    paging: 'server',
    primaryKey: 'id',
    cacheSelection: true,
    selection: 'multiple',
    pageSize: 20,
    autoLocateFirst: false,
    fields: [
      { name: 'id', type: 'string' },
      { name: 'name', type: 'string', label: name, required: true },
      { name: 'code', type: 'string', label: code, required: true },
    ],
    transport: {
      read: {
        url: `/iam/yqc/${tenantId}/domains/canContain?id=${domainId}`,
        method: 'get',
      },
    },
    queryFields: [
      { name: 'name', type: 'string', label: name },
      { name: 'code', type: 'string', label: code },
    ],
  };
};
