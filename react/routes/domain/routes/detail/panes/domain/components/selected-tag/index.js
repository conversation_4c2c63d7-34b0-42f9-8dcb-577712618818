import React from 'react';
import { Tag } from 'choerodon-ui';

export default function SelectedTag(props) {
  const { dataSet, record, onClose } = props;

  function handleClose() {
    onClose({ record, dataSet });
  }

  return (
    <Tag
      closable
      onClose={handleClose}
      style={{
        background: 'rgba(0, 0, 0, 0.05)',
        borderRadius: '2px',
        border: '1px solid rgb(216, 216, 216)',
        color: '#000',
      }}
    >
      {record.get('name')}
    </Tag>
  );
}
