import React, { useContext, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { Modal, Table } from 'choerodon-ui/pro';
import { axios } from '@yqcloud/apps-master';
import { Button, TableHoverAction, YqTable } from '@zknow/components';
import Store from './stores';
import ModalView from './modal-view';

const { Column } = Table;
const modalKey = Modal.key();

function ListView() {
  const {
    listDataset,
    relationListDataset,
    intl,
    intlPrefix,
    prefixCls,
    addKey,
    domainId,
    AppState: { currentMenuType: { organizationId: tenantId } },
  } = useContext(Store);
  const modalStyle = useRef({ width: 800 });

  function cancel() {
    relationListDataset.unSelectAll();
    relationListDataset.clearCachedSelected();
  }

  async function submit() {
    try {
      const domainIds = relationListDataset.selected.map(r => r.get('id'));
      await axios.post(`/iam/yqc/${tenantId}/domains/${addKey}?id=${domainId}`, domainIds);
      listDataset.query();
    } catch (e) {
      //
    } finally {
      cancel();
    }
  }

  function openModal() {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.domain.desc.pick', defaultMessage: '选择域' }),
      children: (
        <ModalView
          dataSet={relationListDataset}
          intl={intl}
          prefixCls={prefixCls}
        />
      ),
      key: modalKey,
      style: modalStyle.current,
      drawer: false,
      destroyOnClose: true,
      onOk: submit,
      onCancel: cancel,
    });
  }

  function renderAction({ record }) {
    return (
      <TableHoverAction
        record={record}
        actions={[
          {
            name: intl.formatMessage({ id: 'zknow.common.button.remove', defaultMessage: '移除' }),
            icon: 'delete',
            onClick: () => {
              listDataset.delete(record);
            },
          },
        ]}
      />
    );
  }

  return (
    <Table
      pristine
      dataSet={listDataset}
      selectionMode="none"
      condition="params"
      placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
      buttons={[
        <Button funcType="raised" color="primary" onClick={openModal}>
          {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
        </Button>,
      ]}
      queryBarProps={{
        title: intl.formatMessage({ id: 'iam.domain.desc.contain.domain', defaultMessage: '包含域' }),
      }}
    >
      <Column name="name" />
      <Column name="code" />
      <Column width={10} renderer={renderAction} />
    </Table>
  );
}

export default observer(ListView);
