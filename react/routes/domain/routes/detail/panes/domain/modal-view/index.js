import React from 'react';
import { observer } from 'mobx-react-lite';
import { YqTable } from '@zknow/components';
import { Table } from 'choerodon-ui/pro';
import SelectedTag from '../components/selected-tag';

import './index.less';

const { Column } = Table;

const closeTag = ({ record, dataSet }) => {
  dataSet.unSelect(record);
};

function ModalView(props) {
  const { dataSet, intl, prefixCls, intlPrefix } = props;

  return (
    <div className="modal-table">
      <Table
        pristine
        dataSet={dataSet}
        queryBarProps={{
          title: intl.formatMessage({ id: 'iam.domain.desc.pick', defaultMessage: '选择域' }),
          queryFieldsStyle: {
            name: { width: 100 },
            code: { width: 140 },
          },
        }}
      >
        <Column name="name" />
        <Column name="code" />
      </Table>
      <div className={`${prefixCls}-modal-header`}>{intl.formatMessage({ id: 'iam.domain.desc.general.selected', defaultMessage: '已选记录' })}</div>
      {dataSet.selected.map(r => (
        <SelectedTag
          record={r}
          dataSet={dataSet}
          onClose={closeTag}
        />
      ))}
    </div>
  );
}

export default observer(ModalView);
