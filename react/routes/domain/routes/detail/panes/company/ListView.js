import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Table } from 'choerodon-ui/pro';
import { YqTable } from '@zknow/components';
import Store from './stores';

const { Column } = Table;

function ListView() {
  const { listDataset, intl, intlPrefix } = useContext(Store);

  return (
    <Table
      pristine
      dataSet={listDataset}
      selectionMode="none"
      placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
      queryBarProps={{
        title: intl.formatMessage({ id: 'zknow.common.model.company', defaultMessage: '公司' }),
      }}
    >
      <Column name="companyName" />
      <Column name="companyCode" />
      <Column name="abbreviation" />
    </Table>
  );
}

export default observer(ListView);
