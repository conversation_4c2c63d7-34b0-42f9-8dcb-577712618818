import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import ListDataSet from './ListDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')((props) => {
  const {
    children,
    intl,
    intlPrefix,
    domainId,
    AppState: { currentMenuType: { organizationId: tenantId } },
  } = props;
  const listDataset = useMemo(() => new DataSet(ListDataSet({ intl, intlPrefix, tenantId, domainId })), [tenantId, domainId]);

  const value = {
    ...props,
    intlPrefix,
    intl,
    listDataset,
  };
  return (
    <Store.Provider value={value}>
      {children}
    </Store.Provider>
  );
}));
