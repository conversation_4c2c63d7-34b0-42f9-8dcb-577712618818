import { getQueryParams } from '@zknow/utils';

export default ({ intlPrefix, intl, tenantId, domainId }) => {
  const name = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const code = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const abbreviation = intl.formatMessage({ id: 'iam.domain.model.abbreviation', defaultMessage: '简称' });

  return {
    autoQuery: true,
    paging: 'server',
    selection: false,
    pageSize: 20,
    autoLocateFirst: false,
    fields: [
      { name: 'id', type: 'number' },
      { name: 'companyName', type: 'string', label: name },
      { name: 'companyCode', type: 'string', label: code },
      { name: 'abbreviation', type: 'string', label: abbreviation },
    ],
    transport: {
      read: ({ data }) => ({
        url: `/iam/yqc/${tenantId}/companies/page?domainId=${domainId}`,
        method: 'get',
        data: getQueryParams(data),
      }),
    },
    queryFields: [
      { name: 'companyName', type: 'string', label: name },
      { name: 'companyCode', type: 'string', label: code },
      { name: 'abbreviation', type: 'string', label: abbreviation },
    ],
  };
};
