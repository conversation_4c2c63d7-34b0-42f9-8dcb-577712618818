import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { TabPage, Content, axios } from '@yqcloud/apps-master';
import { Table, Modal } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { TableStatus, ClickText, YqTable, Button, TableHoverAction } from '@zknow/components';
import Store from './stores';
import FormView from './FormView';

import './index.less';

const { Column } = Table;
const modalKey = Modal.key();
const confirmModalKey = Modal.key();

function ListView() {
  const {
    listDataset,
    prefixCls,
    intl,
    intlPrefix,
    history,
    AppState: { currentMenuType: { organizationId: tenantId } },
  } = useContext(Store);

  function openModal() {
    listDataset.create({});
    Modal.open({
      title: intl.formatMessage({ id: 'iam.domain.desc.create', defaultMessage: '新建域' }),
      children: (
        <FormView
          dataSet={listDataset}
          intlPrefix={intlPrefix}
          intl={intl}
        />
      ),
      key: modalKey,
      drawer: false,
      destroyOnClose: true,
      afterClose: () => {
        if (listDataset?.current?.getState() !== 'sync') {
          listDataset.remove(listDataset.current);
        }
      },
    });
  }

  function renderName({ record, name }) {
    const defaultFlag = record.get('defaultFlag');
    const primaryFlag = record.get('primaryFlag');
    return (
      <div className={`${prefixCls}-cell`}>
        <ClickText
          record={record}
          onClick={() => {
            history.push(`/iam/domain/detail/${record.get('id')}${history.location?.search}`);
          }}
          valueField={name}
        />
        {primaryFlag && (
          <Tag>{intl.formatMessage({ id: 'iam.domain.model.primary.flag', defaultMessage: '主域' })}</Tag>
        )}
      </div>
    );
  }

  function renderEnabledFlag({ value }) {
    return (
      <TableStatus
        status={value}
        enabledText={intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' })}
        disabledText={intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' })}
      />
    );
  }

  function renderAction({ record }) {
    const saveS = intl.formatMessage({ id: 'zknow.common.success.save', defaultMessage: '保存成功' });
    const saveF = intl.formatMessage({ id: 'iam.renderer.desc.save.failed', defaultMessage: '保存失败' });
    const parentInvaildWarning = intl.formatMessage({ id: 'iam.domain.desc.parent.make.invalid.failed', defaultMessage: '请先失效下级，再进行上级的失效!' });
    const invalidConfirm = intl.formatMessage({ id: 'iam.domain.desc.make.invalid.confirm', defaultMessage: '确认失效？' });

    const enabledFlag = record.get('enabledFlag');
    const changeStatus = async () => {
      try {
        record.set('enabledFlag', !enabledFlag);
        const res = await listDataset.submit();
        if (res && !res.failed) {
          await listDataset.query();
          return true;
        } else {
          return false;
        }
      } catch (e) {
        return false;
      }
    };
    const confirm = async () => {
      if (enabledFlag) {
        await Modal.confirm({
          children: (
            <div>
              {invalidConfirm}
            </div>
          ),
          onOk: changeStatus,
          key: confirmModalKey,
          destroyOnClose: true,
        });
      } else {
        await changeStatus();
      }
    };
    return (
      <TableHoverAction
        record={record}
        actions={[
          {
            name: !enabledFlag ? intl.formatMessage({ id: 'zknow.common.action.make.valid', defaultMessage: '生效' }) : intl.formatMessage({ id: 'zknow.common.action.make.invalid', defaultMessage: '失效' }),
            icon: !enabledFlag ? 'icon-read' : 'icon-Expires',
            onClick: confirm,
          },
        ]}
      />
    );
  }

  return (
    <TabPage>
      <Content className={prefixCls} style={{ padding: 0 }}>
        <Table
          dataSet={listDataset}
          mode="tree"
          onRow={({ dataSet, record, index, expandedRow }) => {
            const isLeaf = record.get('isLeaf');
            // 隐藏临时 record
            const tempRecord = record.get('path');
            const extraStyle = !tempRecord ? {
              style: {
                display: 'none',
              },
            } : null;
            // 新建节点时由于 isLeaf 字段是 undefined，所以会显示 icon，无论是不是叶子节点
            // 设置默认不先显示 icon
            return {
              isLeaf: typeof isLeaf === 'boolean' ? isLeaf : true,
              ...extraStyle,
            };
          }}
          condition="params"
          queryFieldsLimit={20}
          placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
          className={`${prefixCls}-table`}
          treeAsync
          pristine
          autoHeight
          autoLocateFirst={false}
          buttons={[
            <Button
              funcType="raised"
              color="primary"
              onClick={openModal}
              icon="plus"
            >
              {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
            </Button>,
          ]}
          queryBarProps={{
            title: intl.formatMessage({ id: 'iam.domain.desc.menu.title.domain', defaultMessage: '域定义' }),
          }}
        >
          <Column name="name" renderer={renderName} tooltip="overflow" />
          <Column name="code" />
          <Column name="description" />
          <Column name="enabledFlag" align="left" width={160} renderer={renderEnabledFlag} />
          <Column width={10} renderer={renderAction} />
        </Table>
      </Content>
    </TabPage>
  );
}

export default observer(ListView);
