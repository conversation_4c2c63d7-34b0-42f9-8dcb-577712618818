import React from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, TextArea, CheckBox, Lov } from 'choerodon-ui/pro';

export default observer((props) => {
  const { modal, dataSet } = props;

  modal.handleOk(async () => {
    try {
      const res = await dataSet.submit();
      if (res && !res.failed) {
        await dataSet.query();
        return true;
      } else {
        return false;
      }
    } catch (err) {
      return false;
    }
  });

  return (
    <div>
      <Form labelLayout="horizontal" record={dataSet.current} labelWidth="auto">
        <TextField autoFocus name="name" />
        <TextField name="code" restrict="_|A-Z|a-z|0-9" />
        <Lov name="parentIdLov" />
        {/* <CheckBox name="defaultFlag" /> */}
        <CheckBox name="primaryFlag" />
        <TextArea name="description" newLine colSpan={2} rows={1} autoSize={{ minRows: 1, maxRows: 4 }} resize="height" />
      </Form>
    </div>
  );
});
