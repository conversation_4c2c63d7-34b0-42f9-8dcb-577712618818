import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import { formatterCollections } from '@zknow/utils';
import ListDataSet from './ListDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: 'iam.domain' })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
    } = props;
    const intlPrefix = 'iam.domain';
    const prefixCls = 'iam-domain';
    const listDataset = useMemo(() => new DataSet(ListDataSet({ intl, intlPrefix, tenantId })), [tenantId]);

    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      intl,
      listDataset,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }
)));
