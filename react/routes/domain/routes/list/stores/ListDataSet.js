import { DataSet } from 'choerodon-ui/pro';
import { Constants, getQueryParams } from '@zknow/utils';

const { CODE_REGEX } = Constants;

export default ({ intlPrefix, intl, tenantId }) => {
  const url = `/iam/yqc/${tenantId}/domains`;
  const domainName = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const code = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const status = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const desc = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const defaultFlag = intl.formatMessage({ id: 'iam.domain.model.default.flag', defaultMessage: '默认域' });
  const primaryFlag = intl.formatMessage({ id: 'iam.domain.model.primary.flag', defaultMessage: '主域' });
  const parent = intl.formatMessage({ id: 'iam.domain.model.parent', defaultMessage: '父级域' });

  const enabledFlagDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' }), value: 'true' },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' }), value: 'false' },
    ],
  });

  function codeValidator(value) {
    if (!CODE_REGEX.test(value)) {
      return intl.formatMessage({ id: 'iam.common.model.validate.message.code', defaultMessage: '编码只能由大写字母，数字和下划线构成' });
    }
  }

  return {
    autoQuery: true,
    autoLocateFirst: false,
    selection: false,
    autoQueryAfterSubmit: false,
    pageSize: 20,
    paging: 'server',
    idField: 'id',
    parentField: 'parentId',
    primaryKey: 'id',
    fields: [
      { name: 'id', type: 'string' },
      { name: 'name', type: 'string', label: domainName, required: true },
      {
        name: 'code',
        type: 'string',
        label: code,
        required: true,
        format: 'uppercase',
        validator: (value) => codeValidator(value),
      },
      { name: 'defaultFlag', type: 'boolean', label: defaultFlag },
      {
        name: 'primaryFlag',
        type: 'boolean',
        label: primaryFlag,
        dynamicProps: {
          disabled: ({ record }) => record.get('parentId'),
        },
      },
      { name: 'enabledFlag', type: 'boolean', label: status },
      { name: 'description', type: 'string', label: desc },
      {
        name: 'parentId',
        bind: 'parentIdLov.id',
        type: 'string',
      },
      {
        name: 'parentName',
        bind: 'parentIdLov.name',
        type: 'string',
      },
      {
        name: 'parentIdLov',
        label: parent,
        textField: 'name',
        valueField: 'id',
        type: 'object',
        lovCode: 'DOMAIN',
        ignore: 'always',
      },
      { name: 'isLeaf', type: 'boolean' },
    ],
    transport: {
      read: ({ data, params }) => {
        let searchFlag = false;
        Object.keys(data).forEach((v) => {
          if (v.indexOf('search_') !== -1) {
            searchFlag = true;
          }
        });
        let paramsObj = {};
        if (searchFlag) {
          paramsObj = {
            ...params,
          };
        } else {
          paramsObj = {
            ...params,
            parentId: 0,
          };
        }
        return ({
          url,
          method: 'get',
          params: paramsObj,
          data: getQueryParams(data, ['parentId']),
          transformResponse: (response) => {
            try {
              const resData = JSON.parse(response);
              if (resData && resData.failed) {
                return resData;
              } else {
                resData.content.filter((item) => (+item.parentId === 0)).forEach((root) => {
                  root.parentId = null;
                });
                return resData;
              }
            } catch (e) {
              return response;
            }
          },
        });
      },
      create: ({ data: [data] }) => ({
        url,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => ({
        url,
        method: 'put',
        data,
      }),
    },
    events: {
      update: ({ record, name, value }) => {
        if (name === 'parentIdLov') {
          record.set('primaryFlag', false);
        }
      },
    },
    queryFields: [
      { name: 'name', type: 'string', label: domainName },
      { name: 'code', type: 'string', label: code },
      { name: 'description', type: 'string', label: desc },
      { name: 'enabledFlag', type: 'string', label: status, options: enabledFlagDs },
    ],
  };
};
