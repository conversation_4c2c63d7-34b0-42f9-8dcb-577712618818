import React, { useContext, useRef, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Table, Lov, message, Modal } from 'choerodon-ui/pro';
import { axios } from '@yqcloud/apps-master';
import { Button, TableHoverAction, YqTable } from '@zknow/components';
import EnabledFlag from '@/components/enabled-flag';
import FormView from './FormView';
import Store from '../stores';

const { Column } = Table;
const modalKey = Modal.key();
const AccountList = (props) => {
  const { editable, roleId, handleRefresh, currentRole, tenantId } = props;
  const context = useContext(Store);
  const { childRolesDataSet, intl, intlPrefix, transDataSetRole } = context;
  const mdModalStyle = useMemo(() => ({ width: 800 }), []);

  function handleCreate() {
    openModal('create');
    transDataSetRole.setQueryParameter('front', 'roles');
    transDataSetRole.setQueryParameter('id', roleId);
    transDataSetRole.query().then(() => {
    });
  }

  const createButton = () => (
    <Button funcType="raised" color="primary" icon="ListAdd" onClick={handleCreate} disabled={!editable}>
      {intl.formatMessage({ id: 'zknow.common.button.append', defaultMessage: '添加' })}
    </Button>
  );

  function openModal(type) {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.role.title.rolesAdd', defaultMessage: '添加角色' }),
      children: (
        <FormView
          type={type}
          parentRoleId={roleId}
          context={context}
          dataSet={transDataSetRole}
          curDataSet={childRolesDataSet}
          front="sub-roles"
        />
      ),
      key: modalKey,
      drawer: false,
      style: mdModalStyle,
      destroyOnClose: true,
      className: 'iam-role-detail-modal',
    });
  }

  const buttons = useMemo(() => {
    if (+(currentRole?.get('tenantId')) === 0) {
      return [];
    } else {
      return createButton();
    }
  }, [editable, currentRole]);

  function handleRemove(record) {
    const urlPrefix = `/iam/yqc/${tenantId === '0' ? '' : `${tenantId}/`}role_rels/${roleId}/${record.get('id')}`;
    axios.delete(`${urlPrefix}`).then(res => {
      if (res && res.failed) {
        message.error(res.message);
      } else {
        const newData = childRolesDataSet.toData().filter((v) => v.id !== record.get('id'));
        childRolesDataSet.loadData(newData);
        childRolesDataSet.current = childRolesDataSet.get(0);
        // handleRefresh();
      }
    }).catch(e => {
      message.error(e.message);
    });
  }

  function renderAction({ record }) {
    const actions = [
      {
        name: intl.formatMessage({ id: 'iam.common.action.remove', defaultMessage: '移除' }),
        icon: 'delete',
        onClick: () => handleRemove(record),
      },
    ];
    return editable ? <TableHoverAction record={record} actions={actions} /> : null;
  }

  return (
    <div>
      <Table
        labelLayout="float"
        dataSet={childRolesDataSet}
        placeholder={intl.formatMessage({ id: 'iam.common.action.search', defaultMessage: '搜索' })}
        className="iam.role.model-table"
        buttons={[buttons]}
        queryBarProps={{
          title: intl.formatMessage({ id: 'iam.role.model.childRole', defaultMessage: '子角色' }),
        }}
      >
        <Column name="name" />
        <Column name="code" width={300} />
        <Column name="description" />
        {/* <Column name="enabled" renderer={({ value }) => <EnabledFlag enabledFlag={value} />} /> */}
        <Column width={1} renderer={renderAction} />
      </Table>
    </div>
  );
};

export default observer(AccountList);
