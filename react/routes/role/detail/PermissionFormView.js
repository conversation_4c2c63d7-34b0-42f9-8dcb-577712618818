import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, message, Lov } from 'choerodon-ui/pro';
import { ExternalComponent } from '@zknow/components';
import { axios } from '@yqcloud/apps-master';
import './PermissionFormView.less';

export default observer((props) => {
  const { dataSet, type, intl, prefixCls, intlPrefix, tenantId } = props;
  const record = dataSet.current;
  const isEdit = record?.getState('isEdit');
  const [condition, setCondition] = useState([]);
  const [fieldTableData, setTableFieldData] = useState([]);
  const businessObject = record?.get('businessObject')?.id || record?.get('businessObjectId');

  useEffect(() => {
    setCondition(JSON.parse(record?.get('condition') || '[]'));
  }, [record]);

  useEffect(() => {
    async function loadFields() {
      const res = await axios.get(`/lc/v1/${tenantId}/object_fields/all/${businessObject}?conditionFlag=true`);
      if (res?.failed) {
        message.error(res?.message);
        setTableFieldData([]);
      } else {
        setTableFieldData(res);
      }
    }
    if (businessObject) {
      loadFields();
    }
  }, [businessObject]);

  if (type !== 'modify') {
    return (
      <div className={`${prefixCls}-permission-form-wrapper`}>
        <Form
          className={`${prefixCls}-permission-form`}
          labelWidth="auto"
          labelLayout="horizontal"
          header={intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' })}
          record={record}
        >
          <TextField autoFocus name="description" placeholder={intl.formatMessage({ id: 'iam.role.detail.permissionRule.description.tip', defaultMessage: '请填写规则描述,例如：可查看,处理自己提交的事件单' })} />
          <Lov name="businessObject" />
          <TextField name="code" restrict="_|A-Z|a-z|0-9" placeholder={intl.formatMessage({ id: 'iam.role.detail.permissionRule.code.tip', defaultMessage: '请填写编码' })} />
        </Form>
        <Form
          className={`${prefixCls}-solution-form`}
          record={record}
          header={intl.formatMessage({ id: 'iam.role.detail.permissionRule.setting', defaultMessage: '权限配置' })}
          labelWidth={55}
        />
        <ExternalComponent
          system={{ scope: 'itsm', module: 'FilterCondition' }}
          conditionData={condition}
          tableId={businessObject}
          fieldTableData={fieldTableData}
          onChange={(data) => {
            setCondition(data);
            record.set('condition', JSON.stringify(data));
          }}
          sourceFrom="filter"
          isSider={false}
          isCascader
          canHideUser
        />
      </div>
    );
  }

  return (
    <div className={`${prefixCls}-permission-form-wrapper-modify`}>
      <Form
        className={`${prefixCls}-permission-form`}
        labelWidth="auto"
        labelLayout="horizontal"
        header={intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' })}
        record={record}
        disabled={!isEdit}
      >
        <TextField autoFocus name="description" placeholder={intl.formatMessage({ id: 'iam.role.detail.permissionRule.description.tip', defaultMessage: '请填写规则描述,例如：可查看,处理自己提交的事件单' })} />
        <TextField name="code" disabled placeholder={intl.formatMessage({ id: 'iam.role.detail.permissionRule.code.tip', defaultMessage: '请填写编码' })} />
      </Form>
      <Form
        className={`${prefixCls}-solution-form`}
        record={record}
        header={intl.formatMessage({ id: 'iam.role.detail.permissionRule.setting', defaultMessage: '权限配置' })}
        labelWidth={55}
      />
      <ExternalComponent
        system={{ scope: 'itsm', module: 'FilterCondition' }}
        conditionData={condition}
        tableId={businessObject}
        fieldTableData={fieldTableData}
        onChange={(data) => {
          setCondition(data);
          record.set('condition', JSON.stringify(data));
        }}
        sourceFrom="filter"
        isSider={false}
        isCascader
        canHideUser
        readOnly={!isEdit}
      />
    </div>
  );
});
