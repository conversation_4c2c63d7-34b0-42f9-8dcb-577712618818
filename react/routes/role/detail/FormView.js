import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import axios from 'axios';
import { Table, message } from 'choerodon-ui/pro';

const { Column } = Table;

export default observer((props) => {
  const {
    modal, dataSet, type, curDataSet, parentRoleId, front,
    context: { intl, prefixCls, intlPrefix, tenantId },
  } = props;

  useEffect(() => {
    return () => {
      dataSet?.queryDataSet?.reset();
      dataSet?.setState('__CONDITIONSTATUS__', 'sync');
      dataSet?.reset();
    };
  });

  modal.handleOk(async () => {
    try {
      if (front === 'sub-roles') {
        const postData = dataSet.selected.map((v) => {
          return {
            parentRoleId,
            roleId: v.get('id'),
            _status: 'create',
          };
        });
        const resp = await axios.post(`iam/yqc/${tenantId === '0' ? '' : `${tenantId}/`}role_rels`, JSON.stringify(postData));
        if (resp) {
          curDataSet.query();
        } else {
          return false;
        }
      } else if (front === 'interface') {
        const postData = dataSet.selected.map((v) => v.toData());
        const resp = await axios.post(`hitf/v1/${tenantId}/hitf-client-roles/${parentRoleId}`, JSON.stringify(postData));
        if (resp) {
          curDataSet.query();
        } else {
          return false;
        }
      } else if (front === 'assign-account') {
        const userIds = dataSet.selected.map((v) => v.get('id'));
        const resp = await axios.post(`iam/yqc/v1/${tenantId}/memberRoles/assign-role-users?roleId=${parentRoleId}`, userIds);
        if (!resp?.failed) {
          curDataSet.query();
        } else {
          return false;
        }
      } else {
        const postData = [];
        dataSet.selected.forEach((v) => {
          postData.push(v.get('id'));
        });
        const resp = await axios.post(`iam/yqc/${tenantId === '0' ? '' : `${tenantId}/`}role_permissions/data_rule/${parentRoleId}`, JSON.stringify(postData));
        if (resp) {
          curDataSet.query();
        } else {
          return false;
        }
      }
    } catch (err) {
      message.error(err?.message);
      return false;
    }
  });

  if (front === 'sub-roles') {
    return (
      <div className="modal-table">
        <Table
          labelLayout="float"
          style={{ maxHeight: 500 }}
          dataSet={dataSet}
          placeholder={intl.formatMessage({ id: 'iam.common.action.search', defaultMessage: '搜索' })}
          className="iam.role.model-table"
          onRow={({ record }) => ({
            onClick: (e) => {
              if (record.isSelected) {
                dataSet.unSelect(record);
              } else {
                dataSet.select(record);
              }
            },
          })}
          queryBarProps={{
            fuzzyQuery: false,
            simpleMode: true,
            inlineSearch: false,
            queryFieldsStyle: {
              name: {
                width: 140,
              },
              code: {
                width: 140,
              },
              description: {
                width: 200,
              },
            },
          }}
          autoHeight
          pagination={{
            showTotal: true,
            showPager: true,
            showQuickJumper: false,
            showSizeChanger: false,
          }}
        >
          <Column name="name" />
          <Column name="code" />
          <Column name="description" />
        </Table>
      </div>
    );
  } else if (front === 'interface') {
    return (
      <div className="modal-table">
        <Table
          labelLayout="float"
          style={{ maxHeight: 500 }}
          dataSet={dataSet}
          autoHeight
          placeholder={intl.formatMessage({ id: 'iam.common.action.search', defaultMessage: '搜索' })}
          className="iam.role.model-table"
          onRow={({ record }) => ({
            onClick: (e) => {
              if (record.isSelected) {
                dataSet.unSelect(record);
              } else {
                dataSet.select(record);
              }
            },
          })}
          queryBarProps={{
            fuzzyQuery: false,
            simpleMode: true,
            inlineSearch: false,
            queryFieldsStyle: {
              interfaceName: {
                width: 140,
              },
              interfaceCode: {
                width: 140,
              },
              serverName: {
                width: 140,
              },
              serverCode: {
                width: 140,
              },
            },
          }}
          pagination={{
            showTotal: true,
            showPager: true,
            showQuickJumper: false,
            showSizeChanger: false,
          }}
        >
          <Column name="interfaceName" />
          <Column name="interfaceCode" />
          <Column name="serverName" />
          <Column name="serverCode" />
          <Column name="tenantName" />
        </Table>
      </div>
    );
  } else if (front === 'assign-account') {
    return (
      <div className="modal-table">
        <Table
          style={{ maxHeight: 500 }}
          autoHeight
          pristine
          placeholder={intl.formatMessage({ id: 'iam.role.action.addUser', defaultMessage: '添加人员' })}
          dataSet={dataSet}
          queryBarProps={{
            fuzzyQuery: false,
            simpleMode: true,
            inlineSearch: false,
            queryFieldsStyle: {
              real_name: { width: 140 },
              email: { width: 200 },
              phone: { width: 140 },
            },
          }}
        >
          <Column name="real_name" />
          <Column name="email" />
          <Column name="phone" />
        </Table>
      </div>
    );
  }
  return (
    <div className="modal-table">
      <Table
        labelLayout="float"
        dataSet={dataSet}
        placeholder={intl.formatMessage({ id: 'iam.common.action.search', defaultMessage: '搜索' })}
        className="iam.role.model-table"
        onRow={({ record }) => ({
          onClick: (e) => {
            if (record.isSelected) {
              dataSet.unSelect(record);
            } else {
              dataSet.select(record);
            }
          },
        })}
        queryBarProps={{
          fuzzyQuery: false,
          simpleMode: true,
          inlineSearch: false,
          queryFieldsStyle: {
            description: {
              width: 200,
            },
            code: {
              width: 140,
            },
            businessObjectName: {
              width: 140,
            },
            operation: {
              width: 100,
            },
            type: {
              width: 100,
            },
          },
        }}
        autoHeight
        pagination={{
          showTotal: true,
          showPager: true,
          showQuickJumper: false,
          showSizeChanger: false,
        }}
      >
        <Column name="description" />
        <Column name="code" width={150} />
        <Column name="businessObjectName" width={100} />
        <Column name="operation" width={80} />
        <Column name="type" width={80} />
      </Table>
    </div>
  );
});
