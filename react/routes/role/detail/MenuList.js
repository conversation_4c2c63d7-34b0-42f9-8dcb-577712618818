import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Table } from 'choerodon-ui/pro';

const { Column } = Table;

const MenuList = (props) => {
  const { dataSet } = props;
  useEffect(() => {
    (async () => {
      const res = await dataSet.query();
      dataSet.setState('queryData', res);
      dataSet.setState('firstQuery', true);
      dataSet.reset();
      dataSet.queryDataSet?.reset();
      dataSet.setState('__CONDITIONSTATUS__', 'sync');
    })();
  }, []);

  return (
    <Table
      dataSet={dataSet}
      mode="tree"
      selectionMode="rowbox"
      pristine
      queryBarProps={{
        fuzzyQuery: false,
        queryFieldsStyle: {
          name: {
            width: 140,
          },
          code: {
            width: 140,
          },
        },
        onReset: () => {
          dataSet.loadData(dataSet.getState('queryData'));
        },
      }}
    >
      <Column name="name" />
      <Column name="code" />
    </Table>
  );
};

export default observer(MenuList);
