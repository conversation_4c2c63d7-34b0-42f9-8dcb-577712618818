import React, { useContext, useMemo, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { isArray } from 'lodash';
import { Table, Modal, DataSet, Menu, Lov } from 'choerodon-ui/pro';
import { Button, Icon, TableHoverAction, ClickText } from '@zknow/components';
import MenuList from './MenuList';
import PermissionList from './PermissionList';
import PermissionDataSet from '../stores/PermissionDataSet';
import Store from '../stores';

const { Column } = Table;

const MenuTreeList = (props) => {
  const { editable, roleId } = props;
  const { menuDataSet, intl, intlPrefix, roleDetailDataSet, solutionMenuDataSet } = useContext(Store);
  const permissions = {};
  const solutionDataSet = new DataSet({
    autoCreate: true,
    fields: [
      {
        name: 'solution',
        type: 'object',
        lovCode: 'SOLUTION',
        multiple: true,
        lovPara: {
          exIncludeIds: menuDataSet?.toData()?.filter(v => v.type === 'solution').map(v => v.id),
          enabledFlag: true,
        },
      },
    ],
  });

  // useEffect(() => {
  //   if (menuDataSet.status === 'ready') {
  //     menuDataSet.forEach(record => {
  //       // 所有的permissions
  //       const assigendIds = record.get('permissions')?.filter(o => o.granted && o.type !== 'page');
  //       record.set('assignedPermission', assigendIds?.length || 0);
  //     });
  //     menuDataSet?.filter(record => record.get('type') !== 'root').forEach(record => {
  //       // 过滤权限类型为page ,page类型的默认
  //       record.get('permissions') && record.set('allPermission', record.get('permissions')?.filter(p => p.type !== 'page').length);
  //     });
  //     // 数据变更了，再次搜索的时候报错数据未保存
  //     // const data = menuDataSet?.toData();
  //     // menuDataSet.loadData(data);
  //   }
  // }, [menuDataSet.status]);

  const optionMapData = useMemo(() => ({
    top: intl.formatMessage({ id: 'iam.common.model.top.menu', defaultMessage: '顶层菜单' }),
    filter: intl.formatMessage({ id: 'iam.common.model.filter', defaultMessage: '筛选器' }),
    filter_item: intl.formatMessage({ id: 'iam.common.model.filter.item', defaultMessage: '筛选项' }),
    menu: intl.formatMessage({ id: 'zknow.common.model.menu', defaultMessage: '菜单' }),
    link: intl.formatMessage({ id: 'iam.common.model.link', defaultMessage: '链接' }),
    solution: intl.formatMessage({ id: 'iam.common.model.solution', defaultMessage: '解决方案' }),
    dir: intl.formatMessage({ id: 'iam.common.model.dir', defaultMessage: '目录' }),
  }), []);

  function renderType({ record }) {
    return <span>{optionMapData[record.get('type')] || record.get('type')}</span>;
  }

  const handleDeleteMenu = async () => {
    const expandIds = menuDataSet.filter(r => r.isExpanded).map(r => r.get('id'));
    await menuDataSet.delete(menuDataSet.selected, false);
    await menuDataSet.query();
    menuDataSet.forEach(r => {
      if (expandIds.includes(r.get('id'))) {
        r.isExpanded = true;
      }
    });
  };

  const handleChangeLov = async (data) => {
    if (isArray(data)) {
      const expandIds = menuDataSet.filter(r => r.isExpanded).map(r => r.get('id'));
      data.forEach(v => {
        menuDataSet.create({
          roleId,
          solutionId: v.id,
        }, 0);
      });
      await menuDataSet.submit();
      menuDataSet.setState('firstQuery', false);
      await menuDataSet.query();
      menuDataSet.forEach(r => {
        if (expandIds.includes(r.get('id'))) {
          r.isExpanded = true;
        }
      });
    }
  };

  const tableButtons = [
    menuDataSet.status === 'ready' && roleDetailDataSet.current?.get('builtIn') === false
    && <Lov
      dataSet={solutionDataSet}
      name="solution"
      mode="button"
      clearButton={false}
      className="solution-select"
      onChange={handleChangeLov}
    >
      <Button icon="Plus" funcType="raised">
        {intl.formatMessage({ id: 'iam.common.action.solutionAdd', defaultMessage: '添加解决方案' })}
      </Button>
    </Lov>,
  ];

  const tableActions = () => {
    if (roleDetailDataSet.current?.get('builtIn') === false) {
      return [
        {
          element: (
            <Menu.Item onClick={handleDeleteMenu}>
              <span style={{ display: 'flex', alignItems: 'center' }}>
                <Icon type="Delete" style={{ marginRight: '8px' }} />
                {intl.formatMessage({ id: 'iam.common.action.batchRemove', defaultMessage: '批量移除' })}
              </span>
            </Menu.Item>
          ),
        },
      ];
    }
    return [];
  };

  const handleAddMenu = async (record) => {
    solutionMenuDataSet.setState('solutionId', record.get('id'));
    solutionMenuDataSet.setState('roleId', roleId);
    const ids = menuDataSet.toData().filter(v => v.solutionId === record.get('id') && v.isLeaf).map(v => v.id);
    solutionMenuDataSet.setState('exIncludeIds', ids);
    Modal.open({
      title: intl.formatMessage({ id: 'iam.common.action.menuAdd', defaultMessage: '添加菜单' }),
      style: { width: 800 },
      bodyStyle: { padding: 0 },
      children: (
        <MenuList
          dataSet={solutionMenuDataSet}
        />
      ),
      key: 'role-menu-add',
      onOk: async () => {
        let queryFlag = false;
        const selectRecords = solutionMenuDataSet.filter(v => v.get('isChecked'));
        const unSelectRecords = solutionMenuDataSet.filter(v => !v.get('isChecked'));
        if (selectRecords.length > 0) {
          if (selectRecords.find(v => !ids.includes(v.get('id')))) {
            selectRecords.forEach(v => {
              menuDataSet.create({
                roleId,
                menuId: v.get('menuId'),
                solutionId: v.get('solutionId'),
              }, 0);
            });
            queryFlag = true;
            await menuDataSet.submit();
          }
        }
        const unSelectRecord = unSelectRecords.filter(v => {
          if (ids.includes(v.get('id'))) {
            return true;
          }
          return false;
        });
        if (unSelectRecord.length > 0) {
          queryFlag = true;
          await solutionMenuDataSet.delete(unSelectRecord, false);
        }

        solutionMenuDataSet.setState('firstQuery', false);
        if (queryFlag) {
          const expandIds = menuDataSet.filter(r => r.isExpanded).map(r => r.get('id'));
          menuDataSet.setState('firstQuery', false);
          await menuDataSet.query();
          menuDataSet.forEach(r => {
            if (expandIds.includes(r.get('id'))) {
              r.isExpanded = true;
            }
          });
        }
      },
      onCancel: () => {
        solutionMenuDataSet.setState('firstQuery', false);
      },
      destroyOnClose: true,
    });
  };

  const renderTableAction = ({ record }) => {
    const actions = [];
    const handleDelete = {
      name: intl.formatMessage({ id: 'iam.common.action.remove', defaultMessage: '移除' }),
      icon: 'Delete',
      onClick: async () => {
        const expandIds = menuDataSet.filter(r => r.isExpanded).map(r => r.get('id'));
        menuDataSet.setState('roleId', roleId);
        await menuDataSet.delete([record], false);
        menuDataSet.setState('firstQuery', false);
        await menuDataSet.query();
        menuDataSet.forEach(r => {
          if (expandIds.includes(r.get('id'))) {
            r.isExpanded = true;
          }
        });
      },
    };
    const handleAdd = {
      name: intl.formatMessage({ id: 'iam.common.action.menuAdd', defaultMessage: '添加菜单' }),
      icon: 'Plus',
      onClick: () => handleAddMenu(record),
    };
    if (record.get('type') === 'solution') {
      actions.push(handleAdd);
    }
    actions.push(handleDelete);
    return (
      <TableHoverAction
        record={record}
        actions={actions}
        intlBtnIndex={3}
      />
    );
  };

  const handleClickName = (record) => {
    if (!permissions[record.get('id')]) {
      const permissionDataSet = new DataSet(PermissionDataSet({ intl, intlPrefix, menuDataSet }));
      permissionDataSet.setState('menuId', record.get('menuId'));
      // 过滤 权限 type为 page 类型的
      const canSelectedPermission = record.get('permissions').filter((v) => v.type !== 'page');
      permissionDataSet.loadData(canSelectedPermission);
      permissionDataSet.forEach(r => {
        if (r.get('granted')) {
          permissionDataSet.select(r);
        }
      });
      permissions[record.get('id')] = permissionDataSet;
    }
    Modal.open({
      title: null,
      drawer: true,
      style: { width: 800 },
      children: (
        <PermissionList
          menu={record}
          dataSet={permissions[record.get('id')]}
          editable={editable}
          currentRole={roleDetailDataSet.current}
        />
      ),
      key: 'modal-permission',
      footer: null,
      destroyOnClose: true,
    });
  };

  const renderName = ({ record, value }) => {
    const clickable = record.get('permissions')?.length > 0;
    return clickable ? (
      <ClickText
        record={record}
        onClick={() => handleClickName(record)}
        valueField="name"
      />
    ) : value;
  };

  const renderPermission = ({ record }) => {
    const allPermission = record.get('permissions')?.length;
    const permissionId = record.get('permissionId');
    const type = record.get('type');
    const assignedPermission = record.get('permissions')?.filter(i => i.granted).length;

    return (type === 'root' || permissionId || !allPermission) ? '-' : `(${assignedPermission}/${allPermission})`;
  };

  return (
    <Table
      dataSet={menuDataSet}
      mode="tree"
      labelLayout="float"
      buttons={tableButtons}
      pristine
      queryBarProps={{
        tableActions: tableActions(),
      }}
      onRow={({ record: current }) => ({
        isLeaf: current.get('isLeaf'),
      })}
    >
      <Column name="name" renderer={renderName} />
      <Column name="type" renderer={renderType} />
      <Column name="permission" renderer={renderPermission} />
      <Column width={50} hidden={roleDetailDataSet.current?.get('builtIn') === true} tooltip="none" renderer={renderTableAction} />
    </Table>
  );
};

export default observer(MenuTreeList);
