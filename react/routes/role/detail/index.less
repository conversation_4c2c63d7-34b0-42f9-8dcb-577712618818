@import '~choerodon-ui/lib/style/themes/default';

.iam-role-detail {
  &-content {
    padding: .18rem .16rem;

    .solution-select {
      background: transparent !important;
      padding: 0;
    }
  }

  &-modal {
    .c7n-pro-modal-body {
      padding: 0;
      height: calc(100vh - 3rem);
      overflow: hidden; // 分页器下面有1px border，显示出来会看着很粗

      .modal-table {
        height: calc(100vh - 3.95rem);
      }
    }
    .c7n-pro-modal-footer {
      border-top: none;
    }
  }
}
