import React, { useContext, useEffect, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Table, Lov, message, Modal } from 'choerodon-ui/pro';
import { axios } from '@yqcloud/apps-master';
import { Button, TableHoverAction, YqTable } from '@zknow/components';
import EnabledFlag from '@/components/enabled-flag';
import FormView from './FormView';
import Store from '../stores';

const { Column } = Table;
const modalKey = Modal.key();
const AccountList = (props) => {
  const { editable, roleId, handleRefresh, currentRole } = props;
  const context = useContext(Store);
  const { authorityRolesDataSet, intl, intlPrefix, interfaceDataSet, authDataSet, tenantId } = context;
  const mdModalStyle = useMemo(() => ({ width: 800 }), []);

  useEffect(() => {
    interfaceDataSet.setQueryParameter('id', roleId);
    interfaceDataSet.query();
  }, []);

  function handleCreate() {
    openModal('create');
  }

  const createButton = () => (
    <Button funcType="raised" color="primary" icon="ListAdd" onClick={handleCreate} disabled={!editable}>
      {intl.formatMessage({ id: 'zknow.common.button.append', defaultMessage: '添加' })}
    </Button>
  );

  async function openModal(type) {
    authDataSet.setQueryParameter('id', roleId);
    authDataSet.query();
    Modal.open({
      title: intl.formatMessage({ id: 'iam.role.action.interfaceAdd', defaultMessage: '添加接口' }),
      children: (
        <FormView
          type={type}
          parentRoleId={roleId}
          context={context}
          front="interface"
          dataSet={authDataSet}
          curDataSet={interfaceDataSet}
        />
      ),
      key: modalKey,
      drawer: false,
      style: mdModalStyle,
      destroyOnClose: true,
      className: 'iam-role-detail-modal',
    });
  }

  const buttons = useMemo(() => {
    return createButton();
  }, [editable, currentRole]);

  const handleRemove = async (record) => {
    try {
      const res = await axios.post(`hitf/v1/${tenantId}/hitf-client-roles/${roleId}/recycle`, [record?.toData()]);
      if (res?.failed) {
        message.error(res?.message);
      } else {
        interfaceDataSet.query();
      }
    } catch (e) {
      return false;
    }
  };

  function renderAction({ record }) {
    const actions = [
      {
        name: intl.formatMessage({ id: 'iam.common.action.remove', defaultMessage: '移除' }),
        icon: 'delete',
        onClick: () => handleRemove(record),
      },
    ];
    return editable && record.get('tenantId') !== 0 ? <TableHoverAction record={record} actions={actions} /> : null;
  }

  return (
    <div>
      <Table
        labelLayout="float"
        dataSet={interfaceDataSet}
        placeholder={intl.formatMessage({ id: 'iam.common.action.search', defaultMessage: '搜索' })}
        className="iam.role.model-table"
        buttons={[buttons]}
        queryBarProps={{
          title: intl.formatMessage({ id: 'iam.role.desc.interface', defaultMessage: '接口' }),
          fuzzyQuery: false,
        }}
      >
        <Column name="interfaceName" />
        <Column name="interfaceCode" />
        {/* <Column name="publishUrl" /> */}
        <Column name="serverName" />
        <Column name="serverCode" />
        <Column name="tenantName" />
        <Column width={1} renderer={renderAction} tooltip="none" />
      </Table>
    </div>
  );
};

export default observer(AccountList);
