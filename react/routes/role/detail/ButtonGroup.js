import React, { useState, useCallback, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Button } from '@zknow/components';

const ButtonGroup = ({
  intl,
  dataSet,
  type,
  close,
}) => {
  const [isEdit, setIsEdit] = useState(false);

  useEffect(() => {
    setIsEdit(false);
  }, [dataSet.current]);

  const handleOk = async (ds) => {
    try {
      const res = await ds.submit();
      if (res?.content?.length) {
        ds.current?.set('objectVersionNumber', res?.content[0]?.objectVersionNumber);
      }
      if (res && type !== 'modify') {
        close();
      } else {
        setIsEdit(false);
        dataSet.current.setState('isEdit', false);
      }
      close();
      return true;
    } catch (err) {
      return false;
    }
  };

  const handleClickSaveBtn = useCallback(() => {
    handleOk(dataSet);
  }, [dataSet.current]);

  const handleClickCancelBtn = useCallback(() => {
    setIsEdit(false);
    dataSet.current.setState('isEdit', false);
    dataSet.reset();
    if (type === 'create' && close) {
      close();
    }
  }, []);

  const handleClickEditBtn = useCallback(() => {
    setIsEdit(true);
    dataSet.current.setState('isEdit', true);
  }, [isEdit]);

  // if (isSite || +dataSet.current?.get('tenantId') === 0) {
  //   return '';
  // }

  if (isEdit || type !== 'modify') {
    return (
      <div>
        <Button key="confirm" funcType="raised" color="primary" onClick={handleClickSaveBtn}>
          {intl.formatMessage({ id: type === 'modify' ? 'zknow.common.button.save' : 'zknow.common.button.ok', defaultMessage: type === 'modify' ? '保存' : '确定' })}
        </Button>
        <Button key="cancel" funcType="raised" onClick={handleClickCancelBtn}>
          {intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}
        </Button>
      </div>
    );
  }

  return (
    <div>
      <Button key="edit" funcType="raised" icon="icon-edit" onClick={handleClickEditBtn}>
        {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
      </Button>
    </div>
  );
};

export default observer(ButtonGroup);
