import React, { useContext, useRef, useMemo, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Table, Lov, message, Modal } from 'choerodon-ui/pro';
import { axios } from '@yqcloud/apps-master';
import { Button, TableHoverAction, YqTable, ClickText } from '@zknow/components';
import FormView from './FormView';
import PermissionFormView from './PermissionFormView';
import Store from '../stores';
import ButtonGroup from './ButtonGroup';

const { Column } = Table;
const modalKey = Modal.key();
const AccountList = (props) => {
  const { editable, roleId, handleRefresh, currentRole, tenantId } = props;
  const context = useContext(Store);
  const { authorityRolesDataSet, intl, intlPrefix, transDataSetRule, prefixCls, operationMap, typeMap } = context;
  const mdModalStyle = useMemo(() => ({ width: 800 }), []);
  let modal;

  useEffect(() => {
    authorityRolesDataSet.setState('roleId', roleId);
  }, []);

  function handleCreate() {
    transDataSetRule.setQueryParameter('front', 'rules');
    transDataSetRule.setQueryParameter('id', roleId);
    transDataSetRule.query();
    openModal('create');
  }

  function handleCreatePermission() {
    openPermissionModal('create', authorityRolesDataSet);
  }

  function handleModify(record) {
    record.dataSet.current = record;
    openPermissionModal('modify', record.dataSet);
  }

  const createButton = () => (
    <>
      <Button funcType="raised" icon="Plus" color="primary" onClick={handleCreatePermission} disabled={!editable}>
        {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
      </Button>
      <Button funcType="raised" icon="Add" color="secondary" onClick={handleCreate} disabled={!editable}>
        {intl.formatMessage({ id: 'zknow.common.button.append', defaultMessage: '添加' })}
      </Button>
    </>

  );

  function openPermissionModal(mode, dataSet) {
    if (modal) {
      return;
    }
    if (mode === 'create') {
      dataSet.create();
    }
    dataSet.current.setState('isEdit', false);
    modal = Modal.open({
      title: mode === 'modify' ? intl.formatMessage({ id: 'iam.role.model.authorityRules', defaultMessage: '数据权限' }) : intl.formatMessage({ id: 'iam.role.action.addAuthorityRules', defaultMessage: '新建数据权限' }),
      children: (
        <PermissionFormView
          type={mode}
          intl={intl}
          prefixCls={prefixCls}
          intlPrefix={intlPrefix}
          dataSet={dataSet}
          tenantId={tenantId}
        />
      ),
      key: modalKey,
      drawer: mode === 'modify',
      style: mdModalStyle,
      bodyStyle: mode === 'modify' ? { paddingBottom: 0 } : null,
      destroyOnClose: true,
      onClose: () => {
        dataSet.reset();
        modal = false;
      },
      footer: (
        <div className="flex-align-center">
          <ButtonGroup
            dataSet={dataSet}
            intl={intl}
            type={mode}
            close={close}
          />
        </div>
      ),
    });

    function close() {
      modal.close();
    }
  }
  function openModal(type) {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.role.action.addAuthorityRules', defaultMessage: '新建数据权限' }),
      children: (
        <FormView
          type={type}
          parentRoleId={roleId}
          context={context}
          dataSet={transDataSetRule}
          curDataSet={authorityRolesDataSet}
        />
      ),
      key: modalKey,
      drawer: false,
      style: mdModalStyle,
      destroyOnClose: true,
      className: 'iam-role-detail-modal',
    });
  }

  const buttons = useMemo(() => {
    if (+(currentRole?.get('tenantId')) === 0) {
      return [];
    } else {
      return createButton();
    }
  }, [editable, currentRole]);

  function handleRemove(record) {
    const urlPrefix = `/iam/yqc/${tenantId === '0' ? '' : `${tenantId}/`}role_permissions/data_rule/${roleId}?permission_id=${record.get('id')}`;
    axios.delete(`${urlPrefix}`).then(res => {
      if (res && res.failed) {
        message.error(res.message);
      } else {
        handleRefresh();
      }
    }).catch(e => {
      message.error(e.message);
    });
  }

  function renderAction({ record }) {
    const actions = [];
    if (+(currentRole?.get('tenantId')) !== 0) {
      actions.push({
        name: intl.formatMessage({ id: 'iam.common.action.remove', defaultMessage: '移除' }),
        icon: 'delete',
        onClick: () => handleRemove(record),
      });
    }
    return <TableHoverAction record={record} actions={actions} intlBtnIndex={0} />;
  }

  function renderDescription({ record, name }) {
    if (record.get('tenantId') === '0') {
      return record.get(name);
    }
    return (
      <ClickText
        record={record}
        onClick={() => handleModify(record)}
        valueField={name}
      />
    );
  }
  function renderOperation({ value }) {
    return value ? operationMap[value] : undefined;
  }
  function renderType({ value }) {
    return value ? typeMap[value] : undefined;
  }

  return (
    <div>
      <Table
        labelLayout="float"
        dataSet={authorityRolesDataSet}
        placeholder={intl.formatMessage({ id: 'iam.common.action.search', defaultMessage: '搜索' })}
        className="iam.role.model-table"
        buttons={[buttons]}
        pristine
        queryBarProps={{
          title: intl.formatMessage({ id: 'iam.role.model.authorityRules', defaultMessage: '数据权限' }),
        }}
      >
        <Column name="description" width={280} renderer={renderDescription} />
        <Column name="code" width={200} />
        <Column name="businessObjectName" />
        <Column name="operation" renderer={renderOperation} />
        <Column name="type" renderer={renderType} />
        <Column width={1} renderer={renderAction} tooltip="none" />
      </Table>
    </div>
  );
};

export default observer(AccountList);
