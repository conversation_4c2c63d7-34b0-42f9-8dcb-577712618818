.iam-role-permission-form {
    &-wrapper {
      .filter-condition-title {
        display: none;
      }
      .iam-role-solution-form {
        margin-top: .16rem;
      }
      &-modify {
        display: flex;
        flex-direction: column;
        height: 100%;
        .filter-condition-title {
          display: none;
        }
        .iam-role-solution-form {
            margin-top: .16rem;
          }
        .c7n-pro-table-wrapper {
          flex: 1;
          display: flex;
          flex-direction: column;
          .c7n-spin-nested-loading {
            flex: 1;
            .c7n-spin-container {
              height: 100%;
              .c7n-pro-table.c7n-pro-table-parity-row {
                height: 100%;
              }
            }
          }
        }
      }
    }
  }
