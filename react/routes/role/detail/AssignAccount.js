import React, { useContext, useRef, useMemo, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Table, Lov, message, Modal } from 'choerodon-ui/pro';
import { axios } from '@yqcloud/apps-master';
import { Button, TableHoverAction } from '@zknow/components';
import FormView from './FormView';
import Store from '../stores';

const { Column } = Table;
const modalKey = Modal.key();
const AccountList = (props) => {
  const { editable, roleId, tenantId } = props;
  const context = useContext(Store);
  const { assignAccountDataSet, intl, intlPrefix, accountDataSet } = context;
  const mdModalStyle = useMemo(() => ({ width: 800 }), []);

  useEffect(() => {
    assignAccountDataSet.setState('roleId', roleId);
    assignAccountDataSet.query();
  }, []);

  function handleCreate() {
    accountDataSet.query();
    openModal('create');
  }

  const createButton = () => (
    <>
      <Button funcType="raised" icon="ListAdd" color="secondary" onClick={handleCreate} disabled={!editable}>
        {intl.formatMessage({ id: 'zknow.common.button.append', defaultMessage: '添加' })}
      </Button>
    </>

  );

  function openModal(type) {
    Modal.open({
      title: intl.formatMessage({ id: 'iam.role.action.addUser', defaultMessage: '添加人员' }),
      children: (
        <FormView
          type={type}
          parentRoleId={roleId}
          context={context}
          dataSet={accountDataSet}
          curDataSet={assignAccountDataSet}
          front="assign-account"
        />
      ),
      key: modalKey,
      drawer: false,
      style: mdModalStyle,
      destroyOnClose: true,
      className: 'iam-role-detail-modal',
    });
  }

  const buttons = useMemo(() => createButton(), [editable]);

  function handleRemove(record) {
    axios.delete(`/iam/yqc/v1/${tenantId}/memberRoles/removeRolesForUser?roleId=${roleId}&userId=${record.get('userId')}`).then(res => {
      if (!res?.failed) {
        assignAccountDataSet.query();
      } else {
        message.error(res.message);
      }
    }).catch(e => {
      message.error(e.message);
    });
  }

  const renderAction = ({ record }) => {
    const actions = [];
    !record.get('userSource') && actions.push({
      name: intl.formatMessage({ id: 'iam.common.action.remove', defaultMessage: '移除' }),
      icon: 'delete',
      onClick: () => handleRemove(record),
    });
    return <TableHoverAction record={record} actions={actions} intlBtnIndex={-1} />;
  };

  const renderSource = ({ value }) => {
    return value || intl.formatMessage({ id: 'iam.role.desc.givenDirect', defaultMessage: '人员直接赋予' });
  };
  return (
    <div>
      <Table
        labelLayout="float"
        dataSet={assignAccountDataSet}
        placeholder={intl.formatMessage({ id: 'iam.common.action.search', defaultMessage: '搜索' })}
        className="iam.role.model-table"
        buttons={[buttons]}
        pristine
        queryBarProps={{
          title: intl.formatMessage({ id: 'iam.role.model.authorityRules', defaultMessage: '数据权限' }),
        }}
      >
        <Column name="realName" />
        <Column name="loginName" />
        <Column name="phone" />
        <Column name="email" />
        <Column name="userSource" renderer={renderSource} />
        <Column width={1} renderer={renderAction} tooltip="none" />
      </Table>
    </div>
  );
};

export default observer(AccountList);
