import React, { useState, useContext, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, TextField, TextArea, Modal, message, CheckBox } from 'choerodon-ui/pro';
import { TabPage, Header, Content, axios } from '@yqcloud/apps-master';
import { Button } from '@zknow/components';
import { Tabs } from 'choerodon-ui';
import ChildRoles from './ChildRoles';
import AuthorityRules from './AuthorityRules';
import MenuTreeList from './MenuTreeList';
import Interface from './Interface';
import AssignAccount from './AssignAccount';
import Store from '../stores';

import './index.less';

const { TabPane } = Tabs;

const DetailForm = (props) => {
  const { match: { params: { id } } } = props;
  const [editable, setEditable] = useState(false);
  const [init, setInit] = useState(false);
  const {
    roleDetailDataSet,
    loadRolesInfo,
    intl,
    intlPrefix,
    prefixCls,
    history,
    menuDataSet,
    tenantId,
  } = useContext(Store);

  const search = history.location?.search;

  const currentRole = roleDetailDataSet.current;

  const refresh = async () => {
    roleDetailDataSet.setQueryParameter('id', id);
    roleDetailDataSet.query().then(() => {
      roleDetailDataSet.current = roleDetailDataSet.get(0);
    });
    menuDataSet.setQueryParameter('roleId', id);
    const res = await menuDataSet.query();
    menuDataSet.setState('queryData', res);
    menuDataSet.setState('firstQuery', true);
  };

  useEffect(() => {
    if (id) {
      refresh();
    }
    return () => {
      menuDataSet.setState('firstQuery', false);
      menuDataSet.loadData([]);
    };
  }, [id]);

  const toggleEdit = () => {
    setEditable(!editable);
  };

  async function handleSave() {
    const permissions = [];
    menuDataSet.forEach(v => {
      if (v.get('permissions')) {
        v.get('permissions').forEach(i => permissions.push(i));
      }
      permissions.push(v.toData());
    });
    const permissionsIds = permissions.filter(v => v.granted).map(v => v.id);
    const originChildren = roleDetailDataSet.children;
    // 不提交级联数据
    roleDetailDataSet.children = {};
    const [submitRes, putRes] = await Promise.all([
      roleDetailDataSet.submit(),
      // axios.put(`iam/yqc/v1/${tenantId}/roles/${id}/permission-sets/assign`, permissionsIds),
    ]);
    roleDetailDataSet.children = originChildren;
    try {
      if (!putRes?.failed) {
        if (!submitRes) {
          message.success(intl.formatMessage({ id: 'iam.common.desc.save.success', defaultMessage: '保存成功' }));
          const expandIds = menuDataSet.filter(r => r.isExpanded).map(r => r.get('id'));
          menuDataSet.setState('firstQuery', false);
          await menuDataSet.query();
          menuDataSet.forEach(r => {
            if (expandIds.includes(r.get('id'))) {
              r.isExpanded = true;
            }
          });
        }
        if (submitRes !== undefined) {
          refresh();
        }
        toggleEdit();
      } else {
        message.error(putRes.message || intl.formatMessage({ id: 'iam.common.desc.save.failed', defaultMessage: '保存失败' }));
      }
    } catch (e) {
      message.error(e.message);
    }
  }

  function handleCancel() {
    roleDetailDataSet.reset();
    toggleEdit();
  }

  async function handleChangeEnabledFlag(flag) {
    try {
      const res = await axios.put(flag ? `/iam/yqc/v1/${tenantId}/roles/enable` : `/iam/yqc/v1/${tenantId}/roles/disable`, [currentRole.toData()]);
      if (res?.failed) {
        message.error(res.message);
      } else {
        message.success(intl.formatMessage({ id: 'iam.common.desc.save.success', defaultMessage: '保存成功' }));
        loadRolesInfo();
        refresh();
      }
    } catch (e) {
      // e
    }
  }

  function renderHeaderButton() {
    const record = roleDetailDataSet.current;
    if (!record || `${record.get('tenantId')}` === '0') {
      return null;
    } else if (!record.get('enabled')) {
      return (
        <div>
          <Button
            funcType="raised"
            color="primary"
            onClick={() => handleChangeEnabledFlag(true)}
            icon="icon-read"
          >{intl.formatMessage({ id: 'zknow.common.action.make.valid', defaultMessage: '生效' })}</Button>
        </div>
      );
    }
    return editable ? (
      <div>
        <Button funcType="raised" onClick={handleSave} color="primary">{intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}</Button>
        <Button funcType="raised" onClick={handleCancel}>{intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}</Button>
      </div>
    ) : (
      <div>
        <Button
          funcType="raised"
          onClick={toggleEdit}
          color="primary"
          icon="write"
        >{intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}</Button>
      </div>
    );
  }

  function tabChange() {
    setInit(false);
    Modal.destroyAll();
  }

  return (
    <TabPage className={`${prefixCls}-detail`}>
      <Header
        backPath={`/iam/role${search}`}
        dataSet={roleDetailDataSet}
        onRefresh={refresh}
      >
        <h1>{intl.formatMessage({ id: 'iam.role.detail.title', defaultMessage: '角色详情' })}</h1>
        {renderHeaderButton()}
      </Header>
      <Content className={`${prefixCls}-detail-content`}>
        <Form
          disabled={!editable}
          dataSet={roleDetailDataSet}
          labelLayout="horizontal"
          labelAlign="right"
          columns={2}
          header={intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' })}
        >
          <TextField name="name" />
          <TextField name="code" />
          <CheckBox name="allocableFlag" />
          <CheckBox name="builtIn" />
          <TextArea
            name="description"
            newLine
            colSpan={2}
            rows={1}
            autoSize={{ minRows: 1, maxRows: 4 }}
            resize="height"
          />
        </Form>
        <Tabs
          defaultActiveKey="1"
          type="card"
          onChange={tabChange}
        >
          <TabPane tab={intl.formatMessage({ id: 'iam.common.menu.authority', defaultMessage: '菜单权限' })} key="1" forceRender={init}>
            <MenuTreeList
              editable={editable}
              currentRole={currentRole}
              tenantId={tenantId}
              roleId={id}
            />
          </TabPane>
          <TabPane tab={intl.formatMessage({ id: 'iam.role.model.childRole', defaultMessage: '子角色' })} key="2">
            <ChildRoles
              editable
              roleId={id}
              handleRefresh={refresh}
              currentRole={currentRole}
              tenantId={tenantId}
            />
          </TabPane>
          <TabPane tab={intl.formatMessage({ id: 'iam.role.detail.assignAccount', defaultMessage: '分配人员' })} key="3">
            <AssignAccount
              editable
              roleId={id}
              handleRefresh={refresh}
              currentRole={currentRole}
              tenantId={tenantId}
            />
          </TabPane>
          <TabPane tab={intl.formatMessage({ id: 'iam.role.model.authorityRules', defaultMessage: '数据权限' })} key="4">
            <AuthorityRules
              editable
              roleId={id}
              handleRefresh={refresh}
              currentRole={currentRole}
              tenantId={tenantId}
            />
          </TabPane>
          <TabPane tab={intl.formatMessage({ id: 'iam.role.detail.interfaceAuthorization', defaultMessage: '接口权限' })} key="5">
            <Interface
              editable
              roleId={id}
              handleRefresh={refresh}
              currentRole={currentRole}
            />
          </TabPane>
        </Tabs>
      </Content>
    </TabPage>
  );
};

export default observer(DetailForm);
