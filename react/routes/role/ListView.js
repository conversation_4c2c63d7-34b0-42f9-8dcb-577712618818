import React, { useEffect, useContext, useMemo } from 'react';
import uniq from 'lodash/uniq';
import { observer } from 'mobx-react-lite';
import { TabPage, Content, axios } from '@yqcloud/apps-master';
import {
  Icon,
  ClickText,
  TableHoverAction,
  TableStatus,
  Button as YQButton,
  YqTable,
  useFilter,
} from '@zknow/components';
import { Table, Button, Modal, Dropdown, Menu, message } from 'choerodon-ui/pro';
import CreateForm from './form/CreateForm';
import Store from './stores';
import './index.less';

const { Column } = Table;
const { Item } = Menu;
const modalKey = Modal.key();

const ListView = () => {
  const { intl, intlPrefix, roleDataSet, loadRolesInfo, history, match, prefixCls, tenantId } = useContext(Store);

  useFilter(roleDataSet);

  function renderCreate() {
    return (
      <YQButton funcType="raised" color="primary" icon="Plus" onClick={openCreateModal}>
        {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
      </YQButton>
    );
  }

  function openCreateModal() {
    roleDataSet.create({ level: 'organization' });
    Modal.open({
      key: modalKey,
      title: intl.formatMessage({ id: 'iam.role.button.create', defaultMessage: '新建角色' }),
      children: <CreateForm dataSet={roleDataSet} loadRolesInfo={loadRolesInfo} />,
      style: { width: '8rem' },
    });
  }

  function openModal({ batch }) {
    Modal.open({
      key: modalKey,
      title: intl.formatMessage({ id: 'iam.common.desc.disable.confirm', defaultMessage: '确认失效' }),
      children: <span>{intl.formatMessage({ id: 'iam.role.action.disable.tip', defaultMessage: '角色可能会影响用户可见的菜单，请确认是否要失效角色？' })}</span>,
      style: { width: '5.2rem' },
      destroyOnClose: true,
      onOk: () => {
        const requestData = batch ? roleDataSet.selected.map(r => r.toData()) : [roleDataSet.current?.toData()];
        axios.put(`/iam/yqc/v1/${tenantId === '0' ? '' : `${tenantId}/`}roles/disable`, requestData).then(res => {
          if (res && res.failed) {
            message.error(res.message);
          } else {
            message.success(intl.formatMessage({ id: 'iam.common.desc.save.success', defaultMessage: '保存成功' }));
            loadRolesInfo();
            roleDataSet.query();
          }
        }).catch(e => {
          message.error(e.message);
        });
      },
    });
  }

  function handleChangeEnabledFlag({ batch, enabledFlag, disabled }) {
    if (disabled) {
      return false;
    }

    if (enabledFlag) {
      const requestData = batch ? roleDataSet.selected.map(r => r.toData()) : [roleDataSet.current?.toData()];
      axios.put(`/iam/yqc/v1/${tenantId === '0' ? '' : `${tenantId}/`}roles/enable`, requestData).then(res => {
        if (res && res.failed) {
          message.error(res.message);
        } else {
          message.success(intl.formatMessage({ id: 'iam.common.desc.save.success', defaultMessage: '保存成功' }));
          loadRolesInfo();
          roleDataSet.query();
        }
      }).catch(e => {
        message.error(e.message);
      });
    } else {
      openModal({ batch });
    }
  }

  const actionContent = () => (
    <Menu>
      <Item disabled={uniq(roleDataSet.selected.map(r => r.get('enabled'))).includes(false)}>
        <div onClick={() => handleChangeEnabledFlag({
          batch: true,
          enabledFlag: false,
          disabled: uniq(roleDataSet.selected.map(r => r.get('enabled'))).includes(false),
        })}
        >
          <Icon type="icon-Expires" style={{ marginRight: '0.04rem' }} />
          {intl.formatMessage({ id: 'iam.common.action.batchDisabled', defaultMessage: '批量失效' })}
        </div>
      </Item>
      <Item disabled={uniq(roleDataSet.selected.map(r => r.get('enabled'))).includes(true)}>
        <div onClick={() => handleChangeEnabledFlag({
          batch: true,
          enabledFlag: true,
          disabled: uniq(roleDataSet.selected.map(r => r.get('enabled'))).includes(true),
        })}
        >
          <Icon type="icon-read" style={{ marginRight: '0.04rem' }} />
          {intl.formatMessage({ id: 'iam.common.action.batchEnabled', defaultMessage: '批量生效' })}
        </div>
      </Item>
    </Menu>
  );

  function renderBatchAction() {
    const disabled = roleDataSet.selected.length <= 0;
    return (
      <Dropdown trigger="click" overlay={actionContent()}>
        <Button funcType="raised" disabled={disabled}>
          {intl.formatMessage({ id: 'iam.common.action.batch', defaultMessage: '批量操作' })}
          <Icon type="icon-drop-down" style={{ marginLeft: '0.08rem' }} />
        </Button>
      </Dropdown>
    );
  }

  const buttons = useMemo(() => [
    renderCreate(),
    // renderBatchAction(),
  ], [roleDataSet.selected.length]);

  function renderName({ record, name }) {
    return (
      <React.Fragment>
        <ClickText
          record={record}
          path={`${match.url}/detail/${record.get('id')}`}
          history={history}
          valueField={name}
        />
      </React.Fragment>
    );
  }

  function renderAction({ record }) {
    const enabled = record?.get('enabled');
    const actions = enabled ? [
      {
        key: 'disable',
        name: intl.formatMessage({ id: 'zknow.common.action.make.invalid', defaultMessage: '失效' }),
        icon: 'icon-Expires',
        onClick: () => handleChangeEnabledFlag({ batch: false, enabledFlag: false }),
      },
    ] : [
      {
        key: 'enable',
        name: intl.formatMessage({ id: 'zknow.common.action.make.valid', defaultMessage: '生效' }),
        icon: 'icon-read',
        onClick: () => handleChangeEnabledFlag({ batch: false, enabledFlag: true }),
      },
    ];
    return record.get('modified') ? (
      <TableHoverAction record={record} actions={actions} />
    ) : null;
  }

  function renderPreset({ record }) {
    const isPreset = !!record.get('builtIn');
    return isPreset ? intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' }) : intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' });
  }

  const renderStatus = ({ record }) => {
    const flag = record.getPristineValue('enabled');
    return (
      <TableStatus
        status={flag}
        enabledText={intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' })}
        disabledText={intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' })}
      />
    );
  };

  return (
    <TabPage>
      <Content className={`${prefixCls}-content`} style={{ padding: 0 }}>
        <Table
          labelLayout="float"
          dataSet={roleDataSet}
          pristine
          placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
          autoHeight
          autoLocateFirst={false}
          buttons={buttons}
          queryBarProps={{
            title: intl.formatMessage({ id: 'zknow.common.model.role', defaultMessage: '角色' }),
          }}
        >
          <Column name="name" renderer={renderName} />
          <Column name="code" minWidth={300} tooltip="overflow" />
          <Column name="description" width={150} />
          <Column name="builtIn" renderer={renderPreset} />
          <Column name="enabled" width={100} renderer={renderStatus} />
          <Column renderer={renderAction} width={20} tooltip="none" />
        </Table>
      </Content>
    </TabPage>
  );
};

export default observer(ListView);
