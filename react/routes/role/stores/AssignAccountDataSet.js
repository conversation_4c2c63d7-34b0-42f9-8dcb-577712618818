import qs from 'qs';
import { getQueryParams } from '@zknow/utils';

const AssignAccountDataSet = ({ intl, intlPrefix, tenantId }) => {
  const urlPrefix = `/iam/yqc/v1/${tenantId}/memberRoles/query-role-users`;
  const phoneLabel = intl.formatMessage({ id: 'iam.common.model.phone', defaultMessage: '手机号' });
  const emailLabel = intl.formatMessage({ id: 'iam.common.model.email', defaultMessage: '邮箱' });
  const sourceLabel = intl.formatMessage({ id: 'iam.role.model.source', defaultMessage: '来源' });
  const userNameLabel = intl.formatMessage({ id: 'iam.role.model.username', defaultMessage: '用户名' });
  const loginNameLabel = intl.formatMessage({ id: 'iam.common.model.loginName', defaultMessage: '登录名' });

  return {
    autoQuery: false,
    autoLocateFirst: false,
    dataKey: 'content',
    totalKey: 'totalElements',
    autoQueryAfterSubmit: true,
    paging: true,
    pageSize: 20,
    selection: false,
    transport: {
      read: ({ data: { id }, data, dataSet }) => ({
        url: `${urlPrefix}?roleId=${dataSet.getState('roleId')}`,
        method: 'get',
        data: getQueryParams(data),
        paramsSerializer: (params) => {
          delete params.id;
          return qs.stringify(params);
        },
      }),
      create: ({ data: [data], dataSet }) => {
        if (!data?.businessObject?.code) {
          return false;
        }
        data.businessObject = data.businessObject.code;
        return ({
          url: `/iam/yqc/${tenantId}/role_permissions/data_rule/permission/new?role_id=${dataSet.getState('roleId')}`,
          method: 'post',
          data,
        });
      },
      update: ({ data: [data] }) => {
        return ({
          url: `iam/yqc/v1/${tenantId}/permissions`,
          method: 'put',
          data,
        });
      },

    },
    fields: [
      { 
        name: 'id', 
        type: 'string',
      },
      { 
        name: 'realName',
        type: 'string',
        label: userNameLabel,
      },
      { 
        name: 'loginName',
        type: 'string',
        label: loginNameLabel, 
      },
      { 
        name: 'phone', 
        type: 'string', 
        label: phoneLabel,
      },
      { 
        name: 'email', 
        type: 'string', 
        label: emailLabel,
      },
      { 
        name: 'userSource', 
        type: 'string', 
        label: sourceLabel,
      },
    ],
    queryFields: [
      { name: 'realName', type: 'string', label: userNameLabel },
      { name: 'loginName', type: 'string', label: loginNameLabel },
      { name: 'email', type: 'string', label: emailLabel },
      { name: 'userSource', type: 'string', label: sourceLabel },
    ],
  };
};

export default AssignAccountDataSet;
