import { getQueryParams } from '@zknow/utils';
// eslint-disable-next-line camelcase
// import worker_script from './trans';
import { isUndefined, orderBy } from 'lodash';
import { getFlatTreeChildrenIds } from '@/utils';

const MenuDataSet = ({ intl, intlPrefix, menuTypeDataSet, tenantId }) => {
  const menuName = intl.formatMessage({ id: 'iam.role.model.menuName', defaultMessage: '菜单名称' });
  const type = intl.formatMessage({ id: 'iam.common.model.type', defaultMessage: '类型' });
  const permission = intl.formatMessage({ id: 'iam.common.model.authority', defaultMessage: '权限' });
  const entrance = intl.formatMessage({ id: 'iam.role.model.entrance', defaultMessage: '页面入口' });

  return {
    autoQuery: false,
    autoLocateFirst: false,
    dataKey: 'content',
    totalKey: 'totalElement',
    autoQueryAfterSubmit: false,
    paging: false,
    idField: 'id',
    parentField: 'parentId',
    primaryKey: 'id',
    fields: [
      { name: 'id', type: 'string' },
      { name: 'name', type: 'string', label: menuName },
      { name: 'type', type: 'string', label: type, textField: 'text', valueField: 'value', options: menuTypeDataSet },
      { name: 'allPermission', type: 'number' },
      { name: 'assignedPermission', type: 'number' },
      { name: 'permission', type: 'string', label: permission },
      { name: 'path', type: 'string', label: entrance },
      { name: 'permissions', type: 'object' },
      { name: 'granted', type: 'boolean' },
    ],
    queryFields: [
      { name: 'name', type: 'string', label: menuName },
      { name: 'type', type: 'string', label: type, textField: 'text', valueField: 'value', options: menuTypeDataSet },
    ],
    transport: {
      read: ({ data, dataSet }) => {
        const { roleId } = data;
        const path = tenantId === '0' ? '' : `${tenantId}/`;
        return {
          url: `/iam/yqc/v1/${path}menuSolution/role/${roleId}`,
          method: 'GET',
          transformResponse: (res) => transformResponse(res),
        };
      },
      create: ({ data, dataSet }) => {
        const path = tenantId === '0' ? '' : `${tenantId}/`;
        return {
          url: `/iam/yqc/${path}role_permissions`,
          method: 'POST',
          data,
        };
      },
      destroy: ({ data, dataSet }) => {
        const roleId = dataSet.getState('roleId');
        const path = tenantId === '0' ? '' : `${tenantId}/`;
        const param = data.map(v => ({ solutionId: v.solutionId || v.id, menuId: v.menuId }));
        return {
          url: `/iam/yqc/${path}role_permissions/batch/${roleId}`,
          method: 'DELETE',
          data: param,
        };
      },
    },
    events: {
      query: ({ dataSet, data }) => {
        if (dataSet.getState('firstQuery') === true) {
          const res = dataSet.getState('queryData');
          const searchFuzzyQuery = dataSet.getState('__SEARCHTEXT__');
          const searchName = data.name;
          const searchType = data.type;
          if (isUndefined(searchFuzzyQuery) && isUndefined(searchName) && isUndefined(searchType)) {
            dataSet.loadData(res);
          } else {
            const newData = res.slice()
              .filter(v => (searchFuzzyQuery ? v.name?.toLowerCase?.()?.includes?.(searchFuzzyQuery.toLowerCase()) || v.type?.toLowerCase?.()?.includes?.(searchFuzzyQuery.toLowerCase()) : true))
              .filter(v => (searchName ? v.name?.toLowerCase?.()?.includes?.(searchName?.toLowerCase?.()) : true))
              .filter(v => (searchType ? v.type?.toLowerCase?.()?.includes?.(searchType?.toLowerCase?.()) : true));
            dataSet.loadData(newData);
          }
          return false;
        }
        return true;
      },
      select: ({ dataSet, record }) => {
        const id = record.get('id');
        const childrenIds = getFlatTreeChildrenIds(id, dataSet.toData());
        const children = dataSet.filter(r => childrenIds.includes(r.get('id')));
        dataSet.batchSelect(children);
      },
    },
  };
};

export default MenuDataSet;

function transformResponse(response) {
  try {
    // const myWorker = new Worker(worker_script);
    // myWorker.onmessage = (m) => {
    //   window.console.info('worker message', m.data);
    // };
    // myWorker.postMessage('5');
    const data = JSON.parse(response);
    if (data?.failed) {
      return response;
    }

    // tree 数据平铺
    const treeData = [];
    const isLeafList = {}; // { [id]: true }
    data?.forEach((item) => {
      item.relations?.forEach((relation) => {
        const parentId = relation.parentId !== '0' ? relation.parentId : item.id;
        treeData.push({ ...relation, parentId });
        if (!isLeafList[parentId]) isLeafList[parentId] = true;
      });
      treeData.push({
        ...item,
        type: 'solution',
        relations: [],
        parentId: '0',
      });
    });
    // 找不到父级id并且父级id不为0 则过滤 /脏数据/
    const ids = [];
    let idsL = -1;
    while (idsL !== ids.length) {
      idsL = ids.length;
      treeData.forEach(v => {
        if (!ids.includes(v.id)) {
          const flag = treeData.find(item => item.id === v.parentId);
          if (v.parentId !== '0' && !flag) {
            ids.push(v.id);
          } else if (flag && ids.includes(v.parentId)) {
            ids.push(v.id);
          }
        }
      });
    }

    treeData.forEach(t => { t.isLeaf = !isLeafList[t.id]; });

    const allData = orderBy(treeData, ['rankNum'], ['asc']).filter(v => !ids.includes(v.id));

    // 过滤出目标菜单项
    const result = allData;
    // 找到所有的子节点
    allData.forEach(v => {
      const d = result.find(j => j.parentId === v.id);
      if (d && !result.find(j => j.id === v.id)) {
        result.push(v);
      }
    });
    return result.length ? result : allData;
  } catch (e) {
    return response;
  }
}
