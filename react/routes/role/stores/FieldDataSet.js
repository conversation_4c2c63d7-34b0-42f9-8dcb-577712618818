const RoleLevelDataSet = ({ intl, intlPrefix }) => ({
  fields: [
    { name: 'text', type: 'string' },
    { name: 'value', type: 'string' },
  ],
  data: [
    { text: intl.formatMessage({ id: 'iam.role.data.level.site', defaultMessage: '平台层' }), value: 'site' },
    { text: intl.formatMessage({ id: 'iam.role.data.level.organization', defaultMessage: '租户层' }), value: 'organization' },
  ],
});

const MenuTypeDataSet = ({ intl, intlPrefix }) => ({
  fields: [
    { name: 'text', type: 'string' },
    { name: 'value', type: 'string' },
  ],
  data: [
    { text: intl.formatMessage({ id: 'iam.role.data.menu.root', defaultMessage: '目录' }), value: 'root' },
    { text: intl.formatMessage({ id: 'zknow.common.model.menu', defaultMessage: '菜单' }), value: 'menu' },
    { text: intl.formatMessage({ id: 'iam.common.model.solution', defaultMessage: '解决方案' }), value: 'solution' },
    { text: intl.formatMessage({ id: 'iam.common.model.filter', defaultMessage: '筛选器' }), value: 'filter' },
    { text: intl.formatMessage({ id: 'iam.common.model.link', defaultMessage: '链接' }), value: 'link' },
  ],
});

export { RoleLevelDataSet, MenuTypeDataSet };
