import { getQueryParams } from '@zknow/utils';
import isUndefined from 'lodash/isUndefined';

const SolutionMenuDataSet = ({ intl, intlPrefix, tenantId }) => {
  const menuName = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const code = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });

  return {
    autoQuery: false,
    parentField: 'parentId',
    idField: 'id',
    paging: false,
    primaryKey: 'id',
    selection: false,
    checkField: 'isChecked',
    fields: [
      { name: 'id', type: 'string' },
      { name: 'parentId', type: 'string' },
      { name: 'name', type: 'string', label: menuName },
      { name: 'code', type: 'string', label: code },
      { name: 'isChecked', type: 'boolean' },
    ],
    queryFields: [
      { name: 'name', type: 'string', label: menuName },
      { name: 'code', type: 'string', label: code },
    ],
    transport: {
      read: ({ data, dataSet }) => {
        const path = tenantId === '0' ? '' : `${tenantId}/`;
        return {
          url: `/iam/yqc/v1/${path}menuSolution/all?solutionId=${dataSet.getState('solutionId')}&roleId=${dataSet.getState('roleId')}`,
          method: 'GET',
          // data: getQueryParams(data),
          transformResponse: (res) => {
            try {
              const ids = dataSet.getState('exIncludeIds')?.slice();
              return JSON.parse(res).map(v => {
                if (ids?.includes(v.id)) {
                  return {
                    ...v,
                    isChecked: true,
                  };
                }
                return {
                  ...v,
                  isChecked: false,
                };
              });
              // return JSON.parse(res).filter(v => {
              //   if (ids?.includes(v.id)) {
              //     return false;
              //   }
              //   return true;
              // });
            } catch (error) {
              return JSON.parse(res);
            }
          },
        };
      },
      destroy: ({ data, dataSet }) => {
        const roleId = dataSet.getState('roleId');
        const path = tenantId === '0' ? '' : `${tenantId}/`;
        const param = data.filter(v => v.solutionId)?.map(v => ({ solutionId: v.solutionId, menuId: v.menuId }));
        return {
          url: `/iam/yqc/${path}role_permissions/batch/${roleId}`,
          method: 'DELETE',
          data: param,
        };
      },
    },
    events: {
      query: ({ dataSet, data }) => {
        if (dataSet.getState('firstQuery') === true) {
          const res = dataSet.getState('queryData');
          const searchName = data.name;
          const searchCode = data.code;
          if (isUndefined(searchName) && isUndefined(searchCode)) {
            dataSet.loadData(res);
          } else {
            const newData = res.slice()
              .filter(v => (searchName ? v.name?.toLowerCase?.()?.includes?.(searchName?.toLowerCase?.()) : true))
              .filter(v => (searchCode ? v.code?.toLowerCase?.()?.includes?.(searchCode?.toLowerCase?.()) : true));
            dataSet.loadData(newData);
          }
          return false;
        }
        return true;
      },
      // load: ({ dataSet }) => {
      //   const exIncludeIds = dataSet.getState('exIncludeIds')?.slice();
      //   const parentId = [];
      //   dataSet.forEach(v => {
      //     if (exIncludeIds.includes(v.get('id'))) {
      //       dataSet.select(v);
      //     }
      //   });
      // },
    },
  };
};

export default SolutionMenuDataSet;
