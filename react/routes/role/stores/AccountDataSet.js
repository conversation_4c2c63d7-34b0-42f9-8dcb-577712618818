import { getQueryParams } from '@zknow/utils';

const AssignAccountDataSet = ({ intl, intlPrefix, tenantId }) => {
  const phoneLabel = intl.formatMessage({ id: 'iam.common.model.phone', defaultMessage: '手机号' });
  const emailLabel = intl.formatMessage({ id: 'iam.common.model.email', defaultMessage: '邮箱' });
  const userNameLabel = intl.formatMessage({ id: 'iam.role.model.username', defaultMessage: '用户名' });

  return {
    autoQuery: false,
    autoLocateFirst: false,
    dataKey: 'content',
    totalKey: 'totalElements',
    autoQueryAfterSubmit: true,
    paging: true,
    pageSize: 20,
    selection: 'multiple',
    transport: {
      read: ({ data: { id }, data, dataSet }) => ({
        url: `/lc/v1/engine/${tenantId}/options/190900073398276096/queryWithCondition`,
        method: 'POST',
        data: {
          params: {
            ...getQueryParams(data, ['current_params', '__page_params']),
            __page_params: data?.__page_params,
          },
        },
        transformResponse: (originData) => {
          try {
            const jsonData = JSON.parse(originData);
            return {
              ...jsonData,
              content: jsonData?.content?.map(item => {
                return {
                  ...item,
                  primaryKey: item.id,
                };
              }) || [],
            };
          } catch (error) {
            return [];
          }
        },
      }),
    },
    fields: [
      { name: 'real_name', label: userNameLabel, type: 'string' },
      { name: 'email', label: emailLabel, type: 'string' },
      { name: 'phone', label: phoneLabel, type: 'string' },
    ],
    queryFields: [
      { name: 'real_name', label: userNameLabel, type: 'string' },
      { name: 'email', label: emailLabel, type: 'string' },
      { name: 'phone', label: phoneLabel, type: 'string' },
    ],
  };
};

export default AssignAccountDataSet;
