/* eslint-disable */
const workerCode = () => {
  self.onmessage = function (event) {
    console.log('worker received', event.data);
    setTimeout(() => {
      postMessage('5 秒后了');
    }, 5000)
  };
};

let code = workerCode.toString();
code = code.substring(code.indexOf('{') + 1, code.lastIndexOf('}'));

const blob = new Blob([code], { type: 'application/javascript' });
const worker_script = URL.createObjectURL(blob);

module.exports = worker_script;
