import qs from 'qs';
import { DataSet } from 'choerodon-ui/pro';
import { getQueryParams } from '@zknow/utils';

const ChildRolesDataSet = ({ intl, intlPrefix, tenantId, operationMap, typeMap }) => {
  const urlPrefix = `/iam/yqc/${tenantId}/role_permissions/data_rule`;
  const codeLabel = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const objectLabel = intl.formatMessage({ id: 'iam.common.model.businessObject', defaultMessage: '业务对象' });
  const operationLabel = intl.formatMessage({ id: 'iam.role.model.operation', defaultMessage: '操作' });
  const typeLabel = intl.formatMessage({ id: 'iam.common.model.type', defaultMessage: '类型' });
  const descriptionLabel = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });

  const operationMapDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.common.action.update', defaultMessage: '更新' }), value: 'update' },
      { meaning: intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' }), value: 'insert' },
      { meaning: intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' }), value: 'delete' },
      { meaning: intl.formatMessage({ id: 'zknow.common.button.search', defaultMessage: '查询' }), value: 'select' },
      { meaning: intl.formatMessage({ id: 'zknow.common.model.other', defaultMessage: '其他' }), value: 'other' },
    ],
  });
  const typeMapDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'iam.role.model.api', defaultMessage: '接口类型' }), value: 'api' },
      { meaning: intl.formatMessage({ id: 'iam.common.model.page', defaultMessage: '页面' }), value: 'page' },
      { meaning: intl.formatMessage({ id: 'iam.common.model.record', defaultMessage: '记录' }), value: 'record' },
      { meaning: intl.formatMessage({ id: 'iam.common.model.field', defaultMessage: '字段' }), value: 'field' },
    ],
  });

  return {
    autoQuery: false,
    autoLocateFirst: false,
    dataKey: 'content',
    totalKey: 'totalElements',
    autoQueryAfterSubmit: true,
    paging: true,
    pageSize: 20,
    selection: false,
    transport: {
      read: ({ data: { id }, data }) => ({
        url: `${urlPrefix}/${id}`,
        method: 'get',
        data: getQueryParams(data),
        paramsSerializer: (params) => {
          delete params.id;
          return qs.stringify(params);
        },
      }),
      create: ({ data: [data], dataSet }) => {
        if (!data?.businessObject?.code) {
          return false;
        }
        data.businessObject = data.businessObject.code;
        return ({
          url: `/iam/yqc/${tenantId}/role_permissions/data_rule/permission/new?role_id=${dataSet.getState('roleId')}`,
          method: 'post',
          data,
        });
      },
      update: ({ data: [data] }) => {
        return ({
          url: `iam/yqc/v1/${tenantId}/permissions`,
          method: 'put',
          data,
        });
      },

    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'description', type: 'string', label: descriptionLabel, required: true, isIntl: true },
      { name: 'code', type: 'string', format: 'uppercase', label: codeLabel, required: true },
      { name: 'businessObjectId', type: 'string' },
      { name: 'businessObjectName', type: 'string', label: objectLabel },
      { name: 'businessObject', type: 'object', label: objectLabel, valueField: 'code', lovCode: 'BUSINESS_OBJECT', required: true },
      {
        name: 'operation',
        type: 'string',
        label: operationLabel,
      },
      {
        name: 'type',
        type: 'string',
        label: typeLabel,
      },
    ],
    queryFields: [
      { name: 'description', type: 'string', label: descriptionLabel },
      { name: 'code', type: 'string', label: codeLabel },
      { name: 'businessObjectName', type: 'string', label: objectLabel },
      { name: 'operation', type: 'string', label: operationLabel, options: operationMapDs },
      { name: 'type', type: 'string', label: typeLabel, options: typeMapDs },
    ],
  };
};

export default ChildRolesDataSet;
