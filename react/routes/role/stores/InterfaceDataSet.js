import qs from 'qs';
import { getQueryParams } from '@zknow/utils';
import { getItfSearch } from '@/utils';

export default ({ intl, intlPrefix, tenantId, type }) => {
  const url = `hitf/v1/${tenantId}/hitf-client-roles`;
  const itfName = intl.formatMessage({ id: 'iam.role.model.interfaceName', defaultMessage: '接口名称' });
  const itfCode = intl.formatMessage({ id: 'iam.role.model.interfaceCode', defaultMessage: '接口编码' });
  const serName = intl.formatMessage({ id: 'iam.role.model.serverName', defaultMessage: '服务名称' });
  const serCode = intl.formatMessage({ id: 'iam.role.model.serverCode', defaultMessage: '服务编码' });
  const tenantName = intl.formatMessage({ id: 'iam.role.model.interfaceTenant', defaultMessage: '所属租户' });
  const publishUrlLabel = intl.formatMessage({ id: 'iam.role.model.publishUrl', defaultMessage: '发布地址' });

  return {
    autoQuery: false,
    autoLocateFirst: false,
    dataKey: 'content',
    totalKey: 'totalElement',
    autoQueryAfterSubmit: true,
    selection: type ? 'multiple' : false,
    paging: 'server',
    pageSize: 20,
    transport: {
      read: ({ data: { id }, data }) => {
        return {
          method: 'get',
          url: `${url}/${id}${type === 'auth' ? `/authorizable?tenantId=${tenantId}` : ''}`,
          data: getQueryParams(data),
          paramsSerializer: (params) => {
            delete params.id;
            return qs.stringify(params);
          },
        };
      },
    },

    fields: [
      { name: 'interfaceName', type: 'string', label: itfName },
      { name: 'interfaceCode', type: 'string', label: itfCode },
      { name: 'serverName', type: 'string', label: serName },
      { name: 'serverCode', type: 'string', label: serCode },
      { name: 'publishUrl', type: 'string', label: publishUrlLabel },
      { name: 'tenantName', type: 'string', label: tenantName },
    ],
    queryFields: [
      { name: 'interfaceName', type: 'string', label: itfName },
      { name: 'interfaceCode', type: 'string', label: itfCode },
      { name: 'serverName', type: 'string', label: serName },
      { name: 'serverCode', type: 'string', label: serCode },
    ],
    events: {
      query: ({ dataSet, params, data }) => {
        getItfSearch(params);
      },
    },
  };
};
