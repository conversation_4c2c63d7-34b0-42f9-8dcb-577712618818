import { DataSet } from 'choerodon-ui/pro';
import { Constants } from '@zknow/utils';
import { getQueryParams } from '@zknow/utils';

const { CODE_REGEX } = Constants;
const RoleDataSet = ({ intl, intlPrefix, roleLevelDataSet, childRolesDataSet, menuDataSet, authorityRolesDataSet, tenantId }) => {
  const urlPrefix = `/iam/yqc/v1/${tenantId}/roles`;

  const roleName = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const roleCode = intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' });
  const desc = intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' });
  const status = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const level = intl.formatMessage({ id: 'iam.common.model.level', defaultMessage: '层级' });
  const platformBuildIn = intl.formatMessage({ id: 'iam.role.model.platformBuildIn', defaultMessage: '是否预置' });
  const allocableFlagLabel = intl.formatMessage({ id: 'iam.role.model.allocableFlag', defaultMessage: '可分配' });

  const enabledFlagDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' }), value: 'true' },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' }), value: 'false' },
    ],
  });
  const builtInDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' }), value: 'true' },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' }), value: 'false' },
    ],
  });

  return {
    autoQuery: false,
    primaryKey: 'id',
    autoLocateFirst: false,
    dataKey: 'content',
    totalKey: 'totalElements',
    autoQueryAfterSubmit: true,
    paging: 'server',
    pageSize: 20,
    selection: false,
    transport: {
      read: ({ data }) => {
        const { id, ...rest } = data;
        const url = id ? `${urlPrefix}/${id}` : `${urlPrefix}/paging`;
        return {
          url,
          method: 'get',
          data: getQueryParams(rest),
        };
      },
      create: ({ data: [data] }) => ({
        url: urlPrefix,
        method: 'post',
        data,
      }),
      update: ({ data: [data] }) => ({
        url: urlPrefix,
        method: 'put',
        data,
      }),
    },
    fields: [
      { name: 'id', type: 'string' },
      {
        name: 'name',
        type: 'string',
        label: roleName,
        required: true,
        isIntl: true,
        dynamicProps: {
          disabled: ({ record }) => record.get('builtIn'),
        },
      },
      {
        name: 'code',
        type: 'string',
        label: roleCode,
        required: true,
        format: 'uppercase',
        validator: (value, name, record) => {
          if (record.get('id')) {
            return true;
          } else if (CODE_REGEX.test(value)) {
            return true;
          } else {
            return intl.formatMessage({ id: 'iam.common.validate.message.code', defaultMessage: '编码只能由大写字母，数字和下划线构成' });
          }
        },
        dynamicProps: {
          disabled: ({ record }) => record.get('id'),
        },
      },
      {
        name: 'description',
        type: 'string',
        label: desc,
        dynamicProps: {
          disabled: ({ record }) => record.get('builtIn'),
        },
      },
      {
        name: 'allocableFlag',
        type: 'boolean',
        label: allocableFlagLabel,
        defaultValue: true,
        dynamicProps: {
          disabled: ({ record }) => record.get('builtIn'),
        },
      },
      { name: 'builtIn', type: 'boolean', disabled: true, label: platformBuildIn },
      { name: 'enabled', type: 'boolean', label: status },
      { name: 'modified', type: 'boolean', defaultValue: true },
      {
        name: 'level',
        type: 'string',
        label: level,
        textField: 'text',
        valueField: 'value',
        options: roleLevelDataSet,
        required: true,
        dynamicProps: {
          disabled: ({ record }) => record.get('builtIn'),
        },
      },
    ],
    queryFields: [
      { name: 'name', type: 'string', label: roleName },
      { name: 'code', type: 'string', label: roleCode },
      { name: 'description', type: 'string', label: desc },
      { name: 'builtIn', type: 'string', label: platformBuildIn, options: builtInDs },
      { name: 'enabled', type: 'string', label: status, options: enabledFlagDs },
    ],
    children: {
      child: childRolesDataSet,
      authority: authorityRolesDataSet,
      // menu: menuDataSet,
    },
  };
};

export default RoleDataSet;
