import React, { createContext, useMemo } from 'react';
import { axios } from '@yqcloud/apps-master';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import { RoleLevelDataSet, MenuTypeDataSet } from './FieldDataSet';
import MenuDataSet from './MenuDataSet';
import SolutionMenuDataSet from './SolutionMenuDataSet';
import RoleDataSet from './RoleDataSet';
import ChildRolesDataSet from './ChildRolesDataSet';
import AuthorityRolesDataSet from './AuthorityRulesDataSet';
import TransDataSet from './TransDataSet';
import InterfaceDataSet from './InterfaceDataSet';
import AssignAccountDataSet from './AssignAccountDataSet';
import AccountDataSet from './AccountDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: ['iam.common', 'iam.role'] })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      match,
      AppState: { currentMenuType: { organizationId: tenantId } },
    } = props;
    const id = match?.params?.id;
    const intlPrefix = 'iam.role';
    const prefixCls = 'iam-role';

    const operationMap = useMemo(() => ({
      update: intl.formatMessage({ id: 'iam.common.action.update', defaultMessage: '更新' }),
      insert: intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' }),
      delete: intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' }),
      select: intl.formatMessage({ id: 'zknow.common.button.search', defaultMessage: '查询' }),
      other: intl.formatMessage({ id: 'iam.common.model.other', defaultMessage: '其他' }),
    }), []);

    const typeMap = useMemo(() => ({
      api: intl.formatMessage({ id: 'iam.role.model.api', defaultMessage: '接口类型' }),
      page: intl.formatMessage({ id: 'iam.common.model.page', defaultMessage: '页面' }),
      record: intl.formatMessage({ id: 'iam.common.model.record', defaultMessage: '记录' }),
      field: intl.formatMessage({ id: 'iam.common.model.field', defaultMessage: '字段' }),
    }), []);

    const roleLevelDataSet = useMemo(() => new DataSet(RoleLevelDataSet({ intl, intlPrefix })), []);

    const menuTypeDataSet = useMemo(() => new DataSet(MenuTypeDataSet({ intl, intlPrefix })), []);

    const menuDataSet = useMemo(() => new DataSet(MenuDataSet({ intl, intlPrefix, menuTypeDataSet, tenantId })), []);
    const solutionMenuDataSet = useMemo(() => new DataSet(SolutionMenuDataSet({ intl, intlPrefix, tenantId })), []);

    const roleDataSet = useMemo(() => new DataSet(RoleDataSet({
      intl,
      intlPrefix,
      roleLevelDataSet,
      menuDataSet,
      tenantId,
    })), [tenantId, menuDataSet]);

    const childRolesDataSet = useMemo(() => new DataSet(ChildRolesDataSet({ intl, intlPrefix, tenantId, operationMap, typeMap })), []);
    const authorityRolesDataSet = useMemo(() => new DataSet(AuthorityRolesDataSet({ intl, intlPrefix, tenantId, operationMap, typeMap })), []);
    const roleDetailDataSet = useMemo(() => new DataSet(RoleDataSet({
      intl,
      intlPrefix,
      roleLevelDataSet,
      childRolesDataSet,
      authorityRolesDataSet,
      menuDataSet,
      tenantId,
    })), [tenantId, childRolesDataSet, authorityRolesDataSet, menuDataSet]);
    const transDataSetRole = useMemo(() => new DataSet(TransDataSet({
      intl,
      intlPrefix,
      operationMap,
      typeMap,
      source: 'roles',
      tenantId,
    })), [tenantId]);
    const transDataSetRule = useMemo(() => new DataSet(TransDataSet({
      intl,
      intlPrefix,
      operationMap,
      typeMap,
      source: 'rule',
      tenantId,
    })), [tenantId]);

    const interfaceDataSet = useMemo(() => new DataSet(InterfaceDataSet({ intl, intlPrefix, tenantId, id })), []);

    const authDataSet = useMemo(() => new DataSet(InterfaceDataSet({ intl, intlPrefix, tenantId, id, type: 'auth' })), []);

    const assignAccountDataSet = useMemo(() => new DataSet(AssignAccountDataSet({ intl, intlPrefix, tenantId })), []);
    const accountDataSet = useMemo(() => new DataSet(AccountDataSet({ intl, intlPrefix, tenantId })), []);
    async function loadRolesInfo() {
      // const data = await axios.get('/iam/yqc/v1/member-roles/self-roles');
      // data && props.AppState.setRolesInfo(data);
    }

    const value = {
      ...props,
      tenantId,
      intlPrefix,
      prefixCls,
      roleLevelDataSet,
      menuTypeDataSet,
      menuDataSet,
      solutionMenuDataSet,
      roleDataSet,
      roleDetailDataSet,
      loadRolesInfo,
      authDataSet,
      childRolesDataSet,
      transDataSetRole,
      transDataSetRule,
      authorityRolesDataSet,
      interfaceDataSet,
      typeMap,
      operationMap,
      assignAccountDataSet,
      accountDataSet,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
