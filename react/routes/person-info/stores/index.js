import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import InfoDataSet from './InfoDataSet';
import PasswordDataSet from './PasswordDataSet';
import OpenInfoDataSet from './OpenInfoDataSet';
import ChangeDataSet from './ChangeDataSet';
import { checkPassword } from '../../../utils';
import wechatImg from '../../open-login-config/assets/wechat.svg';
import wechatEnterpriseImg from '../assets/wechat_enterprise.svg';
import dingdingImg from '../assets/ding_talk.svg';
import larkImg from '../assets/lark.svg';
import oauth2Img from '../../open-login-config/assets/oauth2.svg';
import yqImg from '../../open-login-config/assets/yqchat.png';
import miniProgramImg from '../../open-login-config/assets/wx-xcx.svg';
import ChoerodonImg from '../../open-login-config/assets/choerodon-logo.svg';

const Store = createContext();

const imgMap = {
  wechat: wechatImg,
  wechat_open_account: wechatImg,
  wechat_enterprise: wechatEnterpriseImg,
  ding_talk: dingdingImg,
  dingding: dingdingImg,
  lark: larkImg,
  other: oauth2Img,
  wechat_agent: wechatImg,
  quick_authentication: yqImg,
  wechat_mini_program: miniProgramImg,
  choerodon: ChoerodonImg,
};

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: ['iam.common', 'iam.personInfo'] })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId, tenantId }, getUserInfo: { tenantId: userTenantId } },
    } = props;
    const intlPrefix = 'iam.person-info';
    const prefixCls = 'iam-person-info';

    const nameMap = {
      ding_talk: intl.formatMessage({ id: 'iam.common.dingding', defaultMessage: '钉钉' }),
      lark: intl.formatMessage({ id: 'iam.common.lark', defaultMessage: '飞书' }),
      wechat_enterprise: intl.formatMessage({ id: 'iam.common.wechatEnterprise', defaultMessage: '企业微信' }),
      wechat_open_account: intl.formatMessage({ id: 'iam.common.wechatOpenAccount', defaultMessage: '微信公众号' }),
      other: intl.formatMessage({ id: 'iam.common.otherOauth2', defaultMessage: '其他Oauth2.0' }),
      wechat_agent: intl.formatMessage({ id: 'iam.common.wechatAgent', defaultMessage: '微信客服' }),
      quick_authentication: intl.formatMessage({ id: 'iam.common.quickAuthentication', defaultMessage: '嵌入式应用' }),
      wechat_mini_program: intl.formatMessage({ id: 'iam.common.wechatMiniProgram', defaultMessage: '小程序' }),
      choerodon: intl.formatMessage({ id: 'iam.common.choerodon', defaultMessage: '猪齿鱼' }),
    };

    const infoDataSet = useMemo(() => new DataSet(InfoDataSet({ intlPrefix, intl, organizationId })), []);
    const passwordDataSet = useMemo(() => new DataSet(PasswordDataSet({ intlPrefix, intl, organizationId, checkPassword, userTenantId, infoDataSet })), []);
    const openInfoDataSet = useMemo(() => new DataSet(OpenInfoDataSet({ intlPrefix, intl, organizationId })), []);
    const changeDataSet = useMemo(() => new DataSet(ChangeDataSet({ intlPrefix, intl, organizationId })), []);

    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      infoDataSet,
      passwordDataSet,
      openInfoDataSet,
      changeDataSet,
      tenantId,
      imgMap,
      nameMap,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
