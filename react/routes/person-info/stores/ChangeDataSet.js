export default ({ intlPrefix, intl, type = 'email' }) => {
  const url = '/iam/yqc/users/self/detail';

  const name = intl.formatMessage({ id: 'iam.personInfo.model.name', defaultMessage: '昵称' });
  const email = intl.formatMessage({ id: 'iam.common.model.email', defaultMessage: '邮箱' });
  const emailCaptcha = intl.formatMessage({ id: 'iam.personInfo.captcha.email', defaultMessage: '新邮箱验证码' });
  const newEmail = intl.formatMessage({ id: 'iam.personInfo.model.newEmail', defaultMessage: '新邮箱' });

  return {
    autoQuery: false,
    selection: false,
    paging: false,
    autoCreate: true,

    transport: {
      submit: ({ data: [data] }) => ({
        url: '/iam/yqc/v1/users/default',
        method: 'put',
        data,
      }),
    },
    fields: [
      { name: 'realName', type: 'string', label: name },
      { name: 'email', type: 'string', label: email },
      { name: 'emailCaptcha', type: 'string', label: emailCaptcha },
      { name: 'newEmail', type: 'email', label: newEmail, required: true },
    ],
  };
};
