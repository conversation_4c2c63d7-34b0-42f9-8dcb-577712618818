export default ({ intlPrefix, intl }) => {
  const url = '/iam/yqc/users/self/detail';

  const nameLabel = intl.formatMessage({ id: 'iam.common.model.realName', defaultMessage: '姓名' });
  const aliasNameLabel = intl.formatMessage({ id: 'iam.common.model.aliasName', defaultMessage: '别名' });
  const language = intl.formatMessage({ id: 'zknow.common.model.language', defaultMessage: '语言' });
  const timeZone = intl.formatMessage({ id: 'iam.common.model.timeZone', defaultMessage: '时区' });
  const email = intl.formatMessage({ id: 'iam.common.model.email', defaultMessage: '邮箱' });
  const phone = intl.formatMessage({ id: 'iam.common.model.phone', defaultMessage: '手机号' });
  const wechat = intl.formatMessage({ id: 'iam.common.model.wechat', defaultMessage: '微信' });
  const wechatEnterprise = intl.formatMessage({ id: 'iam.common.model.wechatEnterprise', defaultMessage: '企业微信' });
  const dingding = intl.formatMessage({ id: 'iam.common.model.dingding', defaultMessage: '钉钉' });
  const lark = intl.formatMessage({ id: 'iam.common.model.lark', defaultMessage: '飞书' });

  const phonePattern = /^1[3-9]\d{9}$/;
  const validatePolicy = (value, name, record) => {
    const phoneValue = record.get('phone');
    if (phoneValue) {
      if (phonePattern.test(phoneValue)) {
        return true;
      }
      return intl.formatMessage({ id: 'iam.common.model.phone.error', defaultMessage: '请填写正确的手机号' });
    }
    return true;
  };
  return {
    autoQuery: true,
    selection: false,
    paging: false,

    transport: {
      read: {
        url,
        method: 'get',
      },
      submit: ({ data: [data] }) => ({
        url: '/iam/yqc/v1/users/default',
        method: 'put',
        data,
      }),
    },
    fields: [
      { name: 'realName', type: 'string', label: nameLabel, required: true },
      { name: 'aliasName', type: 'string', label: aliasNameLabel },
      { name: 'language', type: 'string', label: language, required: true, lookupCode: 'TENANT_LANGUAGE_OPTIONS' },
      { name: 'timeZone', type: 'string', label: timeZone, required: true, lookupUrl: 'hpfm/v1/lookup/queryCode?lookupTypeCode=TIME_ZONE' },
      { name: 'email', type: 'string', label: email },
      { name: 'phone', type: 'string', label: phone, validator: validatePolicy },
      { name: 'wechat', type: 'string', label: wechat },
      { name: 'wechat_enterprise', type: 'string', label: wechatEnterprise },
      { name: 'ding_talk', type: 'string', label: dingding },
      { name: 'lark', type: 'string', label: lark },
      { name: 'publicKey', type: 'string' },
      // { name: 'lovTest', type: 'string', label: 'lovTest', lovCode: 'selectCategory', textField: 'name', valueField: 'code' },
    ],
  };
};
