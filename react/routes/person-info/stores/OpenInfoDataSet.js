export default ({ intlPrefix, intl }) => {
  const url = '/iam/yqc/v1/user-open-account';

  return {
    autoQuery: true,
    selection: false,
    paging: false,
    dataKey: null,
    transport: {
      read: {
        url,
        method: 'get',
      },
      submit: ({ data: [data] }) => ({
        url: '/iam/yqc/v1/users/default',
        method: 'put',
        data,
      }),
    },
    fields: [

    ],
  };
};
