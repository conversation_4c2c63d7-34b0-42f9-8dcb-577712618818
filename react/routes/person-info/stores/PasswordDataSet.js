import JSEncrypt from 'jsencrypt';

const encrypt = new JSEncrypt();

export default ({ intl, organizationId, checkPassword, userTenantId, infoDataSet }) => {
  const url = '/iam/hzero/v1/users/password';

  const originalPassword = intl.formatMessage({ id: 'iam.personInfo.originalPassword', defaultMessage: '原始密码' });
  const password = intl.formatMessage({ id: 'iam.personInfo.newPassword', defaultMessage: '新密码' });
  const passwordConfirm = intl.formatMessage({ id: 'iam.personInfo.passwordConfirm', defaultMessage: '确认新密码' });

  function validatePassword(value, name, record) {
    if (value !== record.get('password')) {
      return intl.formatMessage({ id: 'iam.personInfo.invalidPassword', defaultMessage: '两次密码不相同' });
    }
  }

  async function validatePolicy(value) {
    const ret = await checkPassword(value, userTenantId, intl);
    return ret;
  }

  return {
    selection: false,
    paging: false,
    transport: {
      submit: ({ data: [data] }) => {
        encrypt.setPublicKey(infoDataSet.current?.get('publicKey'));
        const newData = {
          ...data,
          originalPassword: encrypt.encrypt(data.originalPassword),
          password: encrypt.encrypt(data.password),
        };
        return {
          url,
          method: 'put',
          data: newData,
        };
      },
    },
    fields: [
      { name: 'originalPassword', type: 'password', label: originalPassword, required: true },
      { name: 'password', type: 'password', label: password, required: true, validator: validatePolicy },
      { name: 'passwordIsEncrypt', type: 'boolean', defaultValue: false },
      { name: 'organizationId', type: 'string', defaultValue: organizationId },
      { name: 'passwordConfirm', type: 'password', label: passwordConfirm, required: true, validator: validatePassword, ignore: 'always' },
    ],
    // feedback: {
    //   submitFailed: (error) => {
    //     message.error(intl.formatMessage({ id: error.message }));
    //   },
    // },
  };
};
