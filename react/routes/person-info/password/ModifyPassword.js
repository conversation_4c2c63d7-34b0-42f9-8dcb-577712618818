/* eslint-disable no-nested-ternary */
import React, { useContext, useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Content } from '@yqcloud/apps-master';
import axios from 'axios';
import { Button, Form, Password, message } from 'choerodon-ui/pro';
import { Alert } from 'choerodon-ui';
import { logout } from '@zknow/utils';
import Store from '../stores';
import './index.less';

function ListView() {
  const context = useContext(Store);
  const { intl, prefixCls, intlPrefix, passwordDataSet, AppState: { getUserInfo, currentMenuType: { tenantId } } } = context;
  const isLdap = getUserInfo.person.ldap;
  const isInterfaceLogin = getUserInfo.person.interfaceLoginFlag;
  const [passwordPolicies, setPasswordPolicies] = useState();

  useEffect(() => {
    // 获取密码策略
    getPasswordPolicies();
    // 修改密码不允许粘贴
    document.querySelectorAll('input[type=password]').forEach(value => {
      value.onpaste = () => false;
    });
  }, []);

  async function getPasswordPolicies() {
    const res = await axios.get(`/iam/yqc/${tenantId}/password-policies`);
    if (res?.failed) {
      message.error(res?.message);
    } else {
      setPasswordPolicies(res);
    }
  }

  function handleCancel() {
    passwordDataSet.reset();
  }

  async function handleSave() {
    const validate = await passwordDataSet.validate();
    if (!validate) {
      return false;
    }
    const result = await passwordDataSet.submit();
    if (result && result?.failed) {
      message.error(result.message);
    } else {
      setTimeout(() => {
        logout();
      });
    }
  }

  function renderAlert() {
    const upperComma = passwordPolicies?.lowercaseCount || passwordPolicies?.digitsCount || passwordPolicies?.specialCharCount;
    const lowerComma = passwordPolicies?.digitsCount || passwordPolicies?.specialCharCount;
    const digitsComma = passwordPolicies?.specialCharCount;
    return (
      <Alert
        type="info"
        showIcon
        style={{
          margin: '16px',
        }}
        message={
          intl.formatMessage(
            { id: 'iam.personInfo.password.require', defaultMessage: '密码至少包含{characterTypeCount}种字符类型，必须包含：{uppercaseCount}{lowercaseCount}{digitsCount}{specialCharCount}。{hasSpecialChar}' },
            {
              characterTypeCount: passwordPolicies?.characterTypeCount,
              uppercaseCount: passwordPolicies?.uppercaseCount ? upperComma ? `${intl.formatMessage({ id: 'iam.personInfo.model.uppercaseLetter', defaultMessage: '大写字母' })}、` : `${intl.formatMessage({ id: 'iam.personInfo.model.uppercaseLetter', defaultMessage: '大写字母' })}` : '',
              lowercaseCount: passwordPolicies?.lowercaseCount ? lowerComma ? `${intl.formatMessage({ id: 'iam.personInfo.model.lowercaseLetter', defaultMessage: '小写字母' })}、` : `${intl.formatMessage({ id: 'iam.personInfo.model.lowercaseLetter', defaultMessage: '小写字母' })}` : '',
              digitsCount: passwordPolicies?.digitsCount ? digitsComma ? `${intl.formatMessage({ id: 'iam.personInfo.model.policyNumber', defaultMessage: '数字' })}、` : `${intl.formatMessage({ id: 'iam.personInfo.model.policyNumber', defaultMessage: '数字' })}` : '',
              specialCharCount: passwordPolicies?.specialCharCount ? `${intl.formatMessage({ id: 'iam.personInfo.model.policySymbol', defaultMessage: '特殊符号' })}` : '',
              hasSpecialChar: passwordPolicies?.specialCharCount ? `${intl.formatMessage({ id: 'iam.personInfo.model.complexity.help', defaultMessage: '特殊字符包括：[ ]~~@#$%&*\\-_=+l/()<>,.;:!' })}` : '',
            },
          )
        }
      />
    );
  }

  return (
    <Content>
      <div className={`${prefixCls}-password`}>
        <div className={`${prefixCls}-password-header`}>
          <div className={`${prefixCls}-password-header-title`}>
            {intl.formatMessage({ id: 'iam.personInfo.passwordChange', defaultMessage: '修改密码' })}
          </div>
          {isLdap || isInterfaceLogin ? (
            <div className={`${prefixCls}-password-header-tips`}>
              {isLdap ? intl.formatMessage({ id: 'iam.personInfo.password.changeLDAP.help', defaultMessage: 'LDAP用户不支持修改密码' }) : intl.formatMessage({ id: 'iam.personInfo.password.interfaceUserChangePassword.help', defaultMessage: '接口用户不支持修改密码' })}
            </div>
          ) : null}
        </div>
        <div className={`${prefixCls}-password-message`}>
          {renderAlert()}
          <div className={`${prefixCls}-password-message-form`}>
            <Form disabled={isLdap || isInterfaceLogin} dataSet={passwordDataSet} labelLayout="horizontal" labelWidth={160}>
              <Password
                name="originalPassword"
                autoComplete="new-password"
                placeholder={intl.formatMessage({ id: 'iam.personInfo.originalPassword.tips', defaultMessage: '请输入原始密码' })}
              />
              <Password
                name="password"
                autoComplete="new-password"
                restrict={/[^\x00-\xff ]/gi}
                placeholder={intl.formatMessage({ id: 'iam.personInfo.password.tips', defaultMessage: '请输入新密码' })}
              />
              <Password
                name="passwordConfirm"
                autoComplete="new-password"
                restrict={/[^\x00-\xff ]/gi}
                placeholder={intl.formatMessage({ id: 'iam.personInfo.password.tips.again', defaultMessage: '再次输入新密码' })}
              />
            </Form>
          </div>
          {!isLdap && (
            <div className="iam-person-info-password-btns">
              <Button onClick={handleSave} color="primary" funcType="raised">
                {intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}
              </Button>
              <Button onClick={handleCancel} color="secondary" funcType="raised">
                {intl.formatMessage({ id: 'zknow.common.button.reset', defaultMessage: '重置' })}
              </Button>
            </div>
          )}
        </div>
      </div>
    </Content>
  );
}

export default observer(ListView);
