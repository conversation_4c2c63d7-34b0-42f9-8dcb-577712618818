#login_container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.iam-person-info-openAppBind {
  display: flex;
  flex-direction: column;
  align-items: center;

  &-header {
    width: 720px;
    padding-left: 24px;
    margin-bottom: 18px;
    color: #12274d;

    &-title {
      height: 28px;
      font-size: 20px;
      font-weight: 500;
      font-family: PingFangSC-Medium, PingFang SC;
      line-height: 28px;
      margin-bottom: 4px;
    }

    &-tips {
      height: 22px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      line-height: 22px;
      opacity: 0.65;
    }
  }

  &-message {
    width: 720px;
    padding-left: 16px;
    background: #fff;
    box-shadow: 0 1px 6px 0 rgba(31, 35, 41, 0.04);
    border-radius: 4px;
    border: 1px solid rgba(203, 210, 220, 0.65);

    &-item {
      min-height: 82px;
      border-bottom: 1px solid rgba(203, 210, 220, 0.5);
      display: flex;
      align-items: center;

      &.hidden-border {
        border-bottom: none;
      }

      &-left {
        width: 40px;
        height: 40px;

        &-img {
          width: 100%;
          height: 100%;
        }
      }

      &-middle {
        width: 462px;
        margin: 0 64px 0 18px;
        padding: 16px 0;
        color: #12274d;

        &-title {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          margin-bottom: 4px;

          &-status {
            min-width: 60px;
            height: 24px;
            padding: 0 12px;
            margin-left: 16px;
            background-color: rgba(203, 210, 220, 0.25);
            border-radius: 12px;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;

            &.enabled {
              background-color: rgba(217, 247, 190, 0.5);
              color: #5ca916;
            }

            &.repeat {
              background-color: #fffce9;
              color: #ffb400;
            }
          }
        }

        &-tips {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          opacity: 0.65;
        }
      }

      &-children {
        min-height: 62px;
        display: flex;
        align-items: center;
        margin-left: 58px;
        padding-right: 36px;
        border-bottom: 1px solid rgba(203, 210, 220, 0.5);

        &-title {
          display: flex;
          align-items: center;
          width: 5.26rem;
        }
      }
    }
  }
}