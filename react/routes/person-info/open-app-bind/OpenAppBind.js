import React, { useContext, useMemo, useCallback, useEffect, useRef } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';
import { Spin, Modal, message, Button } from 'choerodon-ui/pro';
import { axios } from '@yqcloud/apps-master';
import { Empty } from '@zknow/components';
import classnames from 'classnames';
import Bind from './Bind';
import Store from '../stores';
import './index.less';

// 'choerodon'猪齿鱼先去掉
const showApplist = ['wechat_enterprise', 'ding_talk', 'lark', 'wechat_open_account'];

const OpenAppBind = () => {
  const { intl, prefixCls, intlPrefix, openInfoDataSet, imgMap, nameMap, tenantId } = useContext(Store);
  const modal = useRef();
  const uniqueOpenInfoDataSet = useMemo(() => {
    const uniqueDataSet = new DataSet();
    const openAppCodeMap = {};
    openInfoDataSet.forEach(r => {
      if (!openAppCodeMap[r.get('openAppCode')]) {
        openAppCodeMap[r.get('openAppCode')] = true;
        const record = r.toData();
        uniqueDataSet.create(record);
      }
    });
    // uniqueDataSet.create({
    //   name: '微信公众号',
    //   openAppCode: 'wechat',
    // });
    return uniqueDataSet.filter(r => showApplist.includes(r.get('openAppCode')));
  });

  const handleUnBind = async (record) => {
    const ok = await Modal.confirm({
      title: intl.formatMessage({ id: 'iam.personInfo.unbind.confirm.title', defaultMessage: '确认解除绑定？' }),
      children: intl.formatMessage({ id: 'iam.personInfo.unbind.confirm.content', defaultMessage: '解除绑定后，不能再用当前{name}号登录系统。' }, { name: record?.get('name') }),
      closeable: true,
    });
    if (ok === 'ok') {
      try {
        const res = await axios.post('iam/yqc/v1/user-open-account/unbind', record?.toData());
        if (!res.failed) {
          message.success(intl.formatMessage({ id: 'iam.personInfo.unbind.success', defaultMessage: '解除绑定成功!' }));
          await openInfoDataSet.query();
        }
      } catch (err) {
        message.error(err.message);
      }
    }
  };

  const handleBind = async (record) => {
    modal.current = Modal.open({
      title: intl.formatMessage({ id: 'iam.personInfo.qrcode.bind', defaultMessage: '扫码绑定' }),
      children: <Bind modalRef={modal} openInfoDataSet={openInfoDataSet} bindInfo={record} tenantId={tenantId} intl={intl} />,
      onClose: () => {
        modal.current = null;
      },
      footer: null,
      closable: true,
    });
  };

  const renderBindButton = useCallback((record) => {
    return record.get('openAccountId') ? (
      <Button color="red" onClick={() => handleUnBind(record)} funcType="raised">
        <span>{intl.formatMessage({ id: 'iam.personInfo.action.unbind', defaultMessage: '解除绑定' })}</span>
      </Button>
    ) : (
      <Button color="primary" funcType="raised" onClick={() => handleBind(record)}>
        <span style={{ width: '56px' }}>{intl.formatMessage({ id: 'iam.personInfo.action.bind', defaultMessage: '绑定' })}</span>
      </Button>
    );
  }, [intl]);

  const renderOpenAppInfoChildren = (record) => {
    const curPrefixCls = `${prefixCls}-openAppBind-message-item`;
    const name = record.get('name');
    const isBind = record.get('openAccountId');
    return (
      <div className={`${curPrefixCls}-children`}>
        <div className={`${curPrefixCls}-children-title`}>
          {name}
          <div className={classnames(`${curPrefixCls}-middle-title-status`, { enabled: isBind })}>
            {isBind ? intl.formatMessage({ id: 'iam.personInfo.status.bind', defaultMessage: '已绑定' }) : intl.formatMessage({ id: 'iam.personInfo.notbind', defaultMessage: '未绑定' })}
          </div>
        </div>
        <div>{renderBindButton(record)}</div>
      </div>
    );
  };

  const renderOpenAppInfo = (record, index, arr) => {
    const curPrefixCls = `${prefixCls}-openAppBind-message-item`;
    const classNames = classnames(curPrefixCls, { 'hidden-border': index === arr.length - 1 });
    const name = nameMap[record.get('openAppCode')] || record.get('name');
    const repeatRecords = openInfoDataSet.filter(r => r.get('openAppCode') === record.get('openAppCode'));
    const isRepeat = repeatRecords.length > 1;
    const repeatBind = repeatRecords.some(r => r.get('openAccountId'));
    const repeatBindLength = repeatRecords.filter(r => r.get('openAccountId'))?.length;
    return (
      <React.Fragment>
        <div className={classNames} key={record.get('appId')}>
          <div className={`${curPrefixCls}-left`}>
            <img alt="" src={imgMap[record.get('openAppCode')]} className={`${curPrefixCls}-left-img`} />
          </div>
          <div className={`${curPrefixCls}-middle`}>
            <div className={`${curPrefixCls}-middle-title`}>
              {name}
              {!isRepeat ? (
                <div className={classnames(`${curPrefixCls}-middle-title-status`, { enabled: record.get('openAccountId') })}>
                  {record.get('openAccountId') ? intl.formatMessage({ id: 'iam.personInfo.status.bind', defaultMessage: '已绑定' }) : intl.formatMessage({ id: 'iam.personInfo.notbind', defaultMessage: '未绑定' })}
                </div>
              ) : (
                <div className={classnames(`${curPrefixCls}-middle-title-status`, { repeat: repeatBind })}>
                  {(repeatBind ? intl.formatMessage({ id: 'iam.personInfo.status.bind', defaultMessage: '已绑定' }) : intl.formatMessage({ id: 'iam.personInfo.notbind', defaultMessage: '未绑定' })).replace(/{length}/g, repeatBindLength)}
                </div>
              )}
            </div>
            <div className={`${curPrefixCls}-middle-tips`}>
              {record.get('openAppCode') !== 'choerodon'
                ? intl.formatMessage({ id: 'iam.personInfo.thirdPartyAccountBind.tips', defaultMessage: '绑定后支持{name}扫码登录，可通过{name}工作台免密访问系统、接收系统通知' }).replace(/{name}/g, name)
                : intl.formatMessage({ id: 'iam.personInfo.thirdPartyAccountBind.choerodon.tips', defaultMessage: '绑定后可将燕千云中的事件单等单据同步到猪齿鱼，实现两个系统业务单据的融合' })}
            </div>
          </div>
          {!isRepeat && <div className={`${curPrefixCls}-right`}>{renderBindButton(record)}</div>}
        </div>
        {isRepeat && <div>{repeatRecords.map(renderOpenAppInfoChildren)}</div>}
      </React.Fragment>
    );
  };

  return (
    <div className={`${prefixCls}-openAppBind`}>
      <div className={`${prefixCls}-openAppBind-header`}>
        <div className={`${prefixCls}-openAppBind-header-title`}>{intl.formatMessage({ id: 'iam.personInfo.thirdPartyAccount', defaultMessage: '第三方账号' })}</div>
        <div className={`${prefixCls}-openAppBind-header-tips`}>{intl.formatMessage({ id: 'iam.personInfo.thirdPartyAccount.tips', defaultMessage: '第三方账号绑定后，支持接收燕千云系统消息或者提交工单' })}</div>
      </div>
      <div className={`${prefixCls}-openAppBind-message`}>
        <Spin spinning={openInfoDataSet.status === 'loading' && !modal.current}>
          {uniqueOpenInfoDataSet?.length > 0 ? uniqueOpenInfoDataSet.map(renderOpenAppInfo) : <Empty type="empty" />}
        </Spin>
      </div>
    </div>
  );
};

export default observer(OpenAppBind);
