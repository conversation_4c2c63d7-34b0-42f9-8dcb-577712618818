import React, { useEffect } from 'react';
import { getAccessToken } from '@zknow/utils';
import { inject } from 'mobx-react';
import { message } from 'choerodon-ui/pro';
import './index.less';
import useInterval from '../../../components/use-interval';

export default inject('AppState')(({ bindInfo, tenantId, intl, AppState, openInfoDataSet, modalRef }) => {
  const type = bindInfo?.get('openAppCode');
  const clientId = window.location.host?.split('.')[0];
  const { userInfo: { personId: userId } } = AppState;
  const typeNameMap = {
    wechat: `${intl.formatMessage({ id: 'iam.common.bind.wechat', defaultMessage: '微信绑定' })}`,
    wechat_enterprise: `${intl.formatMessage({ id: 'iam.common.bind.enterprise.wechat', defaultMessage: '企业微信绑定' })}`,
    ding_talk: `${intl.formatMessage({ id: 'iam.common.bind.enterprise.dingding', defaultMessage: '钉钉绑定' })}`,
    lark: `${intl.formatMessage({ id: 'iam.common.bind.enterprise.lark', defaultMessage: '飞书绑定' })}`,
  };

  const appId = bindInfo?.get('appId');
  const agentId = bindInfo?.get('agentId');
  const authUrl = bindInfo?.get('authUrl');
  const uri = encodeURIComponent(`${window._env_.API_HOST || window.location.origin}/oauth/oauth/open/${type}/callback?agentid=${agentId}&organization_id=${tenantId}&client_id=${clientId}&redirect_uri=${encodeURIComponent(window.location.href)}&tenant_id=${tenantId}&access_token=${getAccessToken().split('bearer ')[1]}`);
  const lang = AppState?.userInfo?.language || 'zh_CN';
  function showQrCode() {
    if (authUrl) return;
    const redirectUri = encodeURIComponent(`${window._env_.API_HOST || window.location.origin}/oauth/oauth/open/${type}/callback?agentid=${agentId}&organization_id=${tenantId}&client_id=${clientId}&redirect_uri=${encodeURIComponent(window.location.href)}&access_token=${getAccessToken().split('bearer ')[1]}`);
    if (type === 'wechat') {
      const obj = new window.WxLogin({
        self_redirect: false,
        id: 'login_container',
        appid: appId,
        scope: 'snsapi_login',
        redirect_uri: redirectUri,
        state: '',
        style: '',
        href: `${(window._env_.API_HOST || window.location.origin).replace('http://', 'https://')}/oauth/static/main/css/part/index.css`,
        // href: 'http://localhost:8020/oauth/static/main/css/part/index.css',
      });
    } else if (type === 'wechat_enterprise') {
      const wwlogin = new window.WwLogin({
        self_redirect: false,
        id: 'login_container',
        appid: appId,
        agentid: agentId,
        scope: 'snsapi_login',
        redirect_uri: encodeURIComponent(`${window._env_.API_HOST || window.location.origin}/oauth/oauth/open/${type}/callback?agentid=${agentId}&organization_id=${tenantId}&client_id=${clientId}&redirect_uri=${encodeURIComponent(window.location.href)}&tenant_id=${tenantId}&access_token=${getAccessToken().split('bearer ')[1]}`),
        state: '123',
        style: '',
        href: `${(window._env_.API_HOST || window.location.origin).replace('http://', 'https://')}/oauth/static/main/css/part/index.css`,
        // href: 'http://localhost:8020/oauth/static/main/css/part/index.css',
      });
    } else if (type === 'ding_talk') {
      const dingdingUri = encodeURIComponent(`${window._env_.API_HOST || window.location.origin}/oauth/oauth/open/${type}/callback?lang=${lang}&organization_id=${tenantId}&client_id=${clientId}&redirect_uri=${encodeURIComponent(window.location.href)}&tenant_id=${tenantId}&access_token=${getAccessToken().split('bearer ')[1]}&way=WEB`);
      const goto = encodeURIComponent(`https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=${appId}&response_type=code&scope=snsapi_login&state=&redirect_uri=${dingdingUri}`);
      const handleMessage = (event) => {
        // 钉钉扫码跳转
        const { origin } = event;
        if (origin === 'https://login.dingtalk.com') { // 判断是否来自ddLogin扫码事件。
          const loginTmpCode = event.data;
          // 获取到loginTmpCode后就可以在这里构造跳转链接进行跳转了
          const url = `https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=${appId}&response_type=code&scope=snsapi_login&state=&redirect_uri=${dingdingUri}&loginTmpCode=${loginTmpCode}`;
          window.location.assign(url);
        }
      };
      if (typeof window.addEventListener !== 'undefined') {
        window.addEventListener('message', handleMessage, false);
      } else if (typeof window.attachEvent !== 'undefined') {
        window.attachEvent('onmessage', handleMessage);
      }
      window.DDLogin({
        id: 'login_container',
        goto,
        style: 'border:none;background-color:#FFFFFF;',
        width: '365',
        height: '400',
      });
    } else if (type === 'lark') {
      const larkUri = encodeURIComponent(`${window._env_.API_HOST || window.location.origin}/oauth/oauth/open/${type}/callback?organization_id=${tenantId}&client_id=${clientId}&way=BIND&redirect_uri=${encodeURIComponent(window.location.href)}&tenant_id=${tenantId}&access_token=${getAccessToken().split('bearer ')[1]}`);
      const goto = `https://passport.feishu.cn/suite/passport/oauth/authorize?client_id=${appId}&redirect_uri=${larkUri}&response_type=code&state=STATE`;
      const QRLoginObj = window.QRLogin({
        id: 'login_container',
        goto,
        width: '365',
        height: '400',
        style: 'border:none;background-color:#FFFFFF;',
      });
      const handleMessage = (event) => {
        const { origin } = event;
        // 使用 matchOrigin 方法来判断 message 是否来自飞书页面
        if (QRLoginObj.matchOrigin(origin)) {
          const loginTmpCode = event.data;
          // 在授权页面地址上拼接上参数 tmp_code，并跳转
          window.location.href = `${goto}&tmp_code=${loginTmpCode}`;
        }
      };
      if (typeof window.addEventListener !== 'undefined') {
        window.addEventListener('message', handleMessage, false);
      } else if (typeof window.attachEvent !== 'undefined') {
        window.attachEvent('onmessage', handleMessage);
      }
    }
  }
  function loadScript(url, callback) {
    const script = document.createElement('script');
    if (script.readyState) { // IE
      script.onreadystatechange = function () {
        if (script.readyState === 'loaded' || script.readyState === 'complete') {
          script.onreadystatechange = null;
          callback();
        }
      };
    } else { // 其他浏览器
      script.onload = function () {
        callback();
      };
    }
    script.src = url;
    document.getElementsByTagName('head')[0].appendChild(script);
  }

  function loadJs() {
    const jsSrcMap = {
      wechat: 'https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js',
      wechat_enterprise: 'https://wwcdn.weixin.qq.com/node/wework/wwopen/js/wwLogin-1.2.7.js',
      ding_talk: 'https://g.alicdn.com/dingding/dinglogin/0.0.5/ddLogin.js',
      lark: 'https://sf3-cn.feishucdn.com/obj/static/lark/passport/qrcode/LarkSSOSDKWebQRCode-1.0.1.js',
    };
    loadScript(jsSrcMap[type], showQrCode);
  }
  useEffect(() => {
    if (type === 'wechat') {
      // axios.get('')
      // if (!window.WxLogin) {
      //   loadJs();
      // } else showQrCode();
    } else if (type === 'wechat_enterprise') {
      if (!window.WwLogin) {
        loadJs();
      } else showQrCode();
    } else if (type === 'ding_talk') {
      if (!window.DDLogin) {
        loadJs();
      } else showQrCode();
    } else if (type === 'lark') {
      if (!window.QRLogin) {
        loadJs();
      } else showQrCode();
    }
  }, []);

  useInterval(() => {
    if (type === 'wechat_open_account') {
      openInfoDataSet.query();
      const wechatBindInfoRecord = openInfoDataSet.find(record => record.get('openAppCode') === 'wechat_open_account');
      if (wechatBindInfoRecord?.get('creationDate')) {
        modalRef.current?.close();
        modalRef.current = null;
        message.success(intl.formatMessage({ id: 'iam.common.operation.success', defaultMessage: '操作成功' }));
      }
    }
  }, 1000);

  return (
    <div className="iam-open-bind">
      <h4 style={{ textAlign: 'center' }}>{typeNameMap[type] || `${intl.formatMessage({ id: 'iam.common.model.bind', defaultMessage: '绑定' })}`}</h4>
      {type === 'wechat_open_account' && (
        <div style={{ flexDirection: 'column' }} id="login_container">
          <div style={{ textAlign: 'center' }}>{intl.formatMessage({ id: 'iam.common.wechatScanBind.tips', defaultMessage: '请使用微信扫码后关注公众号自动绑定' })}</div>
          <img style={{ width: '50%' }} src={`${window._env_.API_HOST}/iam/yqc/a/public/quickResponseCode?userId=${userId}&tenantId=${tenantId}`} alt="" />
        </div>
      )}
      {type === 'wechat_enterprise' && authUrl
        // eslint-disable-next-line jsx-a11y/iframe-has-title
        ? (<iframe style={{ border: '0' }} src={`${authUrl}/wwopen/sso/qrConnect?state=123&appid=${appId}&agentid=${agentId}&redirect_uri=${uri}&login_type=jssdk&href=https://api.dev.yqcloud.com/oauth/static/main/css/part/index.css`} />)
        : (<><div id="login_container" /></>)}
    </div>
  );
});
