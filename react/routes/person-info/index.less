@import '~choerodon-ui/lib/style/themes/default';

.iam-open-bind {
  min-height: 3.33rem;
  display: flex;
  justify-content: center;
  flex-direction: column;
  iframe {
    min-height: 2.4rem !important;
  }
  h4 {
    font-size: 24px;
    text-align: center;
    font-weight: 500;
    color: #262626;
    line-height: 33px;
  }
  div {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.iam-person-info {
  &-account {
    margin: 0 auto;
    padding: 0 0.24rem;
    &-bind {
      display: flex;
      justify-content: space-between;
      align-items: center;
      &.email {
        > div {
          width: 100%;
          min-height: 0.32rem;
          position: relative;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          padding-right: 50px;
        }
        // button > span:not(.i-icon) {
        //   display: none;
        // }
        &:hover {
          button > span:not(.i-icon) {
            display: inline-block;
          }
        }
      }
      &-info {
        display: inline-flex;
        align-items: center;
        span {
          margin-right: 0.04rem;
        }
      }
    }
    &-header {
      display: flex;
      margin-bottom: 0.24rem;
      justify-content: space-between;
      align-items: center;
      &-btns {
        flex-shrink: 0;
      }
      &-avatar {
        margin-right: 0.12rem;
        flex-shrink: 0;
        &.editable {
          cursor: pointer;
          pointer-events: none;
        }
      }
      .camera {
        z-index: 100;
        width: 56px;
        height: 56px;
        background: #000;
        opacity: 0;
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        border-radius: 50%;
        cursor: pointer;
      }
      .camera:hover {
        opacity: 0.618;
        transition: all 0.3s;
      }
      &-info {
        width: 100%;
        .name {
          color: #020e26;
          font-size: 0.16rem;
          font-weight: 500;
          line-height: 0.24rem;
          margin-bottom: 0.06rem;
        }
        .email {
          color: #8c8c8c;
          line-height: 0.2rem;
          font-size: 0.12rem;
          display: flex;
          align-items: center;
          > span {
            margin-right: 0.08rem;
          }
        }
      }
    }
    &-table-header {
      display: flex;
      height: 0.14rem;
      margin-bottom: 0.28rem;
      justify-content: space-between;
      align-items: center;
    }
  }
}
.account-bind-form {
  .c7n-pro-form-header {
    margin-top: 0;
  }
}
