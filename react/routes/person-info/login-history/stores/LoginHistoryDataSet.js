import moment from 'moment';
import { getQueryParams } from '@zknow/utils';

const MOMENT_FORMAT = 'YYYY-MM-DD HH:mm:ss';

export default ({ intlPrefix, intl }) => {
  const loginDate = intl.formatMessage({ id: 'iam.personInfo.loginHistory.loginDate', defaultMessage: '登录时间' });
  const loginDevice = intl.formatMessage({ id: 'iam.personInfo.loginHistory.loginDevice', defaultMessage: '登录设备' });
  const loginIp = intl.formatMessage({ id: 'iam.personInfo.loginHistory.loginIp', defaultMessage: '登录地址' });
  const loginPort = intl.formatMessage({ id: 'iam.personInfo.loginHistory.loginPort', defaultMessage: '登录端口号' });

  return {
    autoQuery: true,
    selection: false,
    autoLocateFirst: false,
    transport: {
      read: ({ data, params }) => {
        // 处理 DatePicker 的范围筛选
        if (data?.search_loginDate) {
          const [startLoginDate, endLoginDate] = data?.search_loginDate;
          const startDate = startLoginDate ? moment(startLoginDate).format(MOMENT_FORMAT) : startLoginDate;
          const endDate = endLoginDate ? moment(endLoginDate).format(MOMENT_FORMAT) : endLoginDate;
          data.search_startLoginDate = startDate;
          data.search_endLoginDate = endDate;
          delete data.search_loginDate;
        }
        return {
          url: 'hpfm/yqc/v1/audit-logins/self',
          method: 'get',
          data: getQueryParams(data),
        };
      },
    },
    fields: [
      { name: 'loginDate', type: 'dateTime', format: MOMENT_FORMAT, label: loginDate },
      { name: 'loginDevice', type: 'string', label: loginDevice },
      { name: 'loginIp', type: 'string', label: loginIp },
      { name: 'loginPort', type: 'string', label: loginPort },
      { name: 'auditId', type: 'string' },
      // kickoff 在线状态，true 为在线
      { name: 'kickoff', type: 'boolean' },
      { name: 'accessToken', type: 'string' },
    ],
    queryFields: [
      { name: 'loginDevice', type: 'string', label: loginDevice },
      // { name: 'loginIp', type: 'string', label: loginIp },
      // {
      //   name: 'loginDate',
      //   type: 'dateTime',
      //   range: true,
      //   format: 'YYYY-MM-DD HH:mm:ss',
      //   label: loginDate,
      // },
    ],
  };
};
