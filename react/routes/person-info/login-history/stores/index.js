import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import LoginHistoryDataSet from './LoginHistoryDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState', 'HeaderStore')(formatterCollections({ code: ['iam.common', 'iam.personInfo'] })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      HeaderStore: { getTenantConfig: { themeColor } },
    } = props;

    const intlPrefix = 'iam.login-history';

    const loginHistoryDataSet = useMemo(() => new DataSet(LoginHistoryDataSet({ intlPrefix, intl })), []);

    const value = {
      ...props,
      intlPrefix,
      loginHistoryDataSet,
      themeColor,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
