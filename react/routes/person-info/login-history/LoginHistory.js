import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { getCookieToken } from '@zknow/utils';
import { Content, TabPage, axios } from '@yqcloud/apps-master';
import { Table, message } from 'choerodon-ui/pro';
import { TableHoverAction } from '@zknow/components';
import DeviceColumn from './device';
import Store from './stores';
import './index.less';

const { Column } = Table;

const LoginHistory = observer(() => {
  const { intl, intlPrefix, loginHistoryDataSet } = useContext(Store);
  // getCookieToken 不带 bearer， getAccessToken 带
  const accessToken = getCookieToken();

  function renderDevice({ record, name }) {
    return (
      <DeviceColumn
        name={name}
        record={record}
      />
    );
  }

  async function handleLogout(auditId) {
    try {
      if (auditId) {
        await axios.post(`/websocket/v1/yqc/yqcloud/logout?auditId=${auditId}`);
        message.success(intl.formatMessage({ id: 'iam.common.request.success', defaultMessage: '请求成功' }));
      }
    } catch (e) {
      message.error(intl.formatMessage({ id: 'iam.common.request.error', defaultMessage: '请求失败' }));
    } finally {
      loginHistoryDataSet.query();
    }
  }

  function renderOption({ record }) {
    const loginToken = record.get('accessToken');
    const auditId = record.get('auditId');
    const disabled = !record.get('kickoff');
    return (accessToken !== loginToken && !disabled) ? (
      <TableHoverAction
        record={record}
        actions={[
          {
            name: intl.formatMessage({ id: 'iam.personInfo.loginHistory.logout', defaultMessage: '退出登录' }),
            icon: 'logout',
            onClick: () => handleLogout(auditId),
          },
        ]}
      />
    ) : null;
  }

  // function refresh() {
  //   loginHistoryDataSet.query();
  // }

  return (
    <TabPage>
      <Content className="yqcloud-iam-history">
        <Table pristine autoHeight dataSet={loginHistoryDataSet}>
          <Column name="loginDevice" renderer={renderDevice} />
          <Column name="loginIp" />
          <Column name="loginPort" width={120} />
          <Column name="loginDate" />
          <Column width={140} renderer={renderOption} />
        </Table>
      </Content>
    </TabPage>
  );
});

export default LoginHistory;
