import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Icon } from '@zknow/components';
import { getCookieToken } from '@zknow/utils';
import Store from '../stores';

import './index.less';

const DISABLE_COLOR = '#d9d9d9';
const DEFAULT_COLOR = '#2979ff';

function DeviceColumn({ record, name }) {
  const { intl, intlPrefix, themeColor = DEFAULT_COLOR } = useContext(Store);
  const value = record.get(name);
  const loginToken = record.get('accessToken');
  const logged = record.get('kickoff');
  const token = getCookieToken();
  const icon = deviceIcon(value);

  return (
    <div className="iam-login-history-device">
      <span className="iam-login-history-device-icon">
        <Icon
          type={icon}
          theme="filled"
          fill={logged ? themeColor : DISABLE_COLOR}
          strokeLinecap="butt"
        />
      </span>
      <span className="iam-login-history-device-text">{value}</span>
      {token === loginToken && logged
        && <span className="iam-login-history-device-flag">{intl.formatMessage({ id: 'iam.personInfo.loginHistory.current', defaultMessage: '本机' })}</span>}
    </div>
  );
}

export default observer(DeviceColumn);

function deviceIcon(str) {
  const mobilePattern = /(iOS|Android)\S+/i;
  const webPattern = /(OS X|Windows|Linux)\S+/i;
  if (mobilePattern.test(str)) {
    return 'phone';
  } else if (webPattern.test(str)) {
    return 'computer';
  } else {
    return 'helpcenter';
  }
}
