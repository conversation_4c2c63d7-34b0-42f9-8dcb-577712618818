.iam-person-info {
  &-base {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    &-header {
      width: 720px;
      display: flex;
      padding-left: 24px;
      margin-bottom: 16px;
      justify-content: space-between;
      align-items: center;

      &-avatar {
        flex-shrink: 0;

        &.editable {
          cursor: pointer;
          pointer-events: none;
        }

        .c7n-avatar-string {
          font-size: 28px;
        }
      }

      .camera {
        width: 20px;
        height: 20px;
        z-index: 100;
        background: #fff;
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
        bottom: 0;
        right: 0;
        border-radius: 50%;
        cursor: pointer;
        box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(203, 210, 220, 0.5);
      }

      &-info {
        width: 100%;
        height: 56px;
        margin-left: 16px;

        .name {
          display: flex;
          align-items: center;
          height: 28px;
          font-size: 20px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #12274d;
          line-height: 28px;
          margin-bottom: 2px;

          .yq-cmp-status-tag {
            color: @primary-color !important;
            background-color: fade(@primary-color, 10%);
            margin-left: 12px;
          }
        }

        .email {
          height: 22px;
          display: flex;
          font-size: 14px;
          color: #8c8c8c;
          line-height: 22px;

          &-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: rgba(18, 39, 77, 0.65);

            .c7n-pro-btn-wrapper {
              width: 22px;
              margin-left: 16px;
              justify-content: center;
            }

            &-bind {
              display: inline-flex;
              align-items: center;

              &-edit {
                width: 22px;
                height: 22px;
                background-color: #fff;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                color: #12274d;
                margin-left: 16px;
                border-radius: 2px;
                &:hover {
                  background-color: fade(@primary-color, 12);
                  color: @primary-color;
                  transition-duration: 200ms;
                }
              }

              &-edit.verify {
                color: #ff9100;
                margin-left: 0.1rem;

                &:hover {
                  background-color: fade(#ff9100, 10);
                }
              }
            }
          }
        }
      }
    }

    &-message {
      width: 720px;
      background: #fff;
      box-shadow: 0 1px 6px 0 rgba(31, 35, 41, 0.04);
      border-radius: 4px;
      border: 1px solid rgba(203, 210, 220, 0.65);

      &-form {
        padding: 16px 24px;
        border-bottom: 1px solid rgba(203, 210, 220, 0.5);

        .c7n-pro-form-header {
          font-size: 14px;
          font-weight: 500;
          color: #12274d;
        }

        .c7n-pro-field-wrapper {
          padding: 0.04rem 0.16rem;
        }

        .c7n-pro-input,
        .c7n-pro-field-label {
          color: #12274d;
        }
      }

      &-buttons {
        height: 53px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  &-modify-container {
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-top: 50px;

    &-email {
      width: 354px;

      .back {
        height: 20px;
        font-size: 14px;
        margin-bottom: 12px;
        line-height: 20px;
        opacity: 0.65;
        cursor: pointer;
        display: flex;
        align-items: center;
      }

      .title {
        font-size: 28px;
        font-weight: 500;
        margin-bottom: 12px;
      }

      .description {
        opacity: 0.65;
        margin-bottom: 20px;
      }

      .form {
        .c7n-pro-field-wrapper {
          padding: 0;
        }
      }

      .verify-email {
        width: 100%;
      }

      .captcha {
        margin-top: 16px;
        height: 38px;
        width: 100%;
      }

      .resend {
        margin-top: 12px;
        color: #2979ff;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          cursor: pointer;
        }
      }
    }
  }
}
