import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import axios from 'axios';
import { Icon } from '@zknow/components';
import { message, Button, Form, TextField } from 'choerodon-ui/pro';
import queryString from 'query-string';
import Captcha from './captcha';
import './index.less';
import useInterval from '../../../components/use-interval';

function ModifyEmail(props) {
  const { context, setType, type, email } = props;
  const { intl, prefixCls, changeDataSet, intlPrefix } = context;
  const [disableTimer, setDisableTimer] = useState(0);
  const [step, setStep] = useState(0);
  const [captcha, setCaptcha] = useState('');

  useEffect(() => {
    changeDataSet.reset();
  }, []);

  const handleSubmit = async () => {
    if (type === 'modify') {
      if (!changeDataSet.current?.get('newEmail')) {
        message.info(intl.formatMessage({ id: 'iam.personInfo.noNewEmail.tips', defaultMessage: '请先填写新邮箱' }));
        return false;
      } else if (!changeDataSet.current?.get('captchaKey')) {
        message.info(intl.formatMessage({ id: 'iam.personInfo.noNewEmailCaptcha.tips', defaultMessage: '请先填写新邮箱验证码' }));
        return false;
      }
      try {
        const res = await axios.put(`/iam/hzero/v1/users/email/authenticate?${queryString.stringify({
          captcha,
          captchaKey: changeDataSet.current?.get('captchaKey'),
          email: changeDataSet.current?.get('newEmail'),
        })}`);
        if (!res.failed) {
          message.success(intl.formatMessage({ id: 'iam.personInfo.newEmail.success', defaultMessage: '修改邮箱成功' }));
          setTimeout(() => {
            window.location.reload();
          }, 500);
          return true;
        } else {
          message.error(res.message);
          return false;
        }
      } catch (err) {
        message.error(err.message);
        return false;
      }
    } else if (type === 'verify') {
      const res = await axios.post('/iam/yqc/users/valid-user-email', {
        email,
        verificationCode: captcha,
        captchaKey: changeDataSet.current?.get('captchaKey'),
      });
      if (!res?.failed) {
        message.success(intl.formatMessage({ id: 'iam.personInfo.verifyEmail.success', defaultMessage: '验证邮箱成功' }));
        setTimeout(() => {
          window.location.reload();
        }, 500);
        return true;
      } else {
        message.error(res.message);
        return false;
      }
    }
  };

  const handleGetCaptcha = async () => {
    const trueEmail = encodeURIComponent(type === 'modify' ? changeDataSet.current?.get('newEmail') : email);
    const res = await axios.get(`/iam/yqc/users/email/send-captcha?email=${trueEmail}`);
    if (!res.failed) {
      message.success(res.message || intl.formatMessage({ id: 'iam.personInfo.sendCaptcha.success', defaultMessage: '验证码发送成功' }));
      changeDataSet.current?.set('captchaKey', res.captchaKey);
      setDisableTimer(res.interval);
      setStep(1);
    } else {
      message.error(res.message);
    }
  };

  useInterval(() => {
    if (disableTimer !== 0) {
      setDisableTimer(disableTimer - 1);
    }
  }, 1000);

  const backProfile = () => {
    setType('profile');
  };

  const renderStep0 = () => (
    <div className={`${prefixCls}-modify-container`}>
      <div className={`${prefixCls}-modify-container-email`}>
        <div onClick={backProfile} className="back">
          <Icon type="ArrowLeft" size={16} />
          <div style={{ marginLeft: 6 }}>{intl.formatMessage({ id: 'zknow.common.button.back', defaultMessage: '返回' })}</div>
        </div>
        {type === 'modify' ? (
          <>
            <div className="title">{intl.formatMessage({ id: 'iam.personInfo.modify.email', defaultMessage: '修改邮箱' })}</div>
            <div className="description">{intl.formatMessage({ id: 'iam.personInfo.modifyEmail.tips', defaultMessage: '请修改邮箱，并进行验证' })}</div>
            <Form labelLayout="none" className="form" dataSet={changeDataSet}>
              <TextField name="newEmail" placeholder={intl.formatMessage({ id: 'iam.personInfo.modify.newEmail', defaultMessage: '请输入邮箱' })} />
            </Form>
          </>
        ) : (
          <>
            <div className="title">{intl.formatMessage({ id: 'iam.personInfo.verify.email', defaultMessage: '验证邮箱' })}</div>
            <div className="description">{intl.formatMessage({ id: 'iam.personInfo.verify.email.desc', defaultMessage: '为提升账号安全性，请进行邮箱验证' })}</div>
            <TextField disabled value={email} className="verify-email" />
          </>
        )}
        <Button
          funcType="raised"
          color="primary"
          className="captcha"
          onClick={handleGetCaptcha}
        >
          {intl.formatMessage({ id: 'iam.personInfo.getCaptcha', defaultMessage: '获取验证码' })}
        </Button>
      </div>
    </div>
  );

  const renderStep1 = () => (
    <div className={`${prefixCls}-modify-container`}>
      <div className={`${prefixCls}-modify-container-email`}>
        <div onClick={backProfile} className="back">
          <Icon type="ArrowLeft" size={16} />
          <div style={{ marginLeft: 6 }}>{intl.formatMessage({ id: 'zknow.common.button.back', defaultMessage: '返回' })}</div>
        </div>
        {type === 'modify' ? (
          <>
            <div className="title">{intl.formatMessage({ id: 'iam.personInfo.modify.email', defaultMessage: '修改邮箱' })}</div>
            <div className="description">{intl.formatMessage({ id: 'iam.personInfo.modifyEmail.help', defaultMessage: '验证码已发送至 {email}，有效期5分钟' }, { email: changeDataSet.current?.get('newEmail') })}</div>
          </>
        ) : (
          <>
            <div className="title">{intl.formatMessage({ id: 'iam.personInfo.verify.email', defaultMessage: '验证邮箱' })}</div>
            <div className="description">{intl.formatMessage({ id: 'iam.personInfo.verify.email.desc', defaultMessage: '为提升账号安全性，请进行邮箱验证' }, { email: changeDataSet.current?.get('newEmail') })}</div>
          </>
        )}
        <Captcha value={captcha} onChange={setCaptcha} theme="box" autoFocus length={6} />
        <Button
          funcType="raised"
          color="primary"
          className="captcha"
          disabled={captcha?.length !== 6}
          onClick={handleSubmit}
        >
          {intl.formatMessage({ id: 'zknow.common.button.submit', defaultMessage: '提交' })}
        </Button>
        {disableTimer !== 0 && (
          <div className="resend" style={{ color: '#12274d' }}>{intl.formatMessage({ id: 'iam.personInfo.sendCaptcha.timer', defaultMessage: '{second} 秒后可重新获取验证码' }, { second: disableTimer })}</div>
        )}
        {disableTimer === 0 && (
          <div
            className="resend"
            onClick={handleGetCaptcha}
          >
            {intl.formatMessage({ id: 'iam.personInfo.resendCaptcha', defaultMessage: '重新发送验证码' })}
          </div>
        )}
      </div>
    </div>
  );

  switch (step) {
  case 0:
    return renderStep0();
  case 1:
    return renderStep1();
  default:
    return null;
  }
}

export default observer(ModifyEmail);
