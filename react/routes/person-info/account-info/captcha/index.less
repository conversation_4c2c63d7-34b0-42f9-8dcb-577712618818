.captcha {
  .code-box {
    display: flex;
    margin: 24px 0;
		padding: 0 23px;

    .item-content {
      flex: 1;
      height: 32px;
			width: 28px;
      font-weight: 600;
      font-size: 14px;
      line-height: 32px;
      text-align: center;

      &.item-content-active::before {
        position: absolute;
        top: 25%;
        left: 50%;
        display: block;
        width: 2px;
        height: 50%;
        margin-left: -1px;
        background-color: #2979ff;
        animation: rc-captcha-input-flash steps(2) 1s infinite;
        content: '';
      }
    }

    .input-split {
      width: 28px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .input-box-wrap {
    position: absolute;
    left: -9999px;
  }

  &.captcha-theme-line {
    .code-box {
      .item-content {
        position: relative;
        color: #161823;
        border-bottom: 1px solid rgba(22, 24, 35, 0.5);
      }
    }
  }

  &.captcha-theme-box {
    .code-box {
      .item-content {
        color: #121212;
        border: 1px solid #d7e2ec;
        border-radius: 4px;

        &.item-content-active {
          position: relative;
          border-color: #2979ff;
        }
      }
    }
  }

  @keyframes rc-captcha-input-flash {
    0% {
      visibility: hidden;
    }

    100% {
      visibility: visible;
    }
  }
}
