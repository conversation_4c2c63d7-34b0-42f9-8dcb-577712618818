import React, { useContext, useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Icon, YqAvatar, FileUploader, StatusTag } from '@zknow/components';
import { Select, Form, TextField, Spin, Output, Modal, Tooltip, Button } from 'choerodon-ui/pro';
import ModifyEmail from './ModifyEmail';
import Store from '../stores';
import './index.less';

const PersonInfo = () => {
  const context = useContext(Store);
  const [type, setType] = useState('profile');
  const { intl, prefixCls, infoDataSet, tenantId, AppState: { getUserInfo } } = context;

  const handleSave = async () => {
    let refreshFlag = false;
    ['imageUrl', 'language', 'timeZone'].forEach((name) => {
      if (infoDataSet.current?.get(name) !== infoDataSet.current?.getPristineValue(name)) {
        refreshFlag = true;
      }
    });
    const flag = await Promise.all(infoDataSet.map(record => record.validate(true)));
    if (flag.some(f => !f)) return;
    const res = await infoDataSet.submit();
    if (res && refreshFlag) {
      setTimeout(() => {
        window.location.reload();
      }, 500);
    } else {
      infoDataSet.reset();
    }
    if (res) {
      infoDataSet.query();
    }
  };

  const modifyEmail = () => (
    <ModifyEmail context={context} setType={setType} type={type} email={infoDataSet.current?.get('email')} />
  );

  const renderEmail = ({ text }) => {
    const userCheckedFlag = infoDataSet?.current?.get('userCheckedFlag');
    return (
      <div className="email-info">
        {text || (
          <div className="email-info-bind">
            <Icon style={{ color: '#FF9500', display: 'inline-flex', fontSize: 16, marginRight: 8 }} type="info" />
            <span />
            <span style={{ color: '#262626' }}>{intl.formatMessage({ id: 'iam.personInfo.notbind', defaultMessage: '未绑定' })}</span>
          </div>
        )}
        <Tooltip placement="top" title={text ? intl.formatMessage({ id: 'iam.personInfo.modify.email', defaultMessage: '修改邮箱' }) : intl.formatMessage({ id: 'iam.personInfo.bind.email', defaultMessage: '绑定邮箱' })}>
          {!getUserInfo.person.ldap && (
            <div className="email-info-bind-edit" onClick={() => setType('modify')}>
              <Icon type="write" size={14} />
            </div>
          )}
        </Tooltip>
        <Tooltip placement="top" title={intl.formatMessage({ id: 'iam.personInfo.verify.email.tips', defaultMessage: '点击此处进行邮箱验证，提升账号安全性' })}>
          {!userCheckedFlag && (
            <div className="email-info-bind-edit verify" onClick={() => setType('verify')}>
              <Icon type="protect" size={14} />
            </div>
          )}
        </Tooltip>
      </div>
    );
  };

  const renderProfile = () => (
    <div className={`${prefixCls}-base`}>
      <div className={`${prefixCls}-base-header`}>
        <FileUploader name="imageUrl" record={infoDataSet.current} tenantId={tenantId}>
          <div style={{ cursor: 'pointer', position: 'relative' }}>
            <YqAvatar className={`${prefixCls}-base-header-avatar editable`} size={56} src={infoDataSet.current?.get('imageUrl')}>
              {infoDataSet.current?.get('realName')}
            </YqAvatar>
            <div className="camera"><Icon type="camera" size={12} fill="#000" theme="filled" /></div>
          </div>
        </FileUploader>
        <div className={`${prefixCls}-base-header-info`}>
          <div className="name">
            {infoDataSet.current?.get('realName')}
            {(infoDataSet.current?.get('ldap') || getUserInfo.person.ldap) && <StatusTag>LDAP</StatusTag>}
          </div>
          <div className="email">
            <Output name="email" record={infoDataSet.current} renderer={renderEmail} />
          </div>
        </div>
      </div>
      <div className={`${prefixCls}-base-message`}>
        <Spin dataSet={infoDataSet}>
          <div className={`${prefixCls}-base-message-form`}>
            <Form header={intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' })} labelWidth={80} columns={2} labelLayout="horizontal" dataSet={infoDataSet}>
              <TextField name="realName" />
              <TextField name="aliasName" />
              <TextField autoFocus name="phone" disabled={getUserInfo.person.ldap} />
              <Select name="language" />
              <Select name="timeZone" />
            </Form>
          </div>
          <div className={`${prefixCls}-base-message-buttons`}>
            <Button color="primary" onClick={handleSave} funcType="raised">
              {intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}
            </Button>
            <Button color="secondary" onClick={() => infoDataSet.reset()} funcType="raised">
              {intl.formatMessage({ id: 'zknow.common.button.reset', defaultMessage: '重置' })}
            </Button>
          </div>
        </Spin>
      </div>
    </div>
  );

  switch (type) {
  case 'profile':
    return renderProfile();
  case 'modify':
    return modifyEmail();
  case 'verify':
    return modifyEmail();
  default:
    return renderProfile();
  }
};

export default observer(PersonInfo);
