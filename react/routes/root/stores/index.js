import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import ListDataSet from './DataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({
  code: 'iam.root',
})(injectIntl(
  (props) => {
    const {
      intl,
      children,
    } = props;
    const intlPrefix = 'iam.root';
    const prefixCls = 'iam-root';
    const listDataSet = useMemo(() => new DataSet(ListDataSet({
      intlPrefix, intl, isAdmin: true, paging: true,
    })));
    // const formDataSet = useMemo(() => new DataSet(ListDataSet({
    //   intlPrefix, intl, isAdmin: false, paging: false,
    // })));
    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      listDataSet,
      // formDataSet,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
