import { getQueryParams } from '@zknow/utils';

export default ({ intlPrefix, intl, isAdmin, paging }) => {
  const url = 'iam/yqc/v1/users/root/paging';
  const realName = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const email = intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' });

  return {
    autoQuery: true,
    selection: false,
    paging,
    pageSize: 20,
    autoLocateFirst: false,
    transport: {
      read: ({ data }) => ({
        url,
        method: 'get',
        data: getQueryParams(data),
      }),
    },
    fields: [
      { name: 'id', type: 'string', label: 'ID' },
      { name: 'realName', type: 'string', label: realName },
      { name: 'email', type: 'string', label: email },
    ],
    queryFields: [
      { name: 'realName', type: 'string', label: realName },
      { name: 'email', type: 'string', label: email },
    ],
  };
};
