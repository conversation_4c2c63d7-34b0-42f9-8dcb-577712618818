import React from 'react';
import { observer } from 'mobx-react-lite';
import { Select, Form, DataSet, message, Lov } from 'choerodon-ui/pro';
import { axios } from '@yqcloud/apps-master';

const CreateForm = (props) => {
  const { intl, intlPrefix, modal, listDataSet } = props;
  const realName = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const successMsg = intl.formatMessage({ id: 'iam.root.desc.add.success', defaultMessage: '新建成功' });

  const selectDataSet = React.useMemo(() => {
    return new DataSet({
      autoCreate: true,
      fields: [
        {
          name: 'idObject',
          label: realName,
          required: true,
          textField: 'realName',
          valueField: 'id',
          type: 'object',
          lovCode: 'ROOT_USER',
          transformRequest: (value) => {
            return value?.id;
          },
        },
        { name: 'objectVersionNumber', type: 'number' },
      ],
      events: {
        update: ({ record, value, name }) => {
          if (name === 'idObject') {
            record.set('id', value.id);
            record.set('objectVersionNumber', value.objectVersionNumber);
          }
        },
      },
    });
  }, []);

  modal.handleOk(handleOk);
  async function handleOk() {
    try {
      const result = await selectDataSet.validate();
      if (!result) {
        return false;
      }
      const record = selectDataSet.current;
      const putData = {
        id: record.get('id'),
        objectVersionNumber: record.get('objectVersionNumber'),
        admin: true,
      };
      const res = await axios.put(`iam/hzero/v1/users/${putData.id}/root`);

      if (res.failed) {
        throw new Error(res.message);
      } else {
        message.success(successMsg);
      }
    } catch (err) {
      message.error(err?.message);
    }
    await listDataSet.query();
  }

  // function handleSearch() {
  //   let timer;
  //   return (e) => {
  //     const data = e.target.value;
  //     if (timer) clearTimeout(timer);
  //     timer = setTimeout(() => {
  //       formDataSet.query(0, { condition: data });
  //     }, 500);
  //   };
  // }

  return (
    <Form dataSet={selectDataSet} labelLayout="horizontal" labelAlign="right" labelWidth="auto">
      {/* <Select
        onInput={handleSearch()}
        searchable
        name="id"
        style={{ width: '3.4rem' }}
      /> */}
      <Lov id="yq-test-iam-root_lov" name="idObject" />
    </Form>
  );
};

export default observer(CreateForm);
