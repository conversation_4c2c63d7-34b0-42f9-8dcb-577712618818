import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Content, axios, TabPage } from '@yqcloud/apps-master';
import { TableHoverAction, Button } from '@zknow/components';
import { Table, Modal, message } from 'choerodon-ui/pro';

import Store from './stores';
import FormView from './FormView';

const modalKey = Modal.key();
const { Column } = Table;

function MainView() {
  const { intl, listDataSet, prefixCls, intlPrefix } = useContext(Store);
  const removeSuccessMsg = intl.formatMessage({ id: 'zknow.common.success.remove', defaultMessage: '移除成功' });

  const buttons = [
    renderAddBtn(),
  ];

  function renderAddBtn() {
    return (
      <Button
        id="yq-test-iam-root_create"
        icon="plus"
        funcType="raised"
        color="primary"
        onClick={handleAdd}
      >
        {intl.formatMessage({ id: 'zknow.common.button.add', defaultMessage: '新增' })}
      </Button>
    );
  }

  function handleAdd() {
    Modal.open({
      key: modalKey,
      title: intl.formatMessage({ id: 'iam.root.desc.add.root.user', defaultMessage: '添加Root账号' }),
      children: <FormView
        // formDataSet={formDataSet}
        listDataSet={listDataSet}
        intl={intl}
        intlPrefix={intlPrefix}
      />,
      style: { width: '5rem' },
      okProps: {
        id: 'yq-test-iam-root_save',
      },
      cancelProps: {
        id: 'yq-test-iam-root_cancel',
      },
    });
  }

  function renderTableAction({ record }) {
    return (
      <TableHoverAction
        record={record}
        actions={[
          {
            name: intl.formatMessage({ id: 'zknow.common.button.remove', defaultMessage: '移除' }),
            icon: 'PeopleMinusOne',
            onClick: () => {
              openConfirmModal(record);
            },
          },
        ]}
      />
    );
  }

  function openConfirmModal(record) {
    const content = intl.formatMessage({ id: 'iam.root.desc.remove.confirm.message', defaultMessage: '移除后，用户将无法管理平台！' });
    Modal.confirm({
      title: intl.formatMessage({ id: 'iam.root.desc.remove.confirm', defaultMessage: '确认移除Root账号吗?' }),
      children: (
        <div>
          {content}
        </div>
      ),
      onOk: () => handleRemove(record),
      key: modalKey,
      destroyOnClose: true,
      okProps: {
        id: 'yq-test-iam-root_confirm',
      },
      cancelProps: {
        id: 'yq-test-iam-root_cancel1',
      },
    });
  }

  async function handleRemove(record) {
    try {
      const data = record.toData();
      data.admin = false;
      const res = await axios.delete(`iam/hzero/v1/users/${data.id}/root`);
      if (res.failed) {
        throw new Error(res.message);
      } else {
        message.success(removeSuccessMsg);
      }
    } catch (err) {
      message.error(err.message);
    }

    await record.dataSet.query();
  }

  return (
    <TabPage>
      <Content className={`${prefixCls}`} style={{ padding: 0 }}>
        <Table
          labelLayout="float"
          pristine
          dataSet={listDataSet}
          placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
          autoHeight
          autoLocateFirst={false}
          buttons={buttons}
          queryBarProps={{
            title: intl.formatMessage({ id: 'iam.root.desc.menu.title.root', defaultMessage: 'Root账号' }),
          }}
        >
          <Column name="realName" />
          <Column name="email" />
          <Column width={0} renderer={renderTableAction} tooltip="none" />
        </Table>
      </Content>
    </TabPage>
  );
}

export default observer(MainView);
