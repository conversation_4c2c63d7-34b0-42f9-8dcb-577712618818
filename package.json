{"name": "@yqcloud/iam", "routeName": "iam", "description": "", "license": "ISC", "version": "1.40.0", "keywords": [], "contributors": ["yqcloud"], "main": "./lib/index.js", "files": ["lib"], "scripts": {"start": "zknow-front-boot start --config ./react/config.js", "dist": "zknow-front-boot dist --config ./react/config.js", "lint-staged": "lint-staged", "lint-staged:es": "eslint"}, "peerDependencies": {"@yqcloud/apps-master": ">=1.40.0-develop.0 <1.40.0-develop-1", "@zknow/boot": ">= 1.23.0-feature-7544.0 <1.23.0-feature-7544-1"}, "dependencies": {"@zknow/aj-captcha": "^1.0.6", "copy-to-clipboard": "^3.3.1", "dagre": "^0.8.5", "file-saver": "^2.0.5", "jsencrypt": "3.2.1", "react-flow-renderer": "^9.6.11", "react-sortablejs": "^1.5.1", "sortablejs": "^1.10.1"}, "devDependencies": {"@yqcloud/apps-master": ">=1.40.0-develop.0 <1.40.0-develop-1", "@zknow/boot": ">=1.23.0-feature-7544.0 <1.23.0-feature-7544-1"}, "lint-staged": {"react/**/*.{js,jsx}": ["npm run lint-staged:es"], "react/**/*.{scss,less}": "stylelint --syntax less"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}